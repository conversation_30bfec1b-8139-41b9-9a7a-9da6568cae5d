import * as echarts from 'echarts';
import Vue from 'vue';
import { toThousands, formatDate } from './common';
import qualityFaceStyle from '@/views/home/<USER>/module/quality-face/index.js';
function fontSize(res) {
  const clientWidth = window.innerWidth || document.documentElement.clientWidth || document.body.clientWidth;
  if (!clientWidth) return;
  // const isScreen = clientWidth == 1366
  const fontSize = clientWidth / 1920;
  return res * fontSize;
}
/**
 *
 * 环形图
 *
 */
//系统管理 运维管理 前端设备总量
export function equipmentMonitoringRing(opts) {
  return {
    title: {
      text: ['{a|' + opts.percent + '%\n\n} {b|} {c|' + opts.status + '}'],
      x: 'center',
      y: 'center',
      textStyle: {
        rich: {
          a: {
            fontFamily: 'SourceHanSansCN-Regular',
            fontWeight: '400',
            color: $var('--color-base-text'),
            fontSize: fontSize(20),
            align: 'center',
            textAlign: 'left',
          },
          b: {
            fontFamily: 'SourceHanSansCN-Medium',
            fontWeight: '500',
            backgroundColor: opts.statusColor,
            height: 10,
            width: 10,
            borderRadius: 5,
          },
          c: {
            color: $var('--color-base-text'),
          },
        },
      },
    },
    series: [
      {
        name: '到岗率',
        type: 'pie',
        clockwise: true,
        radius: [70, 90],
        startAngle: 90,
        label: {
          show: false,
        },
        itemStyle: {
          labelLine: {
            show: false,
          },
          color: opts.color,
        },
        emphasis: {
          scale: false,
        },
        data: [
          {
            value: opts.percent,
          },
          {
            value: 100 - opts.percent,
            itemStyle: {
              color: $var('--color-gray-3'), //未完成的圆环的颜色
            },
          },
        ],
      },
      {
        name: '外框',
        type: 'pie',
        animation: false,
        clockwise: false,
        radius: [100, 103],
        label: {
          show: false,
        },
        itemStyle: {
          labelLine: {
            show: false,
          },
          color: $var('--color-gray-2'),
        },
        emphasis: {
          scale: false,
        },
        tooltip: {
          show: false,
        },
        data: [
          {
            value: 100,
          },
          {
            value: 0,
          },
        ],
      },
    ],
  };
}
// 系统管理-运维管理-服务器检测-查看-环形图
export function servermonitoringRing(opts) {
  return {
    title: {
      text: ['{a|' + opts.percent + '%\n} {b|' + opts.text + '\n} {c|' + opts.parameter + '}'],
      x: 'center',
      y: 'center',
      textStyle: {
        rich: {
          a: {
            fontFamily: 'SourceHanSansCN-Regular',
            fontWeight: '400',
            color: $var('--color-base-text'),
            fontSize: fontSize(16),
            align: 'center',
            textAlign: 'left',
            height: 30,
          },
          b: {
            fontFamily: 'SourceHanSansCN-Medium',
            fontWeight: '500',
            color: $var('--color-gray-1'),
            width: 10,
            borderRadius: 5,
            height: 30,
            fontSize: fontSize(12),
          },
          c: {
            color: $var('--color-gray-1'),
            fontSize: fontSize(12),
          },
        },
      },
    },
    color: [$var('--color-white-1'), $var('--color-white-2'), $var('--color-white-3')],
    series: [
      {
        type: 'pie',
        center: ['50%', '50%'],
        radius: ['68%', '72%'],
        emphasis: {
          scale: false,
        },
        data: [
          {
            name: '',
            value: opts.percent,
            itemStyle: {
              color: opts.color,
            },
            labelLine: {
              show: false,
              emphasis: {
                show: false,
              },
            },
          },
          {
            //画中间的图标
            name: '',
            value: 0,
            label: {
              position: 'inside',
              backgroundColor: opts.color,
              width: 10,
              height: 10,
              borderRadius: 10,
              padding: 4,
            },
          },
          {
            //画中间的图标
            name: '',
            value: 0,
            label: {
              position: 'inside',
              backgroundColor: '#fff',
              width: 5,
              height: 5,
              borderRadius: 5,
              padding: 5,
            },
          },
          {
            //画剩余的刻度圆环
            name: '',
            value: 100 - opts.percent,
            label: {
              show: false,
            },
            labelLine: {
              show: false,
              emphasis: {
                show: false,
              },
            },
          },
        ],
      },
    ],
  };
}
// 治理主题首页环形图
export function themeRingChart(opts) {
  let data = [];
  const res = opts.showData;
  for (let i = 0; i < res.length; i++) {
    data.push(
      {
        value: res[i].value,
        name: res[i].name,
        itemStyle: {
          color: {
            type: 'linear',
            colorStops: opts.color[i],
            // global: false // 缺省为 false
          },
        },
      },
      // {
      //   value: res[0].value / 20,
      //   name: '',
      //   itemStyle: {
      // label: {
      //   show: false
      // },
      // labelLine: {
      //   show: false
      // },
      // color: 'rgba(0, 0, 0, 0)',
      // borderColor: 'rgba(0, 0, 0, 0)',
      // borderWidth: 0
      //   }
      // }
    );
  }

  // function _pie2() {
  // 	let dataArr = [];
  // 	for (var i = 0; i < 160; i++) {
  // 		if (i % 2 === 0) {
  // 			dataArr.push({
  // 				name: (i + 1).toString(),
  // 				value: 25,
  // 				itemStyle: {
  // 					normal: {
  // 						color: "#1BF9F9",
  // 						borderWidth: 0,
  // 						borderColor: "#1BF9F9",
  // 					},
  // 				},
  // 			});
  // 		} else {
  // 			dataArr.push({
  // 				name: (i + 1).toString(),
  // 				value: 20,
  // 				itemStyle: {
  // 					normal: {
  // 						color: "rgba(0,0,0,0)",
  // 						borderWidth: 0,
  // 						borderColor: "rgba(0,0,0,0)",
  // 					},
  // 				},
  // 			});
  // 		}
  // 	}
  // 	return dataArr;
  // }
  return {
    title: {
      show: true,
      text: opts.titleName,
      subtext: opts.subtitleName,
      left: 'center',
      top: '25%',
      textStyle: {
        fontSize: fontSize(14),
        color: '#ffffff',
        fontWeight: 300,
      },
      subtextStyle: {
        align: 'center',
        fontSize: fontSize(14),
        color: ['#1BF9F9'],
        fontWeight: 300,
      },
      triggerEvent: true, // 是否触发事件
    },
    legend: {
      show: false,
      // left: 'center',
      // bottom: 50,
      // itemWidth: 8,
      // itemHeight: 8,
      // textStyle: {
      //   color: '#caccd4',
      // },
      // data: data,
      // formatter: (name) => {
      //   for (let j = 0; j < opts.showData.length; j++) {
      //     if (opts.showData[j].name === name) {
      //       return `${opts.showData[j].name}：${opts.showData[j].value}`
      //     }
      //   }
      // }
    },
    // color: opts.color,
    tooltip: {
      show: true,
    },
    toolbox: {
      show: true,
    },
    series: [
      {
        name: '',
        type: 'pie',
        clockwise: false,
        avoidLabelOverlap: false,
        radius: ['50%', '65%'],
        center: ['50%', '30%'],
        emphasis: {
          scale: true,
        },
        itemStyle: {
          label: {
            show: false,
            fontSize: fontSize(14),
            formatter(params) {
              return params.name ? params.name + ' ' + params.value + '%' : '';
            },
          },
        },
        data: data,
      },
      // {
      // 	type: "pie",
      // 	clockwise: false,
      // 	radius: ["56%", "56.4%"],
      // 	center: ["50%", "30%"],
      // 	label: {
      // 		normal: {
      // 			show: false,
      // 		},
      // 	},
      // 	labelLine: {
      // 		normal: {
      // 			show: false,
      // 		},
      // 	},
      // 	// data: dotArr(),
      // },
      {
        type: 'gauge',
        radius: '47%', //图表尺寸
        center: ['50%', '30%'],
        startAngle: 0,
        endAngle: 360,
        splitNumber: 8,
        axisLine: {
          show: false,
        },
        axisTick: {
          show: true,
          lineStyle: {
            color: '#02B1FF',
            width: 3,
          },
          length: 1,
          splitNumber: 10,
        },
        splitLine: {
          show: false,
        },
        axisLabel: {
          show: false,
        },
        data: [],
        detail: {
          show: false,
        },
        triggerEvent: true, // 是否触发事件
      },
    ],
  };
}

// 评测管理查看详情环形图
export function evaluationPageRin(opts) {
  let data = [];
  const res = opts.showData;
  for (let i = 0; i < res.length; i++) {
    data.push({
      value: res[i].value,
      name: res[i].name,
      itemStyle: {
        color: {
          type: 'linear',
          colorStops: opts.color[i],
        },
      },
    });
  }
  return {
    // title: {
    //         itemGap: 10,
    //         text: '注册学生数萨达萨达十大',
    //         subtext: politicsFenBu_total,
    //         left: '30%',
    //         top: '44%',

    //         textStyle: {
    //                 fontWeight: '400',
    //                 fontSize: fontSize(18),
    //                 color: '#999'
    //         },
    // },
    title: {
      text: `${opts.seriesName}`,
      subtext: `${opts.count}`,
      textAlign: 'center',
      textStyle: {
        fontSize: fontSize(14),
        color: '#fff',
      },
      subtextStyle: {
        fontSize: fontSize(14),
        color: '#19C176',
      },
      // left: 'center',
      left: '34%',
      top: '44%',
      // position: 'inside
    },
    // title: {
    //         text: opts.count,
    //         left: '30%',
    //         top: '50%',
    //         // left: "center",
    //         textAlign: "center",
    //         textStyle: {
    //                 fontSize: "12",
    //                 color: "#19C176",
    //         }
    // },
    // graphic: {
    //         type: "text",
    //         left: '30%',
    //         top: '50%',
    //         // left: "25%",
    //         // // left: "center",
    //         // top: "center",
    //         style: {
    //                 text: opts.seriesName,
    //                 textAlign: "center",
    //                 fontSize: "12",
    //                 fill: "#fff",
    //         }
    // },
    legend: {
      orient: 'vertical',
      right: '0',
      top: '50%',
      itemWidth: fontSize(14),
      itemHeight: fontSize(14),
      itemGap: fontSize(10),
      formatter: function (name) {
        for (let i = 0; i < opts.showData.length; i++) {
          if (opts.showData[i].name === name) {
            return '{title|' + opts.showData[i].name + '}{value|' + opts.showData[i].value + '}';
          }
        }
      },
      textStyle: {
        color: '#FFF',
        rich: {
          title: {
            fontSize: fontSize(14),
            color: '#fff',
            padding: [0, 0, 0, 10],
          },
          value: {
            fontSize: fontSize(14),
            padding: [0, 0, 0, 10],
          },
        },
      },
    },
    tooltip: {
      trigger: 'item',
      formatter: '{b} : {c} ({d}%)',
    },
    series: [
      {
        type: 'pie',
        radius: ['50%', '66%'],
        center: ['35%', '50%'],
        avoidLabelOverlap: false,
        labelLine: {
          //设置延长线的长度
          length: 5, //设置延长线的长度
          length2: 30, //设置第二段延长线的长度
        },
        itemStyle: {
          borderWidth: 2, // 间距的宽度
          borderColor: '#08264d', //背景色
          color: '#fff',
          lineStyle: {
            color: '#fff',
          },
        },

        label: {
          show: true,
          position: 'outside',
          formatter: '{d}%\n{b}',
          color: '#fff',
          lineHeight: 14,
          fontSize: fontSize(14),
        },
        data: data,
      },
    ],
  };
}

// 数据治理 - 任务追踪环形图
export function taskTrackingRing(opts) {
  let greenColor = [
    {
      offset: 0,
      color: $var('--linear-gradient-green-1-start'),
    },
    {
      offset: 1,
      color: $var('--linear-gradient-green-1-end'),
    },
  ];
  let redColor = [
    {
      offset: 0,
      color: $var('--linear-gradient-orange-1-start'),
    },
    {
      offset: 1,
      color: $var('--linear-gradient-orange-1-end'),
    },
  ];
  let yellowColor = [
    {
      offset: 0,
      color: $var('--linear-gradient-yellow-4-start'),
    },
    {
      offset: 1,
      color: $var('--linear-gradient-yellow-4-end'),
    },
  ];
  let blueColor = [
    {
      offset: 0,
      color: $var('--linear-gradient-blue-3-start'),
    },
    {
      offset: 1,
      color: $var('--linear-gradient-blue-3-end'),
    },
  ];
  if (opts.data) {
    opts.data = opts.data.map((item) => {
      let one = {
        value: 10,
        name: 'test',
        itemStyle: {
          color: {
            type: 'linear',
          },
        },
      };
      one.value = item.value;
      one.name = item.name;
      if (item.color === 'greenColor') {
        one.itemStyle.color.colorStops = greenColor;
      }
      if (item.color === 'redColor') {
        one.itemStyle.color.colorStops = redColor;
      }
      if (item.color === 'yellowColor') {
        one.itemStyle.color.colorStops = yellowColor;
      }
      if (item.color === 'blueColor') {
        one.itemStyle.color.colorStops = blueColor;
      }
      return one;
    });
  }
  return {
    tooltip: {
      trigger: 'item',
    },
    title: {
      text: opts.text.replace(/\S{5}/g, function (match) {
        return match;
      }),
      x: 'center',
      y: 'center',
      textStyle: {
        fontSize: fontSize(12),
        color: $var('--color-base-text'),
      },
      itemGap: 2, //主副标题间距
      subtext: opts.subtext,
      subtextStyle: {
        fontSize: fontSize(12),
        color: $var('--color-blue-5'),
      },
    },
    legend: {
      show: !!(opts.data.length > 1),
      icon: 'rect', // 设置形状
      itemWidth: fontSize(10), // 设置大小
      itemHeight: fontSize(10),
      itemGap: fontSize(15), // 设置间距
      orient: 'vertical',
      right: '10',
      top: '20',
      data: opts.legendData,
      textStyle: {
        fontSize: fontSize(12),
      },
      // formatter: (name) => {
      //   re
      // }
    },
    series: [
      {
        type: 'gauge',
        radius: '75%', //图表尺寸
        center: ['50%', '50%'],
        startAngle: 0,
        endAngle: 360,
        splitNumber: 5,
        axisLine: {
          show: false,
        },
        axisTick: {
          show: true,
          lineStyle: {
            width: 3,
            color: $var('--color-gauge-axis-tick'),
          },
          length: 1,
          splitNumber: 10,
        },
        splitLine: {
          show: false,
        },
        axisLabel: {
          show: false,
        },
        data: [],
        detail: {
          show: false,
        },
      },
      {
        name: '',
        type: 'pie',
        radius: ['70%', '90%'],
        startAngle: '50',
        avoidLabelOverlap: false,
        top: 15,
        bottom: 15,
        label: {
          show: opts.data.length > 1,
          position: 'outside',
          color: '#fff', // 改变标示文字的颜色
          lineHeight: 22,
          formatter: '{b}\n{d}%',
          distanceToLabelLine: 10,
        },
        labelLine: {
          //引导线设置
          show: opts.data.length > 1,
          smooth: 0.2,
          length: 10,
          length2: 20,
        },
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 0,
        },
        data: opts.data,
      },
    ],
  };
}

export function dotArr() {
  let dataArr = [];
  for (var i = 0; i < 200; i++) {
    if (i % 2 === 0) {
      dataArr.push({
        name: (i + 1).toString(),
        value: 1,
        itemStyle: {
          color: '#8493A6',
          borderWidth: 1,
          borderColor: '#8493A6',
        },
      });
    } else {
      dataArr.push({
        name: (i + 1).toString(),
        value: 2,
        itemStyle: {
          color: 'rgba(0,0,0,0)',
          borderWidth: 0,
          borderColor: 'rgba(0,0,0,0)',
        },
      });
    }
  }
  return dataArr;
}

/**
 *
 * 柱状图
 *
 */
// 数据治理 - 任务追踪柱状图
export function taskTrackingColumn(opts) {
  let titleText = opts.data.length ? '' : '暂无数据';
  return {
    title: {
      text: titleText,
      x: 'center',
      y: 'center',
      textStyle: {
        color: '#fff',
        fontWeight: 'normal',
        fontSize: fontSize(16),
      },
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
      textStyle: {
        fontSize: fontSize(12),
      },
    },
    grid: {
      top: '25%',
      right: '1%',
      left: '5%',
      bottom: '25%',
    },
    xAxis: [
      {
        type: 'category',
        data: opts.xAxisData,
        axisLine: {
          lineStyle: {
            color: 'rgba(255,255,255,0.12)',
          },
        },
        axisLabel: {
          interval: 0,
          color: '#e2e9ff',
          fontSize: fontSize(12),
          rotate: 12,
        },
      },
    ],
    yAxis: [
      {
        name: '单位：个',
        axisLabel: {
          formatter: '{value}',
          color: '#e2e9ff',
          fontSize: fontSize(12),
        },
        axisLine: {
          show: false,
          lineStyle: {
            color: 'rgba(255,255,255,1)',
          },
        },
        splitLine: {
          show: false,
        },
      },
    ],
    series: [
      {
        type: 'bar',
        data: opts.data,
        barWidth: fontSize(13),
        itemStyle: {
          color: new echarts.graphic.LinearGradient(
            0,
            0,
            0,
            1,
            [
              {
                offset: 0,
                color: '#1B6EC7', // 0% 处的颜色
              },
              {
                offset: 1,
                color: '#00D5F7', // 100% 处的颜色
              },
            ],
            false,
          ),
        },
      },
    ],
  };
}

/**
 *
 * 折线图
 *
 */

//评测报表
// 柱状图 综合统计
export function evaluationReportColumn(opts) {
  return {
    grid: {
      left: '1%',
      right: '5%',
      top: 28,
      bottom: 0,
      containLabel: true,
    },
    tooltip: {
      textStyle: {
        align: 'left',
        fontSize: fontSize(12),
      },
      show: 'true',
      padding: [8, 10], //内边距
      extraCssText: 'background: rgba(13, 53, 96, 0.7);border: 1px solid #1684E4;opacity: 1;', //添加阴影
      formatter: function (params) {
        var result = '';
        var seriesName = `<span style="display:inline-block; margin-left:10px;">${params.data.value}%</span>`;
        var titleName = `<span style="display:inline-block;">${params.name}</span>`;
        var standardsIndexAmount = `<span style="display:inline-block; margin-left:10px;">${params.data.standardsIndexAmount}</span>`;
        var unStandardsIndexAmount = `<span style="display:inline-block; margin-left:10px;">${params.data.unStandardsIndexAmount}</span>`;

        result +=
          titleName +
          '</br>' +
          '综合达标值:' +
          seriesName +
          '</br>' +
          '达标指标:' +
          standardsIndexAmount +
          '</br>' +
          '不达标指标：' +
          unStandardsIndexAmount;
        return result;
      },
    },
    xAxis: [
      {
        type: 'category',
        name: `（${opts.targetDataTypeDesc}）`,
        nameTextStyle: {
          color: '#fff',
        },
        color: '#fff',
        data: opts.xAxis,

        axisTick: {
          alignWithLabel: true,
          show: false,
        },
        axisLine: {
          lineStyle: {
            color: '#2762AF',
          },
        },
        axisLabel: {
          interval: 0,
          color: '#fff', //更改坐标轴文字颜色
          fontSize: fontSize(14), //更改坐标轴文字大小
          show: true,
          position: 'inner',
          formatter: function (params) {
            let newName = '';

            if (params.length > 5) {
              newName = params.substring(0, 5) + '...';
            } else {
              newName = params;
            }

            return newName;
          },
        },
        splitLine: {
          show: false,
        },
      },
    ],
    yAxis: [
      {
        min: 0,
        max: 100,
        name: '（综合达标值）',
        nameTextStyle: {
          color: '#fff',
        },
        // boundaryGap: ['20%', '60%'],
        type: 'value',
        axisTick: {
          //y轴刻度线
          show: true,
        },
        splitLine: {
          lineStyle: {
            color: '#063d6b',
            width: fontSize(1),
          },
        },
        axisLine: {
          lineStyle: {
            color: '#2762AF',
          },
        },
        axisLabel: {
          formatter: '{value} %',
          //改变刻度字体样式
          color: '#fff',
          fontSize: fontSize(12),
        },
      },
    ],

    series: [
      {
        type: 'bar',
        barWidth: fontSize(35),
        data: opts.data,

        itemStyle: {
          color: function (params) {
            let colorList = [
              ['#00D5F7', '#0E5095'],
              ['#54F2B3', '#176350'],
              ['#B171F2', '#4B0D88'],
              ['#E2C420', '#5B4F0E'],
              ['#E97575', '#841D1D'],
              ['#4663F5', '#071D75'],
              ['#DB15A6', '#4D0741'],
              ['#0CCE33', '#0A4D46'],
              ['#F78400', '#5A350A'],
              ['#0CE8E8', '#064553'],
            ];
            let index = params.dataIndex;
            if (params.dataIndex >= colorList.length) {
              index = params.dataIndex % colorList.length;
            }
            return new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0,
                color: colorList[index][0],
              },
              {
                offset: 1,
                color: colorList[index][1],
              },
            ]);
          },
        },
      },
    ],
  };
}
export function evaluationTendencyColumn(opts) {
  let data = [];
  opts.data.forEach((row) => {
    if (row.value != '-1') {
      data.push({
        id: row.id,
        value: row.value,
        day: row.day,
      });
    }
  });

  return {
    grid: {
      left: '1%',
      right: '5%',
      top: 30,
      bottom: 0,
      containLabel: true,
    },
    tooltip: {
      trigger: 'item',
      formatter: `{c} %`,
      padding: [4, 10], //内边距
      extraCssText: 'background: rgba(13, 53, 96, 0.7);border: 1px solid #1684E4;opacity: 1;', //添加阴影
      textStyle: {
        fontSize: fontSize(12),
      },
    },
    xAxis: [
      {
        type: 'category',
        name: `（${opts.dayType}）`,
        nameTextStyle: {
          color: '#fff',
        },
        color: '#fff',
        data: opts.xAxis,
        axisTick: {
          alignWithLabel: true,
          show: false,
        },
        axisLine: {
          lineStyle: {
            color: '#2762AF',
          },
        },
        axisLabel: {
          interval: 0,
          color: '#fff',
          fontSize: fontSize(12),
          // rotate: 20,
        },
        splitLine: {
          show: false,
        },
      },
    ],
    yAxis: [
      {
        min: 0,
        max: 100,
        name: '（平均达标值）',
        nameTextStyle: {
          color: '#fff',
        },
        // boundaryGap: ['20%', '20%'],
        type: 'value',
        axisTick: {
          //y轴刻度线
          show: true,
        },
        splitLine: {
          lineStyle: {
            color: '#063d6b',
            width: fontSize(1),
          },
        },
        axisLine: {
          lineStyle: {
            color: '#2762AF',
          },
        },
        axisLabel: {
          formatter: '{value} %',
          //改变刻度字体样式
          color: '#fff',
          fontSize: fontSize(12),
        },
      },
    ],

    series: [
      {
        type: 'line',
        showAllSymbol: true,
        symbol: 'circle',
        symbolSize: 8,
        lineStyle: {
          width: fontSize(1),
          color: 'rgba(43, 132, 226, 1)',
          shadowColor: 'rgba(43, 132, 226, 1)',
          shadowBlur: 1,
          shadowOffsetY: 1,
        },
        label: {
          show: false,
          position: 'top',
          color: 'rgba(43, 132, 226, 1)',
        },

        itemStyle: {
          color: 'rgba(8, 38, 77, 1)',
          borderColor: 'rgba(43, 132, 226, 1)',
          borderWidth: 2,
        },
        tooltip: {
          show: true,
        },
        data: data,
      },
    ],
  };
}
export function evaluationIndexStatistics(opts) {
  let text2 = '';
  if (opts.standard === true) {
    text2 = opts.img1;
  } else {
    text2 = opts.img2;
  }

  return {
    grid: {
      left: '3%',
      right: '6%',
      top: 10,
      containLabel: true,
    },
    graphic: {
      elements: [
        {
          type: 'image',
          z: 12,
          style: {
            image: text2,
            width: 16,
            // shadowBlur: 10,
            // shadowColor: '#000',
            // shadowOffsetX: 0,
            // shadowOffsetY: 0,
          },
          left: 'center',
          top: '47%',
          position: [100, 100],
        },
      ],
    },

    title: [
      {
        text: opts.title,
        x: 'center',
        top: '80%',
        textStyle: {
          fontSize: fontSize(12),
        },
      },
      {
        text: `${opts.subTitle} %`,
        x: 'center',
        top: '35%',
        textStyle: {
          fontSize: fontSize(12),
        },
      },
    ],

    angleAxis: {
      max: 100, // 满分
      clockwise: false, // 逆时针
      // 隐藏刻度线
      axisLine: {
        show: false,
      },
      axisTick: {
        show: false,
      },
      axisLabel: {
        show: false,
      },
      splitLine: {
        show: false,
      },
    },
    radiusAxis: {
      type: 'category',
      // 隐藏刻度线
      axisLine: {
        show: false,
      },
      axisTick: {
        show: false,
      },
      axisLabel: {
        show: false,
      },
      splitLine: {
        show: false,
      },
    },
    polar: {
      center: ['50%', '40%'],
      radius: '110%', //图形大小
    },

    series: [
      {
        type: 'bar',
        data: opts.data,
        coordinateSystem: 'polar',
        roundCap: true,
        barWidth: fontSize(20),
        color: opts.color[0],
        barGap: '-80%', // 两环重叠
        z: 2,
      },
      {
        // 灰色环
        type: 'bar',
        data: [
          {
            value: 100,
            itemStyle: {
              color: opts.color[1],
            },
          },
        ],
        coordinateSystem: 'polar',
        roundCap: true,
        barWidth: fontSize(20),
        barGap: '-100%', // 两环重叠
        z: 1,
      },
    ],
  };
}
export function evaluationSubordinateColumn(opts, axisTooltip) {
  return {
    grid: {
      left: '1%',
      right: '5%',
      top: 28,
      bottom: 10,
      containLabel: true,
    },
    // legend: {
    //   height: 'auto',
    //   left: 150,
    //   itemWidth: 14,
    //   itemHeight: 14,
    //   x: 'center',
    //   orient: 'horizontal',
    //   textStyle: {
    //     color: '#ffffff',
    //   },
    // },
    tooltip: {
      // enterable: true,
      show: 'true',
      trigger: 'axis', //触发类型
      axisPointer: {
        //去掉移动的指示线
        type: 'none',
      },
      padding: [8, 10], //内边距

      formatter: function (params) {
        var result = axisTooltip ? `<div>${params[0].name}</div>` : '';
        params.forEach(function (item) {
          var dotHtml = `<span style="display:inline-block;margin-right:5px;width:10px;height:10px;background: linear-gradient(${item.color.colorStops[0].color} 0%, ${item.color.colorStops[1].color} 100%);"></span>`;
          var seriesName = `<span style="display:inline-block; margin-left:10px;">${item.seriesName}</span>`;
          var number = `<span style="display:inline-block;float: right;margin-left:10px;">${
            opts.dataObj ? item.data.percentage + '/' + item.data.sum : item.data + '%'
          }</span>`;
          result += dotHtml + seriesName + number + '</br>';
        });
        return result;
      },
      textStyle: {
        fontSize: fontSize(12),
      },
    },
    xAxis: [
      {
        type: 'category',
        name: `（${opts.targetDataTypeDesc}）`,
        data: opts.xAxis,
        axisTick: {
          alignWithLabel: true,
          show: false,
        },
        splitLine: {
          show: false,
        },
      },
    ],
    dataZoom: [
      {
        type: 'slider',
        show: false,
        start: opts.zoomStart,
        end: opts.zoomEnd, //默认显示条柱数\
      },
    ],

    yAxis: [
      {
        min: 0,
        max: 100,
        name: '(达标值)',
        // boundaryGap: ['20%', '60%'],
        type: 'value',
        axisTick: {
          //y轴刻度线
          show: true,
        },
        axisLabel: {
          formatter: '{value}%',
          // formatter: function(params) {
          //   console.log(params, 'params888')
          // },
          fontSize: fontSize(12),
        },
      },
    ],
    series: opts.data,
  };
}
// 首页关键属性检测图
export function determinantColumn(opts) {
  // let titleText = !!opts.data.length ? '' : '暂无数据'
  return {
    grid: {
      left: '3%',
      right: '1%',
      top: '20%',
      bottom: 0,
      containLabel: true,
    },
    // title: {
    //   // text: titleText,
    //   x: 'center',
    //   y: 'center',
    //   textStyle: {
    //     color: '#fff',
    //     fontWeight: 'normal',
    //     fontSize: fontSize(16),
    //   },
    // },
    tooltip: {
      show: 'true',
      trigger: 'axis', //触发类型
      axisPointer: {
        type: 'line', // 默认为直线，可选为：'line' | 'shadow' | 'cross' , shadow表示阴影
        lineStyle: {
          type: 'dashed',
        },
      },
      padding: [8, 10], //内边距
      extraCssText: 'background: rgba(13, 53, 96, 0.7);border:  1px solid #1684E4;opacity: 1;', //添加阴影
      formatter: function (params) {
        var result = '';
        var propertyColumn = `<span style="display:inline-block;">${params[0].axisValue}</span>`;
        var total = `<span style="display:inline-block; margin-left:10px;">${params[0].value}</span>`;
        var deviceRate = `<span style="display:inline-block; margin-left:10px;">${params[0].data.deviceRate.toFixed(
          2,
        )}%</span>`;
        result += propertyColumn + ':' + total + '</br>' + '异常占比:' + deviceRate;
        return result;
      },
      textStyle: {
        fontSize: fontSize(12),
      },
    },
    xAxis: [
      {
        type: 'category',
        nameTextStyle: {
          color: '#fff',
        },
        color: '#fff',
        data: opts.xAxis,

        axisTick: {
          alignWithLabel: true,
          show: false,
        },
        axisLine: {
          lineStyle: {
            color: '#2762AF',
          },
        },
        axisLabel: {
          interval: 0,
          color: '#fff', //更改坐标轴文字颜色
          fontSize: fontSize(12), //更改坐标轴文字大小
          show: true,
          position: 'inner',
          formatter: function (params) {
            let newName = '';

            if (params.length > 5) {
              newName = params.substring(0, 5) + '...';
            } else {
              newName = params;
            }
            return newName;
          },
          rotate: 40,
        },
        splitLine: {
          show: false,
        },
      },
    ],

    yAxis: [
      {
        min: 0,
        // max: 100,
        name: '异常设备数量',
        nameTextStyle: {
          color: '#fff',
        },
        // boundaryGap: ['20%', '60%'],
        type: 'value',
        axisTick: {
          //y轴刻度线
          show: true,
        },
        splitLine: {
          lineStyle: {
            color: '#063d6b',
            width: fontSize(1),
          },
        },
        axisLine: {
          lineStyle: {
            color: '#2762AF',
          },
        },
        axisLabel: {
          formatter: '{value}',
          // formatter: function(params) {
          //   console.log(params, 'params888')
          // },
          color: '#e2e9ff',
          fontSize: fontSize(12),
        },
      },
    ],
    series: [
      {
        type: 'bar',
        data: opts.data,
        barWidth: fontSize(13),
        itemStyle: {
          color: new echarts.graphic.LinearGradient(
            0,
            0,
            0,
            1,
            [
              {
                offset: 0,
                color: '#4DB2FF', // 0% 处的颜色
              },
              {
                offset: 1,
                color: '#3357CE', // 100% 处的颜色
              },
            ],
            false,
          ),
        },
      },
    ],
  };
}
// 首页治理趋势图
export function governColumn(opts) {
  return {
    grid: {
      left: '1%',
      right: '1%',
      top: '20%',
      bottom: '1%',
      containLabel: true,
    },
    tooltip: {
      trigger: 'axis',
      confine: true,
      axisPointer: {
        // 坐标轴指示器，坐标轴触发有效
        type: 'line', // 默认为直线，可选为：'line' | 'shadow' | 'cross' , shadow表示阴影
        lineStyle: {
          type: 'dashed',
        },
      },
      // padding: [4, 10], //内边距
      formatter: opts.tooltipFormatter,
      extraCssText: 'background: rgba(13, 53, 96, 0.7);border: 1px solid #1684E4;opacity: 1;', //添加阴影
      textStyle: {
        fontSize: fontSize(12),
      },
    },
    legend: {
      icon: 'rect',
      data: opts.data.map((item) => item.name),
      itemWidth: fontSize(10),
      itemHeight: fontSize(10),
      top: 15,
      left: 70,
      textStyle: {
        color: '#BEE2FB',
        width: fontSize(35),
        overflow: 'truncate',
        fontSize: fontSize(12),
      },
      formatter: function (name) {
        return echarts.format.truncateText(name, fontSize(60), '…');
      },
      tooltip: {
        show: true,
        textStyle: {
          fontSize: fontSize(14),
        },
      },
    },
    xAxis: [
      {
        type: 'category',
        name: `（${opts.dayType}）`,
        nameTextStyle: {
          color: '#fff',
        },
        color: '#fff',
        data: opts.xAxis,
        axisTick: {
          alignWithLabel: true,
          show: false,
        },
        axisLine: {
          lineStyle: {
            color: '#2762AF',
          },
        },
        axisLabel: {
          interval: 0,
          color: '#fff',
          fontSize: fontSize(12),
          // rotate: 20,
        },
        splitLine: {
          show: false,
        },
      },
    ],
    yAxis: [
      {
        name: '达标值',
        min: 0,
        max: 100,
        nameTextStyle: {
          color: '#fff',
          fontSize: fontSize(12),
        },
        // boundaryGap: ['20%', '20%'],
        type: 'value',
        axisTick: {
          //y轴刻度线
          show: true,
        },
        splitLine: {
          lineStyle: {
            color: '#063d6b',
            width: fontSize(1),
          },
        },
        axisLine: {
          lineStyle: {
            color: '#2762AF',
          },
        },
        axisLabel: {
          formatter: '{value} %',
          //改变刻度字体样式
          color: '#fff',
          fontSize: fontSize(12),
        },
      },
    ],
    series: opts.data,
    // series: [
    //   {
    //     type: 'line',
    //     showAllSymbol: true,
    //     symbol: 'circle',
    //     symbolSize: 8,
    //     smooth: false,
    //     // itemStyle: {
    //     //   shadowBlur: 4,
    //     //   shadowColor: 'rgba(4, 205, 244, 1)',
    //     // },
    //     // lineStyle: {
    //     //   normal: {
    //     //     width: 1,
    //     //     color: 'rgba(4, 205, 244, 1)',
    //     //   },
    //     // },

    //     lineStyle: {
    //       normal: {
    //         width: 1,
    //         color: 'rgba(4, 205, 244, 1)', //线条颜色
    //         shadowColor: 'rgba(4, 205, 244, 1)',
    //         shadowBlur: 1,
    //         shadowOffsetY: 1,
    //       },
    //     },
    //     itemStyle: {
    //       color: 'rgba(4, 205, 244, 1)',
    //       borderColor: 'rgba(4, 205, 244, 1)',
    //       borderWidth: 2,
    //     },
    //     label: {
    //       show: false,
    //       position: 'top',
    //       textStyle: {
    //         color: 'rgba(4, 205, 244, 1)',
    //       },
    //     },

    //     areaStyle: {
    //       normal: {
    //         color: new echarts.graphic.LinearGradient(
    //           0,
    //           0,
    //           0,
    //           1,
    //           [
    //             {
    //               offset: 0,
    //               color: 'rgba(4, 205, 244, 0.5)',
    //             },
    //             {
    //               offset: 1,
    //               color: 'rgba(67, 144, 250, 0.1)',
    //             },
    //           ],
    //           false
    //         ),
    //       },
    //     },
    //     tooltip: {
    //       show: true,
    //     },
    //     data: data,
    //   },
    // ],
  };
}
// 首页资产分布 (上下两个柱子)
export function propertyColumn(opts) {
  // let titleText = !!opts.series.length ? '' : '暂无数据'
  return {
    grid: {
      left: '3%',
      right: '1%',
      top: '20%',
      bottom: 0,
      containLabel: true,
    },
    legend: {
      type: 'plain',
      right: '5',
      top: '5',
      data: opts.lengName,
      itemGap: fontSize(10),
      itemWidth: fontSize(10),
      itemHeight: fontSize(10),
      textStyle: {
        fontSize: fontSize(12),
        color: '#A9C1E5',
      },
    },
    // title: {
    //   text: titleText,
    //   x: 'center',
    //   y: 'center',
    //   textStyle: {
    //     color: '#fff',
    //     fontWeight: 'normal',
    //     fontSize: fontSize(12),
    //   },
    // },
    tooltip: {
      show: 'true',
      trigger: 'axis', //触发类型
      axisPointer: {
        type: 'line', // 默认为直线，可选为：'line' | 'shadow' | 'cross' , shadow表示阴影
        lineStyle: {
          type: 'dashed',
        },
      },
      padding: [8, 10], //内边距
      extraCssText: 'background: rgba(13, 53, 96, 0.7);border:  1px solid #1684E4;opacity: 1;', //添加阴影
      textStyle: {
        fontSize: fontSize(12),
      },
      // formatter: function(params) {
      //   console.log(params, 'params11111')
      //   // var result = ''
      //   // var propertyColumn = `<span style="display:inline-block;">${params[0].axisValue}</span>`
      //   // var total = `<span style="display:inline-block; margin-left:10px;">${params[0].value}</span>`
      //   // var deviceRate = `<span style="display:inline-block; margin-left:10px;">${params[0].data.deviceRate}%</span>`
      //   // result += propertyColumn + ':' + total + '</br>' + '异常占比:' + deviceRate
      //   // return result
      // },
    },
    xAxis: [
      {
        type: 'category',
        nameTextStyle: {
          color: '#fff',
        },
        color: '#fff',
        data: opts.xAxis,

        axisTick: {
          alignWithLabel: true,
          show: false,
        },
        axisLine: {
          lineStyle: {
            color: '#2762AF',
          },
        },
        axisLabel: {
          interval: 0,
          color: '#fff', //更改坐标轴文字颜色
          fontSize: fontSize(12), //更改坐标轴文字大小
          show: true,
          position: 'inner',
          formatter: function (params) {
            let newName = '';

            if (params.length > 3) {
              newName = params.substring(0, 3) + '...';
            } else {
              newName = params;
            }
            return newName;
          },
          rotate: 40,
        },
        splitLine: {
          show: false,
        },
      },
    ],

    yAxis: [
      {
        min: 0,
        // max: 100,
        name: '单位：台',
        nameTextStyle: {
          color: '#fff',
          fontSize: fontSize(12),
        },
        // boundaryGap: ['20%', '60%'],
        type: 'value',
        axisTick: {
          //y轴刻度线
          show: true,
        },
        splitLine: {
          lineStyle: {
            color: '#063d6b',
            width: fontSize(1),
          },
        },
        axisLine: {
          lineStyle: {
            color: '#2762AF',
          },
        },
        axisLabel: {
          formatter: '{value}',
          color: '#e2e9ff',
          fontSize: fontSize(12),
        },
      },
    ],

    series: opts.series,
  };
}
// 首页资产分布 (三个柱子)
export function propertColumn(opts) {
  return {
    grid: {
      left: '3%',
      right: '1%',
      top: '20%',
      bottom: 0,
      containLabel: true,
    },
    legend: {
      type: 'plain',
      right: '5',
      top: '5',
      data: opts.lengName,
      itemGap: fontSize(10),
      itemWidth: fontSize(10),
      itemHeight: fontSize(10),
      textStyle: {
        fontSize: fontSize(12),
        color: '#A9C1E5',
      },
    },
    // title: {
    //   text: titleText,
    //   x: 'center',
    //   y: 'center',
    //   textStyle: {
    //     color: '#fff',
    //     fontWeight: 'normal',
    //     fontSize: fontSize(12),
    //   },
    // },
    tooltip: {
      show: 'true',
      trigger: 'axis', //触发类型
      axisPointer: {
        //去掉移动的指示线
        type: 'none',
      },
      padding: [8, 10], //内边距
      extraCssText: 'background: rgba(13, 53, 96, 0.7);border:  1px solid #1684E4;opacity: 1;', //添加阴影
      textStyle: {
        fontSize: fontSize(12),
      },
      // formatter: function(params) {
      //   console.log(params, 'params11111')
      //   // var result = ''
      //   // var propertyColumn = `<span style="display:inline-block;">${params[0].axisValue}</span>`
      //   // var total = `<span style="display:inline-block; margin-left:10px;">${params[0].value}</span>`
      //   // var deviceRate = `<span style="display:inline-block; margin-left:10px;">${params[0].data.deviceRate}%</span>`
      //   // result += propertyColumn + ':' + total + '</br>' + '异常占比:' + deviceRate
      //   // return result
      // },
    },
    xAxis: [
      {
        type: 'category',
        nameTextStyle: {
          color: '#fff',
        },
        color: '#fff',
        data: opts.xAxis,

        axisTick: {
          alignWithLabel: true,
          show: false,
        },
        axisLine: {
          lineStyle: {
            color: '#2762AF',
          },
        },
        axisLabel: {
          interval: 0,
          color: '#fff', //更改坐标轴文字颜色
          fontSize: fontSize(12), //更改坐标轴文字大小
          show: true,
          position: 'inner',
          formatter: function (params) {
            let newName = '';

            if (params.length > 3) {
              newName = params.substring(0, 3) + '...';
            } else {
              newName = params;
            }
            return newName;
          },
          rotate: 40,
        },
        splitLine: {
          show: false,
        },
      },
    ],

    yAxis: [
      {
        min: 0,
        name: '单位：台',
        nameTextStyle: {
          color: '#fff',
          fontSize: fontSize(12),
        },
        // boundaryGap: ['20%', '60%'],
        type: 'value',
        axisTick: {
          //y轴刻度线
          show: true,
        },
        splitLine: {
          lineStyle: {
            color: '#063d6b',
            width: fontSize(1),
          },
        },
        axisLine: {
          lineStyle: {
            color: '#2762AF',
          },
        },
        axisLabel: {
          formatter: '{value}',
          color: '#e2e9ff',
          fontSize: fontSize(12),
        },
      },
    ],
    series: opts.series,
  };
}
// 首页（视图数据检测
export function detectionColumn(opts) {
  // let titleText = !!opts.data.length ? '' : '暂无数据'
  return {
    grid: {
      left: '4%',
      right: '1%',
      top: '20%',
      bottom: 0,
      containLabel: true,
    },
    title: {
      // text: '2018年阅读量统计',
      subtext: `接入总量: {a|${toThousands(opts.count.accessDataCount)}}    检测数量：{b|${toThousands(
        opts.count.checkCount,
      )}}`,
      right: '1%',
      subtextStyle: {
        fontSize: fontSize(12),
        color: 'rgba(190, 226, 251, 1)',
        textShadowBlur: 5,
        rich: {
          a: {
            color: 'rgba(37, 243, 241, 1)',
            fontSize: fontSize(12),
            textShadowBlur: 5,
          },
          b: {
            color: 'rgba(245, 175, 85, 1)',
            fontSize: fontSize(12),
            textShadowBlur: 5,
          },
        },
      },
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'line', // 默认为直线，可选为：'line' | 'shadow' | 'cross' , shadow表示阴影
        lineStyle: {
          type: 'dashed',
        },
      },
      extraCssText: 'background: rgba(13, 53, 96, 0.7);border: 1px solid #1684E4;opacity: 1;', //添加阴影
      textStyle: {
        fontSize: fontSize(12),
      },
    },

    xAxis: [
      {
        type: 'category',
        nameTextStyle: {
          color: '#fff',
        },
        color: '#fff',
        data: opts.xAxis,

        axisTick: {
          alignWithLabel: true,
          show: false,
        },
        axisLine: {
          lineStyle: {
            color: '#2762AF',
          },
        },
        axisLabel: {
          color: '#fff', //更改坐标轴文字颜色
          fontSize: fontSize(12), //更改坐标轴文字大小
          show: true,
          position: 'inner',
          /*formatter: function(params) {
          let newName = ''

          if (params.length > 5) {
            newName = params.substring(0, 5) + '...'
          } else {
            newName = params
          }
          return newName
        },*/
          interval: 0,
          // rotate: 40,
        },
        splitLine: {
          show: false,
        },
      },
    ],

    yAxis: [
      {
        name: opts.targetDataTypeDesc,
        nameTextStyle: {
          color: '#fff',
        },
        // boundaryGap: ['20%', '60%'],
        type: 'value',
        axisTick: {
          //y轴刻度线
          show: true,
        },
        splitLine: {
          lineStyle: {
            color: '#063d6b',
            width: fontSize(1),
          },
        },
        axisLine: {
          lineStyle: {
            color: '#2762AF',
          },
        },
        axisLabel: {
          formatter: '{value}',
          color: '#e2e9ff',
          fontSize: fontSize(12),
        },
      },
    ],
    series: [
      {
        type: 'bar',
        data: opts.data,
        barWidth: fontSize(22),
        itemStyle: {
          color: new echarts.graphic.LinearGradient(
            0,
            0,
            0,
            1,
            [
              {
                offset: 0,
                color: 'rgba(255, 178, 77, 1)', // 0% 处的颜色
              },
              {
                offset: 1,
                color: 'rgba(169, 64, 13, 1)', // 100% 处的颜色
              },
            ],
            false,
          ),
        },
      },
    ],
  };
}

export function homepageLiquidFill(opts) {
  return {
    title: [
      {
        text: '实时视频无法调阅',
        x: '13%',
        y: '42%',
        textStyle: {
          fontSize: fontSize(12),
          fontWeight: '100',
          color: '#BEE2FB',
          lineHeight: 12,
          textAlign: 'center',
        },
      },
      {
        text: '历史视频无法调阅',
        x: '58%',
        y: '42%',
        textStyle: {
          fontSize: fontSize(12),
          fontWeight: '100',
          color: '#BEE2FB',
          lineHeight: 12,
          textAlign: 'center',
        },
      },
      {
        text: 'OSD字幕不合规',
        x: '14%',
        y: '89%',
        textStyle: {
          fontSize: fontSize(12),
          fontWeight: '100',
          color: '#BEE2FB',
          lineHeight: 12,
          textAlign: 'center',
        },
      },
      {
        text: '设备时钟错误',
        x: '60%',
        y: '89%',
        textStyle: {
          fontSize: fontSize(12),
          fontWeight: '100',
          color: '#BEE2FB',
          lineHeight: 12,
          textAlign: 'center',
        },
      },
    ],
    series: [
      {
        type: 'liquidFill',
        data: opts.videoPlayingAccuracy,
        silent: true,
        color: [
          {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: '#C1F7D9',
              },
              {
                offset: 1,
                color: '#59F096',
              },
            ],
            globalCoord: false,
          },
        ],
        backgroundStyle: {
          color: '#274e78',
        },
        outline: {
          show: false,
        },
        radius: '30%',
        center: ['25%', '22%'],
        label: {
          formatter: function ({ data }) {
            return `${data.count}\n\n${data.origin}%`;
          },
          color: '#59F096',
          fontSize: fontSize(12),
          fontFamily: 'Lobster Two',
        },
      },
      {
        type: 'pie',
        clockwise: true,
        cursor: 'auto',
        emphasis: {
          scale: false,
        },
        radius: ['35%', '37%'],
        center: ['25%', '22%'],
        itemStyle: {
          label: {
            show: false,
          },
        },
        data: [
          {
            value: 100,
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0.2, 1, 0.3, 0, [
                {
                  offset: 0,
                  color: '#C1F7D9',
                },
                {
                  offset: 1,
                  color: '#59F096',
                },
              ]),
            },
          },
        ],
      },
      {
        type: 'liquidFill',
        title: {
          text: 100 + '%',
        },
        silent: true,
        data: opts.videoOsdAccuracy,
        color: [
          {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: '#CEB8F1',
              },
              {
                offset: 1,
                color: '#8F34FF',
              },
            ],
            globalCoord: false,
          },
        ],
        backgroundStyle: {
          borderWidth: 1,
          color: '#274e78',
        },
        outline: {
          show: false,
        },
        radius: '30%',
        center: ['25%', '70%'],
        label: {
          formatter: function ({ data }) {
            return `${data.count}\n\n${data.origin}%`;
          },
          color: '#DFCDFA',
          fontSize: fontSize(12),
          fontFamily: 'Lobster Two',
        },
      },
      {
        type: 'pie',
        clockwise: true,
        cursor: 'auto',
        emphasis: {
          scale: false,
        },
        radius: ['35%', '37%'],
        center: ['25%', '70%'],
        itemStyle: {
          label: {
            show: false,
          },
        },
        data: [
          {
            value: 100,
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0.2, 1, 0.3, 0, [
                {
                  offset: 0,
                  color: '#CEB8F1',
                },
                {
                  offset: 1,
                  color: '#8F34FF',
                },
              ]),
            },
          },
        ],
      },
      {
        type: 'liquidFill',
        silent: true,
        data: opts.videoHistory,
        color: [
          {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: '#D5F1B5',
              },
              {
                offset: 1,
                color: '#D6E71A',
              },
            ],
            globalCoord: false,
          },
        ],
        backgroundStyle: {
          borderWidth: 1,
          color: '#274e78',
        },
        outline: {
          show: false,
        },
        radius: '30%',
        center: ['70%', '22%'],
        label: {
          formatter: function ({ data }) {
            return `${data.count}\n\n${data.origin}%`;
          },
          color: '#F6FBC8',
          fontSize: fontSize(12),
          fontFamily: 'Lobster Two',
        },
      },
      {
        type: 'pie',
        clockwise: true,
        cursor: 'auto',
        emphasis: {
          scale: false,
        },
        radius: ['35%', '37%'],
        center: ['70%', '22%'],
        itemStyle: {
          label: {
            show: false,
          },
        },
        data: [
          {
            value: 100,
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0.2, 1, 0.3, 0, [
                {
                  offset: 0,
                  color: '#D5F1B5',
                },
                {
                  offset: 1,
                  color: '#D6E71A',
                },
              ]),
            },
          },
        ],
      },
      {
        type: 'liquidFill',
        title: {
          text: 100 + '%',
        },
        silent: true,
        data: opts.videoClockAccuracy,
        color: [
          {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: '#B3E4ED',
              },
              {
                offset: 1,
                color: '#3AE0E8',
              },
            ],
            globalCoord: false,
          },
        ],
        backgroundStyle: {
          borderWidth: 1,
          color: '#274e78',
        },
        outline: {
          show: false,
        },
        radius: '30%',
        center: ['70%', '70%'],
        label: {
          formatter: function ({ data }) {
            return `${data.count}\n\n${data.origin}%`;
          },
          color: '#92E9F9',
          fontSize: fontSize(12),
          fontFamily: 'Lobster Two',
        },
      },
      {
        type: 'pie',
        clockwise: true,
        cursor: 'auto',
        emphasis: {
          scale: false,
        },
        radius: ['35%', '37%'],
        center: ['70%', '70%'],
        itemStyle: {
          label: {
            show: false,
          },
        },
        data: [
          {
            value: 100,
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0.2, 1, 0.3, 0, [
                {
                  offset: 0,
                  color: '#B3E4ED',
                },
                {
                  offset: 1,
                  color: '#3AE0E8',
                },
              ]),
            },
          },
        ],
      },
    ],
  };
}

// 评测概览(建档率环形)
export function evaluationoverrRate(opts) {
  return {
    title: [
      {
        text: opts.title,
        x: 'center',
        top: '50%',
        textStyle: {
          color: '#FFFFFF',
          fontSize: fontSize(12),
        },
      },
      {
        text: `${Number.parseFloat(opts.subTitle).toFixed(2)}%`,
        x: 'center',
        top: '35%',
        textStyle: {
          fontSize: fontSize(12),
          color: '#FFFFFF',
        },
      },
    ],

    angleAxis: {
      max: 100, // 满分
      clockwise: false, // 逆时针
      // 隐藏刻度线
      axisLine: {
        show: false,
      },
      axisTick: {
        show: false,
      },
      axisLabel: {
        show: false,
      },
      splitLine: {
        show: false,
      },
    },
    radiusAxis: {
      type: 'category',
      // 隐藏刻度线
      axisLine: {
        show: false,
      },
      axisTick: {
        show: false,
      },
      axisLabel: {
        show: false,
      },
      splitLine: {
        show: false,
      },
    },
    polar: {
      center: ['50%', '46%'],
      radius: '150%', //图形大小
    },

    series: [
      {
        type: 'bar',
        data: opts.data,
        coordinateSystem: 'polar',
        roundCap: true,
        barWidth: fontSize(20),
        color: opts.color[0],
        barGap: '-80%', // 两环重叠
        z: 2,
      },
      {
        // 灰色环
        type: 'bar',
        data: [
          {
            value: 100,
            itemStyle: {
              color: opts.color[1],
            },
          },
        ],
        coordinateSystem: 'polar',
        roundCap: true,
        barWidth: fontSize(20),
        barGap: '-100%', // 两环重叠
        z: 1,
      },
    ],
  };
}

export function evaluationOverRin(opts) {
  let data = [];
  const res = opts.showData;
  for (let i = 0; i < res.length; i++) {
    data.push({
      value: res[i].value,
      name: res[i].name,
      itemStyle: {
        color: {
          type: 'linear',
          colorStops: opts.color[i],
        },
      },
    });
  }
  return {
    title: {
      text: `${opts.count}`,
      subtext: `${opts.seriesName}`,
      textAlign: 'center',
      textStyle: {
        fontSize: fontSize(12),
        color: $var('--color-blue-1'),
      },
      subtextStyle: {
        fontSize: fontSize(12),
        color: $var('--color-base-text'),
      },
      // left: 'center',
      left: '49%',
      top: '29%',
      // position: 'inside
    },

    legend: {
      orient: 'vertical',
      right: '10px',
      bottom: '-5px',
      itemWidth: fontSize(10),
      itemHeight: fontSize(10),
      itemGap: fontSize(5),
      formatter: function (name) {
        for (let i = 0; i < opts.showData.length; i++) {
          if (opts.showData[i].name === name) {
            return '{title|' + opts.showData[i].name + '}{value|' + opts.showData[i].value + '}';
          }
        }
      },
      textStyle: {
        color: $var('--color-base-text'),
        rich: {
          title: {
            fontSize: fontSize(12),
            color: $var('--color-base-text'),
            padding: [0, 0, 0, 10],
          },
          value: {
            fontSize: fontSize(12),
            padding: [0, 0, 0, 10],
          },
        },
      },
    },
    tooltip: {
      trigger: 'item',
      formatter: '{b} : {c} ({d}%)',
      textStyle: {
        fontSize: fontSize(12),
        color: $var('--color-base-text'),
      },
      backgroundColor: 'var(--bg-tooltip)',
    },
    // polar: {
    //   center: ['50%', '46%'],
    //   radius: '130%', //图形大小
    // },
    series: [
      {
        type: 'pie',
        radius: ['50%', '66%'],
        center: ['52%', '40%'],
        label: {
          show: false,
          position: 'center',
        },
        labelLine: {
          show: false,
        },
        data: data,
      },
    ],
  };
}

export function captureRationality(opts) {
  return {
    grid: {
      left: '3%',
      right: '10%',
      top: '20%',
      bottom: 0,
      containLabel: true,
    },
    // title: {
    //   text: 'titleText',
    //   x: 'center',
    //   y: 'center',
    //   textStyle: {
    //     color: '#fff',
    //     fontWeight: 'normal',
    //     fontSize: fontSize(16),
    //   },
    // },
    tooltip: {
      show: 'true',
      trigger: 'axis', //触发类型
      axisPointer: {
        //去掉移动的指示线
        type: 'none',
      },
      padding: [8, 10], //内边距
      extraCssText: 'background: rgba(13, 53, 96, 0.7);border:  1px solid #1684E4;opacity: 1;', //添加阴影
      formatter: function (params) {
        var result = '';
        var propertyColumn = `<span style="display:inline-block;">${params[0].axisValue}</span>`;
        var total = `<span style="display:inline-block; margin-left:10px;">${params[0].value}</span>`;
        result += propertyColumn + ':' + total;
        return result;
      },
      textStyle: {
        fontSize: fontSize(12),
      },
    },
    xAxis: [
      {
        name: '异常原因',
        nameLocation: 'end',
        nameGap: 10,
        type: 'category',
        nameTextStyle: {
          color: '#fff',
        },
        color: '#fff',
        data: opts.xAxis,

        axisTick: {
          alignWithLabel: true,
          show: false,
        },
        axisLine: {
          lineStyle: {
            color: '#2762AF',
          },
        },
        axisLabel: {
          interval: 0,
          color: '#fff', //更改坐标轴文字颜色
          fontSize: fontSize(12), //更改坐标轴文字大小
          show: true,
          position: 'inner',
          formatter: function (params) {
            let newName = '';

            if (params.length > 5) {
              newName = params.substring(0, 5) + '...';
            } else {
              newName = params;
            }
            return newName;
          },
          rotate: 40,
        },
        splitLine: {
          show: false,
        },
      },
    ],

    yAxis: [
      {
        min: 0,
        // max: 100,
        name: '异常设备数量',
        nameTextStyle: {
          color: '#fff',
        },
        // boundaryGap: ['20%', '60%'],
        type: 'value',
        axisTick: {
          //y轴刻度线
          show: true,
        },
        splitLine: {
          lineStyle: {
            color: '#063d6b',
            width: fontSize(1),
          },
        },
        axisLine: {
          lineStyle: {
            color: '#2762AF',
          },
        },
        axisLabel: {
          formatter: '{value}',
          // formatter: function(params) {
          //   console.log(params, 'params888')
          // },
          color: '#e2e9ff',
          fontSize: fontSize(12),
        },
      },
    ],
    series: [
      {
        type: 'bar',
        data: opts.data,
        barWidth: fontSize(13),
        itemStyle: {
          color: new echarts.graphic.LinearGradient(
            0,
            0,
            0,
            1,
            [
              {
                offset: 0,
                color: '#4DB2FF', // 0% 处的颜色
              },
              {
                offset: 1,
                color: '#3357CE', // 100% 处的颜色
              },
            ],
            false,
          ),
        },
      },
    ],
  };
}

export function overviewColumn(opts) {
  return {
    grid: {
      left: '2%',
      right: '2%',
      top: 30,
      bottom: 10,
      containLabel: true,
    },
    tooltip: {
      show: 'true',
      trigger: 'axis', //触发类型
      axisPointer: {
        type: 'line', // 默认为直线，可选为：'line' | 'shadow' | 'cross' , shadow表示阴影
        lineStyle: {
          type: 'dashed',
        },
      },
      padding: [8, 10], //内边距
      // extraCssText: 'background: rgba(13, 53, 96, 0.7);border:  1px solid #1684E4;opacity: 1;', //添加阴影
      formatter: function (params) {
        var result = '';
        var propertyColumn = `<span style="display:inline-block;">${params[0].axisValue}</span>`;
        var total = `<span style="display:inline-block; margin-left:10px;">${params[0].value}</span>`;
        var deviceRate = `<span style="display:inline-block; margin-left:10px;">${params[0].data.deviceRate}</span>`;
        result += propertyColumn + ':' + total + '</br>' + '异常占比:' + deviceRate;
        return result;
      },
      textStyle: {
        fontSize: fontSize(12),
      },
    },
    xAxis: [
      {
        type: 'category',
        name: '日期',
        data: opts.xAxis,
        axisTick: {
          alignWithLabel: true,
          show: false,
        },
        axisLabel: {
          interval: 0,
          // color: '#fff', //更改坐标轴文字颜色
          fontSize: fontSize(12), //更改坐标轴文字大小
          show: true,
          position: 'inner',
          formatter: function (params) {
            let newName = '';

            if (params.length > 5) {
              newName = params.substring(0, 5) + '...';
            } else {
              newName = params;
            }
            return newName;
          },
          rotate: 40,
        },
        splitLine: {
          show: false,
        },
      },
    ],

    yAxis: [
      {
        name: '异常设备数量',
        type: 'value',
        nameTextStyle: {
          padding: [0, 0, 0, 30],
        },
        axisTick: {
          //y轴刻度线
          show: true,
        },
        splitLine: {
          show: false,
        },
        axisLabel: {
          formatter: '{value}',
          // formatter: function(params) {
          //   console.log(params, 'params888')
          // },
          fontSize: fontSize(12),
        },
      },
    ],
    series: [
      {
        type: 'bar',
        data: opts.data,
        barWidth: fontSize(21),
        itemStyle: {
          color: new echarts.graphic.LinearGradient(
            0,
            0,
            0,
            1,
            [
              {
                offset: 0,
                color: $var('--linear-gradient-blue-5-start'), // 0% 处的颜色
              },
              {
                offset: 1,
                color: $var('--linear-gradient-blue-5-end'), // 100% 处的颜色
              },
            ],
            false,
          ),
        },
      },
    ],
  };
}
// 设备档案（新）-折线
export function equipmentArchives(opts) {
  let data = [];
  opts.data.forEach((row) => {
    if (row.value != '-1') {
      data.push({
        id: row.id,
        value: row.value,
        day: row.day,
      });
    }
  });

  return {
    grid: {
      left: '1%',
      right: '5%',
      top: 30,
      bottom: 0,
      containLabel: true,
    },
    tooltip: {
      trigger: 'axis',
      padding: [4, 10], //内边距
      textStyle: {
        fontSize: fontSize(12),
      },
      formatter: opts.tooltipFormatter,
    },
    axisPointer: {
      type: 'line',
      lineStyle: {
        type: 'dashed',
      },
    },
    xAxis: [
      {
        type: 'category',
        name: `${opts.dayType}`,
        data: opts.xAxis,
        axisTick: {
          alignWithLabel: true,
          show: false,
        },
        axisLabel: {
          interval: 0,
          fontSize: fontSize(12),
          formatter: function (value) {
            if (value === 0) {
              return value;
            }
            return value;
          },
        },
        splitLine: {
          show: false,
        },
      },
    ],
    yAxis: [
      {
        name: '抓拍数量',
        type: 'value',
        axisTick: {
          //y轴刻度线
          show: true,
        },
        splitLine: {
          lineStyle: {
            width: fontSize(1),
          },
        },
        axisLabel: {
          formatter: function (value) {
            if (value === 0) {
              return value;
            }
            return value;
          },
          fontSize: fontSize(12),
        },
      },
    ],
    series: [
      {
        name: '抓拍数',
        type: 'line',
        showAllSymbol: true,
        symbol: 'circle',
        symbolSize: 8,
        lineStyle: {
          width: fontSize(1),
          color: $var('--color-blue-28'),
          shadowColor: 'rgba(43, 132, 226, 1)',
          shadowBlur: 1,
          shadowOffsetY: 1,
        },
        label: {
          show: false,
          position: 'top',
          color: 'rgba(43, 132, 226, 1)',
        },
        itemStyle: {
          color: $var('--bg-echart'),
          borderColor: $var('--color-blue-28'),
          borderWidth: 2,
        },
        areaStyle: {
          //自定义颜色，渐变色填充折线图区域
          color: new echarts.graphic.LinearGradient(
            0,
            0,
            0,
            1, //变化度
            //渐变色
            [
              {
                offset: 0,
                color: $var('--linear-gradient-blue-7-start'),
              },
              {
                offset: 1,
                color: $var('--linear-gradient-blue-7-end'),
              },
            ],
          ),
        },
        tooltip: {
          show: true,
        },
        data: data,
      },
    ],
  };
}

export function placeRationality(opts) {
  return {
    grid: {
      left: '4%',
      right: '5%',
      top: '20%',
      bottom: '3%',
      containLabel: true,
    },
    tooltip: {
      show: 'true',
      trigger: 'axis', //触发类型
      axisPointer: {
        //去掉移动的指示线
        type: 'none',
      },
      padding: [8, 10], //内边距
      extraCssText: 'background: rgba(13, 53, 96, 0.7);border:  1px solid #1684E4;opacity: 1;', //添加阴影
      formatter: function (params) {
        var total = `<p>${params[0].axisValue}：${params[0].value}</p>`;
        var rateTotal = `<p>异常占比：${opts.echartsData[params[0].dataIndex].proportion}</p>`;
        return total + rateTotal;
      },
      textStyle: {
        fontSize: fontSize(12),
      },
    },
    xAxis: [
      {
        name: '字段',
        nameLocation: 'end',
        nameGap: 10,
        type: 'category',
        nameTextStyle: {
          color: '#fff',
        },
        color: '#fff',
        data: opts.xAxis,

        axisTick: {
          alignWithLabel: true,
          show: false,
        },
        axisLine: {
          lineStyle: {
            color: '#2762AF',
          },
        },
        axisLabel: {
          interval: 0,
          color: '#fff', //更改坐标轴文字颜色
          fontSize: fontSize(12), //更改坐标轴文字大小
          show: true,
          position: 'inner',
          formatter: function (params) {
            let newName = '';

            if (params.length > 5) {
              newName = params.substring(0, 5) + '...';
            } else {
              newName = params;
            }
            return newName;
          },
          rotate: 40,
        },
        splitLine: {
          show: false,
        },
      },
    ],

    yAxis: [
      {
        min: 0,
        name: '异常场所数量',
        nameTextStyle: {
          color: '#fff',
        },
        type: 'value',
        axisTick: {
          show: true,
        },
        splitLine: {
          lineStyle: {
            color: '#063d6b',
            width: fontSize(1),
          },
        },
        axisLine: {
          lineStyle: {
            color: '#2762AF',
          },
        },
        axisLabel: {
          formatter: '{value}',
          color: '#e2e9ff',
          fontSize: fontSize(12),
        },
      },
    ],
    series: [
      {
        type: 'bar',
        data: opts.data,
        barWidth: fontSize(13),
        itemStyle: {
          color: new echarts.graphic.LinearGradient(
            0,
            0,
            0,
            1,
            [
              {
                offset: 0,
                color: '#4DB2FF', // 0% 处的颜色
              },
              {
                offset: 1,
                color: '#3357CE', // 100% 处的颜色
              },
            ],
            false,
          ),
        },
      },
    ],
  };
}
// 四川省考核成绩统计柱状图
export function taskResultMade(opts) {
  return {
    grid: {
      left: '0%',
      right: '4%',
      bottom: '3%',
      containLabel: true,
    },
    legend: {
      right: '4%',
      textStyle: {
        color: ' #fff',
        fontSize: fontSize(12),
      },
    },
    color: [
      '#3DA1EE',
      '#8F3BB2',
      '#1C62F3',
      '#14C271',
      '#EEA126',
      '#CE1755',
      '#99B11C',
      '#C911D6',
      '#07A12C',
      '#DB5A98',
    ],
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        // 坐标轴指示器，坐标轴触发有效
        type: 'line', // 默认为直线，可选为：'line' | 'shadow' | 'cross' , shadow表示阴影
        lineStyle: {
          type: 'dashed',
        },
      },
      enterable: true,
      padding: [4, 10], //内边距
      formatter: opts.tooltipFormatter,
      extraCssText: 'background: rgba(13, 53, 96, 0.7);border: 1px solid #1684E4;opacity: 1;', //添加阴影
      textStyle: {
        fontSize: fontSize(12),
      },
    },
    xAxis: [
      {
        type: 'category',
        data: opts.xAxis,
        nameTextStyle: {
          color: '#fff',
        },
        color: '#fff',
        axisLine: {
          lineStyle: {
            color: '##071B39',
          },
        },
        axisTick: {
          //y轴刻度线
          show: false,
          alignWithLabel: true,
        },
        axisLabel: {
          interval: 0,
          color: '#fff', //更改坐标轴文字颜色
          fontSize: fontSize(12), //更改坐标轴文字大小
          formatter: function (params) {
            let newName = '';
            if (params.length > 5) {
              newName = params.substring(0, 5) + '...';
            } else {
              newName = params;
            }
            return newName;
            // },
          },
        },
      },
    ],
    yAxis: [
      {
        type: 'value',
        name: '得分',
        min: 0,
        nameTextStyle: {
          color: '#fff',
        },
        axisTick: {
          //y轴刻度线
          show: false,
        },
        axisLine: {
          show: false,
          lineStyle: {
            color: '#2762AF',
          },
        },
        axisLabel: {
          formatter: '{value}',
          color: '#e2e9ff',
          fontSize: fontSize(12),
        },
        splitLine: {
          lineStyle: {
            type: 'solid',
            color: 'rgba(255, 255, 255, 0.14901960784313725)',
            width: fontSize(1),
          },
        },
      },
    ],
    series: opts.data,
  };
}
export function taskResultMadeSC(opts) {
  return {
    grid: {
      left: '0%',
      right: '0%',
      bottom: '2%',
      top: '9%',
      containLabel: true,
    },
    legend: {
      right: '0%',
      textStyle: {
        color: $var('--color-base-text'),
      },
      selected: opts.selected,
    },
    color: [
      $var('--color-series-blue-3'),
      $var('--color-series-blue-4'),
      $var('--color-series-blue-5'),
      $var('--color-series-blue-6'),
      $var('--color-series-blue-7'),
      $var('--color-series-blue-8'),
      $var('--color-series-blue-9'),
      $var('--color-series-blue-10'),
      $var('--color-series-blue-11'),
      $var('--color-series-blue-12'),
    ],
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        // 坐标轴指示器，坐标轴触发有效
        type: 'line', // 默认为直线，可选为：'line' | 'shadow' | 'cross' , shadow表示阴影
        lineStyle: {
          type: 'dashed',
        },
      },
      enterable: true,
      padding: [4, 10], //内边距
      formatter: opts.tooltipFormatter,
      extraCssText: 'background: var(--bg-tooltip);opacity: 1;', //添加阴影
      textStyle: {
        fontSize: fontSize(12),
      },
    },
    xAxis: [
      {
        type: 'category',
        data: opts.xAxis,
        nameTextStyle: {
          color: $var('--color-axis-lable'),
        },
        color: $var('--color-axis-lable'),
        axisLine: {
          lineStyle: {
            color: $var('--color-axis-line'),
          },
        },
        axisTick: {
          //y轴刻度线
          show: false,
        },
        axisLabel: {
          interval: 0,
          color: $var('--color-axis-lable'), //更改坐标轴文字颜色
          fontSize: fontSize(12), //更改坐标轴文字大小
          formatter: function (params) {
            let newName = '';
            if (params.length > 3) {
              newName = params.substring(0, 3) + '...';
            } else {
              newName = params;
            }
            return newName;
          },
        },
      },
    ],
    yAxis: [
      {
        type: 'value',
        name: '得分',
        min: 0,
        nameTextStyle: {
          color: $var('--color-axis-lable'),
        },
        axisTick: {
          //y轴刻度线
          show: false,
        },
        axisLine: {
          show: false,
          lineStyle: {
            color: $var('--color-axis-line'),
          },
        },
        axisLabel: {
          formatter: '{value}',
          color: $var('--color-axis-lable'),
          fontSize: fontSize(12),
        },
        splitLine: {
          lineStyle: {
            type: 'solid',
            color: $var('--color-split-line'),
            width: fontSize(1),
          },
        },
      },
    ],
    series: opts.data,
  };
}

//资产分布 在线状态
export function onlineAmount(opts) {
  // let titleText = !!opts.data.length ? '' : '暂无数据'
  return {
    grid: {
      left: '1%',
      right: '1%',
      top: '23%',
      bottom: 0,
      containLabel: true,
    },
    tooltip: {
      show: 'true',
      trigger: 'axis', //触发类型
      axisPointer: {
        type: 'line', // 默认为直线，可选为：'line' | 'shadow' | 'cross' , shadow表示阴影
        lineStyle: {
          type: 'dashed',
        },
      },
      padding: [8, 10], //内边距
      extraCssText: 'background: rgba(13, 53, 96, 0.7);border:  1px solid #1684E4;opacity: 1;', //添加阴影
      formatter: function (params) {
        /*var result = ''
        var propertyColumn = `<span style="display:inline-block;">${params[0].axisValue}</span>`
        var total = `<span style="display:inline-block; margin-left:10px;">${params[0].value}</span>`
        var deviceRate = `<span style="display:inline-block; margin-left:10px;">${params[0].data.deviceRate.toFixed(
          2
        )}%</span>`
        result += propertyColumn + ':' + total + '</br>' + '异常占比:' + deviceRate
        return result*/
        return `在线率：${opts.data[params[0].dataIndex].onlineRate || 0}%</br>
                在线数量：${opts.data[params[0].dataIndex].onlineAmount || 0}</br>
                离线数量：${opts.data[params[0].dataIndex].offlineAmount || 0}`;
      },
      textStyle: {
        fontSize: fontSize(12),
      },
    },
    xAxis: [
      {
        type: 'category',
        nameTextStyle: {
          color: '#fff',
        },
        color: '#fff',
        data: opts.xAxis,

        axisTick: {
          alignWithLabel: true,
          show: false,
        },
        axisLine: {
          lineStyle: {
            color: '#2762AF',
          },
        },
        axisLabel: {
          interval: 0,
          color: '#fff', //更改坐标轴文字颜色
          fontSize: fontSize(12), //更改坐标轴文字大小
          show: true,
          position: 'inner',
          formatter: function (params) {
            let newName = '';

            if (params.length > 5) {
              newName = params.substring(0, 5) + '...';
            } else {
              newName = params;
            }
            return newName;
          },
          rotate: 40,
        },
        splitLine: {
          show: false,
        },
      },
    ],
    title: {
      text: '设备在线率',
      top: 10,
      textStyle: {
        color: '#fff',
        fontSize: fontSize(12),
      },
    },
    yAxis: [
      {
        min: 0,
        // name: '设备在线率',
        // nameTextStyle: {
        //   color: '#fff',
        // },
        type: 'value',
        axisTick: {
          //y轴刻度线
          show: true,
        },
        splitLine: {
          lineStyle: {
            color: '#063d6b',
            width: fontSize(1),
          },
        },
        axisLine: {
          lineStyle: {
            color: '#2762AF',
          },
        },
        axisLabel: {
          formatter: '{value} %',
          color: '#e2e9ff',
          fontSize: fontSize(12),
        },
      },
    ],
    series: opts.series,
  };
}

export function lineCircleShade(opts) {
  let defaultSeries = {
    // name: '抓拍数',
    type: 'line',
    showAllSymbol: true,
    symbol: 'circle',
    symbolSize: 8,
    lineStyle: {
      width: fontSize(2),
      color: $var('--color-blue-28'),
      // shadowColor: 'rgba(43, 132, 226, 1)',
      // shadowBlur: 1,
      // shadowOffsetY: 1,
    },
    label: {
      show: false,
      position: 'top',
    },
    itemStyle: {
      color: $var('--bg-echart'),
      borderColor: $var('--color-blue-28'),
      borderWidth: 2,
    },
    areaStyle: {
      //自定义颜色，渐变色填充折线图区域
      color: new echarts.graphic.LinearGradient(
        0,
        0,
        0,
        1, //变化度
        //渐变色
        [
          {
            offset: 0,
            color: $var('--linear-gradient-green-6-start'),
          },
          {
            offset: 1,
            color: $var('--linear-gradient-green-6-end'),
          },
        ],
      ),
    },
    tooltip: {
      show: true,
    },
  };
  let series = [];
  Object.keys(opts.data).forEach((key) => {
    let data = opts.data[key].map((row) => {
      return {
        value: row.vertical,
        name: row.indexName,
        ...row,
      };
    });
    series.push({
      ...defaultSeries,
      data: data,
      name: data.length && data[0].name,
    });
  });
  let xAxisData = opts.data.default && opts.data.default.map((row) => row.horizontal);
  return {
    grid: {
      left: '1%',
      right: '1%',
      top: 40,
      bottom: 10,
      containLabel: true,
    },
    tooltip: {
      trigger: 'axis',
      padding: [4, 10], //内边距
      formatter: opts.tooltipFormatter,
      textStyle: {
        fontSize: fontSize(12),
      },
    },
    axisPointer: {
      type: 'line',
      lineStyle: {
        type: 'dashed',
        color: $var('--color-blue-28'),
      },
    },
    xAxis: [
      {
        type: 'category',
        data: xAxisData,
        axisTick: {
          alignWithLabel: true,
          show: false,
        },
        axisLabel: {
          interval: 0,
          fontSize: fontSize(12),
          formatter: function (value) {
            if (value === 0) {
              return value;
            }
            return value;
          },
        },
        splitLine: {
          show: false,
        },
      },
    ],
    yAxis: [
      {
        type: 'value',
        name: '达标值',
        nameTextStyle: {
          align: 'right',
        },
        axisTick: {
          //y轴刻度线
          show: true,
        },
        splitLine: {
          show: false,
        },
        axisLabel: {
          formatter: function (value) {
            return `${value}%`;
          },
          fontSize: fontSize(12),
        },
      },
    ],
    series: series,
    /*formatter: (data) => {
      let lineChartDom = Vue.extend(chartDom)
      let _this = new lineChartDom({
        el: document.createElement('div'),
        data () {
          return {
            data,
          }
        },
      })
      return _this.$el.outerHTML
    },*/
  };
}
//视频图像采集区域数量达标率
export function lineCircleShadeQuantityStandard(opts) {
  let defaultSeries = {
    // name: '抓拍数',
    type: 'line',
    showAllSymbol: true,
    symbol: 'circle',
    symbolSize: 8,
    lineStyle: {
      width: fontSize(1),
      color: $var('--color-cyan-3'),
      shadowColor: 'rgba(43, 132, 226, 1)',
      shadowBlur: 1,
      shadowOffsetY: 1,
    },
    label: {
      show: false,
      position: 'top',
      color: 'rgba(43, 132, 226, 1)',
    },
    itemStyle: {
      color: $var('--bg-echart'),
      borderColor: $var('--color-cyan-3'),
      borderWidth: 2,
    },
    areaStyle: {
      //自定义颜色，渐变色填充折线图区域
      color: new echarts.graphic.LinearGradient(
        0,
        0,
        0,
        1, //变化度
        //渐变色
        [
          {
            offset: 0,
            color: $var('--linear-gradient-green-3-start'),
          },
          {
            offset: 1,
            color: $var('--linear-gradient-green-3-end'),
          },
        ],
      ),
    },
    tooltip: {
      show: true,
    },
  };
  let series = [];
  Object.keys(opts.data).forEach((key) => {
    let data = opts.data[key].map((row) => {
      return {
        value: row.vertical,
        name: row.indexName,
        ...row,
      };
    });
    series.push({
      ...defaultSeries,
      data: data,
      name: data.length && data[0].name,
    });
  });
  let xAxisData = opts.data.default && opts.data.default.map((row) => row.horizontal);
  return {
    grid: {
      left: '1%',
      right: '1%',
      top: 30,
      bottom: 10,
      containLabel: true,
    },
    tooltip: {
      trigger: 'axis',
      padding: [4, 10], //内边距
      formatter: opts.tooltipFormatter,
      textStyle: {
        fontSize: fontSize(12),
      },
    },
    axisPointer: {
      type: 'line',
      lineStyle: {
        type: 'dashed',
        color: $var('--color-axis-line'),
      },
    },
    xAxis: [
      {
        type: 'category',
        data: xAxisData,
        axisTick: {
          alignWithLabel: true,
          show: false,
        },
        axisLabel: {
          interval: 0,
          fontSize: fontSize(12),
          formatter: function (value) {
            if (value === 0) {
              return value;
            }
            return value;
          },
        },
        splitLine: {
          show: false,
        },
      },
    ],
    yAxis: [
      {
        type: 'value',
        name: `达标值`,
        nameTextStyle: {
          align: 'right',
        },
        axisTick: {
          //y轴刻度线
          show: true,
        },
        splitLine: {
          show: false,
        },
        axisLabel: {
          formatter: function (value) {
            return `${value}%`;
          },
          //改变刻度字体样式
          fontSize: fontSize(12),
        },
      },
    ],
    series: series,
    /*formatter: (data) => {
      let lineChartDom = Vue.extend(chartDom)
      let _this = new lineChartDom({
        el: document.createElement('div'),
        data () {
          return {
            data,
          }
        },
      })
      return _this.$el.outerHTML
    },*/
  };
}
export function lineCircleShadeCaptureStability(opts) {
  return {
    grid: {
      left: '1%',
      right: '1%',
      top: 30,
      bottom: 10,
      containLabel: true,
    },
    legend: {
      type: 'plain',
      right: '5',
      top: '5',
      data: opts.lengName,
      itemGap: fontSize(10),
      itemWidth: fontSize(10),
      itemHeight: fontSize(10),
      textStyle: {
        fontSize: fontSize(12),
      },
    },
    tooltip: {
      show: 'true',
      trigger: 'axis', //触发类型
      axisPointer: {
        type: 'none',
      },
      padding: [8, 10], //内边距
      formatter: opts.tooltipFormatter ? opts.tooltipFormatter : '',
      textStyle: {
        fontSize: fontSize(12),
      },
    },
    xAxis: [
      {
        type: 'category',
        data: opts.xAxis,

        axisTick: {
          alignWithLabel: true,
          show: false,
        },
        axisLabel: {
          interval: 0,
          fontSize: fontSize(12), //更改坐标轴文字大小
          show: true,
          position: 'inner',
          formatter: function (params) {
            let newName = '';
            if (params.length > 3) {
              newName = params.substring(0, 3) + '...';
            } else {
              newName = params;
            }
            return `${newName}月`;
          },
          rotate: 40,
        },
        splitLine: {
          show: false,
        },
      },
    ],

    yAxis: [
      {
        min: 0,
        nameTextStyle: {
          fontSize: fontSize(12),
        },
        type: 'value',
        axisTick: {
          //y轴刻度线
          show: true,
        },
        splitLine: {
          show: false,
        },
        axisLabel: {
          formatter: '{value}%',
          fontSize: fontSize(12),
        },
      },
      {
        min: 0,
        // name: '单位：台',
        nameTextStyle: {
          fontSize: fontSize(12),
        },
        // boundaryGap: ['20%', '60%'],
        type: 'value',
        axisTick: {
          //y轴刻度线
          show: true,
        },
        splitLine: {
          show: false,
          lineStyle: {
            width: fontSize(1),
          },
        },
        axisLabel: {
          formatter: '{value}',
          fontSize: fontSize(12),
        },
      },
    ],
    series: opts.series,
  };
}
export function lineCircleShadeControlStability(opts) {
  let defaultSeries = {
    // name: '抓拍数',
    type: 'line',
    showAllSymbol: true,
    symbol: 'circle',
    symbolSize: 8,
    lineStyle: {
      width: fontSize(1),
      color: $var('--color-cyan-3'),
      shadowColor: 'rgba(43, 132, 226, 1)',
      shadowBlur: 1,
      shadowOffsetY: 1,
    },
    label: {
      show: false,
      position: 'top',
      color: 'rgba(43, 132, 226, 1)',
    },
    itemStyle: {
      color: $var('--bg-echart'),
      borderColor: $var('--color-cyan-3'),
      borderWidth: 2,
    },
    areaStyle: {
      //自定义颜色，渐变色填充折线图区域
      color: new echarts.graphic.LinearGradient(
        0,
        0,
        0,
        1, //变化度
        //渐变色
        [
          {
            offset: 0,
            color: $var('--linear-gradient-green-3-start'),
          },
          {
            offset: 1,
            color: $var('--linear-gradient-green-3-end'),
          },
        ],
      ),
    },
    tooltip: {
      show: true,
    },
  };
  let series = [];
  Object.keys(opts.data).forEach((key) => {
    let data = opts.data[key].map((row) => {
      return {
        value: row.vertical,
        name: row.indexName,
        ...row,
      };
    });
    series.push({
      ...defaultSeries,
      data: data,
      name: data.length && data[0].name,
    });
  });
  let xAxisData = opts.data.default && opts.data.default.map((row) => row.horizontal);
  return {
    grid: {
      left: '1%',
      right: '1%',
      top: 30,
      bottom: 10,
      containLabel: true,
    },
    tooltip: {
      trigger: 'axis',
      padding: [4, 10], //内边距
      formatter: opts.tooltipFormatter,
      textStyle: {
        fontSize: fontSize(12),
      },
    },
    axisPointer: {
      type: 'line',
      lineStyle: {
        type: 'dashed',
        color: $var('--color-axis-line'),
      },
    },
    xAxis: [
      {
        type: 'category',
        data: xAxisData,
        axisTick: {
          alignWithLabel: true,
          show: false,
        },
        axisLabel: {
          interval: 0,
          fontSize: fontSize(12),
          formatter: function (value) {
            if (value === 0) {
              return value;
            }
            return value;
          },
        },
        splitLine: {
          show: false,
        },
      },
    ],
    yAxis: [
      {
        type: 'value',
        name: `达标值`,
        nameTextStyle: {
          align: 'right',
        },
        axisTick: {
          //y轴刻度线
          show: true,
        },
        splitLine: {
          show: false,
        },
        axisLabel: {
          formatter: function (value) {
            return `${value}%`;
          },
          fontSize: fontSize(12),
        },
      },
    ],
    series: series,
    /*formatter: (data) => {
      let lineChartDom = Vue.extend(chartDom)
      let _this = new lineChartDom({
        el: document.createElement('div'),
        data () {
          return {
            data,
          }
        },
      })
      return _this.$el.outerHTML
    },*/
  };
}
export function lineCircleShadeMultiple(opts) {
  let colorList = ['rgba(12, 208, 132, 1)', 'rgba(245, 152, 53, 1)', 'rgba(157, 105, 226, 1)', 'rgba(4, 205, 244, 1)'];
  let lengName = {
    errorNoData: '历史无抓拍',
    errorTodayNoData: '昨日无抓拍',
    errorTooLessData: '抓拍数量过少',
    errorDataSwoop: '抓拍数量突降',
  };
  let series = [];
  let xAxisData = [];
  Object.keys(opts.data).forEach((key, index) => {
    xAxisData = opts.data[key].map((row) => row.horizontal);
    let data = opts.data[key].map((row) => {
      return {
        value: row.vertical,
        name: lengName[key],
        ...row,
      };
    });
    series.push({
      data: data,
      name: lengName[key],
      type: 'line',
      showSymbol: false,
      lineStyle: {
        width: fontSize(1),
        color: colorList[index], //线条颜色
        shadowColor: colorList[index],
        shadowBlur: 1,
        shadowOffsetY: 1,
      },
      itemStyle: {
        color: colorList[index],
        borderColor: colorList[index],
        borderWidth: 2,
      },
      label: {
        show: false,
        position: 'top',
        color: colorList[index],
      },
      tooltip: {
        show: true,
      },
    });
  });
  return {
    grid: {
      left: '3%',
      right: '1%',
      top: '20%',
      bottom: 0,
      containLabel: true,
    },
    tooltip: {
      trigger: 'axis',
      padding: [4, 10], //内边距
      formatter: opts.tooltipFormatter,
      textStyle: {
        fontSize: fontSize(12),
      },
    },
    axisPointer: {
      type: 'line',
      lineStyle: {
        type: 'dashed',
        color: $var('--color-axis-line'),
      },
    },
    legend: {
      data: Object.values(lengName),
      top: 15,
      right: '1%',
      textStyle: {
        fontSize: fontSize(12),
      },
      tooltip: {
        show: true,
      },
    },
    xAxis: [
      {
        type: 'category',
        data: xAxisData,
        axisTick: {
          alignWithLabel: true,
          show: false,
        },
        axisLabel: {
          interval: 0,
          fontSize: fontSize(12),
          formatter: function (value) {
            if (value === 0) {
              return value;
            }
            return value;
          },
        },
        splitLine: {
          show: false,
        },
      },
    ],
    yAxis: [
      {
        type: 'value',
        name: `异常设备数量`,
        nameTextStyle: {
          align: 'right',
        },
        axisTick: {
          //y轴刻度线
          show: true,
        },
        splitLine: {
          lineStyle: {
            width: fontSize(1),
          },
        },
        axisLabel: {
          formatter: function (value) {
            return `${value}`;
          },
          fontSize: fontSize(12),
        },
      },
    ],
    series: series,
  };
}

export function ReviewConsequenceEcharts(opts) {
  return {
    grid: {
      left: '1%',
      right: '1%',
      top: 30,
      bottom: 0,
      containLabel: true,
    },
    legend: {
      type: 'plain',
      right: '5',
      top: '5',
      data: opts.lengName,
      itemGap: fontSize(10),
      itemWidth: fontSize(10),
      itemHeight: fontSize(10),
      textStyle: {
        fontSize: fontSize(12),
      },
    },
    tooltip: {
      show: 'true',
      trigger: 'axis', //触发类型
      axisPointer: {
        type: 'none',
      },
      padding: [8, 10], //内边距
      formatter: opts.tooltipFormatter,
      textStyle: {
        fontSize: fontSize(12),
      },
    },
    xAxis: [
      {
        type: 'category',
        data: opts.xAxis,

        axisTick: {
          alignWithLabel: true,
          show: false,
        },
        axisLabel: {
          interval: 0,
          fontSize: fontSize(12), //更改坐标轴文字大小
          show: true,
          position: 'inner',
          formatter: function (params) {
            let newName = '';
            if (params.length > 3) {
              newName = params.substring(0, 3) + '...';
            } else {
              newName = params;
            }
            return newName;
          },
          rotate: 40,
        },
        splitLine: {
          show: false,
        },
      },
    ],

    yAxis: [
      {
        min: 0,
        type: 'value',
        axisTick: {
          //y轴刻度线
          show: true,
        },
        splitLine: {
          show: false,
        },
        axisLabel: {
          formatter: '{value}%',
          fontSize: fontSize(12),
        },
      },
      {
        min: 0,
        // name: '单位：台',
        nameTextStyle: {
          fontSize: fontSize(12),
        },
        // boundaryGap: ['20%', '60%'],
        type: 'value',
        axisTick: {
          //y轴刻度线
          show: true,
        },
        splitLine: {
          show: false,
          lineStyle: {
            width: fontSize(1),
          },
        },
        axisLabel: {
          formatter: '{value}',
          fontSize: fontSize(12),
        },
      },
      {
        min: 0,
        nameTextStyle: {
          fontSize: fontSize(12),
        },
        type: 'value',
        axisTick: {
          //y轴刻度线
          show: true,
        },
        splitLine: {
          show: false,
        },
        axisLabel: {
          formatter: '{value}%',
          fontSize: fontSize(12),
        },
      },
    ],
    series: opts.series,
  };
}
export function ReviewConsequencePie(opts) {
  return {
    legend: {
      orient: 'vertical',
      right: '10%',
      top: 'middle',
      itemHeight: fontSize(10),
      itemWidth: fontSize(10),
      itemGap: fontSize(10),
      textStyle: {
        fontSize: fontSize(12),
      },
      ...opts.legend,
    },
    tooltip: {
      show: 'false',
      trigger: 'axis', //触发类型
      axisPointer: {
        type: 'none',
      },
      padding: [8, 10], //内边距
      textStyle: {
        fontSize: fontSize(12),
      },
    },
    series: opts.series,
  };
}
export function abnormalStatisticsChart(opts) {
  return {
    grid: {
      left: '3%',
      right: '1%',
      top: '20%',
      bottom: 0,
      containLabel: true,
    },
    tooltip: {
      show: 'true',
      trigger: 'axis', //触发类型
      axisPointer: {
        type: 'line', // 默认为直线，可选为：'line' | 'shadow' | 'cross' , shadow表示阴影
        lineStyle: {
          type: 'dashed',
        },
      },
      padding: [8, 10], //内边距
      extraCssText: 'background: rgba(13, 53, 96, 0.7);border:  1px solid #1684E4;opacity: 1;', //添加阴影
      formatter: function (params) {
        var result = '';
        var propertyColumn = `<span style="display:inline-block;">${params[0].axisValue}</span>`;
        var total = `<span style="display:inline-block; margin-left:10px;">${params[0].value}</span>`;
        var deviceRate = `<span style="display:inline-block; margin-left:10px;">${params[0].data.deviceRate}</span>`;
        result += propertyColumn + ':' + total + '</br>' + '异常占比:' + deviceRate;
        return result;
      },
      textStyle: {
        fontSize: fontSize(12),
      },
    },
    xAxis: [
      {
        type: 'category',
        name: '日期',
        nameTextStyle: {
          color: '#BBBEC0',
        },
        color: '#fff',
        data: opts.xAxis,

        axisTick: {
          alignWithLabel: true,
          show: false,
        },
        axisLine: {
          lineStyle: {
            color: '#2762AF',
          },
        },
        axisLabel: {
          interval: 0,
          color: '#fff', //更改坐标轴文字颜色
          fontSize: fontSize(12), //更改坐标轴文字大小
          show: true,
          position: 'inner',
          formatter: function (params) {
            let newName = '';

            if (params.length > 5) {
              newName = params.substring(0, 5) + '...';
            } else {
              newName = params;
            }
            return newName;
          },
          rotate: 40,
        },
        splitLine: {
          show: false,
        },
      },
    ],

    yAxis: [
      {
        min: 0,
        // max: 100,
        name: '异常设备数量',
        nameTextStyle: {
          color: '#fff',
        },
        // boundaryGap: ['20%', '60%'],
        type: 'value',
        axisTick: {
          //y轴刻度线
          show: true,
        },
        splitLine: {
          lineStyle: {
            color: '#063d6b',
            width: fontSize(1),
          },
        },
        axisLine: {
          lineStyle: {
            color: '#2762AF',
          },
        },
        axisLabel: {
          formatter: '{value}',
          // formatter: function(params) {
          //   console.log(params, 'params888')
          // },
          color: '#e2e9ff',
          fontSize: fontSize(12),
        },
      },
    ],
    series: [
      {
        type: 'bar',
        data: opts.data,
        barWidth: fontSize(21),
        itemStyle: {
          color: new echarts.graphic.LinearGradient(
            0,
            0,
            0,
            1,
            [
              {
                offset: 0,
                color: '#00D5F7', // 0% 处的颜色
              },
              {
                offset: 1,
                color: '#1B6EC7', // 100% 处的颜色
              },
            ],
            false,
          ),
        },
      },
    ],
  };
}
export function SubordinateAbnormalChart(opts) {
  return {
    grid: {
      left: '1%',
      right: '1%',
      top: 30,
      bottom: 10,
      containLabel: true,
    },
    legend: {
      type: 'plain',
      right: '5',
      top: '5',
      data: opts.lengName,
      itemGap: fontSize(10),
      itemWidth: fontSize(10),
      itemHeight: fontSize(10),
      textStyle: {
        fontSize: fontSize(12),
      },
    },
    tooltip: {
      show: 'true',
      trigger: 'axis', //触发类型
      axisPointer: {
        type: 'none',
      },
      padding: [8, 10], //内边距
      textStyle: {
        fontSize: fontSize(12),
      },
    },
    xAxis: [
      {
        type: 'category',
        data: opts.xAxis,

        axisTick: {
          alignWithLabel: true,
          show: false,
        },
        axisLabel: {
          interval: 0,
          fontSize: fontSize(12), //更改坐标轴文字大小
          show: true,
          position: 'inner',
          formatter: function (params) {
            let newName = '';
            if (params.length > 3) {
              newName = params.substring(0, 3) + '...';
            } else {
              newName = params;
            }
            return newName;
          },
          rotate: 40,
        },
        splitLine: {
          show: false,
        },
      },
    ],
    yAxis: [
      {
        min: 0,
        // name: '单位：台',
        nameTextStyle: {
          fontSize: fontSize(12),
        },
        // boundaryGap: ['20%', '60%'],
        type: 'value',
        axisTick: {
          //y轴刻度线
          show: true,
        },
        splitLine: {
          show: false,
        },
        axisLabel: {
          formatter: '{value}',
          fontSize: fontSize(12),
        },
      },
    ],
    series: opts.series,
  };
}
export function indexTrendChart(opts) {
  let defaultSeries = {
    type: 'line',
    showAllSymbol: true,
    symbol: 'circle',
    symbolSize: 8,
    lineStyle: {
      width: fontSize(2),
      color: $var('--color-blue-28'),
    },
    label: {
      show: false,
      position: 'top',
    },
    itemStyle: {
      color: $var('--bg-echart'),
      borderColor: $var('--color-blue-28'),
      borderWidth: 2,
    },
    areaStyle: {
      //自定义颜色，渐变色填充折线图区域
      color: new echarts.graphic.LinearGradient(
        0,
        0,
        0,
        1, //变化度
        //渐变色
        [
          {
            offset: 0,
            color: $var('--linear-gradient-green-6-start'),
          },
          {
            offset: 1,
            color: $var('--linear-gradient-green-6-end'),
          },
        ],
      ),
    },
    tooltip: {
      show: true,
    },
  };
  let series = [];
  Object.keys(opts.data).forEach((key) => {
    let data = opts.data[key].map((row) => {
      return {
        value: row.vertical,
        name: row.indexName,
        ...row,
      };
    });
    series.push({
      ...defaultSeries,
      data: data,
      name: data.length && data[0].name,
    });
  });
  let xAxisData = opts.data.default && opts.data.default.map((row) => row.horizontal);
  return {
    grid: {
      left: '1%',
      right: '1%',
      top: 30,
      bottom: 10,
      containLabel: true,
    },
    tooltip: {
      trigger: 'axis',
      padding: [4, 10], //内边距
      formatter: opts.tooltipFormatter,
      textStyle: {
        fontSize: fontSize(12),
      },
    },
    axisPointer: {
      type: 'line',
      lineStyle: {
        type: 'dashed',
        color: $var('--color-blue-28'),
      },
    },
    xAxis: [
      {
        type: 'category',
        data: xAxisData,
        axisTick: {
          alignWithLabel: true,
          show: false,
        },
        axisLabel: {
          interval: 0,
          fontSize: fontSize(12),
          formatter: function (value) {
            if (value === 0) {
              return value;
            }
            return value;
          },
        },
        splitLine: {
          show: false,
        },
      },
    ],
    yAxis: [
      {
        type: 'value',
        name: `达标值`,
        nameTextStyle: {
          align: 'right',
        },
        axisTick: {
          //y轴刻度线
          show: true,
        },
        splitLine: {
          show: false,
        },
        axisLabel: {
          formatter: function (value) {
            return `${value}%`;
          },
          fontSize: fontSize(12),
        },
      },
    ],
    series: series,
  };
}
export function reportChart(opts) {
  return {
    color: opts.color,
    grid: {
      left: '1%',
      right: '1%',
      top: 30,
      bottom: 0,
      containLabel: true,
    },
    legend: {
      data: opts.legendData,
      right: 5,
      itemGap: fontSize(10),
      itemWidth: fontSize(10),
      itemHeight: fontSize(10),
      textStyle: {
        fontSize: fontSize(12),
      },
    },
    tooltip: {
      show: !!opts.tooltipFormatter,
      trigger: 'axis', //触发类型
      axisPointer: {
        type: 'none',
      },
      padding: [8, 10], //内边距
      formatter: opts.tooltipFormatter ? opts.tooltipFormatter : '',
      textStyle: {
        fontSize: fontSize(12),
      },
    },
    xAxis: {
      type: 'category',
      axisTick: {
        alignWithLabel: true,
        show: false,
      },
      axisLabel: {
        interval: 0,
        fontSize: fontSize(12),
        formatter: function (value) {
          if (value === 0) {
            return value;
          }
          return value;
        },
        rotate: 40,
      },
      splitLine: {
        show: false,
      },
      data: opts.xData,
    },
    yAxis: {
      show: opts.yAxisShow ? opts.yAxisShow.show : true,
      type: 'value',
      axisLabel: {
        fontSize: fontSize(12),
      },
      splitLine: {
        show: false,
        lineStyle: {
          width: fontSize(1),
        },
      },
    },
    series: opts.series,
  };
}
export function basicInputHistoryTrend(opts) {
  let xAxisData = opts.data.default && opts.data.default.map((row) => row.horizontal);
  return {
    grid: {
      left: '1%',
      right: '1%',
      top: 30,
      bottom: 10,
      containLabel: true,
    },
    legend: {
      type: 'plain',
      right: '5',
      top: '5',
      data: opts.legend.map((item) => item.name),
      itemGap: fontSize(10),
      textStyle: {
        fontSize: fontSize(12),
      },
    },
    tooltip: {
      trigger: 'axis',
      padding: [4, 10], //内边距
      formatter: (params) => {
        let { year, month, horizontal, dateType, detail } = opts.data.default[params[0]['dataIndex']];
        let str = `${year}年${dateType === 'MONTH' ? horizontal : month}月${horizontal}日 <br>建档率 ${
          detail.resultValue || 0
        }%  <br>`;
        for (let i = 0; i < params.length; i++) {
          let item = params[i];
          str += `${item.marker}${item.seriesName} ${item.value}%<br>`;
        }
        return str;
      },
      textStyle: {
        fontSize: fontSize(12),
      },
    },
    axisPointer: {
      type: 'line',
      lineStyle: {
        type: 'dashed',
        color: $var('--color-axis-line'),
      },
    },
    xAxis: [
      {
        type: 'category',
        data: xAxisData,
        axisTick: {
          alignWithLabel: true,
          show: false,
        },
        axisLabel: {
          interval: 0,
          fontSize: fontSize(12),
          formatter: function (value) {
            if (value === 0) {
              return value;
            }
            return value;
          },
        },
        splitLine: {
          show: false,
        },
      },
    ],
    yAxis: [
      {
        type: 'category',
        name: '建档率',
        nameTextStyle: {
          align: 'right',
        },
        axisTick: {
          //y轴刻度线
          show: true,
        },
        splitLine: {
          show: false,
        },
        axisLabel: {
          formatter: function (value) {
            return `${value}%`;
          },
          fontSize: fontSize(12),
        },
      },
    ],
    series: opts.series,
  };
}
export function echartZDRDataQuality(data, baseWidth = 120, gridHeight) {
  let series = [];
  let title = [];
  let configColor = {
    1: ['#002FFD', '#00E7FD'],
    2: ['#FFB500', '#EB5D00'],
    3: ['#00FFC0', '#00FF5F'],
  };
  data.forEach((item, index) => {
    // 20是间距
    let position = [`${baseWidth / 2 + (baseWidth + fontSize(20)) * index}`, '50%'];
    let strWidth = (item.indexName.length * fontSize(12)) / 2; // 一半的文字标题长度
    let bottomOff = gridHeight ? gridHeight / 2 - baseWidth / 2 - fontSize(25) : fontSize(10);
    title.push({
      text: '{a|' + item.indexName + '}',
      x: 'center',
      bottom: bottomOff,
      left: `${position[0] - strWidth}`,
      textStyle: {
        textAlign: 'center',
        fontWeight: 'normal',
        fontSize: fontSize(14),
        color: '#89B9FA',
        rich: {
          a: {
            color: '#bed7f8',
            textAlign: 'center',
          },
        },
      },
    });
    series.push(
      {
        name: `pie${index}`,
        type: 'pie',
        clockwise: true, //饼图的扇区是否是顺时针排布。
        radius: [baseWidth / 2 - fontSize(5), baseWidth / 2], // 半径就是 = baseWidth/2
        center: position,
        emphasis: {
          scale: false,
        },
        startAngle: 90,
        itemStyle: {
          label: {
            show: false,
          },
          labelLine: {
            show: false,
          },
          color: () => {
            return new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0,
                color: configColor[index + (1 % 3)][0],
              },
              {
                offset: 1,
                color: configColor[index + (1 % 3)][1],
              },
            ]);
          },
        },
        data: [
          {
            value: item.resultValue || 0,
            label: {
              formatter: '{c}%',
              position: 'center',
              show: true,
              fontSize: fontSize(20),
              fontWeight: 'bold',
              color: '#ffffff',
            },
            originData: {
              ...item,
              regionCode: item.civilCode,
            },
          },
          {
            value: 100 - parseInt(item.resultValue || 0), //未完成
            itemStyle: {
              color: '#182756', //未完成的圆环的颜色
            },
            originData: {
              ...item,
              regionCode: item.civilCode,
            },
          },
        ],
      },
      {
        center: position,
        type: 'gauge',
        startAngle: 270,
        endAngle: -90,
        axisTick: {
          show: false,
        },
        axisLabel: {
          show: false,
        },
        radius: 0.4 * baseWidth,
        splitNumber: '50',
        axisLine: {
          show: false,
          lineStyle: {
            color: '#2F3A67',
            width: fontSize(2),
          },
        },
        splitLine: {
          length: 1,
          lineStyle: {
            width: fontSize(2),
            color: '#2F3A67',
          },
        },
        detail: {
          show: false,
        },
      },
    );
  });
  return {
    // color: [ ['#002FFD','#00E7FD'],,['#FFB500', '#EB5D00'],['#00FFC0','#00FF5F']],
    title: title,
    series: series,
  };
}
export function baseHomeGovernTendency(opts) {
  function xAxis() {
    let data = [];
    for (var i = 1; i < 13; i++) {
      data.push(i);
    }
    return data;
  }
  return {
    tooltip: {
      trigger: 'axis',
      confine: true,
      backgroundColor: 'transparent',
      borderColor: 'transparent',
      textStyle: {
        color: '#fff',
        fontSize: fontSize(12),
      },
      axisPointer: {
        lineStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: 'rgba(126,199,255,0)', // 0% 处的颜色
              },
              {
                offset: 0.5,
                color: 'rgba(126,199,255,1)', // 100% 处的颜色
              },
              {
                offset: 1,
                color: 'rgba(126,199,255,0)', // 100% 处的颜色
              },
            ],
            global: false, // 缺省为 false
          },
        },
      },
      padding: [4, 10], //内边距
      formatter: opts.tooltipFormatter,
      extraCssText: `background: ${opts.tooltipBg};border: 1px solid #1684E4;opacity: 1;`, //添加阴影
    },
    legend: {
      right: '0%',
      top: '5%',
      itemGap: fontSize(10),
      itemWidth: fontSize(6),
      itemHeight: fontSize(6),
      icon: 'rect',
      // data: opts.data.map((item) => item.name),
      textStyle: {
        color: '#88B9FA',
        overflow: 'truncate',
      },
      formatter: function (name) {
        let width = opts.gridWidth ? opts.gridWidth / opts.legendLength - fontSize(30) : fontSize(70);
        return echarts.format.truncateText(name, width, '…');
      },
      tooltip: {
        show: true,
      },
    },
    grid: {
      top: '20%',
      left: '2%',
      right: '6%',
      bottom: '2%',
      containLabel: true,
    },
    xAxis: [
      {
        type: 'category',
        name: '月',
        nameGap: 5,
        nameTextStyle: {
          color: '#88B9FA',
        },
        color: '#88B9FA',
        data: xAxis(),
        axisTick: {
          alignWithLabel: true,
          show: false,
        },
        axisLine: {
          lineStyle: {
            color: '#0E1A4A',
          },
        },
        axisLabel: {
          interval: 0,
          color: '#88B9FA',
          fontSize: fontSize(12),
        },
        splitLine: {
          show: false,
        },
      },
    ],
    title: {
      y: '0',
      subtext: '达标值',
      subtextStyle: {
        color: '#8ABAFB',
        fontSize: fontSize(12),
      },
    },
    yAxis: [
      {
        name: '',
        min: 0,
        max: 100,
        nameTextStyle: {
          color: '#88B9FA',
          fontSize: fontSize(12),
        },
        // boundaryGap: ['20%', '20%'],
        type: 'value',
        axisTick: {
          //y轴刻度线
          show: true,
        },
        splitLine: {
          lineStyle: {
            color: '#0E1A4A',
            width: fontSize(1),
          },
        },
        axisLine: {
          lineStyle: {
            color: '#0E1A4A',
          },
        },
        axisLabel: {
          formatter: '{value} %',
          //改变刻度字体样式
          color: '#88B9FA',
          fontSize: fontSize(12),
        },
      },
    ],
    series: opts.data,
  };
}
export function videoImageQualityOption(opts) {
  const styleType = qualityFaceStyle[`style${opts.homeStyleType}`] || qualityFaceStyle['style1'];
  let colorBlue = {
    color: new echarts.graphic.LinearGradient(
      0,
      0,
      0,
      1,
      [
        {
          offset: 0,
          color: styleType.colorBlue[0], // 0% 处的颜色
        },
        {
          offset: 1,
          color: styleType.colorBlue[1], // 100% 处的颜色
        },
      ],
      false,
    ),
  };
  let colorRed = {
    color: new echarts.graphic.LinearGradient(
      0,
      0,
      0,
      1,
      [
        {
          offset: 0,
          color: styleType.colorRed[0], // 0% 处的颜色
        },
        {
          offset: 1,
          color: styleType.colorRed[1], // 100% 处的颜色
        },
      ],
      false,
    ),
  };
  let seriesData = opts.data.map((item) => {
    return {
      ...item,
      value: item.resultValue,
      itemStyle: item.qualified === '1' ? colorBlue : colorRed,
    };
  });
  let pictorialBarData = opts.data.map((item) => {
    return {
      ...item,
      value: item.resultValue,
    };
  });
  let xAxis = opts.data.map((item) => item.indexName);
  return {
    grid: {
      left: '1%',
      right: '1%',
      top: 30,
      bottom: '2%',
      containLabel: true,
    },
    xAxis: [
      {
        type: 'category',
        color: '#88B9FA',
        data: xAxis,
        axisTick: {
          alignWithLabel: true,
          show: false,
        },
        triggerEvent: true,
        splitLine: {
          lineStyle: {
            color: '#0E1A4A',
            width: fontSize(1),
          },
        },
        axisLine: {
          lineStyle: {
            color: '#0E1A4A',
          },
        },
        axisLabel: {
          interval: 0,
          color: '#88B9FA',
          fontSize: fontSize(12), //更改坐标轴文字大小
          show: true,
          position: 'inner',
          formatter: function (params) {
            if (params.length < 5) {
              return params;
            } else {
              var strs = params.split(''); //字符串数组
              var str = '';
              for (var i = 0, s; (s = strs[i++]); ) {
                //遍历字符串数组
                str += s;
                if (!(i % 5)) str += '\n'; //按需要求余
              }
              return str;
            }
          },
        },
      },
    ],
    yAxis: [
      {
        type: 'value',
        axisTick: {
          //y轴刻度线
          show: true,
        },
        splitLine: {
          lineStyle: {
            color: '#0E1A4A',
            width: fontSize(1),
          },
        },
        axisLine: {
          lineStyle: {
            color: '#0E1A4A',
          },
        },
        axisLabel: {
          formatter: '{value}%',
          color: '#88B9FA',
          fontSize: fontSize(12),
        },
      },
    ],
    series: [
      {
        name: '白手套',
        type: 'pictorialBar',
        symbol: 'rect',
        symbolSize: [16, 2],
        symbolOffset: [0, -2],
        symbolPosition: 'end',
        itemStyle: {
          color: '#E0EEFF',
        },
        data: pictorialBarData,
      },
      {
        type: 'bar',
        data: seriesData,
        stack: 1,
        barWidth: fontSize(16),
        label: {
          show: true,
          distance: 1,
          formatter: '{c}%',
          color: '#fff',
          position: 'top',
          offset: [0, -5],
        },
      },
    ],
  };
}

/**
 * 首页
 *
 */
export function getHomeAreaBar(options) {
  return {
    tooltip: {
      padding: [0, 0], //内边距
      trigger: 'axis',
      //enterable: true,legend
      axisPointer: {
        type: 'shadow',
      },
      confine: true, // 限制tootip在容器内
      backgroundColor: 'rgba(50, 50, 50, 0.7)',
      borderColor: 'transparent',
      textStyle: {
        color: '#fff',
        fontSize: fontSize(12),
      },
      formatter: function (params) {
        // ToolTipCard为vue组件，直接import , 第二个参数为props
        let tooltip = Vue.extend(options.toolTipDom);
        let _this = new tooltip({
          el: document.createElement('div'),
          name: 'getHomeBar',
          data() {
            return {
              toolTipItem: params[0].data?.data,
            };
          },
        });
        return _this.$el.outerHTML;
      },
    },
    legend: {
      right: '0%',
      top: '5%',
      itemGap: fontSize(10),
      itemWidth: fontSize(6),
      itemHeight: fontSize(6),
      // icon: 'rect',
      // data: opts.data.map((item) => item.name),
      textStyle: {
        color: '#88B9FA',
        width: fontSize(60),
        overflow: 'truncate',
      },
      formatter: function (name) {
        return echarts.format.truncateText(name, fontSize(60), '…');
      },
      tooltip: {
        show: true,
      },
    },

    textStyle: {
      color: '#fff',
    },
    grid: {
      top: '20%',
      left: '2%',
      right: '4%',
      bottom: '0%',
      containLabel: true,
    },
    xAxis: [
      {
        type: 'category',
        data: options.xAxisData,
        nameTextStyle: {
          color: '#8ABAFB',
        },
        axisLabel: {
          interval: 0,
          color: '#8ABAFB', //更改坐标轴文字颜色
          fontSize: fontSize(12), //更改坐标轴文字大小
          show: true,
          position: 'inner',
          formatter: function (params) {
            let newName = '';
            params.length > 3 ? (newName = params.substring(0, 3)) : (newName = params);
            let reallyName = '';
            for (let i = 0; i < newName.length; i++) {
              reallyName = reallyName + `${newName[i]}\n`;
            }
            params.length > 3 ? reallyName + '...' : null;
            return reallyName;
          },
          //rotate: 90,
        },
        splitLine: {
          lineStyle: {
            color: '#0E1A4A',
            width: fontSize(1),
          },
        },
        axisLine: {
          lineStyle: {
            color: '#0E1A4A',
          },
        },
      },
    ],
    title: {
      y: '0',
      subtext: options.title,
      subtextStyle: {
        color: '#8ABAFB',
        fontSize: fontSize(12),
      },
    },
    yAxis: (() => {
      let baseYAxis = {
        type: 'value',
        axisLabel: {
          //y轴文字的配置
          color: '#8ABAFB',
          fontSize: fontSize(12), //更改坐标轴文字大小
        },
        splitLine: {
          lineStyle: {
            color: '#0E1A4A',
            width: fontSize(1),
          },
        },
        axisLine: {
          lineStyle: {
            color: '#0E1A4A',
          },
        },
      };
      if (options?.yAxisData) {
        let haha = options.yAxisData.map((item) => {
          let newYAxis = Object.assign({}, baseYAxis);
          return Object.assign(newYAxis, item);
        });
        return haha;
      }
      return [baseYAxis];
    })(),
    series: options.series.map((item) => {
      if (item?.type === 'line') {
        return {
          type: 'line',
          yAxisIndex: item?.yAxisIndex ?? 0, // 设置数据对齐yAxis
          data: item.data,
          itemStyle: {
            color: '#00F4F4',
          },
        };
      } else {
        return {
          name: item.name,
          barWidth: fontSize(12),
          type: 'bar',
          yAxisIndex: item?.yAxisIndex ?? 0, // 设置数据对齐yAxis
          stack: 'Ad',
          itemStyle: {
            color: new echarts.graphic.LinearGradient(
              0,
              0,
              0,
              1,
              [
                {
                  offset: 0,
                  color: item.color[0],
                },
                {
                  offset: 1,
                  color: item.color[1],
                },
              ],
              false,
            ),
            borderWidth: 1,
            borderColor: (() => {
              if (!('borderColor' in item)) {
                return 'transparent';
              }
              return new echarts.graphic.LinearGradient(
                0,
                0,
                0,
                1,
                [
                  {
                    offset: 0,
                    color: item.borderColor[0],
                  },
                  {
                    offset: 1,
                    color: item.borderColor[1],
                  },
                ],
                false,
              );
            })(),
          },
          data: item.data,
        };
      }
    }),
  };
}
export function getHomeDeviceBar(options) {
  return {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
      show: 'true',
      padding: [0, 0], //内边距
      confine: true, // 限制tootip在容器内
      backgroundColor: 'rgba(50, 50, 50, 0.7)',
      borderColor: 'transparent',
      textStyle: {
        color: '#fff',
        fontSize: fontSize(12),
      },
      formatter: function (params) {
        // ToolTipCard为vue组件，直接import , 第二个参数为props
        let tooltip = Vue.extend(options.toolTipDom);
        let _this = new tooltip({
          el: document.createElement('div'),
          name: 'getHomeBar',
          data() {
            return {
              toolTipItem: params[0].data?.data,
            };
          },
        });
        return _this.$el.outerHTML;
      },
    },
    legend: {
      right: '0%',
      top: '5%',
      itemGap: fontSize(10),
      itemWidth: fontSize(6),
      itemHeight: fontSize(6),
      textStyle: {
        color: '#88B9FA',
        width: fontSize(60),
        overflow: 'truncate',
        fontSize: fontSize(12),
      },
      tooltip: {
        show: true,
      },
    },
    grid: {
      top: '20%',
      left: '2%',
      right: '4%',
      bottom: '3%',
      containLabel: true,
    },
    title: {
      y: '0',
      subtext: options.title,
      subtextStyle: {
        color: '#8ABAFB',
        fontSize: fontSize(12),
      },
    },
    yAxis: {
      axisLabel: {
        interval: 0,
        color: '#8ABAFB', //更改坐标轴文字颜色
        fontSize: fontSize(12), //更改坐标轴文字大小
        show: true,
        position: 'inner',
        formatter: function (params) {
          let newName = '';
          if (params.length > 3) {
            newName = params.substring(0, 3) + '...';
          } else {
            newName = params;
          }
          return newName;
        },
        //rotate: 180,
      },
      splitLine: {
        lineStyle: {
          color: '#0E1A4A',
          width: fontSize(1),
        },
      },
      axisLine: {
        lineStyle: {
          color: '#0E1A4A',
        },
      },
    },
    xAxis: [
      {
        type: 'category',
        data: options.xAxisData,
        nameTextStyle: {
          color: '#8ABAFB',
        },
        axisLabel: {
          interval: 0,
          color: '#8ABAFB', //更改坐标轴文字颜色
          fontSize: fontSize(12), //更改坐标轴文字大小
          show: true,
          position: 'inner',
          formatter: function (params) {
            let newName = '';
            params.length > 3 ? (newName = params.substring(0, 3)) : (newName = params);
            let reallyName = '';
            for (let i = 0; i < newName.length; i++) {
              reallyName = reallyName + `${newName[i]}\n`;
            }
            //params.length > 3 ? reallyName = reallyName + '...' : null
            return reallyName;
          },
          //rotate: 180,
        },
        axisLine: {
          lineStyle: {
            color: '#0E1A4A',
          },
        },
      },
    ],
    series: options.seriesData.map((item) => {
      return {
        name: item.name,
        type: 'bar',
        barWidth: fontSize(4),
        //  barGap: "40%",
        data: item.data,
        itemStyle: {
          borderRadius: [4, 24, 0, 0],
        },
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          {
            offset: 0,
            color: item.color[0],
          },
          {
            offset: 1,
            color: item.color[1],
          },
        ]),
      };
    }),
  };
}
export function VideoReadPromotionRateSubordinateChart(opts) {
  return {
    grid: {
      left: '1%',
      right: '1%',
      top: 30,
      bottom: 0,
      containLabel: true,
    },
    legend: {
      type: 'plain',
      right: '5',
      top: '5',
      data: opts.lengName,
      itemGap: fontSize(10),
      itemWidth: fontSize(10),
      itemHeight: fontSize(10),
      textStyle: {
        fontSize: fontSize(12),
      },
    },
    tooltip: {
      show: 'true',
      trigger: 'axis', //触发类型
      axisPointer: {
        type: 'none',
      },
      padding: [8, 10], //内边距
      formatter: opts.tooltipFormatter,
      textStyle: {
        fontSize: fontSize(12),
      },
    },
    xAxis: [
      {
        type: 'category',
        data: opts.xAxis,

        axisTick: {
          alignWithLabel: true,
          show: false,
        },
        axisLabel: {
          interval: 0,
          fontSize: fontSize(12), //更改坐标轴文字大小
          show: true,
          position: 'inner',
          formatter: function (params) {
            let newName = '';
            if (params.length > 3) {
              newName = params.substring(0, 3) + '...';
            } else {
              newName = params;
            }
            return newName;
          },
          rotate: 40,
        },
        splitLine: {
          show: false,
        },
      },
    ],

    yAxis: {
      min: 0,
      max: 100,
      // name: '单位：台',
      nameTextStyle: {
        fontSize: fontSize(12),
      },
      // boundaryGap: ['20%', '60%'],
      type: 'value',
      axisTick: {
        //y轴刻度线
        show: true,
      },
      splitLine: {
        show: false,
        lineStyle: {
          width: fontSize(1),
        },
      },
      axisLabel: {
        formatter: '{value}%',
        fontSize: fontSize(12),
      },
    },
    series: opts.series,
  };
}
export function DevicePropertyStatistics(opts) {
  return {
    title: {
      text: `${opts.total}`,
      subtext: '设备总数',
      x: 'center',
      y: '40%',
      textStyle: {
        color: $var('--color-blue-1'),
        fontSize: fontSize(14),
      },
      subtextStyle: {
        color: $var('--color-base-text'),
        fontSize: fontSize(14),
      },
    },
    tooltip: {
      trigger: 'item',
      confine: true, //是否将 tooltip 框限制在图表的区域内。
      extraCssText: 'background: var(--bg-echart-tooltip);border: 1px solid var(--border-tooltip)', //添加阴影
      textStyle: {
        fontSize: fontSize(12),
      },
    },
    series: [
      {
        // name: 'Access From',
        type: 'pie',
        minAngle: 5,
        radius: ['44%', '65%'],
        avoidLabelOverlap: false,
        label: {
          show: false,
          position: 'center',
        },
        labelLine: {
          show: false,
        },
        data: opts.data,
      },
    ],
  };
}
export function QuantitativeTrend(opts) {
  return {
    tooltip: {
      axisPointer: {
        // 坐标轴指示器，坐标轴触发有效
        type: 'line', // 默认为直线，可选为：'line' | 'shadow' | 'cross' , shadow表示阴影
        lineStyle: {
          type: 'dashed',
          color: $var('--color-axis-pointer-line'),
        },
      },
      confine: true, //是否将 tooltip 框限制在图表的区域内。
      // extraCssText: 'background: var(--bg-tooltip)', //添加阴影
      trigger: 'axis',
      formatter: (params) => {
        return opts.commonToolTipConfig(opts.toolTipItem(params));
      },
      textStyle: {
        fontSize: fontSize(12),
      },
    },
    legend: {
      right: '1%',
      top: '3%',
      textStyle: {
        color: $var('--color-base-text'),
      },
      itemWidth: fontSize(20),
      itemHeight: fontSize(2),
      itemStyle: {},
      data: ['视频监控', '人脸卡口', '车辆卡口'],
    },
    grid: {
      top: '15%',
      left: '1%',
      bottom: 25,
      right: '1%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12'],
      nameTextStyle: {
        color: $var('--color-axis-lable'),
      },
      color: $var('--color-axis-lable'),
      axisTick: {
        alignWithLabel: true,
        show: false,
      },
      axisLine: {
        lineStyle: {
          color: $var('--color-axis-line'),
        },
      },
      axisLabel: {
        interval: 0,
        color: $var('--color-axis-lable'),
        fontSize: fontSize(12),
        // rotate: 20,
      },
      splitLine: {
        show: false,
      },
    },
    title: {
      text: '达标值',
      top: '3%',
      left: '1%',
      textStyle: {
        fontSize: fontSize(12),
        color: $var('--color-axis-lable'),
        fontWeight: '500',
      },
    },
    yAxis: {
      type: 'value',
      axisTick: {
        //y轴刻度线
        show: false,
      },
      splitLine: {
        show: false,
      },
      axisLine: {
        lineStyle: {
          color: $var('--color-axis-line'),
        },
      },
      axisLabel: {
        //改变刻度字体样式
        color: $var('--color-axis-lable'),
        fontSize: fontSize(12),
      },
    },
    series: opts.series,
  };
}

export function onlineDetail(opts) {
  let dataZoomBgColor = '#071B39';
  let fillerColor = '#116FB4';
  if (['light', 'deepBlue'].includes(document.documentElement.getAttribute('data-theme'))) {
    dataZoomBgColor = 'rgba(47,69,84,0)';
    fillerColor = 'rgba(167,183,204,0.4)';
  }
  return {
    color: opts.colors,
    tooltip: {
      formatter: (params) => {
        return params.name + ':' + params.value[1].split(' ')[1] + '~' + params.value[2].split(' ')[1];
      },
      textStyle: {
        fontSize: fontSize(12),
      },
    },
    legend: {
      data: opts.state,
      icon: 'rect',
      top: 0,
      right: 0,
      itemWidth: fontSize(16),
      itemHeight: fontSize(16),
      selectedMode: false,
    },
    xAxis: {
      type: 'time',
      interval: 3600 * 1000,
      maxInterval: 3600 * 1000,
      name: '单位：小时',
      nameLocation: 'start',
      axisLine: {
        show: true,
      },
      splitLine: {
        show: false,
      },
      axisLabel: {
        formatter: (value) => {
          const date = new Date(value);
          return `${date.getHours()}`;
        },
      },
      axisTick: {
        show: true,
      },
      offset: 20,
    },
    grid: {
      //绘图网格
      top: '5%',
      left: 0,
      containLabel: true,
    },
    dataZoom: [
      {
        type: 'slider',
        show: opts.yAxis.length >= 8,
        yAxisIndex: [0, 1],
        width: 3,
        zoomLock: true,
        fillerColor: fillerColor,
        borderColor: 'transparent',
        backgroundColor: dataZoomBgColor,
        showDetail: false,
        showDataShadow: false,
        brushSelect: false,
        startValue: opts.categoryData.length - 8,
        endValue: opts.categoryData.length - 1,
      },
      {
        type: 'inside',
        yAxisIndex: [0, 1],
        startValue: opts.categoryData.length - 8,
        endValue: opts.categoryData.length - 1,
        zoomOnMouseWheel: false,
        moveOnMouseWheel: true,
        moveOnMouseMove: false,
      },
      {
        type: 'slider',
        filterMode: 'weakFilter',
        start: 0,
        end: 100,
        height: 20,
      },
    ],
    yAxis: [
      {
        data: opts.yAxis,
        axisTick: {
          show: false,
        },
        axisLine: {
          show: false,
        },
        axisLabel: {
          fontSize: fontSize(12),
        },
      },
      {
        type: 'category',
        axisTick: 'none',
        axisLine: 'none',
        show: true,
        data: opts.categoryData,
        axisLabel: {
          show: true,
          fontSize: fontSize(14),
        },
        // 开启触发事件
        triggerEvent: true,
      },
    ],
    series: [
      // 用空bar来显示四个图例
      {
        name: opts.state[0],
        type: 'bar',
        data: [],
      },
      {
        name: opts.state[1],
        type: 'bar',
        data: [],
      },
      {
        name: opts.state[2],
        type: 'bar',
        data: [],
      },
      {
        type: 'custom',
        renderItem: (params, api) => {
          //开发者自定义的图形元素渲染逻辑，是通过书写 renderItem 函数实现的
          const categoryIndex = api.value(0); //这里使用 api.value(0) 取出当前 dataItem 中第一个维度的数值。
          const start = api.coord([api.value(1), categoryIndex]); // 这里使用 api.coord(...) 将数值在当前坐标系中转换成为屏幕上的点的像素值。
          const end = api.coord([api.value(2), categoryIndex]);
          const height = 16; //柱体宽度
          const text = api.value(3)
            ? `${formatDate(new Date(api.value(1)), 'hh:mm')}-${formatDate(new Date(api.value(2)), 'hh:mm')}`
            : null;
          return {
            type: 'group',
            children: [
              {
                type: 'rect',
                // 表示这个图形元素是矩形。还可以是 'circle', 'sector', 'polygon' 等等。
                shape: new echarts.graphic.clipRectByRect(
                  {
                    // 矩形的位置和大小。
                    x: start[0],
                    y: start[1] - height / 2,
                    width: end[0] - start[0],
                    height: height,
                  },
                  {
                    x: params.coordSys.x,
                    y: params.coordSys.y,
                    width: params.coordSys.width,
                    height: params.coordSys.height,
                  },
                ),
                style: api.style(),
              },
              {
                type: 'rect',
                // 表示这个图形元素是矩形。还可以是 'circle', 'sector', 'polygon' 等等。
                shape: new echarts.graphic.clipRectByRect(
                  {
                    // 矩形的位置和大小。
                    x: start[0],
                    y: start[1] - height / 2,
                    width: end[0] - start[0],
                    height: height,
                  },
                  {
                    // 当前坐标系的包围盒。
                    x: params.coordSys.x,
                    y: params.coordSys.y,
                    width: params.coordSys.width,
                    height: params.coordSys.height,
                  },
                ),
                textConfig: {
                  position: 'bottom',
                },
                style: api.style({
                  fill: 'transparent',
                  stroke: 'transparent',
                  text: text,
                  textFill: '#56789C',
                }),
              },
            ],
          };
        },
        encode: {
          x: [1, 2], // data 中『维度1』和『维度2』对应到 X 轴
          y: 0, // data 中『维度0』对应到 Y 轴
        },
        data: opts.data,
      },
    ],
  };
}

import OnlineChangesTooltip from '@/views/home/<USER>/online-changes-tooltip.vue';
export function baseHomeVideoOnlineChanges(opts, callback) {
  return {
    legend: {
      right: '0%',
      top: '10',
      icon: 'rect',
      itemWidth: fontSize(15),
      itemHeight: fontSize(1),
      textStyle: {
        color: '#88B9FA',
      },
    },
    grid: {
      left: '2%',
      right: '5%',
      bottom: '2%',
      containLabel: true,
    },
    tooltip: {
      trigger: 'axis',
      confine: true,
      enterable: true,
      hideDelay: 300,
      backgroundColor: 'transparent',
      borderColor: 'transparent',
      padding: [0, 0], //内边距
      textStyle: {
        color: '#fff',
        fontSize: fontSize(12),
      },
      position: (point) => {
        return [point[0] + 3, point[1] + 3];
      },
      formatter: (data) => {
        let onlineChangesTooltip = Vue.extend(OnlineChangesTooltip);
        let _this = new onlineChangesTooltip({
          el: document.createElement('div'),
          data() {
            return {
              data,
              year: opts.filterTime, // 格式：'2023-06'
            };
          },
          mounted() {
            // 详情 点击触发
            window.openDetails = () => {
              callback(data, opts.filterTime);
            };
          },
        });
        return _this.$el.outerHTML;
      },
      axisPointer: {
        lineStyle: {
          type: 'solid',
          shadowColor: 'rgba(126,199,255,1)',
          shadowBlur: 2,
          color: {
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: 'rgba(126,199,255,0)', // 0% 处的颜色
              },
              {
                offset: 0.5,
                color: 'rgba(126,199,255,1)', // 100% 处的颜色
              },
              {
                offset: 1,
                color: 'rgba(126,199,255,0)', // 100% 处的颜色
              },
            ],
          },
        },
      },
      extraCssText: 'background: rgba(0, 10, 41, 0.7);border-radius: 8px 8px 8px 8px;opacity: 1;', //添加阴影
    },
    xAxis: [
      {
        type: 'category',
        name: '日期',
        nameTextStyle: {
          color: '#88B9FA',
          // padding: 4,
        },
        data: opts.xAxisData,
        axisLine: {
          lineStyle: {
            color: '#0E1A4A',
          },
        },
        axisLabel: {
          color: '#88B9FA',
          fontSize: fontSize(12),
          interval: 0,
        },
        splitLine: {
          show: false,
        },
      },
    ],
    yAxis: [
      {
        type: 'value',
        name: '设备数量',
        nameTextStyle: {
          align: 'center',
          color: '#88B9FA',
        },
        nameGap: 22,
        splitLine: {
          lineStyle: {
            color: '#0E1A4A',
            width: fontSize(1),
          },
        },
        axisLine: {
          lineStyle: {
            color: '#0E1A4A',
          },
        },
        axisLabel: {
          color: '#88B9FA',
          fontSize: fontSize(12),
        },
      },
    ],
    series: opts.data,
  };
}

export function baseHomeVideoOnlineChangesDetails(opts) {
  return {
    title: {
      left: 'center',
      top: '10',
      text: `${opts.startTime}  {b|vs}  ${opts.endTime}`,
      textStyle: {
        color: '#8ABAFB',
        fontSize: fontSize(12),
        rich: {
          b: {
            color: '#1b86ff',
            fontWeight: 'bold',
          },
        },
      },
    },
    legend: {
      right: '0%',
      top: '10',
      icon: 'rect',
      itemWidth: fontSize(8),
      itemHeight: fontSize(8),
      textStyle: {
        color: '#88B9FA',
      },
    },
    grid: {
      left: '2%',
      right: '1%',
      bottom: '2%',
      containLabel: true,
    },
    tooltip: {
      trigger: 'axis',
      confine: true,
      backgroundColor: 'rgba(0, 10, 41, 0.7)',
      borderColor: 'transparent',
      padding: [10, 10], //内边距
      textStyle: {
        color: '#fff',
        fontSize: fontSize(12),
      },
      axisPointer: {
        type: 'shadow',
      },
      position: (point) => {
        return [point[0] + 8, point[1] + 8];
      },
      extraCssText:
        'background: rgba(0, 10, 41, 0.7);border: 1px solid #1981f5;border-radius: 8px 8px 8px 8px;opacity: 1;', //添加阴影
    },
    xAxis: [
      {
        type: 'category',
        data: opts.xAxisData,
        axisLine: {
          show: false, // 坐标轴轴线
        },
        axisTick: {
          show: false, // 坐标轴刻度
        },
        axisLabel: {
          color: '#8ABAFB', //更改坐标轴文字颜色
          show: true,
          interval: 0,
          fontSize: fontSize(12), //更改坐标轴文字大小
          formatter: function (params) {
            if (params.length < 3) {
              return params;
            } else {
              var strs = params.split(''); //字符串数组
              var str = '';
              for (var i = 0, s; (s = strs[i++]); ) {
                //遍历字符串数组
                str += s;
                if (!(i % 3)) str += '\n'; //按需要求余
              }
              return str;
            }
          },
        },
      },
    ],
    yAxis: [
      {
        type: 'value',
        name: '设备数量',
        nameTextStyle: {
          align: 'center',
          color: '#88B9FA',
        },
        nameGap: 20,
        splitLine: {
          lineStyle: {
            color: '#0E1A4A',
            width: fontSize(1),
          },
        },
        axisLine: {
          lineStyle: {
            color: '#0E1A4A',
          },
        },
        axisLabel: {
          color: '#88B9FA',
          fontSize: fontSize(12),
        },
      },
    ],
    series: opts.seriesData.map((item) => {
      return {
        name: item.name,
        type: 'bar',
        barWidth: fontSize(10),
        data: item.data,
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          {
            offset: 0,
            color: item.color[0],
          },
          {
            offset: 1,
            color: item.color[1],
          },
        ]),
      };
    }),
  };
}

export function baseHomeCapturerNumber(opts) {
  // 每月 天数
  function xAxisMonth() {
    let date = new Date(opts.filterTime);
    let year = date.getFullYear();
    let month = date.getMonth() + 1;
    let days = new Date(year, month, 0).getDate();

    let data = [];
    for (var i = 1; i <= days; i++) {
      data.push(i);
    }
    return data;
  }
  // 24小时
  function xAxisDate() {
    let data = [];
    for (var i = 1; i <= 24; i++) {
      data.push(i);
    }
    return data;
  }
  return {
    title: {
      text: '抓拍数量（单位：张）',
      left: '0',
      top: '10px',
      textStyle: {
        color: '#8ABAFB',
        fontSize: fontSize(12),
        fontWeight: 400,
      },
    },
    legend: {
      show: false,
    },
    grid: {
      left: '2%',
      right: '5%',
      bottom: '2%',
      containLabel: true,
    },
    tooltip: {
      trigger: 'axis',
      confine: true,
      enterable: true,
      hideDelay: 300,
      backgroundColor: 'transparent',
      borderColor: 'transparent',
      padding: [0, 0], //内边距
      textStyle: {
        color: '#fff',
        fontSize: fontSize(12),
      },
      position: (point) => {
        return [point[0] + 1, point[1] + 1];
      },
      formatter: opts.tooltipFormatter,
      axisPointer: {
        lineStyle: {
          type: 'solid',
          shadowColor: 'rgba(126,199,255,1)',
          shadowBlur: 2,
          color: {
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: 'rgba(126,199,255,0)', // 0% 处的颜色
              },
              {
                offset: 0.5,
                color: 'rgba(126,199,255,1)', // 100% 处的颜色
              },
              {
                offset: 1,
                color: 'rgba(126,199,255,0)', // 100% 处的颜色
              },
            ],
          },
        },
      },
      extraCssText: `background: ${opts.tooltipBg};`, //添加阴影
    },
    xAxis: [
      {
        type: 'category',
        name: opts.cuptureMonthOrDay === 'month' ? '日期' : '小时',
        nameTextStyle: {
          color: '#88B9FA',
          padding: [0, 0, 0, -5],
        },
        data: opts.cuptureMonthOrDay === 'month' ? xAxisMonth() : xAxisDate(),
        axisLine: {
          lineStyle: {
            color: '#0E1A4A',
          },
        },
        axisLabel: {
          color: '#88B9FA',
          fontSize: fontSize(12),
        },
        splitLine: {
          show: false,
        },
      },
    ],
    yAxis: [
      {
        type: 'value',
        splitLine: {
          lineStyle: {
            color: '#0E1A4A',
            width: fontSize(1),
          },
        },
        axisLine: {
          lineStyle: {
            color: '#0E1A4A',
          },
        },
        axisLabel: {
          color: '#88B9FA',
          fontSize: fontSize(12),
        },
      },
    ],
    series: [
      {
        name: '抓拍数量',
        type: 'line',
        data: opts.seriesData,
        showSymbol: false,
        // smooth: true,
        lineStyle: {
          width: fontSize(2),
          color: '#04CDF4', //线条颜色
          shadowColor: '#04CDF4',
          shadowBlur: 1,
          shadowOffsetY: 1,
        },
        areaStyle: {
          //区域填充样式
          //线性渐变，前4个参数分别是x0,y0,x2,y2(范围0~1);相当于图形包围盒中的百分比。如果最后一个参数是‘true’，则该四个值是绝对像素位置。
          color: new echarts.graphic.LinearGradient(
            0,
            0,
            0,
            1,
            [
              {
                offset: 0,
                color: 'rgba(4, 205, 244, 0.8)',
              },
              {
                offset: 1,
                color: 'rgba(4, 205, 244, 0.1)',
              },
            ],
            false,
          ),
        },
        itemStyle: {
          color: '#04CDF4',
          borderColor: '#04CDF4',
          borderWidth: 2,
          shadowColor: '#04CDF4',
          shadowBlur: 5,
          shadowOffsetX: 0,
          shadowOffsetXY: 0,
        },
      },
    ],
  };
}

export function onlineDetailCompare(opts) {
  return {
    color: opts.colors,
    tooltip: {
      textStyle: {
        fontSize: fontSize(12),
      },
      formatter: (params) => {
        return params.name + ':' + params.value[1].split(' ')[1] + '~' + params.value[2].split(' ')[1];
      },
    },
    xAxis: {
      type: 'time',
      interval: 3600 * 1000,
      maxInterval: 3600 * 1000,
      name: '单位：小时',
      nameLocation: 'start',
      axisLine: {
        show: true,
      },
      splitLine: {
        show: false,
      },
      axisLabel: {
        formatter: (value) => {
          const date = new Date(value);
          return `${date.getHours()}`;
        },
        fontSize: fontSize(12),
      },
      axisTick: {
        show: true,
      },
      offset: 20,
    },
    grid: {
      //绘图网格
      top: '5%',
      left: '6%',
      containLabel: true,
    },
    dataZoom: [
      {
        type: 'slider',
        show: opts.yAxis.length >= 8,
        yAxisIndex: [0, 1, 2],
        width: 3,
        zoomLock: true,
        fillerColor: '#116FB4',
        borderColor: 'transparent',
        backgroundColor: '#071B39',
        showDetail: false,
        showDataShadow: false,
        brushSelect: false,
        startValue: opts.categoryData.length - 8,
        endValue: opts.categoryData.length - 1,
      },
      {
        type: 'inside',
        yAxisIndex: [0, 1, 2],
        startValue: opts.categoryData.length - 8,
        endValue: opts.categoryData.length - 1,
        zoomOnMouseWheel: false,
        moveOnMouseWheel: true,
        moveOnMouseMove: false,
      },
      {
        type: 'slider',
        filterMode: 'weakFilter',
        start: 0,
        end: 100,
        height: 20,
      },
    ],
    yAxis: [
      {
        position: 'left',
        data: opts.yAxis,
        offset: 40,
        axisTick: {
          show: false,
        },
        axisLine: {
          show: false,
        },
        axisLabel: {
          //改变刻度字体样式
          fontSize: fontSize(12),
        },
      },
      {
        position: 'right',
        type: 'category',
        axisTick: 'none',
        axisLine: 'none',
        show: true,
        data: opts.categoryData,
        axisLabel: {
          show: true,
          fontSize: fontSize(14),
        },
        // 开启触发事件
        triggerEvent: true,
      },
      {
        show: opts.showOppose,
        data: opts.optionData,
        position: 'left',
        alignTicks: true,
        offset: 15,
        axisTick: {
          show: false,
        },
        axisLine: {
          show: false,
        },
      },
    ],
    series: [
      // 用空bar来显示四个图例
      {
        name: opts.state[0],
        type: 'bar',
        data: [],
      },
      {
        name: opts.state[1],
        type: 'bar',
        data: [],
      },
      {
        name: opts.state[2],
        type: 'bar',
        data: [],
      },
      {
        type: 'custom',
        renderItem: (params, api) => {
          //开发者自定义的图形元素渲染逻辑，是通过书写 renderItem 函数实现的
          const categoryIndex = api.value(0); //这里使用 api.value(0) 取出当前 dataItem 中第一个维度的数值。
          const start = api.coord([api.value(1), categoryIndex]); // 这里使用 api.coord(...) 将数值在当前坐标系中转换成为屏幕上的点的像素值。
          const end = api.coord([api.value(2), categoryIndex]);
          const height = 16; //柱体宽度
          const text = api.value(3)
            ? `${formatDate(new Date(api.value(1)), 'hh:mm')}-${formatDate(new Date(api.value(2)), 'hh:mm')}`
            : null;
          return {
            type: 'group',
            children: [
              {
                type: 'rect',
                // 表示这个图形元素是矩形。还可以是 'circle', 'sector', 'polygon' 等等。
                shape: new echarts.graphic.clipRectByRect(
                  {
                    // 矩形的位置和大小。
                    x: start[0],
                    y: start[1] - height / 2,
                    width: end[0] - start[0],
                    height: height,
                  },
                  {
                    x: params.coordSys.x,
                    y: params.coordSys.y,
                    width: params.coordSys.width,
                    height: params.coordSys.height,
                  },
                ),
                style: api.style(),
              },
              {
                type: 'rect',
                // 表示这个图形元素是矩形。还可以是 'circle', 'sector', 'polygon' 等等。
                shape: new echarts.graphic.clipRectByRect(
                  {
                    // 矩形的位置和大小。
                    x: start[0],
                    y: start[1] - height / 2,
                    width: end[0] - start[0],
                    height: height,
                  },
                  {
                    // 当前坐标系的包围盒。
                    x: params.coordSys.x,
                    y: params.coordSys.y,
                    width: params.coordSys.width,
                    height: params.coordSys.height,
                  },
                ),
                textConfig: {
                  position: 'bottom',
                },
                style: api.style({
                  fill: 'transparent',
                  stroke: 'transparent',
                  text: text,
                  textFill: '#56789C',
                }),
              },
            ],
          };
        },
        encode: {
          x: [1, 2], // data 中『维度1』和『维度2』对应到 X 轴
          y: 0, // data 中『维度0』对应到 Y 轴
        },
        data: opts.data,
      },
    ],
  };
}

// 数据分析-抓拍趋势
export function DataAnalysisCaptureTrend(opts) {
  return {
    title: {
      subtext: '抓拍数量（单位：张）',
      padding: [5, 0, 0, 40],
    },
    legend: {
      right: '0',
      top: 10,
      icon: 'circle',
      itemWidth: fontSize(5),
      itemHeight: fontSize(5),
      itemGap: fontSize(20),
    },
    grid: {
      left: '3%',
      right: '3%',
      bottom: '2%',
      // top: 150,
      containLabel: true,
    },
    tooltip: {
      trigger: 'axis',
      confine: true,
      hideDelay: 300,
      backgroundColor: 'transparent',
      borderColor: 'transparent',
      padding: [0, 0], //内边距
      textStyle: {
        color: '#fff',
        fontSize: fontSize(12),
      },
      position: (point) => {
        return [point[0] + 3, point[1] + 3];
      },
      formatter: opts.tooltipFormatter,
    },
    xAxis: [
      {
        type: 'category',
        name: '日期',
        data: opts.xAxisData,
        axisLabel: {
          fontSize: fontSize(12),
          interval: 0,
        },
        splitLine: {
          show: false,
        },
      },
    ],
    yAxis: [
      {
        type: 'value',
        // name:'抓拍数量（单位：张）',
        // nameTextStyle:{
        //   align:'center',
        //   color:'rgba(0, 0, 0, 0.6)',
        //   padding: [0, 0, 0, 10]
        // },
        splitLine: {
          lineStyle: {
            width: fontSize(1),
            type: 'dashed', // 设置为虚线
          },
        },
        axisLabel: {
          fontSize: fontSize(12),
        },
      },
    ],
    series: opts.data,
  };
}

export function AbnormalDeviceScale(opts) {
  return {
    tooltip: {
      show: 'false',
      trigger: 'axis', //触发类型
      axisPointer: {
        type: 'none',
      },
      padding: [8, 10], //内边距
      extraCssText: 'background: rgba(13, 53, 96, 0.7);border:  1px solid #1684E4;opacity: 1;', //
      textStyle: {
        fontSize: fontSize(12),
      },
    },
    ...opts,
  };
}

export function CaptureDownTimeBar(opts) {
  return {
    title: {
      subtext: opts.subtext,
      padding: [3, 0, 0, 30],
    },
    grid: {
      top: 40,
      left: 30,
      right: 30,
      bottom: 10,
      containLabel: true,
    },
    xAxis: [
      {
        // name: '时间',
        type: 'category',
        data: opts.xAxisData,
        axisLabel: {
          interval: 0,
          show: true,
          position: 'inner',
          fontSize: fontSize(12),
        },
      },
    ],
    yAxis: [
      {
        // name: '突降设备数量',
        type: 'value',
        axisLabel: {
          //y轴文字的配置
          fontSize: fontSize(12),
        },
        splitLine: {
          lineStyle: {
            width: fontSize(1),
            type: 'dashed', // 设置为虚线
          },
        },
      },
      {
        type: 'value',
        name: '异常率',
        nameTextStyle: {
          padding: [0, 0, 0, 50],
        },
        min: 0,
        max: 100,
        axisLabel: {
          formatter: '{value} %',
          fontSize: fontSize(12),
        },
        splitLine: {
          show: false,
        },
      },
    ],
    series: [
      {
        barWidth: fontSize(46),
        type: 'bar',
        label: {
          show: true,
          distance: 1,
          formatter: (params) => {
            return `${params.data.resultValue}%`;
          },
          position: 'top',
          offset: [0, -5],
          color: $var('--color-base-text'),
        },
        itemStyle: {
          color: $var('--color-blue-9'),
        },
        data: opts.seriesData,
      },
    ],
  };
}

export function DailyCaptureByInfluencingFactor(opts) {
  let size = opts.isFullscreen ? 16 : 12;
  return {
    title: {
      subtext: opts.subtext,
      subtextStyle: {
        fontSize: fontSize(size),
      },
      padding: [5, 0, 0, 20],
    },
    grid: {
      left: '3%',
      right: '3%',
      bottom: opts.isFullscreen ? 35 : 25,
      containLabel: true,
    },
    toolbox: {
      show: true,
      right: 10,
      feature: {
        dataZoom: {
          show: true, // 数据区域缩放
        },
      },
    },
    legend: {
      itemWidth: fontSize(9),
      itemHeight: fontSize(9),
      right: 100,
      top: 8,
      itemGap: fontSize(20),
      textStyle: {
        fontSize: fontSize(size),
      },
    },
    tooltip: {
      showDelay: 0,
      formatter: opts.tooltipFormatter,
      enterable: true, // 鼠标是否可进入提示框浮层中
      textStyle: {
        fontSize: fontSize(12),
      },
      axisPointer: {
        // show: true,
        // type: 'cross',
        // lineStyle: {
        //   type: 'dashed',
        //   width: 1,
        // },
      },
    },
    xAxis: {
      name: opts.xAxisData.name,
      type: opts.xAxisData.type,
      data: opts.xAxisData.data,
      nameTextStyle: {
        padding: [22, 0, 0, 0],
        align: 'right',
        verticalAlign: 'top',
        fontSize: fontSize(size),
      },
      axisLabel: {
        // formatter: function (value) {
        //     return value + ' Day'; // 自定义标签格式
        // }
        interval: 0,
        fontSize: fontSize(size),
      },
      splitLine: {
        show: true,
        lineStyle: {
          type: 'dashed',
        },
      },
    },
    yAxis: {
      // name: opts.yAxisData.name, // 日均抓拍量（张）
      type: 'value',
      splitLine: {
        show: true,
        lineStyle: {
          type: 'dashed',
        },
      },
      axisLabel: {
        fontSize: fontSize(size),
      },
    },
    animation: false,
    series: opts.series,
  };
}

export function assetCompare(opts) {
  return {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'line', // 默认为直线，可选为：'line' | 'shadow' | 'cross' , shadow表示阴影
        lineStyle: {
          type: 'dashed',
          color: $var('--color-axis-pointer-line'),
        },
      },
      padding: [8, 10], //内边距
      extraCssText: 'background: var(--bg-echart-tooltip);', //添加阴影
      textStyle: {
        fontSize: fontSize(12),
      },
    },
    grid: {
      left: '1%',
      right: '1%',
      top: '20%',
      bottom: '2%',
      containLabel: true,
    },
    legend: {
      type: 'plain',
      right: '5',
      top: '0',
      data: ['累加变化', '最终变化', '变化次数'],
      itemGap: fontSize(10),
      itemWidth: fontSize(10),
      itemHeight: fontSize(10),
      textStyle: {
        fontSize: fontSize(12),
        color: $var('--color-base-text'),
      },
    },
    xAxis: [
      {
        type: 'category',
        axisTick: {
          alignWithLabel: true,
        },
        axisLine: {
          lineStyle: {
            color: $var('--color-axis-line'),
          },
        },
        nameTextStyle: {
          color: $var('--color-axis-lable'),
        },
        color: $var('--color-axis-lable'),
        axisLabel: {
          interval: 0,
          color: $var('--color-axis-lable'), //更改坐标轴文字颜色
          fontSize: fontSize(12), //更改坐标轴文字大小
          show: true,
        },
        data: opts.xAxis,
      },
    ],
    yAxis: [
      {
        type: 'value',
        name: '变化次数',
        position: 'left',
        alignTicks: true,
        splitLine: {
          lineStyle: {
            color: $var('--color-axis-line'),
            width: fontSize(1),
          },
        },
        nameTextStyle: {
          color: $var('--color-axis-lable'),
        },
        axisLine: {
          lineStyle: {
            color: $var('--color-axis-line'),
          },
        },
        axisLabel: {
          formatter: '{value}',
          // formatter: function(params) {
          //   console.log(params, 'params888')
          // },
          color: $var('--color-axis-lable'),
          fontSize: fontSize(12),
        },
      },
      {
        type: 'value',
        name: '累加变化',
        position: 'right',
        alignTicks: true,
        splitLine: {
          lineStyle: {
            color: $var('--color-axis-line'),
            width: fontSize(1),
          },
        },
        nameTextStyle: {
          color: $var('--color-axis-lable'),
        },
        axisLine: {
          lineStyle: {
            color: $var('--color-axis-line'),
          },
        },
        axisLabel: {
          formatter: '{value}',
          // formatter: function(params) {
          //   console.log(params, 'params888')
          // },
          color: $var('--color-axis-lable'),
          fontSize: fontSize(12),
        },
      },
    ],
    series: [
      //'累加变化', '最终变化', '变化次数'
      {
        name: '累加变化',
        type: 'bar',
        barWidth: fontSize(18),
        yAxisIndex: 1,
        data: opts.accumulativeCount,
        itemStyle: {
          color: $var('--color-blue-9'),
        },
      },
      {
        name: '最终变化',
        type: 'bar',
        barWidth: fontSize(18),
        data: opts.finalCount,
        itemStyle: {
          color: $var('--color-blue-10'),
        },
      },
      {
        name: '变化次数',
        type: 'line',
        showSymbol: false,
        data: opts.changeCount,
        itemStyle: {
          color: $var('--color-yellow-6'),
        },
      },
    ],
  };
}

export function dataMonitorOfflineDetail(opts) {
  let dataZoomBgColor = '#071B39';
  let fillerColor = '#116FB4';
  if (['light', 'deepBlue'].includes(document.documentElement.getAttribute('data-theme'))) {
    // textColor =  'rgba(0, 0, 0, 0.6)'
    dataZoomBgColor = 'rgba(47,69,84,0)';
    fillerColor = 'rgba(167,183,204,0.4)';
  }
  return {
    color: opts.colors,
    tooltip: {
      textStyle: {
        color: $var('--color-base-text'),
        fontSize: fontSize(12),
      },
      formatter: (params) => {
        let showTitle = params.name ? params.name + ':' : '';
        return showTitle + params.value[1].split(' ')[1] + '~' + params.value[2].split(' ')[1];
      },
    },
    legend: {
      data: opts.state,
      icon: 'rect',
      top: 0,
      right: 0,
      itemWidth: fontSize(16),
      itemHeight: fontSize(16),
      selectedMode: false,
    },
    xAxis: {
      type: 'time',
      interval: 3600 * 1000,
      maxInterval: 3600 * 1000,
      name: '单位：小时',
      nameLocation: 'start',
      axisLine: {
        show: true,
      },
      splitLine: {
        show: false,
      },
      axisLabel: {
        formatter: (value) => {
          const date = new Date(value);
          return `${date.getHours()}`;
        },
        fontSize: fontSize(12),
      },
      axisTick: {
        show: true,
      },
      offset: 20,
    },
    grid: {
      //绘图网格
      top: '5%',
      left: 0,
      containLabel: true,
    },
    dataZoom: [
      {
        type: 'slider',
        show: opts.yAxis.length >= 8,
        yAxisIndex: [0, 1],
        width: 3,
        zoomLock: true,
        fillerColor: fillerColor,
        borderColor: 'transparent',
        backgroundColor: dataZoomBgColor,
        showDetail: false,
        showDataShadow: false,
        brushSelect: false,
        startValue: opts.categoryData.length - 8,
        endValue: opts.categoryData.length - 1,
      },
      {
        type: 'inside',
        yAxisIndex: [0, 1],
        startValue: opts.categoryData.length - 8,
        endValue: opts.categoryData.length - 1,
        zoomOnMouseWheel: false,
        moveOnMouseWheel: true,
        moveOnMouseMove: false,
      },
    ],
    yAxis: [
      {
        data: opts.yAxis,
        axisTick: {
          show: false,
        },
        axisLine: {
          show: false,
        },
        axisLabel: {
          fontSize: fontSize(12),
        },
        triggerEvent: true, // 是否触发事件
      },
      {
        type: 'category',
        axisTick: 'none',
        axisLine: 'none',
        show: true,
        data: opts.categoryData,
        axisLabel: {
          show: true,
          fontSize: fontSize(14),
        },
      },
    ],
    series: [
      // 用空bar来显示四个图例
      {
        name: opts.state[0],
        type: 'bar',
        data: [],
      },
      {
        name: opts.state[1],
        type: 'bar',
        data: [],
      },
      {
        name: opts.state[2],
        type: 'bar',
        data: [],
      },
      {
        type: 'custom',
        renderItem: (params, api) => {
          //开发者自定义的图形元素渲染逻辑，是通过书写 renderItem 函数实现的
          const categoryIndex = api.value(0); //这里使用 api.value(0) 取出当前 dataItem 中第一个维度的数值。
          const start = api.coord([api.value(1), categoryIndex]); // 这里使用 api.coord(...) 将数值在当前坐标系中转换成为屏幕上的点的像素值。
          const end = api.coord([api.value(2), categoryIndex]);
          const height = 16; //柱体宽度
          const text = api.value(3)
            ? `${formatDate(new Date(api.value(1)), 'hh:mm')}-${formatDate(new Date(api.value(2)), 'hh:mm')}`
            : null;
          return {
            type: 'group',
            children: [
              {
                type: 'rect',
                // 表示这个图形元素是矩形。还可以是 'circle', 'sector', 'polygon' 等等。
                shape: new echarts.graphic.clipRectByRect(
                  {
                    // 矩形的位置和大小。
                    x: start[0],
                    y: start[1] - height / 2,
                    width: end[0] - start[0],
                    height: height,
                  },
                  {
                    x: params.coordSys.x,
                    y: params.coordSys.y,
                    width: params.coordSys.width,
                    height: params.coordSys.height,
                  },
                ),
                style: api.style(),
              },
              {
                type: 'rect',
                // 表示这个图形元素是矩形。还可以是 'circle', 'sector', 'polygon' 等等。
                shape: new echarts.graphic.clipRectByRect(
                  {
                    // 矩形的位置和大小。
                    x: start[0],
                    y: start[1] - height / 2,
                    width: end[0] - start[0],
                    height: height,
                  },
                  {
                    // 当前坐标系的包围盒。
                    x: params.coordSys.x,
                    y: params.coordSys.y,
                    width: params.coordSys.width,
                    height: params.coordSys.height,
                  },
                ),
                textConfig: {
                  position: 'bottom',
                },
                style: api.style({
                  fill: 'transparent',
                  stroke: 'transparent',
                  text: text,
                  textFill: '#56789C',
                }),
              },
            ],
          };
        },
        encode: {
          x: [1, 2], // data 中『维度1』和『维度2』对应到 X 轴
          y: 0, // data 中『维度0』对应到 Y 轴
        },
        data: opts.data,
      },
    ],
  };
}
export function dataMonitorDayCapLine(opts) {
  // 24小时
  function xAxisDate() {
    let data = [];
    for (var i = 0; i <= 24; i++) {
      data.push(i);
    }
    return data;
  }
  return {
    title: {
      text: '抓拍数量（单位：张）',
      left: '0',
      top: '15px',
      textStyle: {
        fontSize: fontSize(12),
        fontWeight: 400,
      },
    },
    legend: {
      show: false,
    },
    grid: {
      left: 0,
      right: '5%',
      bottom: '2%',
      containLabel: true,
    },
    tooltip: {
      trigger: 'axis',
      confine: true,
      enterable: true,
      hideDelay: 300,
      padding: [0, 0], //内边距
      textStyle: {
        fontSize: fontSize(12),
      },
      position: (point) => {
        return [point[0] + 1, point[1] + 1];
      },
      formatter: opts.tooltipFormatter,
      axisPointer: {
        lineStyle: {
          type: 'solid',
          shadowColor: 'rgba(126,199,255,1)',
          shadowBlur: 2,
          color: {
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: 'rgba(126,199,255,0)', // 0% 处的颜色
              },
              {
                offset: 0.5,
                color: 'rgba(126,199,255,1)', // 100% 处的颜色
              },
              {
                offset: 1,
                color: 'rgba(126,199,255,0)', // 100% 处的颜色
              },
            ],
          },
        },
      },
    },
    xAxis: [
      {
        type: 'category',
        boundaryGap: false,
        name: '',
        nameTextStyle: {
          padding: [0, 0, 0, -5],
        },
        data: xAxisDate(),
        axisLabel: {
          fontSize: fontSize(12),
          margin: 15,
        },
        splitLine: {
          show: false,
        },
      },
    ],
    yAxis: [
      {
        type: 'value',
        splitLine: {
          lineStyle: {
            width: fontSize(1),
          },
        },
        axisLabel: {
          formatter: function (val) {
            return '{a|' + val + '}';
          },
          rich: {
            a: {
              width: 50,
              backgroundColor: 'none',
              fontSize: fontSize(12),
            },
          },
        },
      },
    ],
    series: [
      {
        name: '抓拍数量',
        type: 'line',
        data: opts.seriesData,
        showSymbol: false,
        // smooth: true,
        lineStyle: {
          width: fontSize(2),
          color: $var('--color-blue-5'), //线条颜色
          shadowColor: $var('--color-blue-5'),
          shadowBlur: 1,
          shadowOffsetY: 1,
        },
        areaStyle: {
          //区域填充样式
          //线性渐变，前4个参数分别是x0,y0,x2,y2(范围0~1);相当于图形包围盒中的百分比。如果最后一个参数是‘true’，则该四个值是绝对像素位置。
          color: new echarts.graphic.LinearGradient(
            0,
            0,
            0,
            1,
            [
              {
                offset: 0,
                color: $var('--linear-gradient-blue-4-start'),
              },
              {
                offset: 1,
                color: $var('--linear-gradient-blue-4-end'),
              },
            ],
            false,
          ),
        },
        itemStyle: {
          color: $var('--color-blue-5'),
          borderColor: $var('--color-blue-5'),
          borderWidth: 2,
          shadowColor: $var('--color-blue-5'),
          shadowBlur: 5,
          shadowOffsetX: 0,
          shadowOffsetXY: 0,
        },
      },
    ],
  };
}

export function dataMonitorDayCapCustom(opts) {
  return {
    color: opts.colors,
    tooltip: {
      textStyle: {
        fontSize: fontSize(12),
      },
      formatter: (params) => {
        return params.name
          ? params.name + ':' + params.value[1].split(' ')[1] + '~' + params.value[2].split(' ')[1]
          : null;
      },
    },
    legend: {
      data: opts.state,
      icon: 'rect',
      bottom: 0,
      right: 0,
      itemWidth: fontSize(16),
      itemHeight: fontSize(16),
      selectedMode: false,
    },
    xAxis: [
      {
        position: 'top',
        type: 'time',
        interval: 3600 * 1000,
        maxInterval: 3600 * 1000,
        boundaryGap: false,
        name: '',
        nameLocation: 'end',
        nameTextStyle: {
          padding: [0, 0, 0, -5],
        },
        axisLine: {
          show: false,
          onZero: false,
        },
        splitLine: {
          show: false,
        },
        axisLabel: {
          show: false,
          fontSize: fontSize(12),
          formatter: (value) => {
            const date = new Date(value);
            return `${date.getHours()}`;
          },
        },
      },
    ],
    grid: {
      //绘图网格
      top: '5%',
      left: 0,
      bottom: '2%',
      right: '5%',
      containLabel: true,
    },
    yAxis: [
      {
        reverse: true,
        data: opts.yAxis,
        axisTick: {
          show: false,
        },
        axisLine: {
          show: false,
        },
        axisLabel: {
          show: true,
          formatter: '{a|' + ' ' + '}',
          rich: {
            a: {
              width: 50,
              backgroundColor: 'none',
              fontSize: fontSize(12),
            },
          },
        },
      },
      {
        type: 'category',
        axisTick: 'none',
        axisLine: 'none',
        show: true,
        data: opts.categoryData,
        axisLabel: {
          show: false,
          fontSize: fontSize(14),
        },
      },
    ],
    series: [
      // 用空bar来显示四个图例
      {
        name: opts.state[0],
        type: 'bar',
        data: [],
      },
      {
        name: opts.state[1],
        type: 'bar',
        data: [],
      },
      {
        name: opts.state[2],
        type: 'bar',
        data: [],
      },
      {
        type: 'custom',
        renderItem: (params, api) => {
          //开发者自定义的图形元素渲染逻辑，是通过书写 renderItem 函数实现的
          const categoryIndex = api.value(0); //这里使用 api.value(0) 取出当前 dataItem 中第一个维度的数值。
          const start = api.coord([api.value(1), categoryIndex]); // 这里使用 api.coord(...) 将数值在当前坐标系中转换成为屏幕上的点的像素值。
          const end = api.coord([api.value(2), categoryIndex]);
          const height = 16; //柱体宽度
          const text = api.value(3)
            ? `${formatDate(new Date(api.value(1)), 'hh:mm')}-${formatDate(new Date(api.value(2)), 'hh:mm')}`
            : null;
          return {
            type: 'group',
            children: [
              {
                type: 'rect',
                // 表示这个图形元素是矩形。还可以是 'circle', 'sector', 'polygon' 等等。
                shape: new echarts.graphic.clipRectByRect(
                  {
                    // 矩形的位置和大小。
                    x: start[0],
                    y: start[1] - height / 2,
                    width: end[0] - start[0],
                    height: height,
                  },
                  {
                    x: params.coordSys.x,
                    y: params.coordSys.y,
                    width: params.coordSys.width,
                    height: params.coordSys.height,
                  },
                ),
                style: api.style(),
              },
              {
                type: 'rect',
                // 表示这个图形元素是矩形。还可以是 'circle', 'sector', 'polygon' 等等。
                shape: new echarts.graphic.clipRectByRect(
                  {
                    // 矩形的位置和大小。
                    x: start[0],
                    y: start[1] - height / 2,
                    width: end[0] - start[0],
                    height: height,
                  },
                  {
                    // 当前坐标系的包围盒。
                    x: params.coordSys.x,
                    y: params.coordSys.y,
                    width: params.coordSys.width,
                    height: params.coordSys.height,
                  },
                ),
                textConfig: {
                  position: 'bottom',
                },
                style: api.style({
                  fill: 'transparent',
                  stroke: 'transparent',
                  text: text,
                  textFill: '#56789C',
                }),
              },
            ],
          };
        },
        encode: {
          x: [1, 2], // data 中『维度1』和『维度2』对应到 X 轴
          y: 0, // data 中『维度0』对应到 Y 轴
        },
        data: opts.data,
      },
    ],
  };
}
export function dataMonitorMonthCapLine(opts) {
  // 每月 天数
  function xAxisMonth() {
    let date = new Date(opts.filterTime);
    let year = date.getFullYear();
    let month = date.getMonth() + 1;
    let days = new Date(year, month, 0).getDate();

    let data = [];
    for (var i = 1; i <= days; i++) {
      data.push(i);
    }
    return data;
  }
  // 24小时
  function xAxisDate() {
    let data = [];
    for (var i = 1; i <= 24; i++) {
      data.push(i);
    }
    return data;
  }
  return {
    title: {
      text: '抓拍数量（单位：张）',
      left: '0',
      top: '10px',
      textStyle: {
        fontSize: fontSize(12),
        fontWeight: 400,
      },
    },
    legend: {
      show: false,
    },
    grid: {
      left: '2%',
      right: '5%',
      bottom: '2%',
      containLabel: true,
    },
    tooltip: {
      trigger: 'axis',
      confine: true,
      enterable: true,
      hideDelay: 300,
      backgroundColor: 'transparent',
      borderColor: 'transparent',
      padding: [0, 0], //内边距
      textStyle: {
        fontSize: fontSize(12),
      },
      position: (point) => {
        return [point[0] + 1, point[1] + 1];
      },
      formatter: opts.tooltipFormatter,
      axisPointer: {
        lineStyle: {
          type: 'solid',
          shadowColor: 'rgba(126,199,255,1)',
          shadowBlur: 2,
          color: {
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: 'rgba(126,199,255,0)', // 0% 处的颜色
              },
              {
                offset: 0.5,
                color: 'rgba(126,199,255,1)', // 100% 处的颜色
              },
              {
                offset: 1,
                color: 'rgba(126,199,255,0)', // 100% 处的颜色
              },
            ],
          },
        },
      },
      extraCssText: `background: ${opts.tooltipBg};`, //添加阴影
    },
    xAxis: [
      {
        type: 'category',
        name: opts.cuptureMonthOrDay === 'month' ? '日期' : '小时',
        nameTextStyle: {
          padding: [0, 0, 0, -5],
        },
        data: opts.cuptureMonthOrDay === 'month' ? xAxisMonth() : xAxisDate(),
        axisLabel: {
          fontSize: fontSize(12),
        },
        splitLine: {
          show: false,
        },
      },
    ],
    yAxis: [
      {
        type: 'value',
        splitLine: {
          lineStyle: {
            width: fontSize(1),
          },
        },
        axisLabel: {
          fontSize: fontSize(12),
        },
      },
    ],
    series: [
      {
        name: '抓拍数量',
        type: 'line',
        data: opts.seriesData,
        showSymbol: false,
        // smooth: true,
        lineStyle: {
          width: fontSize(2),
          color: $var('--color-blue-5'), //线条颜色
          shadowColor: $var('--color-blue-5'),
          shadowBlur: 1,
          shadowOffsetY: 1,
        },
        areaStyle: {
          //区域填充样式
          //线性渐变，前4个参数分别是x0,y0,x2,y2(范围0~1);相当于图形包围盒中的百分比。如果最后一个参数是‘true’，则该四个值是绝对像素位置。
          color: new echarts.graphic.LinearGradient(
            0,
            0,
            0,
            1,
            [
              {
                offset: 0,
                color: $var('--linear-gradient-blue-4-start'),
              },
              {
                offset: 1,
                color: $var('--linear-gradient-blue-4-end'),
              },
            ],
            false,
          ),
        },
        itemStyle: {
          color: $var('--color-blue-5'),
          borderColor: $var('--color-blue-5'),
          borderWidth: 2,
          shadowColor: $var('--color-blue-5'),
          shadowBlur: 5,
          shadowOffsetX: 0,
          shadowOffsetXY: 0,
        },
      },
    ],
  };
}

// 卡口在线数量
export function baseHomeBayonetsOnlineNumber(opts) {
  return {
    title: {
      text: '在线数量',
      left: '0',
      top: '5px',
      textStyle: {
        color: '#8ABAFB',
        fontSize: fontSize(12),
        fontWeight: 400,
      },
    },
    legend: {
      right: '0',
      top: '10',
      icon: 'rect',
      itemWidth: fontSize(15),
      itemHeight: fontSize(1),
      textStyle: {
        color: '#8ABAFB',
      },
    },
    grid: {
      left: '2%',
      right: '5%',
      bottom: '2%',
      containLabel: true,
    },
    tooltip: {
      trigger: 'axis',
      confine: true,
      enterable: true,
      hideDelay: 300,
      backgroundColor: 'transparent',
      borderColor: 'transparent',
      padding: [0, 0], //内边距
      textStyle: {
        color: '#fff',
        fontSize: fontSize(12),
      },
      position: (point) => {
        return [point[0] + 3, point[1] + 3];
      },
      formatter: opts.tooltipFormatter,
      extraCssText: `background: ${opts.tooltipBg};`, //添加阴影
    },
    xAxis: [
      {
        type: 'category',
        name: '日期',
        nameTextStyle: {
          color: '#8ABAFB',
          // padding: 4,
        },
        data: opts.xAxisData,
        axisLine: {
          lineStyle: {
            color: '#0E1A4A',
          },
        },
        axisLabel: {
          color: '#8ABAFB',
          fontSize: fontSize(12),
          interval: 0,
        },
        splitLine: {
          show: false,
        },
      },
    ],
    yAxis: [
      {
        type: 'value',
        nameTextStyle: {
          align: 'center',
          color: '#8ABAFB',
        },
        nameGap: 30,
        splitLine: {
          lineStyle: {
            color: '#16286E',
            width: fontSize(1),
          },
        },
        axisLine: {
          lineStyle: {
            color: '#8ABAFB',
          },
        },
        axisLabel: {
          color: '#8ABAFB',
          fontSize: fontSize(12),
        },
      },
    ],
    series: opts.data,
  };
}

// 卡口在线数量 -- 详情
export function baseHomeBayonetsOnlineNumberDetails(opts) {
  return {
    title: {
      left: 'center',
      top: '5',
      text: opts.currentDay,
      textStyle: {
        color: '#8ABAFB',
        fontSize: fontSize(14),
      },
    },
    legend: {
      right: '0%',
      top: '15',
      icon: 'rect',
      itemWidth: fontSize(8),
      itemHeight: fontSize(8),
      textStyle: {
        color: '#8ABAFB',
      },
      // 设置默认选中的图例
      selected: opts.selectedLegend,
    },
    grid: {
      left: '2%',
      right: '1%',
      bottom: '2%',
      containLabel: true,
    },
    tooltip: {
      trigger: 'axis',
      confine: true,
      backgroundColor: 'transparent',
      borderColor: 'transparent',
      padding: [10, 10], //内边距
      textStyle: {
        color: '#fff',
        fontSize: fontSize(12),
      },
      axisPointer: {
        type: 'shadow',
      },
      position: (point) => {
        return [point[0] + 8, point[1] + 8];
      },
      extraCssText: `background: ${opts.tooltipBg};border: 1px solid #1981f5;border-radius: 8px 8px 8px 8px;opacity: 1;`, //添加阴影
    },
    xAxis: [
      {
        type: 'category',
        data: opts.xAxisData,
        axisLine: {
          show: false, // 坐标轴轴线
        },
        axisTick: {
          show: false, // 坐标轴刻度
        },
        axisLabel: {
          color: '#8ABAFB', //更改坐标轴文字颜色
          show: true,
          interval: 0,
          fontSize: fontSize(12), //更改坐标轴文字大小
          formatter: function (params) {
            if (params.length < 3) {
              return params;
            } else {
              var strs = params.split(''); //字符串数组
              var str = '';
              for (var i = 0, s; (s = strs[i++]); ) {
                //遍历字符串数组
                str += s;
                if (!(i % 3)) str += '\n'; //按需要求余
              }
              return str;
            }
          },
        },
      },
    ],
    yAxis: [
      {
        type: 'value',
        name: '在线设备数量',
        nameTextStyle: {
          align: 'center',
          color: '#8ABAFB',
        },
        nameGap: 30,
        splitLine: {
          lineStyle: {
            color: '#16286E',
            width: fontSize(1),
          },
        },
        axisLine: {
          lineStyle: {
            color: '#8ABAFB',
          },
        },
        axisLabel: {
          color: '#8ABAFB',
          fontSize: fontSize(12),
        },
      },
    ],
    series: opts.seriesData.map((item) => {
      return {
        name: item.name,
        type: 'bar',
        barWidth: fontSize(12),
        data: item.data,
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          {
            offset: 0,
            color: item.color[0],
          },
          {
            offset: 1,
            color: item.color[1],
          },
        ]),
      };
    }),
  };
}

// 治理工单
export function baseHomeGovernWorkOrder(opts) {
  return {
    title: {
      left: '0',
      top: '15',
      text: '工单数量',
      textStyle: {
        color: '#8ABAFB',
        fontSize: fontSize(12),
      },
    },
    grid: {
      left: '5%',
      right: '1%',
      bottom: '2%',
      containLabel: true,
    },
    legend: {
      type: 'plain',
      right: '5',
      top: '15',
      itemGap: fontSize(10),
      itemWidth: fontSize(10),
      itemHeight: fontSize(10),
      textStyle: {
        fontSize: fontSize(12),
        color: '#8ABAFB',
      },
    },
    tooltip: {
      trigger: 'axis',
      confine: true,
      hideDelay: 300,
      backgroundColor: 'transparent',
      borderColor: 'transparent',
      padding: [0, 0], //内边距
      textStyle: {
        color: '#fff',
        fontSize: fontSize(12),
      },
      position: (point) => {
        return [point[0] + 3, point[1] + 3];
      },
      formatter: opts.tooltipFormatter,
      extraCssText: `background: ${opts.tooltipBg};`, //添加阴影
    },
    xAxis: [
      {
        type: 'category',
        data: opts.xAxis,
        nameTextStyle: {
          color: '#8ABAFB',
        },
        axisLine: {
          lineStyle: {
            color: '#8ABAFB',
          },
        },
        axisLabel: {
          interval: 0,
          color: '#8ABAFB', //更改坐标轴文字颜色
          fontSize: fontSize(12), //更改坐标轴文字大小
          show: true,
          position: 'inner',
          formatter: function (params) {
            let newName = '';
            params.length > 3 ? (newName = params.substring(0, 3)) : (newName = params);
            let reallyName = '';
            for (let i = 0; i < newName.length; i++) {
              reallyName = reallyName + `${newName[i]}\n`;
            }
            params.length > 3 ? reallyName + '...' : null;
            return reallyName;
          },
        },
        splitLine: {
          lineStyle: {
            color: '#16286E',
            width: fontSize(1),
          },
        },
      },
    ],
    yAxis: [
      {
        min: 0,
        type: 'value',
        splitLine: {
          lineStyle: {
            color: '#16286E',
            width: fontSize(1),
          },
        },
        axisLine: {
          lineStyle: {
            color: '#8ABAFB',
          },
        },
        axisLabel: {
          formatter: '{value}',
          color: '#8ABAFB',
          fontSize: fontSize(12),
        },
      },
      {
        // name: '完成率',
        min: 0,
        max: 100,
        // nameTextStyle: {
        //   color: '#8ABAFB',
        //   fontSize: fontSize(12),
        // },
        type: 'value',
        splitLine: {
          show: false,
        },
        axisLine: {
          lineStyle: {
            color: '#8ABAFB',
          },
        },
        axisLabel: {
          formatter: '{value}%',
          color: '#8ABAFB',
          fontSize: fontSize(12),
        },
      },
    ],
    series: opts.series,
  };
}

// 视频监控设备撤销率
export function baseHomeVideoCancelRate(opts) {
  return {
    title: {
      left: '5',
      top: '15',
      text: '撤销数量',
      textStyle: {
        color: '#8ABAFB',
        fontSize: fontSize(12),
      },
    },
    grid: {
      left: '5%',
      right: '1%',
      bottom: '2%',
      containLabel: true,
    },
    legend: {
      type: 'plain',
      right: '5',
      top: '15',
      itemGap: fontSize(10),
      itemWidth: fontSize(10),
      itemHeight: fontSize(10),
      textStyle: {
        fontSize: fontSize(12),
        color: '#8ABAFB',
      },
      data: opts.legendList,
    },
    tooltip: {
      trigger: 'axis',
      confine: true,
      hideDelay: 300,
      backgroundColor: 'transparent',
      borderColor: 'transparent',
      padding: [0, 0], //内边距
      textStyle: {
        color: '#fff',
        fontSize: fontSize(12),
      },
      position: (point) => {
        return [point[0] + 3, point[1] + 3];
      },
      formatter: opts.tooltipFormatter,
      extraCssText: `background: ${opts.tooltipBg};`, //添加阴影
    },
    xAxis: [
      {
        type: 'category',
        data: opts.xAxis,
        nameTextStyle: {
          color: '#8ABAFB',
        },
        axisLine: {
          lineStyle: {
            color: '#8ABAFB',
          },
        },
        axisLabel: {
          interval: 0,
          color: '#8ABAFB', //更改坐标轴文字颜色
          fontSize: fontSize(12), //更改坐标轴文字大小
          show: true,
          position: 'inner',
          formatter: function (params) {
            let newName = '';
            params.length > 3 ? (newName = params.substring(0, 3)) : (newName = params);
            let reallyName = '';
            for (let i = 0; i < newName.length; i++) {
              reallyName = reallyName + `${newName[i]}\n`;
            }
            params.length > 3 ? reallyName + '...' : null;
            return reallyName;
          },
        },
        splitLine: {
          lineStyle: {
            color: '#16286E',
            width: fontSize(1),
          },
        },
      },
    ],
    yAxis: [
      {
        // name: '工单数量',
        min: 0,
        nameTextStyle: {
          color: '#fff',
          fontSize: fontSize(12),
        },
        type: 'value',
        splitLine: {
          // show: false,
          lineStyle: {
            color: '#16286E',
            width: fontSize(1),
          },
        },
        axisLine: {
          lineStyle: {
            color: '#8ABAFB',
          },
        },
        axisLabel: {
          formatter: '{value}',
          color: '#8ABAFB',
          fontSize: fontSize(12),
        },
      },
      {
        // name: '撤销率',
        min: 0,
        nameTextStyle: {
          color: '#8ABAFB',
          fontSize: fontSize(12),
        },
        type: 'value',
        splitLine: {
          show: false,
        },
        axisLine: {
          lineStyle: {
            color: '#8ABAFB',
          },
        },
        axisLabel: {
          formatter: '{value}%',
          color: '#8ABAFB',
          fontSize: fontSize(12),
        },
      },
    ],
    series: opts.series,
  };
}

// ZDR数据治理
export function baseHomeZDRDataGovernance(opts) {
  return {
    title: {
      left: '5',
      top: '15',
      text: '轨迹数量',
      textStyle: {
        color: '#8ABAFB',
        fontSize: fontSize(12),
      },
    },
    grid: {
      left: '5%',
      right: '1%',
      bottom: '2%',
      containLabel: true,
    },
    legend: {
      type: 'plain',
      right: '5',
      top: '15',
      itemGap: fontSize(10),
      itemWidth: fontSize(10),
      itemHeight: fontSize(10),
      textStyle: {
        fontSize: fontSize(12),
        color: '#8ABAFB',
      },
    },
    tooltip: {
      trigger: 'axis',
      confine: true,
      hideDelay: 300,
      backgroundColor: 'transparent',
      borderColor: 'transparent',
      padding: [0, 0], //内边距
      textStyle: {
        color: '#fff',
        fontSize: fontSize(12),
      },
      position: (point) => {
        return [point[0] + 3, point[1] + 3];
      },
      formatter: opts.tooltipFormatter,
      extraCssText: `background: ${opts.tooltipBg};`, //添加阴影
    },
    xAxis: [
      {
        type: 'category',
        data: opts.xAxis,
        nameTextStyle: {
          color: '#8ABAFB',
        },
        axisLine: {
          lineStyle: {
            color: '#2762AF',
          },
        },
        axisLabel: {
          interval: 0,
          color: '#8ABAFB', //更改坐标轴文字颜色
          fontSize: fontSize(12), //更改坐标轴文字大小
          show: true,
          position: 'inner',
          formatter: function (params) {
            let newName = '';
            params.length > 3 ? (newName = params.substring(0, 3)) : (newName = params);
            let reallyName = '';
            for (let i = 0; i < newName.length; i++) {
              reallyName = reallyName + `${newName[i]}\n`;
            }
            params.length > 3 ? reallyName + '...' : null;
            return reallyName;
          },
        },
        splitLine: {
          show: false,
        },
      },
    ],
    yAxis: [
      {
        min: 0,
        type: 'value',
        splitLine: {
          lineStyle: {
            color: '#16286E',
            width: fontSize(1),
          },
        },
        axisLine: {
          lineStyle: {
            color: '#8ABAFB',
          },
        },
        axisLabel: {
          formatter: '{value}',
          color: '#8ABAFB',
          fontSize: fontSize(12),
        },
      },
    ],
    series: opts.series,
  };
}

export function examinationTendencyChart(opts) {
  return {
    grid: {
      left: '0%',
      right: '0%',
      bottom: '2%',
      top: '12%',
      containLabel: true,
    },
    legend: {
      right: '0%',
      top: 0,
    },
    color: opts.colors,
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        // 坐标轴指示器，坐标轴触发有效
        type: 'line', // 默认为直线，可选为：'line' | 'shadow' | 'cross' , shadow表示阴影
        lineStyle: {
          type: 'dashed',
        },
      },
      enterable: true,
      padding: [4, 10], //内边距
      formatter: opts.tooltipFormatter,
      textStyle: {
        fontSize: fontSize(12),
      },
    },
    xAxis: [
      {
        type: 'category',
        data: opts.xAxis,
        axisTick: {
          //y轴刻度线
          show: false,
        },
        axisLabel: {
          interval: 0,
          fontSize: fontSize(12), //更改坐标轴文字大小
          formatter: function (params) {
            let newName = '';
            if (params.length > 3) {
              newName = params.substring(0, 3) + '...';
            } else {
              newName = params;
            }
            return newName;
          },
          clickable: true,
        },
        triggerEvent: true,
      },
    ],
    yAxis: [
      {
        type: 'value',
        name: '',
        min: 0,
        axisTick: {
          //y轴刻度线
          show: false,
        },
        axisLine: {
          show: false,
        },
        axisLabel: {
          formatter: '{value}',
          fontSize: fontSize(12),
        },
        splitLine: {
          lineStyle: {
            type: 'solid',
            width: fontSize(1),
          },
        },
      },
    ],
    series: opts.series,
  };
}
export function examinationChildTendencyChart(opts) {
  return {
    grid: {
      left: 0,
      right: 0,
      bottom: '2%',
      top: '12%',
      containLabel: true,
    },
    legend: {
      right: '3%',
      show: false,
      top: 0,
      selected: opts.legendSelected,
    },
    color: opts.colors,
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'line', // 默认为直线，可选为：'line' | 'shadow' | 'cross' , shadow表示阴影
        lineStyle: {
          type: 'dashed',
        },
      },
      enterable: true,
      padding: [4, 10], //内边距
      formatter: opts.tooltipFormatter,
      extraCssText: 'background: var(--bg-tooltip);opacity: 1;', //添加阴影
      textStyle: {
        fontSize: fontSize(12),
      },
    },
    xAxis: [
      {
        type: 'category',
        data: opts.xAxis,
        axisTick: {
          //y轴刻度线
          show: false,
        },
        axisLabel: {
          interval: 0,
          fontSize: fontSize(12), //更改坐标轴文字大小
          formatter: function (params) {
            let newName = '';
            if (params.length > 3) {
              newName = params.substring(0, 3) + '...';
            } else {
              newName = params;
            }
            return newName;
          },
          clickable: true,
        },
        triggerEvent: true,
      },
    ],
    yAxis: [
      {
        type: 'value',
        name: '得分',
        min: 0,
        axisTick: {
          show: false,
        },
        axisLine: {
          show: false,
        },
        axisLabel: {
          formatter: '{value}',
          fontSize: fontSize(12),
        },
        splitLine: {
          lineStyle: {
            type: 'solid',
            width: fontSize(1),
          },
        },
      },
      {
        type: 'value',
        name: '排名',
        min: 0,
        interval: 1,
        axisTick: {
          show: false,
        },
        axisLine: {
          show: false,
        },
        axisLabel: {
          formatter: '{value}',
          fontSize: fontSize(12),
        },
        splitLine: {
          show: false,
        },
      },
    ],
    series: opts.series,
  };
}
