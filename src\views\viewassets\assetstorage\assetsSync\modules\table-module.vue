<template>
  <div class="table-module auto-fill">
    <div class="table-module-box auto-fill">
      <div class="auto-fill mr-sm">
        <p class="table-module-title font-active-color f-16" v-if="compareMode">
          <i class="icon-font icon-shijian mr-sm"></i>{{ `数据源：${tabsListObject[activeTabs]}资产表` }}
        </p>
        <ui-table
          ref="leftTableRef"
          class="ui-table auto-fill"
          :class="compareMode ? 'noscrolly' : ''"
          reserveSelection
          :table-columns="compareMode ? leftTableColumns : tableColumns"
          :table-data="newBaseTableData"
          :loading="loading"
          :default-store-data="defaultStoreData"
          @storeSelectList="storeSelectList"
        >
          <template #deviceId="{ row }">
            <span :class="row.cellClassName && 'deviceId' in row.cellClassName ? 'color-warning ' : 'base-text-color'">
              {{ row.deviceId }}
            </span>
            <circle-word
              v-if="
                [stateOptionsObject['差异设备'].value, stateOptionsObject['新增设备'].value].includes(row.deviceType)
              "
              :word="stateOptionsValueObject[row.deviceType]?.word"
              :word-color="stateOptionsValueObject[row.deviceType]?.color"
            >
            </circle-word>
          </template>
          <template #action="{ row }">
            <ui-btn-tip
              class="operatbtn"
              v-for="(item, index) in row.operationList"
              :key="index"
              :icon="item.iconName"
              :content="item.name"
              @click.native="item.func(row)"
            ></ui-btn-tip>
          </template>
          <template #checkStatus="{ row }">
            <Tag :color="checkStatusListObject[row.checkStatus]?.color" v-if="row.checkStatus in checkStatusListObject">
              {{ checkStatusListObject[row.checkStatus]?.dataValue }}
            </Tag>
          </template>
          <template #compareStatus="{ row }">
            <span :style="{ color: compareStatusListObject[row.compareStatus]?.color || '' }">
              {{ compareStatusListObject[row.compareStatus]?.dataValue || '' }}
            </span>
          </template>
          <template #storageStatus="{ row }">
            <span :style="{ color: storageStatusListObject[row.storageStatus]?.color }">
              {{ storageStatusListObject[row.storageStatus]?.dataValue }}
            </span>
          </template>
        </ui-table>
      </div>
      <div class="auto-fill ml-sm hasline" v-if="compareMode">
        <p class="table-module-title font-active-color f-16">
          <i class="icon-font icon-shijian mr-sm"></i>数据源：系统设备资产表
        </p>
        <ui-table
          ref="rightTableRef"
          class="ui-table auto-fill"
          :table-columns="rightTableColumns"
          :table-data="compareTableData"
          :loading="loading"
        >
          <template slot="deviceId" slot-scope="{ row }">
            <span :class="font - white">{{ row.deviceId }}</span>
          </template>
        </ui-table>
      </div>
    </div>
    <slot name="pageSlot"></slot>
  </div>
</template>
<script>
import { mapActions, mapGetters } from 'vuex';
import {
  tabsListObject,
  tableColumns,
  leftTableColumns,
  rightTableColumns,
  stateOptionsObject,
  stateOptionsValueObject,
} from '../util/enum.js';
import { handleCheckStatusColor, handleStorageStatusColor, handleCompareStatusColor } from '../util/getDic.js';
export default {
  props: {
    compareMode: {
      default: true,
    },
    baseTableData: {
      default: () => [],
    },
    compareTableData: {
      default: () => [],
    },
    // 重置翻页保存
    retDefaultDataFlag: {
      type: Number,
    },
    // 全选进行操作
    isAllSelect: {
      type: Boolean,
    },
    activeTabs: {},
    loading: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      stateOptionsObject: Object.freeze(stateOptionsObject),
      tabsListObject: Object.freeze(tabsListObject),
      stateOptionsValueObject: Object.freeze(stateOptionsValueObject),
      rightTableColumns: rightTableColumns,
      leftTableColumns: leftTableColumns,
      tableColumns: tableColumns,
      defaultStoreData: [],
    };
  },
  mounted() {
    if (!this.phystatusList.length) this.getAlldicData();
  },
  methods: {
    ...mapActions({
      getAlldicData: 'algorithm/getAlldicData',
    }),
    handLeftScroll() {
      this.$refs.rightTableRef.$refs.table.$refs.body.scrollTop =
        this.$refs.leftTableRef.$refs.table.$refs.body.scrollTop;
      this.$refs.rightTableRef.$refs.table.$refs.body.scrollLeft =
        this.$refs.leftTableRef.$refs.table.$refs.body.scrollLeft;
    },
    handRightScroll() {
      this.$refs.leftTableRef.$refs.table.$refs.body.scrollTop =
        this.$refs.rightTableRef.$refs.table.$refs.body.scrollTop;
      this.$refs.leftTableRef.$refs.table.$refs.body.scrollLeft =
        this.$refs.rightTableRef.$refs.table.$refs.body.scrollLeft;
    },
    storeSelectList(selection) {
      this.defaultStoreData = selection;
      this.$emit('checkTableData', selection);
    },
  },
  computed: {
    ...mapGetters({
      aysc_check_status: 'assets/aysc_check_status',
      aysc_storage_status: 'assets/aysc_storage_status',
      aysc_compare_status: 'assets/aysc_compare_status',
      phystatusList: 'algorithm/propertySearch_phystatus',
    }),
    newBaseTableData: {
      // set(){

      // },
      get() {
        let newBaseTableData = this.$util.common.deepCopy(this.baseTableData);
        if (this.isAllSelect) {
          newBaseTableData.forEach((item) => {
            this.$set(item, '_disabled', true);
            this.$nextTick(() => {
              this.$set(item, '_checked', true);
            });
          });
        }
        return newBaseTableData;
      },
    },
    storageStatusListObject() {
      return handleStorageStatusColor(this.aysc_storage_status);
    },
    compareStatusListObject() {
      return handleCompareStatusColor(this.aysc_compare_status);
    },
    checkStatusListObject() {
      return handleCheckStatusColor(this.aysc_check_status);
    },
  },
  watch: {
    retDefaultDataFlag() {
      this.defaultStoreData = [];
    },
    compareMode(val) {
      if (val) {
        this.$nextTick(() => {
          this.$refs.rightTableRef.$refs.table.$refs.body.addEventListener('scroll', this.handRightScroll);
          this.$refs.leftTableRef.$refs.table.$refs.body.addEventListener('scroll', this.handLeftScroll);
        });
      } else {
        this.$refs.rightTableRef.$refs.table.$refs.body.removeEventListener('scroll', this.handRightScroll);
        this.$refs.leftTableRef.$refs.table.$refs.body.removeEventListener('scroll', this.handLeftScroll);
      }
    },
  },
  beforeDestroy() {
    if (!this.compareMode) return;
  },
  components: {
    UiTable: require('@/components/ui-table.vue').default,
    CircleWord: require('../components/circle-word.vue').default,
  },
};
</script>
<style lang="less" scoped>
.table-module {
  position: relative;
  .table-module-box {
    display: flex;
    flex: 1;
    flex-direction: row;
  }
  .table-module-title {
    height: 40px;
    line-height: 40px;
    padding: 0 20px;
  }
  @{_deep}.noscrolly {
    .ivu-table-overflowY {
      overflow-x: scroll !important;
      overflow-y: hidden !important;
    }
    .ivu-table-overflowX {
      overflow-x: scroll !important;
      overflow-y: hidden !important;
    }
  }
  .hasline {
    &::after {
      position: absolute;
      content: '';
      height: 100%;
      width: 1px;
      left: 760px;
      background: var(--devider-line);
    }
  }
  @{_deep}.operatbtn {
    .icon-font {
      font-size: 16px !important;
      margin-right: 8px;
    }
    .icon-chakanxiangqing {
      color: var(--color-table-btn-default);
    }
    .icon-zichanruku2 {
      color: var(--color-success);
    }
    .icon-chayixiangqing {
      color: var(--color-warning);
    }
    .icon-yichangyuanyin {
      color: var(--color-failed);
    }
    .icon-tongbujilu {
      color: #18afcf;
    }
  }
}
</style>
