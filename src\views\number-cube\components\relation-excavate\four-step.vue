<template>
  <div class="four-step-box">
    <div class="top-tips">注：核对关系配置是否正确</div>
    <div class="step-content">
      <div class="left-box" id="mountNodeFour"></div>
      <div class="right-box">
        <div class="title">
          <div class="title-l">关系详情</div>
        </div>
        <div class="label-box">
          <div class="label">
            关&nbsp;&nbsp;系&nbsp;&nbsp;&nbsp;名&nbsp;&nbsp;称 ：
          </div>
          <Input
            v-model="relationName"
            placeholder="请输入"
            @on-change="relationNameChange"
          ></Input>
          <div class="label" style="margin: 0 5px">颜色</div>
          <ColorPicker
            v-model="relationColor"
            @on-change="relationColorChange"
          />
        </div>
        <div v-if="firstStepData.nodes" class="label-box">
          <div class="label">目标实体类型 ：</div>
          {{ firstStepData.nodes[1].label }}
        </div>
        <div class="title" style="background: #fff">
          <div class="title-l">挖掘规则</div>
          <div class="query-box">
            <div v-if="thirdStepData.startTime">
              【 {{ thirdStepData.startTime }} - {{ thirdStepData.endTime }} 】
            </div>
            <div v-if="thirdStepData.isIntersection">
              【 {{ thirdStepData.isIntersection == 1 ? "交集" : "并集" }} 】
            </div>
          </div>
        </div>
        <div class="rules-box">
          <div
            class="rule-type"
            v-for="(value, key, index) in thirdStepData.dataForms"
            :key="index"
          >
            <div class="rule-name">
              <img src="./img/relation.png" alt="" />
              <div>{{ value.ruleName }}</div>
            </div>
            <div class="label-box label-item">
              <div class="label">时间周期：</div>
              <!-- {{ value.ruleList[0].labelValue | commonFiltering(weekData) }}
                  至
                  {{ value.ruleList[1].labelValue | commonFiltering(weekData) }} -->
              {{ transTimePeriodData(value.ruleList[0].labelValue) }}
              <div class="label label2">次数：</div>
              {{ value.ruleList[1].labelValue | commonFiltering(countData)
              }}{{ value.ruleList[2].labelValue }}
            </div>
            <div
              v-for="(item, index) in value.ruleList.slice(
                3,
                value.ruleList.length
              )"
              :key="index"
            >
              <div
                v-if="item.labelValue && item.labelValue.length > 0"
                class="label-box label-item"
              >
                <div class="label">{{ item.labelName }}：</div>
                <span class="label-value" v-if="item.type == 'selectCommon'"
                  >{{
                    item.labelValue
                      | commonFiltering(transData(item.selectOptionList))
                  }}
                  <span v-if="item.multiple && item.isIntersection">{{
                    item.isIntersection == "11"
                      ? "并集"
                      : item.isIntersection == "12"
                      ? "交集"
                      : ""
                  }}</span>
                </span>
                <span v-else>{{ item.labelValue }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import G6 from "@antv/g6";
import { relationNameExists } from "@/api/number-cube.js";
import { mapGetters } from "vuex";

export default {
  components: {},
  props: {
    firstStepData: {
      type: Object,
      default: () => {},
    },
    thirdStepData: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      graphFour: null,
      graphData: {
        nodes: [],
        edges: [],
      },
      startEdge: [], //页面初始化直接关系
      startNodes: [], //页面初始化实体
      relationName: "",
      relationColor: "#2D8CF0",
      weekData: [
        { dataValue: "周一", dataKey: "1" },
        { dataValue: "周二", dataKey: "2" },
        { dataValue: "周三", dataKey: "3" },
        { dataValue: "周四", dataKey: "4" },
        { dataValue: "周五", dataKey: "5" },
        { dataValue: "周六", dataKey: "6" },
        { dataValue: "周日", dataKey: "7" },
      ],
      timePeriodData: [
        { name: "每天", value: "00" },
        { name: "每个周一至周五", value: "01" },
        { name: "每周末", value: "02" },
        { name: "每周", value: "03" },
        { name: "每月", value: "04" },
        { name: "每年", value: "05" },
        { name: "某天", value: "10" },
        { name: "某个周一至周五", value: "11" },
        { name: "某周末", value: "12" },
        { name: "某周", value: "13" },
        { name: "某月", value: "14" },
        { name: "某年", value: "15" },
      ],
      countData: [
        { dataValue: ">", dataKey: "1" },
        { dataValue: ">=", dataKey: "2" },
        { dataValue: "<", dataKey: "3" },
        { dataValue: "<=", dataKey: "4" },
        { dataValue: "=", dataKey: "5" },
        { dataValue: "!=", dataKey: "6" },
      ],
    };
  },
  computed: {
    ...mapGetters({ userInfo: "userInfo" }),
  },
  mounted() {
    this.graphFour = new G6.Graph({
      container: "mountNodeFour",
      width: 646,
      height: 465,
      fitView: true,
      fitViewPadding: [20, 100, 20, 100],
      modes: {
        default: ["drag-canvas", "zoom-canvas", "'drag-node'"],
      },
    });
  },
  methods: {
    //下拉数据转为字典格式数据
    transData(data) {
      let newData = data.map((item) => {
        return {
          dataKey: item.value,
          dataValue: item.name,
        };
      });
      return newData;
    },
    transTimePeriodData(val) {
      let name = "";
      this.timePeriodData.forEach((item) => {
        if (item.value == val) {
          name = item.name;
        }
      });
      return name;
    },
    init() {
      this.relationName = "";
      this.relationColor = "#2D8CF0";
      //   console.log(this.firstStepData, "firstStepData");
      this.startNodes = this.firstStepData.nodes.map((item) => item);
      this.startEdge = this.firstStepData.edges.map((item) => item);
      this.graphData.edges = [...this.startEdge];
      this.graphData.nodes = [...this.startNodes];
      this.graphData.edges[0].label = "";
      this.graphData.edges[0].labelCfg = {
        refY: 5,
        autoRotate: true,
        style: {
          fill: "#2C86F8",
          fontSize: 14,
        },
      };
      this.graphFour.data(this.graphData);
      this.graphFour.render();
    },
    relationNameChange() {
      let params = {
        graphInstanceId:
          this.firstStepData.nodes[1].targetNodeInfo.graphInstanceId,
        ownerIdentifier: this.userInfo.username,
        relationNameCn: this.relationName,
      };
      if (this.relationName) {
        relationNameExists(params).then((res) => {
          if (!res.data) {
            this.graphData.edges[0].label = this.relationName;
            this.$emit("getFourData", this.graphData, this.thirdStepData, 1);
          } else {
            this.graphData.edges[0].label = "";
            this.$Message.warning("关系名称已经存在！");
            this.$emit("getFourData", this.graphData, this.thirdStepData, 2);
          }
          this.graphData.edges[0].style.stroke = this.relationColor;
          this.graphFour.data(this.graphData);
          this.graphFour.render();
        });
      } else {
        this.graphData.edges[0].label = "";
        this.graphData.edges[0].style.stroke = this.relationColor;
        this.graphFour.data(this.graphData);
        this.graphFour.render();
      }
    },
    relationColorChange() {
      this.graphData.edges[0].style.stroke = this.relationColor;
      this.graphFour.data(this.graphData);
      this.graphFour.render();
      this.$emit("getFourData", this.graphData, this.thirdStepData, 1);
    },
  },
  watch: {
    firstStepData(val) {
      this.init();
    },
    thirdStepData(val) {
      console.log(val, "val");

      //     this.graphData.edges[0].label = this.relationName = "";
      //   this.graphFour.data(this.graphData);
      //   this.graphFour.render();
      this.$emit("getFourData", this.graphData, this.thirdStepData, 1);
    },
    immediate: true,
  },
};
</script>
<style lang="less" scoped>
.four-step-box {
  width: 100%;
  height: 100%;
  .top-tips {
    width: 100%;
    text-align: right;
    font-size: 14px;
    margin-bottom: 5px;
    color: #f29f4c;
  }
  .step-content {
    width: 100%;
    height: calc(~"100% - 10px");
    display: flex;
    background: #ffffff;
    #mountNodeFour {
      width: 60%;
      height: 100%;
      border: 1px solid #d3d7de;
      margin-right: 10px;
    }
    .right-box {
      flex: 1;
      height: 100%;
      border: 1px solid #d3d7de;
      .title {
        padding-left: 10px;
        background: #f9f9f9;
        width: 100%;
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        .title-l {
          color: rgba(0, 0, 0, 0.9);
          font-weight: 700;
        }
        .query-box {
          display: flex;
          margin: 10px 5px 0 0;
        }
      }
      .label-box {
        margin: 10px;
        display: flex;
        align-items: center;
        color: rgba(0, 0, 0, 0.9);
        .label {
          color: rgba(0, 0, 0, 0.4513);
        }
        /deep/ .ivu-input-wrapper {
          width: 49%;
        }
        /deep/ .ivu-color-picker {
          .ivu-input-icon,
          .ivu-icon-ios-close {
            display: none;
          }
          .ivu-color-picker-confirm {
            .ivu-color-picker-confirm-color-editable {
              right: 142px;
            }
            .ivu-input {
              height: 29px;
              width: 85px;
              border-color: #4597ff;
            }
          }
          .ivu-color-picker-color {
            left: 2px;
            top: 8px;
          }
        }
      }
      .rules-box::-webkit-scrollbar {
        width: 0;
      }
      .rules-box {
        width: 100%;
        height: calc(~"100% - 165px");
        overflow: auto;
        padding: 10px;
        .rule-type {
          width: 100%;
          background: #f9f9f9;
          padding: 10px;
          margin-bottom: 10px;
          .rule-name {
            display: flex;
            align-items: center;
            height: 30px;
            color: #3d3d3d;
            font-weight: 700;
            border-bottom: 1px solid #d3d7de;
            img {
              width: 16px;
              height: 15px;
              margin-right: 5px;
            }
          }
          .label-item {
            margin: 10px 0 0 0;
            .label2 {
              margin-left: 20px;
            }
            .label-value {
              display: inline-block;
              width: 280px;
            }
          }
        }
      }
    }
  }
}
</style>
