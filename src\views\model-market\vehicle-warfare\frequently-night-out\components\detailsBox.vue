<!--
    * @FileDescription: 频繁夜出=>列表
 * @LastEditors: liyuhang1 <EMAIL>
 * @LastEditTime: 2024-09-18 18:11:20
 -->
<template>
  <div class="detailsBox">
    <div class="details-list">
      <div class="box-hint">
        <Icon type="ios-undo" @click="handleback" />
        <span @click="handleback">频繁夜出 > 查询结果</span>
      </div>
      <div class="table-box">
        <Scroll
          :on-reach-bottom="handleReachBottom"
          height="100%"
          :loading-text="loadingText"
          :distance-to-edge="10"
        >
          <li
            class="freno-item"
            style="display: flex"
            :class="[{ 'freno-item-hover': currentIndex === index }]"
            v-for="(item, index) in vehicleList"
            :key="index"
            @click.stop="showDetail(item, index)"
          >
            <div class="detail">
              <span class="plate-number vehicle-plate">{{ item.plateNo }}</span>
              <p class="nightOut-count">
                夜出天数: &nbsp;&nbsp;{{ item.dayNum }} 天
              </p>
            </div>
            <div class="operate">
              <i
                class="iconfont icon-huodongguilv"
                title="行车轨迹"
                @click.stop="handleTrack(item)"
              />
              <i
                class="iconfont icon-chepai"
                title="复制车牌"
                @click.stop="handleCopy(item.plateNo)"
              />
            </div>
          </li>
        </Scroll>
        <ui-empty v-if="vehicleList.length == 0 && !loading"></ui-empty>
        <ui-loading v-if="loading"></ui-loading>
      </div>
    </div>
  </div>
</template>

<script>
import {
  getFrequentNightOutList,
  getNightOutFrequency,
} from "@/api/modelMarket";
import { copyText } from "@/util/modules/common";
export default {
  name: "",
  data() {
    return {
      vehicleList: [],
      loading: false,
      detailsParams: {},
      loadingText: "加载中",
      isLast: false,
      currentIndex: -1,
    };
  },
  watch: {},
  computed: {},
  created() {},
  mounted() {},
  methods: {
    handleList(param) {
      this.resetAll();
      this.detailsParams = { ...param, pageSize: 20 };
      this.queryList();
    },
    queryList() {
      this.loading = true;
      getFrequentNightOutList(this.detailsParams)
        .then((res) => {
          let list = res.data ? res.data.list : [];
          this.vehicleList = this.vehicleList.concat(list);
          if (list && list.length) {
            this.detailsParams.afterKey = res.data.afterKey;
          } else {
            this.isLast = true;
          }
        })
        .finally(() => {
          this.loading = false;
        });
    },
    handleReachBottom() {
      this.loadingText = "加载中";
      if (this.isLast) {
        this.loadingText = "已经是最后一页了";
        return;
      }
      return this.queryList();
    },
    handleback() {
      this.$emit("backSearch");
    },
    resetAll() {
      this.currentIndex = -1;
      this.isLast = false;
      this.vehicleList = [];
    },
    showDetail(item, index) {
      this.currentIndex = index;
      let param = { ...this.detailsParams };
      delete param.dayNum;
      delete param.afterKey;
      delete param.pageSize;
      getNightOutFrequency({ ...param, plateNo: item.plateNo }).then((res) => {
        this.$emit("heatMap", res.data || []);
      });
    },
    handleTrack(item) {
      const { href } = this.$router.resolve({
        path: "/model-market/vehicle-warfare/car-path?noMenu=1",
        query: {
          plateNo: item.plateNo,
          startDate: this.$dayjs(item.dayList[0] + " 00:00:00").valueOf(),
          endDate: this.$dayjs(
            item.dayList[item.dayList.length - 1] + " 23:59:59"
          ).valueOf(),
        },
      });
      this.$util.openNewPage(href, "_blank");
    },
    handleCopy(plateNo) {
      copyText(plateNo);
    },
  },
};
</script>

<style lang="less" scoped>
.detailsBox {
  position: absolute;
  top: 10px;
  left: 10px;
  height: 85vh;
  .details-list {
    height: 100%;
    background: #fff;
    box-shadow: 0px 3px 5px 0px rgba(0, 0, 0, 0.3);
    border-radius: 4px;
    width: 370px;
    filter: blur(0px);
    position: relative;
    .box-hint {
      width: 370px;
      height: 40px;
      background: #2c86f8;
      border-bottom: 1px solid #d3d7de;
      color: #fff;
      font-size: 14px;
      line-height: 40px;
      padding-left: 14px;
      display: flex;
      align-items: center;
      .icon-jiantou {
        transform: rotate(90deg);
        display: inline-block;
        cursor: pointer;
      }
      .ivu-icon-ios-undo {
        font-size: 20px;
        cursor: pointer;
      }
      span {
        font-size: 14px;
        cursor: pointer;
        margin-left: 10px;
      }
    }
  }
  .table-box {
    height: calc(~"100% - 40px");
    position: relative;
    /deep/ .ivu-scroll-wrapper {
      height: 100%;
      width: 100%;
      position: initial;
      .ivu-scroll-container {
        overflow: auto;
        height: 100%;
      }
    }
  }
  .freno-item {
    position: relative;
    list-style: none;
    padding-left: 20px;
    padding-right: 20px;
    height: 63px;
    cursor: pointer;
    display: flex;
    align-items: center;
    .operate {
      i {
        color: #2c86f8;
        margin: 0 3px;
      }
    }
    &:hover {
      background-color: #2c86f8;
      color: #fff;
      .nightOut-count {
        color: #fff;
      }
      .record-tools {
        display: block;
      }
      .operate {
        i {
          color: #fff;
        }
      }
    }
    .nightOut-count {
      color: #666666;
    }
    .detail {
      height: 100%;
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: center;
    }
    .plate-number {
      font-size: 14px;
      font-weight: normal;
      display: inline-block;
    }
    .record-tools {
      // position: absolute;
      // right: 5px;
      margin-top: 22px;
      margin-left: 30px;
      display: none;
    }
  }
  .freno-item-hover {
    background-color: #2c86f8;
    color: #fff;
    .nightOut-count {
      color: #fff;
    }
    .record-tools {
      display: block;
    }
  }
}
</style>
