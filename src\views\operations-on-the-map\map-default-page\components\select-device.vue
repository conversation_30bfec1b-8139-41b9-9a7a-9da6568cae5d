<template>
  <ui-modal v-model="modalShow"
            :r-width="dialogData.rWidth"
            :title="dialogData.title"
            list-content
            footer-hide>
    <div class="select-label-container modal-list-has-footer-content">
      <div class="organization"
           v-if="showOrganization">
        <div class="title">组织机构</div>
        <ui-tree :select-tree="selectOrgTree"
                 :custorm-node="true"
                 :custorm-node-data="custormNodeData"
                 @selectedTree="selectedOrgTree"
                 placeholder="请选择组织机构">
        </ui-tree>
      </div>
      <div class="select-label-content">
        <Form inline
              ref="formData"
              :model="formData"
              class="form">
          <FormItem label="设备类型:">
            <Select v-model="formData.deviceType"
                    placeholder="请选择"
                    clearable
                    class="width-input"
                    @on-change="deviceTypeChange">
              <Option v-for="(sItem, sIndex) in statusList"
                      :key="sIndex"
                      :value="sItem.value">{{ sItem.label }}</Option>
            </Select>
          </FormItem>
          <FormItem label="设备名称:"
                    prop="deviceName">
            <Input v-model="formData.deviceName"
                   size="small"
                   placeholder="请输入"
                   class="input-width"></Input>
          </FormItem>
          <FormItem label="设备编码:"
                    prop="deviceId">
            <Input v-model="formData.deviceId"
                   size="small"
                   placeholder="请输入"
                   class="input-width"></Input>
          </FormItem>
          <FormItem label="安装地址:"
                    prop="detailAddress">
            <Input v-model="formData.detailAddress"
                   size="small"
                   placeholder="请输入"
                   class="input-width"></Input>
          </FormItem>
          <FormItem class="float-right">
            <Button class="find"
                    type="primary"
                    @click="init">查询</Button>
            <Button type="default"
                    @click="resetForm">重置</Button>
          </FormItem>
        </Form>
        <Table class="auto-fill table"
               ref="table"
               :height="335"
               :columns="columns"
               :data="tableData"
               @on-select="onSelect"
               @on-select-cancel="onSelectCancel"
               @on-select-all="onSelectAll"
               @on-select-all-cancel="onSelectAllCancel">
          <template slot="deviceId"
                    slot-scope="{ row }">
            <span class="link-btn cursor-p"
                  @click="deviceArchives(row)">{{ row.deviceId }}</span>
          </template>
          <template #labels="{ row }">
            <ui-tag-poptip v-if="row.labels && row.labels.length"
                           :data="row.labels" />
          </template>
        </Table>
        <ui-page :current="pageInfo.pageNumber"
                 :total="pageInfo.total"
                 :page-size="pageInfo.pageSize"
                 @pageChange="pageChange"
                 @pageSizeChange="pageSizeChange">
        </ui-page>
      </div>
      <div class="preview-select-label-content">
        <div class="info-bar">
          <span>已经选：<span>{{ allSelectData.length }}</span> 个</span>
          <span class="del-btn"
                @click="removeAllHandle">
            <span class="iconfont icon-shanchu"></span>清空
          </span>
        </div>
        <div class="label-container">
          <ul>
            <li v-for="(item,index) in selectTableData"
                :key="index">
              <Checkbox v-model="item.select"
                        @on-change="selectChange(item)">{{item.deviceName}}</Checkbox>
            </li>
          </ul>
        </div>
      </div>
    </div>
    <div class="footer">
      <Button class="mr-20" type="default" @click.stop="onCancel">取消</Button>
      <Button type="primary" @click.stop="confirmHandle">确定</Button>
    </div>
  </ui-modal>
</template>
<script>
import { queryDeviceList } from '@/api/wisdom-cloud-search'
import { commonMixins } from '@/mixins/app.js';
let statusList = [
  { label: '摄像机', value: 1, LayerType: 'Camera_Face' }, // 其实也是1，自定义为0
  { label: 'Wi-Fi设备', value: 2, LayerType: 'Camera_Wifi' },
  { label: 'RFID设备', value: 3, LayerType: 'Camera_RFID' },
  { label: '电围设备', value: 4, LayerType: 'Camera_Electric' },
]
let LayerTypeObject = {}
statusList.forEach(item => {
  LayerTypeObject[item.LayerType] = item.value
})
export default {
  components: {
    uiTree: require('@/components/ui-tree.vue').default,
  },
  mixins: [commonMixins], //全局的mixin
  props: {
    checkedLabels: {
      // 已选择的标签
      type: Array,
      default: () => [],
    },
    // 设备类型， 1：摄像机， 2：wifi, 3: RFID, 4: 电围
    deviceType: {
      type: Number,
      default: 1
    },
    // 组织机构显示标识
    showOrganization: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      modalShow: false,
      formData: {
        deviceType: 1,
      },
      dialogData: {
        title: "选择设备",
        rWidth: 1500,
      },
      pageInfo: {
        pageNumber: 1,
        pageSize: 20,
        total: 0
      },
      statusList: Object.freeze(statusList),
      // deviceTypeMap: {
      //   Camera: { name: '高清视频', icon: 'shebeizichan', color: '#187AE4' },
      //   Camera_Vehicle: { name: '车辆卡口', icon: 'qiche', color: '#1faf8a' },
      //   Camera_Face: { name: '人脸卡口', icon: 'yonghu', color: '#48BAFF' },
      //   Camera_RFID: { name: 'RFID设备', icon: 'RFID', color: '#8173FF' },
      //   Camera_Wifi: { name: 'Wi-Fi设备', icon: 'wifi', color: '#914FFF' },
      //   Camera_Electric: { name: '电围设备', icon: 'ZM-dianwei', color: '#614FFF' }
      // },
      columns: [
        { title: '选择', width: 40, type: 'selection', key: 'index' },
        { title: '序号', width: 70, type: 'index', key: 'index' },
        { title: '设备名称', key: 'deviceName' },
        { title: '设备编码', key: 'deviceId' },
        { title: '安装地址', key: 'detailAddress' },
      ],
      tableData: [],
      allSelectData: [],
      selectOrgTree: {
        orgCode: null,
      },
      custormNodeData: {
        label: '未分配组织机构',
        orgCode: '-1',
      },
      outSelectData: []
    };
  },
    computed: {
        selectTableData () {
            return this.allSelectData.filter(item => {
                if(item.deviceType == this.formData.deviceType) {
                    item.select = true
                }
                return item.deviceType == this.formData.deviceType
            })
        }
    },
    reated () {
    },
  methods: {
    init () {
      let params = { ...this.formData, ...this.pageInfo,filter: this.judgeUser, orgCodes: [] }
      queryDeviceList(params).then((res) => {
        const { total, entities } = res.data
        this.pageInfo.total = total
        this.tableData = entities
        this.tableIsSelect()
      }).catch(err => {
        console.error(err)
      }).finally(() => {
      })
    },
    /**
     * table回显
     */
    tableIsSelect () {
      //  需要加延迟，否则数据切换分页，数据是上一次的数据
      setTimeout(() => {
        var obj = this.$refs.table.objData
        // 清空table选中状态
        if (this.allSelectData.length == 0) {
          Object.keys(obj).forEach(key => {
            obj[key]._isChecked = false
          })
          return
        }
        // 回显
        Object.keys(obj).forEach(key => {
          var row = this.allSelectData.find(i => { return obj[key].deviceId == i.deviceId })
          if (row) {
            this.$refs.table.objData[key]._isChecked = true
          }
        })
      }, 20)
    },
    /**
     * 显示model
     */
    show (list = [], keyWords = "") {
      this.modalShow = true;
      // let noRepeatDataObject = {}
      // list.forEach(item => {
      //   item.deviceId in noRepeatDataObject ? null : noRepeatDataObject[item.deviceId] = item
      // })
      this.allSelectData = list
      this.formData.deviceType = this.deviceType
      this.formData.deviceName = keyWords
      this.init()
    },
    deviceTypeChange(type){
      this.formData.deviceType = type
      this.init()
    },
    // 页数改变
    pageChange (size) {
      this.pageInfo.pageNumber = size
      this.init()
    },
    // 页数量改变
    pageSizeChange (size) {
      this.pageInfo.pageNumber = 1
      this.pageInfo.pageSize = size
      this.init()
    },
    /**
     * 右侧清空
     */
    removeAllHandle () {
      this.allSelectData = []
      this.$refs.table.selectAll(false);
      this.$emit("selectData", [])
    },

    /**
     * 确定按钮
     */
    confirmHandle () {
      var ids = []
      this.$emit("selectData", this.allSelectData)
      this.modalShow = false;
      // this.selectTableData.forEach(item => {
      //   ids.push(item.deviceId)
      // })
      // this.$emit("selectData", ids)
    },
    onCancel () {
      this.modalShow = false;
    },
    /**
     * table 选中一项
     */
    onSelect (selection, row) {
      var obj = this.allSelectData.find(item => { return item.deviceId == row.deviceId })
      // console.log(obj)
      row.select = true
      if (!obj) {
        this.allSelectData.push(row)
      } else {
        obj.select = true
      }
    },
    /**
     * table 取消选中一项
     */
    onSelectCancel (selection, row) {
      var num = this.allSelectData.findIndex(item => { return item.deviceId == row.deviceId })
      this.allSelectData.splice(num, 1)
    },
    /**
     * table 全选
     */
    onSelectAll (selection) {
      selection.forEach(item => {
        item.select = true
        var obj = this.allSelectData.find(itm => { return itm.deviceId == item.deviceId })
        if (!obj) {
          this.allSelectData.push(item)
        }
      })
    },

    /**
     * table 取消全选
     */
    onSelectAllCancel (selection) {
      this.tableData.forEach(item => {
        var num = this.allSelectData.findIndex(itm => { return itm.deviceId == item.deviceId })
        if (num != -1) {
          this.allSelectData.splice(num, 1)
        }
      })
    },

    /**
     * 表格右侧 已选中内容操作
     */
    selectChange (row) {
      var obj = this.$refs.table.objData
      if (row.select) { // 选中
        Object.keys(obj).forEach(key => {
          if (obj[key].deviceId == row.deviceId) {
            obj[key]._isChecked = true
          }
        })
      } else { // 取消选中
        Object.keys(obj).forEach(key => {
          if (obj[key].deviceId == row.deviceId) {
            obj[key]._isChecked = false
          }
        })
      }
    },
    /**
     * 选择组织机构树
     */
    selectedOrgTree () {

    },
    /**
     * 重置表单
     */
    resetForm () {
      this.$refs['formData'].resetFields();
      this.init()
    }
  },
};
</script>
<style lang="less" scoped>
.find {
  margin-right: 10px;
}

/deep/ .ivu-input-wrapper {
  width: 100% !important;
}
.select-label-container {
  border: 1px solid #d3d7de;
  border-radius: 4px;
  margin: 0 20px 20px;
  display: flex;
  .select-label-content {
    flex: 1;
    padding: 20px;
    padding-bottom: 0;
    height: 100%;
    display: flex;
    flex-direction: column;
    border-right: 1px solid #d3d7de;
  }
  .label-head {
    height: 20px;
    background: linear-gradient(90deg, rgba(35, 168, 249, 0.2) 0%, rgba(73, 211, 253, 0) 100%);
    color: #2b84e2;
    position: relative;
    padding: 0 20px;
    font-size: 16px;
    margin-bottom: 5px;
    display: flex;
    align-items: center;
    &:before {
      content: '';
      height: 20px;
      width: 4px;
      background: #2b84e2;
      position: absolute;
      top: 0;
      left: 0;
    }
  }
  .label-container {
    overflow: auto;
    height: 400px;
    position: relative;
  }
  .organization {
    width: 200px;
    // padding: 20px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    height: 472px;
    overflow: auto;
    border-right: 1px solid #d3d7de;

    .title {
      height: 40px;
      line-height: 40px;
      font-size: 14px;
      font-weight: 600;
      padding-left: 10px;
      color: rgba(0, 0, 0, 0.8);
      background: #f9f9f9;
    }
  }
  .preview-select-label-content {
    width: 250px;
    padding: 20px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: auto;
    .info-bar {
      color: #515a6e;
      display: flex;
      justify-content: space-between;
      font-size: 14px;
      align-items: center;
      padding-bottom: 10px;
      .icon-shanchu {
        font-size: 14px;
        margin-right: 5px;
      }
    }
    .label-container {
      padding-top: 10px;
    }
  }
}
.footer {
  display: flex;
  justify-content: center;
}
.del-btn {
  cursor: pointer;
}
.mr20 {
  margin-right: 20px !important;
}

.table {
  margin-top: 20px;
}

/deep/ .ivu-icon {
  // color: #fff;
}
.input-width {
  width: 250px;
}
.form {
  width: 100%;
  .search-input {
    display: inline-flex;
    margin-right: 15px;
  }
  /deep/ .ivu-input {
    width: 220px;
  }
  /deep/.ivu-select {
    width: 220px;
    margin-bottom: 10px;
  }
  /deep/.ivu-form-item {
    margin-bottom: 0;
    margin-right: 15px;
  }
  /deep/ .ivu-form-item-label {
    white-space: nowrap;
    width: 72px;
    text-align-last: justify;
    text-align: justify;
    text-justify: distribute-all-lines; // 这行必加，兼容ie浏览器
  }
  /deep/ .ivu-form-item-content {
    display: flex;
  }
  .float-right {
    float: right;
  }
  // .btn-group {
  //   margin-right: 0;
  // }
}
</style>
