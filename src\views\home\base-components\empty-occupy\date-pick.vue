<template>
  <DatePicker
    :open="open"
    :value="year"
    confirm
    :options="options"
    type="year"
    format="yyyy"
    placeholder="请选择年"
    @on-change="handleChange"
    @on-ok="handleOk"
  >
    <i @click="handleClick" class="year-color">{{ year }}年</i>
  </DatePicker>
</template>
<script>
export default {
  name: 'DatePicker',
  props: {},
  components: {},
  watch: {},
  data() {
    return {
      open: false,
      year: `${new Date().getFullYear()}`,
      options: {
        disabledDate: function (date) {
          let now = new Date().getFullYear();
          return date.getFullYear() > now;
        },
      },
    };
  },
  computed: {},
  mounted() {},
  methods: {
    /**
     * 日期选择筛选
     */
    handleClick() {
      this.open = !this.open;
    },
    handleChange(date) {
      this.year = date;
    },
    async handleOk() {
      this.open = false;
      this.$emit('chooseDate', this.year);
    },
  },
};
</script>
<style lang="less" scoped>
@{_deep} .ivu-picker-confirm {
  .ivu-btn-default {
    display: none;
  }
  display: flex;
  justify-content: center;
  align-items: center;
}
.year-color {
  color: #63ccfc;
}
</style>
