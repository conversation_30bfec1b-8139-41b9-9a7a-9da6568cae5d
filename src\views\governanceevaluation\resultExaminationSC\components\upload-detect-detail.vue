<template>
  <div class="detect-detail-container">
    <ui-modal v-model="visible" :title="title" :footerHide="isUpload" :width="1000" class="">
      <div v-ui-loading="{ loading: getFilesLoading }">
        <p v-if="modalType === 'upload'" class="mb-sm pl-lg">
          <i class="icon-font icon-jinggao font-warning f-16"></i>
          <span>【上传附件】说明：支持格式：doc、xlsx、bmp、jpg、png、JPEG、zip、7z；最大支持20M文件或图片上传</span>
        </p>
        <div class="file-list-box" v-scroll="300">
          <div class="file-list-item mt-lg" v-for="(civilItem, index) in civilDataList" :key="index">
            <div class="file-item-title flex-row mb-lg">
              <span class="tilte-name">{{ civilItem.civilName }}</span>
              <Upload
                v-if="isUpload"
                action="/ivdg-device-data-service/governance/importExcel"
                :beforeUpload="
                  (file) => {
                    beforeUpload(file, civilItem);
                    return false;
                  }
                "
                ref="upload"
                :show-upload-list="false"
              >
                <Button>
                  <i class="icon-font icon-shangchuan f-16"></i>
                  <span class="vt-middle ml-sm">上传附件</span>
                </Button>
              </Upload>
            </div>
            <div class="pl-lg">
              <p class="mb-lg file-name f-14" v-for="fileItem in civilItem.fileVoList" :key="fileItem.id">
                <span> {{ fileItem.originalName }} </span>
                <span> {{ Math.ceil(fileItem.size / 1024) }}K </span>
                <Button v-if="isUpload" class="ml-md" type="text" @click="handleDelete(fileItem, civilItem)">
                  <span class="font-warning">删除</span></Button
                >
                <Button v-if="isDownLoad" class="ml-md" type="text" @click="handleDownLoad(fileItem)">下载</Button>
              </p>
            </div>
          </div>
        </div>
      </div>
      <template #footer>
        <Button @click="onCancel()" class="plr-30">取 消</Button>
        <Button
          type="primary"
          @click="handleDownLoadAll()"
          :loading="allDownLoading"
          :disabled="getFilesLoading"
          class="plr-30"
          >一键下载</Button
        >
      </template>
    </ui-modal>
  </div>
</template>
<script>
import equipmentassets from '@/config/api/equipmentassets.js';
import governanceevaluation from '@/config/api/governanceevaluation.js';
import { mapGetters } from 'vuex';

export default {
  name: 'uploadDetectDetail',
  props: {
    value: {
      type: Boolean,
      default: false,
    },
    modalType: {
      type: String,
      default: 'upload',
    },
    taskInfo: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      visible: false,
      title: '上传检测明细',
      getFilesLoading: false,
      allDownLoading: false,
      civilDataList: [
        {
          examTaskId: 1,
          civilCode: '320100',
          civilName: '南京市',
          fileVoList: [
            {
              id: 71,
              type: 'WORK_REPORT_TYPE',
              originalName: 'warning-chart.png',
              filePath: '2024/06/04/56f90ee0e18f4d40985076f03539b578.png',
              fileUrl: 'http://*************:19002/qsdi/2024/06/04/56f90ee0e18f4d40985076f03539b578.png',
              fileCode: '56f90ee0e18f4d40985076f03539b578',
              contentType: 'image/png',
              size: 510,
            },
          ],
        },
      ],
    };
  },
  computed: {
    ...mapGetters({
      userInfo: 'user/getUserInfo',
    }),
    isUpload() {
      return this.modalType === 'upload';
    },
    isDownLoad() {
      return this.modalType === 'download';
    },
  },
  watch: {
    value: {
      handler(val) {
        this.visible = val;
        if (val) {
          this.getFiles();
        }
      },
      immediate: true,
    },
    visible(val) {
      this.$emit('input', val);
    },
  },
  methods: {
    //取消
    onCancel() {
      this.visible = false;
    },
    //获取所有任务
    async getFiles() {
      try {
        this.getFilesLoading = true;
        const param = {
          examTaskId: this.taskInfo.id,
          civilCode: this.taskInfo.regionCode,
        };
        const {
          data: { data },
        } = await this.$http.post(governanceevaluation.examTaskGetExamTaskFile, param);
        this.civilDataList = data;
      } catch (err) {
        console.log(err);
      } finally {
        this.getFilesLoading = false;
      }
    },
    //下载全部
    handleDownLoadAll() {
      this.allDownLoading = true;
      this.$Message.success('下载成功');
      this.allDownLoading = false;
    },
    //上传文件之前,进行校验
    async beforeUpload(file, civilItem) {
      try {
        if (!/\.(doc|xlsx|xls|bmp|jpg|png|JPEG|zip|7z)$/.test(file.name)) {
          this.$Message.error('文件格式错误，请上传“doc、xlsx、bmp、jpg、png、JPEG、zip、7z”结尾的excel文件！');
          return false;
        }
        const isLt20M = file.size / 1024 / 1024 < 20;
        if (!isLt20M) {
          this.$Message.error('上传文件大小不能超过 20MB!');
          return false;
        }
        //获取上传文件的id
        let formParams = new FormData();
        formParams.append('file', file);
        let config = {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        };
        const {
          data: { data },
        } = await this.$http.post(equipmentassets.workReportUpload, formParams, config);
        let originFileIds = civilItem.fileVoList.map((item) => item.id);
        originFileIds.push(data);
        const saveParams = {
          fileIds: originFileIds.join(','), //文件id(多个,拼接)"
          examTaskId: this.taskInfo.id, //考核方案id
          civilCode: civilItem.civilCode, //任务名称
          civilName: civilItem.civilName, //上传文件
        };
        await this.saveFile(saveParams);
        this.$Message.success('上传成功');
        this.getFiles();
      } catch (err) {
        console.log(err);
      }
      return false;
    },
    //保存,上传和删除调用此接口
    async saveFile(saveParams) {
      try {
        const params = {
          ...saveParams,
        };
        await this.$http.post(governanceevaluation.examTaskSaveExamFile, params);
      } catch (e) {
        console.log(e);
      }
    },
    // 删除文件
    async handleDelete(fileItem, civilItem) {
      let isOK = await this.$UiConfirm({
        content: `您确定删除 ${fileItem.originalName} 吗？`,
      });
      if (!isOK) return;
      try {
        if (!fileItem.id || !civilItem.fileVoList.length) {
          return;
        }
        let allFileIds = civilItem.fileVoList.map((item) => item.id);
        const handleIdIndex = allFileIds.findIndex((item) => item === fileItem.id);
        if (handleIdIndex !== -1) {
          allFileIds.splice(handleIdIndex, 1);
        }
        const saveParams = {
          fileIds: allFileIds.join(','), //文件id(多个,拼接)"
          examTaskId: this.taskInfo.id, //考核方案id
          civilCode: civilItem.civilCode,
          civilName: civilItem.civilName,
        };
        await this.saveFile(saveParams);
        this.$Message.success('删除成功');
        this.getFiles();
      } catch (err) {
        console.log(err);
      }
    },
    // 下载文件
    handleDownLoad(fileItem) {
      this.$util.common.transformBlob(fileItem.fileUrl, fileItem.originalName);
    },
  },
};
</script>
<style lang="less" scoped>
.detect-detail-container {
  @{_deep}.ivu-modal-body {
    padding: 30px 100px;
  }
  .file-list-box {
    height: 400px;
    max-height: 650px;
    padding: 0 20px;
    .file-list-item {
      border-bottom: 1px dashed #163a6f;
      &:last-child {
        border-bottom: none;
      }
      .file-item-title {
        padding: 8px 20px;
        border-radius: 4px;
        opacity: 1;
        background: var(--bg-form-item);
        .tilte-name {
          font-size: 18px;
          line-height: 24px;
          letter-spacing: 0px;
          color: var(--color-primary);
        }
      }
      .file-name {
        color: #a9bed9;
      }
    }
  }
}
</style>
