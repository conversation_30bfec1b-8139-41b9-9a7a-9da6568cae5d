<!--
 * @Date: 2025-01-24 15:26:03
 * @LastEditors: liyuhang1 <EMAIL>
 * @LastEditTime: 2025-02-27 10:06:37
 * @FilePath: \icbd-view\src\views\juvenile-target-control\juvenile-alarm-manager\school-out\index.vue
-->
<template>
  <div class="container">
    <searchForm
      ref="searchForm"
      @query="query"
      @reset="reset"
      type="livingAlone"
      :radioList="[]"
    >
      <query ref="slotQuery" />
    </searchForm>
    <!-- 列表 -->
    <div class="list">
      <ui-table
        ref="tableRef"
        :columns="columns"
        :data="tableList"
        :loading="tableLoading"
      >
        <template #photoUrl="{ row }">
          <div
            class="table-image"
            style="width: 65px; height: 80px; margin: auto"
          >
            <ui-image viewer type="people" :src="row.photoUrl" />
          </div>
        </template>
        <template #traitImg="{ row }">
          <div
            class="table-image"
            style="width: 65px; height: 80px; margin: auto"
          >
            <ui-image viewer type="people" :src="row.traitImg" />
          </div>
        </template>
        <template #nativePlace="{ row }">
          <div>{{ row.nativePlace || "--" }}</div>
        </template>
        <template #cardType="{ row }">
          <div>{{ row.cardType | commonFiltering(identityTypeList) }}</div>
        </template>
        <template #national="{ row }">
          <div>{{ row.national | commonFiltering(nationTypeList) }}</div>
        </template>
        <template #idCardNo="{ row }">
          <span class="primary click-point" @click="goArchivesInfo(row)">{{
            row.idCardNo
          }}</span>
        </template>
        <template #noAppearDayNumber="{ row, index }">
          <span
            class="click-point"
            style="color: #ea4a36"
            @click="handleDetailFn(row, index)"
            >{{ row.noAppearDayNumber + "天" }}</span
          >
        </template>
      </ui-table>
    </div>

    <!-- 分页 -->
    <ui-page
      :current="params.pageNumber"
      :total="total"
      countTotal
      :page-size="params.pageSize"
      @pageChange="pageChange"
      @pageSizeChange="pageSizeChange"
    ></ui-page>
    <CaptureDetail
      ref="videoDetail"
      isNoSearch
      :tableList="tableList"
    ></CaptureDetail>
  </div>
</template>

<script>
import { mapActions, mapGetters } from "vuex";
import searchForm from "../../components/search-form.vue";
import query from "./components/query.vue";
import { getElderlyPageList } from "@/api/monographic/community-management.js";
import CaptureDetail from "@/views/juvenile/components/detail/capture-detail.vue";
export default {
  name: "",
  components: { searchForm, query, CaptureDetail },
  props: {
    compareType: {
      type: [String, Number],
      default: () => "",
    },
    radioList: {
      type: Array,
      default: () => [
        { key: 99, value: "全部" },
        { key: 0, value: "未处理" },
        { key: 1, value: "有效" },
        { key: 2, value: "无效" },
      ],
    },
  },
  data() {
    return {
      tableList: [],
      total: 0,
      params: {
        pageNumber: 1,
        pageSize: 20,
      },
      columns: [
        { type: "selection", align: "center", width: 60 },
        { title: "序号", type: "index", width: 80, align: "center" },
        { title: "未出现天数", slot: "noAppearDayNumber", align: "center" },
        { title: "人员照片", slot: "photoUrl", align: "center", width: 100 },
        { title: "姓名", key: "name", align: "center", width: 100 },
        { title: "年龄", key: "age", align: "center", width: 80 },
        { title: "住址", key: "communityName", align: "center" },
        { title: "证件号码", slot: "idCardNo", align: "center" },
        { title: "民族", slot: "national", align: "center", width: 80 },
        { title: "最后一次抓拍照片", slot: "traitImg", align: "center" },
        { title: "最后一次出现时间", key: "absTime", align: "center" },
        {
          title: "最后一次出现地点",
          key: "deviceName",
          align: "center",
          render: (h, { row }) => {
            return h("div", { class: "primary" }, row.deviceName || "--");
          },
        },
        { title: "报警时间", key: "alarmTime", align: "center" },
      ],
      tableLoading: false,
      taskList: [],
    };
  },
  computed: {
    ...mapGetters({
      identityTypeList: "dictionary/getIdentityTypeList", // 证件类型
      nationTypeList: "dictionary/getNationTypeList", //民族类型
    }),
  },
  async created() {
    await this.getDictData();
  },
  mounted() {
    this.query();
  },
  methods: {
    ...mapActions({
      getDictData: "dictionary/getDictAllData",
    }),
    /**
     * @description: 获取报警列表
     */
    tableListFn() {
      this.tableLoading = true;
      let data = {
        ...this.params,
        ...this.$refs.searchForm.getQueryParams(),
        ...this.$refs.slotQuery.getQueryParams(),
      };
      getElderlyPageList(data)
        .then((res) => {
          this.total = res.data?.total || 0;
          this.tableList =
            res?.data?.entities?.map((item) => ({
              ...item.faceCaptureVo,
              ...item,
            })) || [];
        })
        .finally(() => {
          this.tableLoading = false;
        });
    },
    /**
     * @description: 手动触发查询
     */
    query() {
      this.params = {
        pageNumber: 1,
        pageSize: 20,
      };
      this.tableListFn();
    },

    /**
     * @description: 重置，由searchForm组件手动点击触发的重置，不需要调用this.$refs.searchFormRef.reset()
     */
    reset() {
      this.$refs.slotQuery.reset();
      this.query();
    },
    pageChange(pageNumber) {
      this.params.pageNumber = pageNumber;
      this.tableListFn();
    },

    /**
     * @description: 改变每页数量
     * @param {number} size 每页数量
     */
    pageSizeChange(size) {
      this.params.pageSize = size;
      this.params.pageNumber = 1;
      this.tableListFn();
    },
    // 详情
    handleDetailFn(item, index) {
      this.selectIdCard = item.idCardNo;
      this.$refs.videoDetail.showList(index);
    },
    // 跳转档案
    goArchivesInfo(item) {
      const { href } = this.$router.resolve({
        path: "/community-archive/people-dashboard",
        query: {
          archiveNo: item.idCardNo,
          source: "people",
          initialArchiveNo: item.idCardNo,
        },
      });
      window.open(href, "_blank");
    },
  },
};
</script>

<style lang="less" scoped>
.container {
  display: flex;
  flex-direction: column;
  overflow: hidden;
  .list {
    flex: 1;
    padding-top: 16px;
    overflow: scroll;
    /deep/ .ui-table {
      height: 100%;
    }
  }
}
.primary {
  color: #2c86f8;
}
.click-point {
  cursor: pointer;
}
</style>
