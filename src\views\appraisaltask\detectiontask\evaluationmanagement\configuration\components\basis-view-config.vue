<template>
  <div>
    <!-- 视图基础数据指标配置 -->
    <!--
  BASIC_INPUT === 建档率
  BASIC_FULL_DIR === 全量目录完整率
  BASIC_QUANTITY_STANDARD ===  数量达标率
  BASIC_EMPHASIS_LOCATION === 视频图像设备位置类型完整率
  BASIC_EMPHASIS_QUANTITY === 重点位置类型视频图像设备数量达标率
    -->
    <Form ref="modalData" :model="formData" :rules="ruleCustom" class="form-content" :label-width="160">
      <common-form
        ref="commonForm"
        :label-width="160"
        :form-data="formData"
        :form-model="formModel"
        :module-action="moduleAction"
        :task-index-config="taskIndexConfig"
        @updateFormData="updateFormData"
      >
      </common-form>
      <div v-if="['BASIC_INPUT'].includes(moduleAction.indexType)">
        <FormItem label="考核类型" v-if="['BASIC_INPUT'].includes(moduleAction.indexType)">
          <RadioGroup v-model="formData.checkType">
            <!-- <Radio label="superior">
              <span>我是上级，对下考核</span>
            </Radio> -->
            <Radio label="subordinate">
              <span>我是下级，上报考核</span>
            </Radio>
          </RadioGroup>
        </FormItem>
        <FormItem label="上级建档设备数量" v-if="['BASIC_INPUT'].includes(moduleAction.indexType)">
          <RadioGroup v-model="formData.typeSource">
            <Radio label="organization">
              <span>自定义输入</span>
            </Radio>
            <Radio label="superior">
              <span>上报成功设备为上级建档设备</span>
            </Radio>
          </RadioGroup>
        </FormItem>
        <FormItem label="" v-if="formData.typeSource === 'organization'">
          <Button type="dashed" @click="setBasicInputNum">
            查看上级建档设备数量 <span>{{ `已选择 ${(formData.data || 0) && formData.data.length}个` }} </span>
          </Button>
        </FormItem>
      </div>
      <!-- <FormItem label="需上报目录" v-if="['BASIC_FULL_DIR'].includes(moduleAction.indexType)">
        <Button type="dashed" class="area-btn" @click="orgSelectVisible = true"
          >请选择需上报的组织机构目录
          <span>{{ `已选择 ${(formData.orgData || 0) && formData.orgData.length}个` }}</span>
        </Button>
      </FormItem> -->
      <FormItem
        label="需上报目录"
        class="right-item mb-sm"
        prop="name"
        v-if="['BASIC_FULL_DIR'].includes(moduleAction.indexType)"
      >
        <Button type="dashed" class="area-btn" :disabled="isView" @click="orgSelectVisible = true"
          >请选择需上报的组织机构目录
          <span v-if="formData.orgData">{{ `已选择 ${formData.orgData.length}个` }}</span>
        </Button>
      </FormItem>
      <!-- 数量达标率 重点位置类型视频图像设备数量达标率 -->
      <FormItem
        label="达标数量设置"
        v-if="['BASIC_QUANTITY_STANDARD', 'BASIC_EMPHASIS_QUANTITY'].includes(moduleAction.indexType)"
      >
        <Button type="dashed" class="area-btn" @click="regionalizationSelectVisible = true">
          达标数量设置
          <span>{{ `已选择 ${(formData.quantityData || 0) && formData.quantityData.length}个` }}</span>
        </Button>
      </FormItem>
      <!--视频图像设备位置类型完整率 重点位置类型视频图像设备数量达标率  -->
      <FormItem
        label="摄像机位置类型"
        v-if="['BASIC_EMPHASIS_LOCATION', 'BASIC_EMPHASIS_QUANTITY'].includes(moduleAction.indexType)"
      >
        <RadioGroup class="mb-sm" v-model="formData.typeSource" @on-change="setDefaultEmphasisData">
          <Radio label="deviceTag">设备标签</Radio>
          <Radio label="deviceGather">采集区域字典</Radio>
        </RadioGroup>
        <div class="params-content mb-sm modal-video-image" v-if="formData.typeSource === 'deviceTag'">
          <ui-tag
            v-for="(item, index) in formData.emphasisData"
            :closeable="item.source != 1"
            :key="index + '-a' + item.key"
            @close="handleClose(formData.emphasisData, item, index)"
          >
            {{ item.value }}
          </ui-tag>
          <Button type="primary" @click="clickAdd('设备标签')">
            <i class="icon-font icon-tianjia f-12 mr-sm vt-middle" title="新增"></i>
            <span class="vt-middle">新增</span>
          </Button>
        </div>
        <div class="area" v-else-if="formData.typeSource === 'deviceGather'">
          <Button type="dashed" class="area-btn" @click="clickArea">
            请选择采集区域类型
            <span>{{ `已选择 ${(formData.emphasisData || 0) && formData.emphasisData.length}个` }}</span>
          </Button>
        </div>
      </FormItem>
    </Form>
    <basic-input-select
      v-model="basicInputSelectVisible"
      v-if="basicInputSelectVisible"
      :data="formData.data"
      @query="query"
    >
    </basic-input-select>
    <org-select
      v-model="orgSelectVisible"
      v-if="orgSelectVisible"
      :edit="false"
      :data="formData.orgData"
      @query="orgQuery"
    ></org-select>
    <regionalization-select
      v-model="regionalizationSelectVisible"
      v-if="regionalizationSelectVisible"
      :data="formData.quantityData"
      @query="quantityQuery"
    ></regionalization-select>
    <customize-filter
      v-model="customSearch"
      :customize-action="customizeAction"
      :content-style="contentStyle"
      :checkbox-list="allDeviceFileList"
      :default-checked-list="defaultCheckedList"
      :field-name="fieldName"
      @confirmFilter="confirmFilter"
    >
    </customize-filter>
    <area-select
      v-model="areaSelectModalVisible"
      :checked-tree-data-list="checkedTreeData"
      @confirm="confirmArea"
    ></area-select>
  </div>
</template>

<script>
import {
  defaultEmphasisData,
  defaultGeneralData,
} from '@/views/appraisaltask/detectiontask/evaluationmanagement/configuration/static-field.js';
import governanceevaluation from '@/config/api/governanceevaluation';
import taganalysis from '@/config/api/taganalysis';
export default {
  props: {
    indexType: {
      type: String,
      default: '',
    },
    moduleAction: {
      type: Object,
      default: () => {},
    },
    // 配置回显详情
    configInfo: {
      required: true,
      type: Object,
      default: () => {},
    },
    //标识 add-新增/edit-编辑
    formModel: {
      required: true,
      type: String,
      default: 'add',
    },
    // 配置详情
    taskIndexConfig: {
      type: Object,
      default: () => ({}),
    },
  },

  data() {
    return {
      visible: false,
      ruleCustom: {},
      detect: null,
      formData: {
        checkType: 'subordinate',
        ruleList: [],
        orgData: [],
        data: [],
        typeSource: 'deviceTag',
      },
      indexRuleList: [],
      basicInputSelectVisible: false,
      orgSelectVisible: false,
      regionalizationSelectVisible: false,
      customSearch: false,
      areaSelectModalVisible: false,
      checkedTreeData: [],
      defaultCheckedList: [],
      customizeAction: {
        title: '新增分析数据字段',
        leftContent: '所有分析数据字段',
        rightContent: '已选择分析数据字段',
        moduleStyle: {
          width: '80%',
        },
      },
      allDeviceFileList: [],
      fieldName: {
        id: 'tagId',
        value: 'tagName',
      },
      contentStyle: {
        height: '3.125rem',
      },
      list: [],
    };
  },
  async created() {},
  mounted() {},
  methods: {
    //设置默认值
    setDefaultEmphasisData(val) {
      if (val === 'deviceGather') {
        if (
          this.moduleAction.indexType === 'BASIC_EMPHASIS_QUANTITY' &&
          (!this.formData.emphasisData || this.formData.emphasisData.length === 0)
        ) {
          this.formData.emphasisData = defaultEmphasisData;
        }
        if (
          this.moduleAction.indexType === 'BASIC_EMPHASIS_LOCATION' &&
          (!this.formData.emphasisData || this.formData.emphasisData.length === 0)
        ) {
          this.formData.emphasisData = defaultGeneralData;
        }
      } else {
        this.formData.emphasisData = [];
      }
    },
    confirmArea(data, dataWithName) {
      this.formData.emphasisData = dataWithName;
    },
    confirmFilter(data) {
      this.customSearch = false;
      let list = [];
      data.map((item) => {
        list.push({
          key: item.tagId,
          value: item.tagName,
        });
      });
      this.formData.emphasisData = list;
    },
    handleClose(data, item, index) {
      this.$Modal.confirm({
        title: '警告',
        content: `您要删除${item.value}，是否确认?`,
        onOk: () => {
          data.splice(index, 1);
        },
      });
    },
    async clickAdd(type) {
      this.defaultCheckedList = [];
      try {
        this.defaultCheckedList = (this.formData.emphasisData || []).map((item) => item.key);
        this.customizeAction = {
          title: `新增${type}`,
          leftContent: `所有${type}`,
          rightContent: `已选择${type}`,
          moduleStyle: {
            width: '80%',
          },
        };
        this.customSearch = true;
        let params = {
          tagType: '2',
          isPage: false,
        };
        let {
          data: { data },
        } = await this.$http.post(taganalysis.getDeviceTag, params);
        this.allDeviceFileList = data;
      } catch (error) {
        console.log(error);
      }
    },
    clickArea() {
      try {
        this.areaSelectModalVisible = true;
        let data = this.formData.emphasisData || [];
        this.checkedTreeData = data.map((item) => item.key);
      } catch (e) {
        console.log(e);
      }
    },
    setBasicInputNum() {
      this.basicInputSelectVisible = true;
    },
    query(data) {
      this.formData.data = data;
    },
    quantityQuery(data) {
      this.formData.quantityData = data;
    },
    orgQuery(data) {
      this.formData.orgData = data;
    },

    validateForm() {
      let flag = false;
      if (!this.formData.ruleList || !this.formData.ruleList.length) flag = true;
      this.formData.ruleList &&
        this.formData.ruleList.map((item) => {
          if (item.isConfigure == 1) flag = true;
        });
      return flag;
    },

    async getCheckRule() {
      try {
        let res = await this.$http.get(governanceevaluation.getCheckRuleByIndexType, {
          params: { indexType: this.moduleAction.indexType },
        });
        let data = res.data.data.map((item) => {
          return {
            ruleId: item.id,
            isConfigure: item.status,
            ruleName: item.ruleName,
            ruleDesc: item.ruleDesc,
            ruleCode: item.ruleCode,
          };
        });
        this.formData.ruleList = data;
      } catch (err) {
        console.log(err);
      }
    },
    // 表单提交校验
    handleSubmit() {
      if (!this.validateForm()) {
        this.$Message.error('请选择检测规则');
        return;
      }
      return this.$refs['commonForm'].handleSubmit();
    },
    // 参数提交
    updateFormData(val) {
      this.formData = {
        ...val,
      };
    },
  },

  watch: {
    indexType: {
      handler() {
        const { regionCode } = this.moduleAction;
        this.formData = {
          ...this.formData,
          regionCode, // 检测对象
        };
      },
      immediate: true,
    },
    formModel: {
      handler(val) {
        if (val === 'edit') {
          this.formData = {
            ...this.configInfo,
          };
        } else {
          this.getCheckRule();
          const { regionCode } = this.moduleAction;
          this.formData = {
            ...this.formData,
            regionCode: regionCode, // 检测对象
            detectMode: '1',
            deviceQueryForm: {
              detectPhyStatus: '0',
            },
          };
        }
      },
      immediate: true,
    },
  },
  computed: {
    isView() {
      return this.formModel === 'view';
    },
  },
  components: {
    CommonForm: require('./common-form/index.vue').default,
    BasicInputSelect: require('@/views/appraisaltask/indexmanagement/components/basic-input-select').default,
    OrgSelect: require('@/views/appraisaltask/indexmanagement/components/org-select').default,
    regionalizationSelect: require('@/views/appraisaltask/indexmanagement/components/regionalization-select').default,
    UiTag: require('@/components/ui-tag').default,
    AreaSelect: require('@/components/area-select').default,
    CustomizeFilter: require('@/components/customize-filter').default,
  },
};
</script>
<style lang="less" scoped>
.setting {
  color: var(--color-primary);
}
.form-content {
  /deep/.width-input {
    width: 468px;
    .ivu-input-group-append {
      background-color: #02162b;
      border: 1px solid #10457e;
      border-left: none;
    }
  }
  /deep/.ivu-form-item {
    .ivu-form-item-content {
      .ivu-checkbox-group {
        display: flex;
        // align-items: center;
        flex-direction: column;
        .ivu-checkbox-wrapper {
          display: flex;
          align-items: center;
          > span {
            margin-right: 10px;
          }
        }
      }
    }
  }
}
</style>
