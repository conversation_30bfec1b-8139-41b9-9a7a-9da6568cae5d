// import CryptoJS from 'crypto-js'
// import iconsList from '@/assets/configureIcons/iconfont.json'
import { Message } from "view-design";
import dayjs from "dayjs";
import mapImages from "@/map/core/enum/map-image.js";
import { getToken } from "@/libs/configuration/util.common";
/**
 * 深拷贝, 可以提取公共方法
 */
// 返回传递给他的任意对象的类
export function isClass(o) {
  if (o === null) return "Null";
  if (o === undefined) return "Undefined";
  return Object.prototype.toString.call(o).slice(8, -1);
}

export function deepCopy(obj) {
  if (!obj) {
    return null;
  }
  let result,
    oClass = isClass(obj);
  // 确定result的类型
  if (oClass === "Object") {
    result = {};
  } else if (oClass === "Array") {
    result = [];
  } else {
    return obj;
  }

  for (const key in obj) {
    if (obj.hasOwnProperty(key)) {
      const copy = obj[key];
      if (isClass(copy) === "Object") {
        result[key] = deepCopy(copy); // 递归调用
      } else if (isClass(copy) === "Array") {
        result[key] = deepCopy(copy);
      } else {
        result[key] = obj[key];
      }
    }
  }
  return result;
}
// 时间戳转换成天年月日
export function timeFn(dateDiff) {
  const dayDiff = Math.floor(dateDiff / (24 * 3600 * 1000)); // 计算出相差天数
  const leave1 = dateDiff % (24 * 3600 * 1000); // 计算天数后剩余的毫秒数
  const hours = Math.floor(leave1 / (3600 * 1000)); // 计算出小时数
  // 计算相差分钟数
  const leave2 = leave1 % (3600 * 1000); // 计算小时数后剩余的毫秒数
  const minutes = Math.floor(leave2 / (60 * 1000)); // 计算相差分钟数
  // 计算相差秒数
  const leave3 = leave2 % (60 * 1000); // 计算分钟数后剩余的毫秒数
  const seconds = Math.round(leave3 / 1000);
  const TypeEnum = {
    dayDiff: "天",
    hours: "小时",
    minutes: "分",
    seconds: "秒",
  };
  let oneString = "";
  Object.keys(TypeEnum).forEach((item) => {
    if (eval(item) !== 0) {
      oneString = oneString + `${eval(item) + TypeEnum[item]}`;
    }
  });
  return oneString;
}
// 时间戳转化为年月日时分秒
export function dateTime(timestamp = "") {
  let date = new Date();
  if (timestamp) {
    date = new Date(timestamp); //时间戳为10位需*1000，时间戳为13位的话不需乘1000
  }
  var Y = date.getFullYear() + "-";
  var M =
    (date.getMonth() + 1 < 10
      ? "0" + (date.getMonth() + 1)
      : date.getMonth() + 1) + "-";
  var D =
    date.getDate() < 10 ? "0" + date.getDate() + " " : date.getDate() + " ";
  var h =
    date.getHours() < 10 ? "0" + date.getHours() + ":" : date.getHours() + ":";
  var m =
    date.getMinutes() < 10
      ? "0" + date.getMinutes() + ":"
      : date.getMinutes() + ":";
  var s = date.getSeconds() < 10 ? "0" + date.getSeconds() : date.getSeconds();
  return Y + M + D + h + m + s;
}
/*
 *将Date/String类型,解析为String类型.
 *传入String类型,则先解析为Date类型
 *不正确的Date,返回 ''
 *如果时间部分为0,则忽略,只返回日期部分.
 *日期格式对应字符如下(年-yyyy,月-MM,日-dd,时-hh,分-mm,秒-ss,毫秒-S 字符区分大小写)
 */
export function formatDate(v, format) {
  if (!format) {
    format = "yyyy-MM-dd hh:mm:ss";
  }
  if (typeof v === "string") v = this.parseDate(v);
  if (!(v instanceof Date)) {
    return "";
  }
  var o = {
    "M+": v.getMonth() + 1, // month
    "d+": v.getDate(), // day
    "h+": v.getHours(), // hour
    "m+": v.getMinutes(), // minute
    "s+": v.getSeconds(), // second
    "q+": Math.floor((v.getMonth() + 3) / 3), // quarter
    // "S" : v.getMilliseconds() //millisecond
  };

  if (/(y+)/.test(format)) {
    format = format.replace(
      RegExp.$1,
      (v.getFullYear() + "").substr(4 - RegExp.$1.length)
    );
  }

  if (/(S+)/.test(format)) {
    format = format.replace(
      RegExp.$1,
      ("000" + v.getMilliseconds()).substr(("" + v.getMilliseconds()).length)
    );
  }

  for (var k in o) {
    if (new RegExp("(" + k + ")").test(format)) {
      format = format.replace(
        RegExp.$1,
        RegExp.$1.length == 1 ? o[k] : ("00" + o[k]).substr(("" + o[k]).length)
      );
    }
  }
  return format;
}
/**
 * 获取当前时间的前后N天日期
 * @param {*} num 获取前N天的数据，2：两天后， -2 两天前
 */
export function getConfigDate(num) {
  var today = new Date();
  var targetday_milliseconds = today.getTime() + 1000 * 60 * 60 * 24 * num;
  var targetday = new Date();
  targetday.setTime(targetday_milliseconds); //注意，这行是关键代码

  var cur_year = today.getFullYear();
  var cur_month = today.getMonth() + 1;
  var cur_day = today.getDate();
  cur_month = (cur_month < 10 ? "0" : "") + cur_month;
  cur_day = (cur_day < 10 ? "0" : "") + cur_day;

  var year = targetday.getFullYear();
  var month = targetday.getMonth() + 1;
  var day = targetday.getDate();
  month = (month < 10 ? "0" : "") + month;
  day = (day < 10 ? "0" : "") + day;
  return [
    year + "-" + month + "-" + day,
    cur_year + "-" + cur_month + "-" + cur_day,
  ];
}
/*
     将String类型解析为Date类型.
     parseDate('2006-1') return new Date(2006,0)
     parseDate(' 2006-1 ') return new Date(2006,0)
     parseDate('2006-1-1') return new Date(2006,0,1)
     parseDate(' 2006-1-1 ') return new Date(2006,0,1)
     parseDate('2006-1-1 15:14:16') return new Date(2006,0,1,15,14,16)
     parseDate(' 2006-1-1 15:14:16 ') return new Date(2006,0,1,15,14,16);
     parseDate('2006-1-1 15:14:16.254') return new Date(2006,0,1,15,14,16,254)
     parseDate(' 2006-1-1 15:14:16.254 ') return new Date(2006,0,1,15,14,16,254)
     parseDate('不正确的格式') retrun null
     */
export function parseDate(str) {
  if (typeof str === "string") {
    var results = str.match(/^ *(\d{4})-(\d{1,2})-(\d{1,2}) *$/);
    if (!results && str.match(/^ *(\d{4})-(\d{1,2}) *$/)) {
      results = str.match(/^ *(\d{4})-(\d{1,2}) *$/);
      return new Date(parseInt(results[1], 10), parseInt(results[2], 10) - 1);
    }
    if (results && results.length > 3) {
      return new Date(
        parseInt(results[1], 10),
        parseInt(results[2], 10) - 1,
        parseInt(results[3], 10)
      );
    }
    results = str.match(
      /^ *(\d{4})-(\d{1,2})-(\d{1,2}) +(\d{1,2}):(\d{1,2}):(\d{1,2}) *$/
    );
    if (results && results.length > 6) {
      return new Date(
        parseInt(results[1], 10),
        parseInt(results[2], 10) - 1,
        parseInt(results[3], 10),
        parseInt(results[4], 10),
        parseInt(results[5], 10),
        parseInt(results[6], 10)
      );
    }
    results = str.match(
      /^ *(\d{4})-(\d{1,2})-(\d{1,2}) +(\d{1,2}):(\d{1,2}):(\d{1,2})\.(\d{1,5}) *$/
    );
    if (results && results.length > 7) {
      return new Date(
        parseInt(results[1], 10),
        parseInt(results[2], 10) - 1,
        parseInt(results[3], 10),
        parseInt(results[4], 10),
        parseInt(results[5], 10),
        parseInt(results[6], 10),
        parseInt(results[7], 10)
      );
    }
  }
  return null;
}

// 时间快速选择
/*
    obj:选择的时间
*/
export function quickDate(obj) {
  obj.timePickerShow = false;
  let now = new Date(),
    year = now.getFullYear(),
    mon = now.getMonth() + 1,
    day = now.getDate(),
    hours = now.getHours(),
    min = now.getMinutes(),
    sec = now.getSeconds();
  switch (obj.value) {
    case "defined":
      obj.timePickerShow = true;
      break;
    case "all":
      obj.startTime = "";
      obj.endTime = "";
      break;
    case "day":
      let nowY = new Date().getTime();
      nowY = nowY - 86400000; // 86400000是一天的毫秒数
      let nowYe = new Date(nowY),
        yearY = nowYe.getFullYear(),
        monY = nowYe.getMonth() + 1,
        dayY = nowYe.getDate();
      obj.startTime = this.formatDate(
        `${yearY}-${monY}-${dayY} ${hours}:${min}:${sec}`
      );
      obj.endTime = this.formatDate(
        `${year}-${mon}-${day} ${hours}:${min}:${sec}`
      );
      break;
    case "today":
      obj.startTime = this.formatDate(`${year}-${mon}-${day} 00:00:00`);
      obj.endTime = this.formatDate(
        `${year}-${mon}-${day} ${hours}:${min}:${sec}`
      );
      break;
    case "week":
      let nowD = new Date().getTime();
      nowD = nowD - 86400000 * 7; // 86400000是一天的毫秒数
      let nowDe = new Date(nowD),
        yearD = nowDe.getFullYear(),
        monD = nowDe.getMonth() + 1,
        dayD = nowDe.getDate();
      obj.startTime = this.formatDate(`${yearD}-${monD}-${dayD} 00:00:00`);
      obj.endTime = this.formatDate(
        `${year}-${mon}-${day} ${hours}:${min}:${sec}`
      );
      break;
    case "month":
      let nowM = new Date().getTime();
      nowM = nowM - 86400000 * 30; // 86400000是一天的毫秒数
      let nowMe = new Date(nowM),
        yearM = nowMe.getFullYear(),
        monM = nowMe.getMonth() + 1,
        dayM = nowMe.getDate();
      obj.startTime = this.formatDate(`${yearM}-${monM}-${dayM} 00:00:00`);
      obj.endTime = this.formatDate(
        `${year}-${mon}-${day} ${hours}:${min}:${sec}`
      );
      break;
    case "halfYear":
      obj.endTime = this.formatDate(
        `${year}-${mon}-${day} ${hours}:${min}:${sec}`
      );
      if (mon > 6) {
        mon -= 6;
      } else {
        year -= 1;
        mon = 12 + mon - 6;
      }
      obj.startTime = this.formatDate(`${year}-${mon}-${day} 00:00:00`);
      break;
    default:
      this.initSearchTime(obj);
      break;
  }
}

// 初始化检索时间alarm & retrieval
export function initSearchTime(obj) {
  let now = new Date(),
    year = now.getFullYear(),
    mon = now.getMonth() + 1,
    day = now.getDate(),
    hours = now.getHours(),
    min = now.getMinutes(),
    sec = now.getSeconds();
  if (mon == 1) {
    mon = 12;
    year = year - 1;
  }
  obj.startTime = this.formatDate(`${year}-${mon}-${day} 00:00:00`);
  obj.endTime = this.formatDate(`${year}-${mon}-${day} ${hours}:${min}:${sec}`);
}
/**
 * 判断年份是否为润年
 *
 * @param {Number} year
 */
function isLeapYear(year) {
  return year % 400 == 0 || (year % 4 == 0 && year % 100 != 0);
}
/**
 * 获取某一年份的某一月份的天数
 *
 * @param {Number} year
 * @param {Number} month
 */
function getMonthDays(year, month) {
  return (
    [31, null, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31][month] ||
    (isLeapYear(year) ? 29 : 28)
  );
}
/**
 * 获取某年的某天是第几周
 * @param {Number} y
 * @param {Number} m
 * @param {Number} d
 * @returns {Number}
 */
export function getWeekNumber(date) {
  var now = new Date(date),
    year = now.getFullYear(),
    month = now.getMonth(),
    days = now.getDate();
  // 那一天是那一年中的第多少天
  for (var i = 0; i < month; i++) {
    days += getMonthDays(year, i);
  }

  // 那一年第一天是星期几
  var yearFirstDay = new Date(year, 0, 1).getDay() || 7;

  var week = null;
  if (yearFirstDay == 1) {
    week = Math.ceil(days / yearFirstDay);
  } else {
    days -= 7 - yearFirstDay + 1;
    week = Math.ceil(days / 7) + 1;
  }

  return week;
}
/**
 * 获取时间的前后5秒
 * @param {Number} y
 * @param {Number} m
 * @param {Number} d
 * @returns {Number}
 */
export function getFrontBackDate(dateStr, num, index) {
  //dateStr格式为yyyy-mm-dd hh:mm:ss
  var dt = new Date(dateStr.replace(/-/, "/")); //将传入的日期格式的字符串转换为date对象 兼容ie
  // var dt=new Date(dateStr);//将传入的日期格式的字符串转换为date对象 非ie
  var ndt =
    index == 0 ? new Date(dt.getTime() + num) : new Date(dt.getTime() - num); //将转换之后的时间减去两秒
  var Y = ndt.getFullYear() + "-";
  var M =
    (ndt.getMonth() + 1 < 10
      ? "0" + (ndt.getMonth() + 1)
      : ndt.getMonth() + 1) + "-";
  var D = ndt.getDate() < 10 ? "0" + ndt.getDate() + " " : ndt.getDate() + " ";
  var h =
    ndt.getHours() < 10 ? "0" + ndt.getHours() + ":" : ndt.getHours() + ":";
  var m =
    ndt.getMinutes() < 10
      ? "0" + ndt.getMinutes() + ":"
      : ndt.getMinutes() + ":";
  var s = ndt.getSeconds() < 10 ? "0" + ndt.getSeconds() : ndt.getSeconds();
  return Y + M + D + h + m + s;
}
/**
 * 获取两个日期相隔天数
 * @param {Number} startTime
 * @param {Number} endTime
 */
export function getDaysBetween(startTime, endTime) {
  const sDate = Date.parse(startTime);
  const eDate = Date.parse(endTime);
  if (sDate > eDate) {
    return 0;
  }
  if (sDate === eDate) {
    return 1;
  } else {
    return (eDate - sDate) / (1 * 24 * 60 * 60 * 1000);
  }
}

/**
 * 仿照jquery.extend方法
 * 使用方式  extend({}, {}); 深拷贝 extend(true, {}, {});
 * @returns {any|{}}
 */
export function extend(__params1, __params2, __params3, __params4) {
  var options,
    name,
    src,
    copy,
    copyIsArray,
    clone,
    target = arguments[0] || {},
    i = 1,
    length = arguments.length,
    deep = false,
    toString = Object.prototype.toString,
    hasOwn = Object.prototype.hasOwnProperty,
    push = Array.prototype.push,
    slice = Array.prototype.slice,
    trim = String.prototype.trim,
    indexOf = Array.prototype.indexOf,
    class2type = {
      "[object Boolean]": "boolean",
      "[object Number]": "number",
      "[object String]": "string",
      "[object Function]": "function",
      "[object Array]": "array",
      "[object Date]": "date",
      "[object RegExp]": "regexp",
      "[object Object]": "object",
    },
    jQuery = {
      isFunction: function (obj) {
        return jQuery.type(obj) === "function";
      },
      isArray:
        Array.isArray ||
        function (obj) {
          return jQuery.type(obj) === "array";
        },
      isWindow: function (obj) {
        return obj != null && obj == obj.window;
      },
      isNumeric: function (obj) {
        return !isNaN(parseFloat(obj)) && isFinite(obj);
      },
      type: function (obj) {
        return obj == null
          ? String(obj)
          : class2type[toString.call(obj)] || "object";
      },
      isPlainObject: function (obj) {
        if (!obj || jQuery.type(obj) !== "object" || obj.nodeType) {
          return false;
        }
        try {
          if (
            obj.constructor &&
            !hasOwn.call(obj, "constructor") &&
            !hasOwn.call(obj.constructor.prototype, "isPrototypeOf")
          ) {
            return false;
          }
        } catch (e) {
          return false;
        }
        var key;
        for (key in obj) {
        }
        return key === undefined || hasOwn.call(obj, key);
      },
    };
  if (typeof target === "boolean") {
    deep = target;
    target = arguments[1] || {};
    i = 2;
  }
  if (typeof target !== "object" && !jQuery.isFunction(target)) {
    target = {};
  }
  if (length === i) {
    target = this;
    --i;
  }
  for (i; i < length; i++) {
    if ((options = arguments[i]) != null) {
      for (name in options) {
        src = target[name];
        copy = options[name];
        if (target === copy) {
          continue;
        }
        if (
          deep &&
          copy &&
          (jQuery.isPlainObject(copy) || (copyIsArray = jQuery.isArray(copy)))
        ) {
          if (copyIsArray) {
            copyIsArray = false;
            clone = src && jQuery.isArray(src) ? src : [];
          } else {
            clone = src && jQuery.isPlainObject(src) ? src : {};
          }
          // WARNING: RECURSION
          target[name] = extend(deep, clone, copy);
        } else if (copy !== undefined) {
          target[name] = copy;
        }
      }
    }
  }
  return target;
}

export function getUUID() {
  return "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g, function (c) {
    var r = (Math.random() * 16) | 0,
      v = c == "x" ? r : (r & 0x3) | 0x8;
    return v.toString(16);
  });
}

/**
 *
 * @param {*} totalData 获取到的所有的数据
 * @param {*} currentPage 当前的页码
 * @param {*} pageSize 当前的每页多少条
 * pageData 当前的分页分出的数据
 */
export function pagination(totalData, currentPage, pageSize) {
  const pageData = [];
  for (let i = 0, length = totalData.length; i < length; i++) {
    if (i < pageSize * currentPage && i >= pageSize * (currentPage - 1)) {
      pageData.push(totalData[i]);
    }
  }
  return pageData;
}

// 代码雨
export function codeRain(flag, id, domWidth, domHeight) {
  let timer = null;
  if (flag) {
    var c = document.getElementById(id);
    var ctx = c.getContext("2d");
    c.width = domWidth;
    c.height = domHeight;
    //				ctx.fillRect(0,0,100,100);
    //				a,b,c,d分别代表x方向偏移,y方向偏移,宽，高
    var string1 = "abcdefghijklmnopqrstuvwxyz";
    // string1.split('')
    var fontsize = 16;
    var columns = c.width / fontsize;
    var drop = [];
    for (var x = 0; x < columns; x++) {
      drop[x] = 0;
    }

    function drap() {
      ctx.fillStyle = "rgba(16,39,93,0.07)";
      ctx.fillRect(0, 0, c.width, c.height);
      ctx.fillStyle = "#0598c0";
      ctx.font = fontsize + "px arial";
      for (var i = 0; i < drop.length; i++) {
        var text1 = string1[Math.floor(Math.random() * string1.length)];
        ctx.fillText(text1, i * fontsize, drop[i] * fontsize);
        drop[i]++;
        if (drop[i] * fontsize > c.height && Math.random() > 0.9) {
          // 90%的几率掉落
          drop[i] = 0;
        }
      }
    }
    timer = setInterval(drap, 20);
  } else {
    clearInterval(timer);
    timer = null;
  }
}

// 数组对象去重
export function deduplication(list, target) {
  const hash = {};
  let array = [];
  array = list.reduceRight((item, next) => {
    // hash[next[target]] ? '' : (hash[next[target]] = true && item.unshift(next))
    if (!hash[next[target]]) {
      hash[next[target]] = true;
      item.unshift(next);
    }
    return item;
  }, []);
  return array;
}

// 秒级时间转换为分秒格式
export function totalCostTimeFormat(val) {
  if (val > 59) {
    return `${Math.floor(val / 60)} 分${val % 60} 秒`;
  } else {
    return `${val} 秒`;
  }
}

/**
 * 一维数组转树状数组
 * arr: 要转换的一维数组
 * id: 唯一识别
 * pid: 父级唯一识别
 */
export function arrayToJson(arr, id, pid, children = "children") {
  const tempArr = [];
  const tempObj = {};
  for (let i = 0, l = arr.length; i < l; i++) {
    tempObj[arr[i][id]] = arr[i];
  }
  for (let i = 0, l = arr.length; i < l; i++) {
    const key = tempObj[arr[i][pid]];

    if (key) {
      if (!key[children]) {
        key[children] = [];
        key[children].push(arr[i]);
      } else {
        key[children].push(arr[i]);
      }
    } else {
      tempArr.push(arr[i]);
    }
  }
  return tempArr;
}

export function jsonToArray(nodes, children = "children") {
  let r = [];
  if (Array.isArray(nodes)) {
    for (let i = 0, l = nodes.length; i < l; i++) {
      r.push(nodes[i]);
      if (Array.isArray(nodes[i][children]) && nodes[i][children].length > 0) {
        // 将children递归的push到最外层的数组r里面
        r = r.concat(jsonToArray(nodes[i][children]));
      }
      delete nodes[i][children];
    }
  }
  return r;
}

/**
 *  人员标签tagList转换带color
 */
export function exchangeTagList(tagList, libList) {
  const newTagList = [];
  const tempObj = {};
  if (!!tagList && tagList.length > 0 && libList.length > 0) {
    tagList.forEach((row) => {
      tempObj[row] = row;
    });
    libList.forEach((row) => {
      row.tagList.forEach((one) => {
        if (tempObj[one.tag]) {
          const newTags = one;
          newTags.originalLib = row.name;
          newTagList.push(newTags);
        }
      });
    });
    newTagList.forEach((item) => {
      item.newColor = `tag-${item.color}`;
    });
  }
  return newTagList;
}

/**
 * 获取 url参数
 */
export function getRegust() {
  const url = location.search;
  const theRequest = new Object();
  if (url.indexOf("?") !== -1) {
    const str = url.substr(1);
    const strs = str.split("&");
    for (let i = 0; i < strs.length; i++) {
      theRequest[strs[i].split("=")[0]] = unescape(strs[i].split("=")[1]);
    }
  }
  // location.search = '';
  return theRequest;
}

/**
 * export excel导出
 *
 */
export function exportfile(res, filename = "") {
  return new Promise((reslove, reject) => {
    const resData = res.data;
    const fileReader = new FileReader();
    fileReader.onloadend = () => {
      try {
        const errJson = JSON.parse(fileReader.result); // 说明是普通对象数据，后台转换失败
        reject(errJson);
      } catch (err) {
        // 解析成对象失败，说明是正常的文件流
        reslove({ code: 200 });
        // 下载文件
        downloadFile(res, filename);
      }
    };
    fileReader.readAsText(resData);
  });
}

function downloadFile(res, filename) {
  const blob = new Blob([res.data], {
    type: "application/vnd.ms-excel;charset=UTF-8",
  });
  const url = window.URL.createObjectURL(blob);
  const aLink = document.createElement("a");
  // 获取heads中的filename文件名
  const temp =
    filename ||
    res.headers["content-disposition"].split(";")[1].split("filename=")[1];
  const fileName = decodeURIComponent(temp);
  aLink.setAttribute("download", fileName);
  aLink.style.display = "none";
  aLink.href = url;
  document.body.appendChild(aLink);
  aLink.click();
  document.body.removeChild(aLink);
  window.URL.revokeObjectURL(url);
}

// export const emptyJSX = `
// <div class="no-data">
//     <img class="no-data-img" src=${require('@/assets/img/common/nodata.png')} />
//     <div class='null-data-text'>暂无数据</div>
// </div>
// `
// export const emptyJSXImg = `
// <div class="no-data">
//     <img class="no-data-img" src=${require('@/assets/img/common/nodata-img.png')} />
//     <div class='null-data-text'>暂无数据</div>
// </div>
// `

// // des解码
// export const decryptDes = (ciphertext, key) => {
//   const keyHex = CryptoJS.enc.Latin1.parse(key)
//   // direct decrypt ciphertext
//   const decrypted = CryptoJS.DES.decrypt(
//     {
//       ciphertext: CryptoJS.enc.Base64.parse(ciphertext)
//     },
//     keyHex,
//     {
//       mode: CryptoJS.mode.ECB,
//       padding: CryptoJS.pad.Pkcs7
//     },
//   )
//   return decrypted.toString(CryptoJS.enc.Utf8)
// }

// // des加密
// export const encryptByDES = (message, key) => {
//   var keyHex = CryptoJS.enc.Utf8.parse(key)
//   var encrypted = CryptoJS.DES.encrypt(message, keyHex, {
//     mode: CryptoJS.mode.ECB,
//     padding: CryptoJS.pad.Pkcs7
//   })
//   return encrypted.toString()
// }

/**
 * @description 根据iconid 匹配icon className
 * @param {*} shortNumber iconid
 */
// const iconsDataList = iconsList.glyphs
// export const geticonClassName = (iconUrl) => {
//   let font_class
//   for (let i = 0; i < iconsDataList.length; i++) {
//     if (iconsDataList[i].icon_id == iconUrl) {
//       font_class = iconsDataList[i].font_class
//       break
//     }
//   }
//   return font_class
// }

/**
 * 字节转kb、mb、gb
 *
 * @param {Number} byte
 */
export function byteFormat(bytes) {
  if (bytes < 1024) {
    return bytes + " B";
  } else if (bytes < 1024 * 1024) {
    return (bytes / 1024).toFixed(2) + " KB";
  } else if (bytes < 1024 * 1024 * 1024) {
    return (bytes / (1024 * 1024)).toFixed(2) + " MB";
  } else {
    return (bytes / (1024 * 1024 * 1024)).toFixed(2) + " GB";
  }
}

/**
 * 复制文字
 *
 * @param {String} value
 */
export function copyText(value) {
  let input = document.createElement("input");
  input.setAttribute("value", value);
  document.body.appendChild(input);
  input.select();
  let res = document.execCommand("copy");
  document.body.removeChild(input);
  if (res) {
    Message.success({
      content: "复制成功！",
      duration: 5,
    });
  }
}

/**
 * base64转file
 *
 * @param {String} base64Url
 * @param {String} filename
 */
export function base64ToFile(base64Url, filename) {
  const dataSepartorIndex = base64Url.indexOf(";");
  const fileType = base64Url.substring(5, dataSepartorIndex).split("/")[1];
  const data = base64Url.substring(dataSepartorIndex + 8);

  const bstr = atob(data);
  let l = bstr.length;
  const u8Arr = new Uint8Array(l);
  while (l--) {
    u8Arr[l] = bstr.charCodeAt(l);
  }
  const file = new Blob([u8Arr], { type: `image/${fileType}` });
  if (filename) file.name = `${filename}.${fileType}`;
  return file;
}

/**
 * 16进制颜色转带透明度颜色
 *
 * @param {String} thisColor
 * @param {Number} thisOpacity
 */
export function getOpacityColor(thisColor, thisOpacity) {
  var theColor = thisColor.toLowerCase();
  //十六进制颜色值的正则表达式
  var r = /^#([0-9a-fA-f]{3}|[0-9a-fA-f]{6})$/;
  // 如果是16进制颜色
  if (theColor && r.test(theColor)) {
    if (theColor.length === 4) {
      var sColorNew = "#";
      for (var i = 1; i < 4; i += 1) {
        sColorNew += theColor.slice(i, i + 1).concat(theColor.slice(i, i + 1));
      }
      theColor = sColorNew;
    }
    //处理六位的颜色值
    var sColorChange = [];
    for (var i = 1; i < 7; i += 2) {
      sColorChange.push(parseInt("0x" + theColor.slice(i, i + 2)));
    }
    return "rgba(" + sColorChange.join(",") + "," + thisOpacity + ")";
  }
  return theColor;
}
// 加密身份证
export function hiddenId(str, frontLen, endLen) {
  var len = str.length - frontLen - endLen;
  var xing = "";
  for (var i = 0; i < len; i++) {
    xing += "*";
  }
  return str.substring(0, frontLen) + xing + str.substring(str.length - endLen);
}

export function base64toFile(base64Str, fileName) {
  let arr = base64Str.split(",");
  let mime = arr[0].match(/:(.*?);/)[1];
  let bstr = atob(arr[1]);
  let n = bstr.length;
  let u8arr = new Uint8Array(n);
  while (n--) {
    u8arr[n] = bstr.charCodeAt(n);
  }
  return new File([u8arr], fileName, { type: mime });
}

/**
 *  计算时间差 支持 年 月 日 时 分 秒 。
 * @param  {String}  日期字符窜, 如YYYY-MM－DD hh:mm:ss
 * * @param  {String}  日期字符窜, 如YYYY-MM－DD hh:mm:ss
 * @param  {String}  时间差单位，如 years, months, days, hours, minutes, seconds, milliseconds
 * @returns {String} 时间对象
 */
export function getDateDiff(startTime, endTime, flg) {
  flg = flg || "seconds";
  var time1 = dayjs(startTime);
  var time2 = dayjs(endTime);
  return time2.diff(time1, flg, true);
}

/**
 *  计算一个时间和当前时间的时间差
 * @param  {String}  如hh:mm
 */
export function getTimeDiff(time) {
  const [hours, minutes] = time.split(":").map(Number);
  const current = new Date();
  const hours2 = current.getHours();
  const minutes2 = current.getMinutes();
  const totalMinutes = hours * 60 + minutes;
  const totalMinutes2 = hours2 * 60 + minutes2;
  const timeDiffInMinutes = (totalMinutes2 - totalMinutes) * 60 * 1000;
  return timeDiffInMinutes;
}

export function isFunction(value) {
  return typeof value === "function";
}

export function isString(val) {
  return (
    Object.prototype.toString.call(val) === "[object String]" &&
    val.trim() !== ""
  );
}

export function isObject(val) {
  return (
    Object.prototype.toString.call(val) === "[object Object]" && !isEmpty(val)
  );
}

export function isNumber(val) {
  return typeof val === "number" && !isNaN(val);
}

export function isEmpty(val) {
  // object 或 null类型
  if (typeof val === "object") return !Object.keys(val || {}).length;
  else if (typeof val === "undefined") return true;
  else if (typeof val === "boolean") return false;
  else if (typeof val === "string") return !val;
  else if (typeof val === "symbol") return false;
  else if (typeof val === "function") return false;
  // 排除数字 0
  else if (typeof val === "number") return isNaN(val);
  else return false;
}

/**
 * 根据传递的key值，格式化成地图撒点需要的格式
 * @param {String} key 图标key值请查阅build/common/conf/map-image.js中对应的key
 */
export function getMapMarkerImage(key) {
  return {
    ...traversingObject(key, mapImages),
    // url: key,
  };
}

/**
 * 根据传递的keys值，格式化成地图注册图片需要的格式
 * @param {Array} keys
 */
export function getMapRegisterImages(keys = []) {
  return keys.reduce((newValue, key) => {
    let obj = traversingObject(key, mapImages);
    obj && newValue.push([key, obj.url]);
    return newValue;
  }, []);
}

/**
 * 递归遍历对象，获取key对应的value值
 * @param {*} key - 要查找的key值
 * @param {*} obj - 要遍历的对象
 */
function traversingObject(key, obj = {}) {
  let value;
  Object.keys(obj).some((item) => {
    if (key === item) {
      value = obj[key];
      return true;
    } else {
      if (isObject(obj[item]) && !obj[item].hasOwnProperty("url")) {
        value = traversingObject(key, obj[item]);
        if (value) {
          return true;
        }
      }
    }
  });
  return value;
}

/**
 * 普通marker的事件绑定处理
 * @param mouseover - 鼠标移入事件
 * @param mouseout - 鼠标移出事件
 * @param click - 点击事件
 * @param drag - 拖拽事件
 * @returns {Map<any, any>}
 */
export function getMarkerOptsEvent({
  mouseover = function () {},
  mouseout = function () {},
  click = function () {},
}) {
  return new window.Map([
    [NPMap.MARKER_EVENT_MOUSE_OVER, mouseover],
    [NPMap.MARKER_EVENT_MOUSE_OUT, mouseout],
    [NPMap.MARKER_EVENT_CLICK, click],
  ]);
}
/**
 * 获取当前时间的前后N天日期 年月日时分秒
 * @param {*} num 获取前N天的数据，2：两天后， -2 两天前
 */
export function getDateTime(num) {
  var today = new Date();
  var targetday_milliseconds = today.getTime() + 1000 * 60 * 60 * 24 * num;
  var targetday = new Date();
  targetday.setTime(targetday_milliseconds); //注意，这行是关键代码

  var cur_year = today.getFullYear();
  var cur_month = today.getMonth() + 1;
  var cur_day = today.getDate();
  cur_month = (cur_month < 10 ? "0" : "") + cur_month;
  cur_day = (cur_day < 10 ? "0" : "") + cur_day;

  var year = targetday.getFullYear();
  var month = targetday.getMonth() + 1;
  var day = targetday.getDate();
  month = (month < 10 ? "0" : "") + month;
  day = (day < 10 ? "0" : "") + day;
  var h =
    today.getHours() < 10
      ? "0" + today.getHours() + ":"
      : today.getHours() + ":";
  var m =
    today.getMinutes() < 10
      ? "0" + today.getMinutes() + ":"
      : today.getMinutes() + ":";
  var s =
    today.getSeconds() < 10 ? "0" + today.getSeconds() : today.getSeconds();
  return year + "-" + month + "-" + day + " " + h + m + s;
}

export function getBaseAppRouterAddress(url) {
  return (
    baseAppIp +
    url?.replace(BASE_URL, "") +
    "&refresh_token=" +
    getToken("token")
  );
}
