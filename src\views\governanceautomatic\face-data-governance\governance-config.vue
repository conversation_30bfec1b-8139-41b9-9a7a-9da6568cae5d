<template>
  <ui-modal v-model="visible" title="治理配置" :styles="styles">
    <div class="config-item">
      <p>图像不可访问治理</p>
      <div class="config-wrap mb-md">
        <span class="config-title">小图不可访问</span>
        <div class="config-content">
          <div class="width-full t-right">
            <i-switch v-model="configInfo.methodData.urlAvailable.checkFacePath" />
          </div>
        </div>
      </div>
      <div class="config-wrap">
        <span class="config-title">大图不可访问</span>
        <div class="config-content">
          <div class="width-full t-right">
            <i-switch v-model="configInfo.methodData.urlAvailable.checkScenePath" />
          </div>
        </div>
      </div>
    </div>
    <div class="config-item">
      <p>低质量图像治理</p>
      <div class="config-wrap mb-md">
        <span class="config-title">低质量检测</span>
        <div class="config-content">
          <ui-label label="阈值 : ≤ ">
            <Input class="width-xs" v-model="configInfo.methodData.vqdDetect.threshold" disabled></Input>
            <span class="ml-sm">分</span>
          </ui-label>
          <ui-label label="算法配置 :">
            <RadioGroup v-model="configInfo.methodData.vqdDetect.videoQualityDetectMode">
              <Radio v-for="(item, index) in vqdDetectAlgorithmList" :key="index" :label="item.dataKey" disabled>
                {{ item.dataValue }}
              </Radio>
            </RadioGroup>
          </ui-label>
          <i-switch v-model="configInfo.methodData.vqdDetect.available" disabled />
        </div>
      </div>
      <div class="config-wrap">
        <span class="config-title">小图唯一人脸检测</span>
        <div class="config-content">
          <ui-label label="算法配置 : ">
            <CheckboxGroup
              class="inline algorithm-list"
              v-model="configInfo.methodData.fullImageExtract.algorithmTypeList"
            >
              <Checkbox v-for="(item, index) in algorithmList" :key="index" :label="item.dataKey">
                {{ item.dataValue }}
              </Checkbox>
            </CheckboxGroup>
          </ui-label>
          <i-switch v-model="configInfo.methodData.fullImageExtract.available" />
        </div>
      </div>
    </div>
    <div class="config-item">
      <p>重复图像治理</p>
      <div class="config-wrap">
        <span class="config-title flex-start">数据范围</span>
        <div class="config-content">
          <div>
            <Input class="width-xs" v-model="configInfo.methodData.imageRepeat.effectiveTime"></Input>
            <span class="ml-sm">时</span>
          </div>
          <i-switch v-model="configInfo.methodData.imageRepeat.available" />
          <p class="width-full mt-md tips">
            说明：在配置的数据范围内进行重复图像检测治理，最大支持2小时的数据重复治理。
          </p>
        </div>
      </div>
    </div>
    <div class="config-item">
      <p>抓拍时间异常图像治理</p>
      <div class="config-wrap">
        <div class="config-title">
          <div class="t-left">抓拍时间和接收时间允许时间误差</div>
        </div>
        <div class="config-content">
          <div>
            <Input class="width-xs" v-model="configInfo.methodData.shotTime.timeInterval"></Input>
            <span class="ml-sm">秒</span>
          </div>
          <i-switch v-model="configInfo.methodData.shotTime.available" />
        </div>
      </div>
    </div>
    <template #footer>
      <Button @click="visible = false"> 取 消</Button>
      <Button class="ml-sm" type="primary" :loading="submitLoading" @click="query"> 确 定 </Button>
    </template>
  </ui-modal>
</template>
<script>
import { mapGetters, mapActions } from 'vuex';
import imageDataGovernance from '@/config/api/image-data-governance';
export default {
  props: {
    value: {
      type: Boolean,
    },
  },
  data() {
    return {
      visible: false,
      loading: false,
      submitLoading: false,
      styles: {
        width: '4.7rem',
      },
      vqdDetectAlgorithmList: [],
      configInfo: {
        type: 1,
        methodData: {
          urlAvailable: {
            checkFacePath: false,
            checkScenePath: false,
          },
          vqdDetect: {
            threshold: '',
            videoQualityDetectMode: '',
            available: false,
          },
          fullImageExtract: {
            algorithmTypeList: [],
            available: false,
          },
          imageRepeat: {
            effectiveTime: '',
            available: false,
          },
          shotTime: {
            timeInterval: '',
            available: false,
          },
        },
      },
    };
  },
  created() {
    this.getAlldicData();
    this.getVideoQualityDetectMode();
  },
  methods: {
    ...mapActions({
      getAlldicData: 'algorithm/getAlldicData',
    }),
    async getVideoQualityDetectMode() {
      try {
        const res = await this.$http.get(imageDataGovernance.getVideoQualityDetectMode);
        this.vqdDetectAlgorithmList = res.data.data;
      } catch (err) {
        console.log(err);
      }
    },
    async init() {
      await this.getDataTask();
    },
    async getDataTask() {
      try {
        const res = await this.$http.get(imageDataGovernance.getDataTask, {
          params: {
            id: 1,
          },
        });
        const extensionData = res.data.data.extensionData;
        if (extensionData) {
          Object.assign(this.configInfo.methodData, JSON.parse(extensionData).methodData);
        }
      } catch (err) {
        console.log(err);
      }
    },
    async query() {
      try {
        this.submitLoading = true;
        const params = {
          id: 1,
          extensionData: this.dealData(),
        };
        const res = await this.$http.post(imageDataGovernance.updateDataTask, params);
        this.$Message.success(res.data.msg);
        this.visible = false;
      } catch (err) {
        console.log(err, 'err');
      } finally {
        this.submitLoading = false;
      }
    },
    dealData() {
      const params = {
        type: 1,
        methodData: {},
      };
      Object.keys(this.configInfo.methodData).forEach((key) => {
        if (this.configInfo.methodData[key]?.available) {
          params.methodData[key] = Object.assign({}, this.configInfo.methodData[key]);
        } else if (this.configInfo.methodData[key]?.available === undefined) {
          params.methodData[key] = Object.assign({}, this.configInfo.methodData[key]);
        }
      });
      return JSON.stringify(params);
    },
    reset() {
      this.configInfo = {
        type: 1,
        methodData: {
          urlAvailable: {
            checkFacePath: false,
            checkScenePath: false,
          },
          vqdDetect: {
            threshold: '',
            videoQualityDetectMode: '',
            available: false,
          },
          fullImageExtract: {
            algorithmTypeList: [],
            available: false,
          },
          imageRepeat: {
            effectiveTime: '',
            available: false,
          },
          shotTime: {
            timeInterval: '',
            available: false,
          },
        },
      };
    },
  },
  watch: {
    visible(val) {
      this.$emit('input', val);
    },
    value(val) {
      if (val) {
        this.reset();
        this.init();
      }
      this.visible = val;
    },
  },
  computed: {
    ...mapGetters({
      algorithmList: 'algorithm/algorithmVendorType',
    }),
  },
  components: {},
};
</script>
<style lang="less" scoped>
@{_deep}.ivu-modal-body {
  height: 750px;
  width: 850px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  margin-left: 50%;
  transform: translateX(-50%);
  padding-right: 30px;
}
.config-item {
  border-top: 1px dashed var(--devider-line);
  padding-top: 15px;
  margin-bottom: 25px;
  color: var(--color-content);
  &:first-child {
    border-top: none;
    padding-top: 0;
  }
  > p {
    color: var(--color-active);
    font-size: 16px;
    font-weight: 900;
  }
  .flex-start {
    margin-top: -40px;
  }
  .algorithm-list {
    width: 490px;
  }
  .config-wrap {
    margin-top: 25px;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .config-title {
    width: 130px;
    text-align: right;
  }
  .config-content {
    width: calc(100% - 126px);
    background-color: var(--bg-form-item);
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    margin-left: 10px;
    padding: 10px 20px;
    > .width-full {
      width: 100%;
    }
    .tips {
      color: var(--color-warning);
    }
  }
}
</style>
