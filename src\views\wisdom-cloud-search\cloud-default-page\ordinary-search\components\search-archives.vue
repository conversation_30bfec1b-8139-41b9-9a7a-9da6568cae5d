<template>
  <div class="search">
    <Form ref="form" :inline="true" :model="formData" class="form">
      <!-- 实名档 -->
      <template v-if="dataType === 'name'">
        <FormItem label="身份证号:" prop="idcardNo">
          <Input placeholder="请输入" v-model="formData.idcardNo" maxlength="50" />
        </FormItem>
        <FormItem label="姓名:" prop="name">
          <Input placeholder="请输入" v-model="formData.name" maxlength="50" />
        </FormItem>
        <FormItem label="民族:" prop="nations">
          <Select v-model="formData.nations" ref="select" transfer multiple filterable :max-tag-count="1">
            <Option v-for="item in nationList" :value="item.dataKey" :key="item.dataKey" placeholder="请选择">
              {{ item.dataValue }}
            </Option>
          </Select>
        </FormItem>
        <FormItem label="标签:" prop="">
          <div class="select-tag-button" @click="selectLabelHandle">请选择标签{{ formData.labelIds && formData.labelIds.length > 0 ? `{已选（${formData.labelIds.length}）}` : '' }}</div>
        </FormItem>
        <FormItem class="nullLabel" prop="">
          <Select v-model="formData.labelType" placeholder="请选择" style="width: 80px;" transfer>
            <Option value="2">并集</Option>
            <Option value="1">交集</Option>
          </Select>
        </FormItem>
        <div class="other-search-bottom">
          <FormItem label="性别:" prop="sex">
            <ui-tag-select @input="input" :value="formData.sex" ref="tagSelect">
              <ui-tag-select-option v-for="(item, $index) in genderList" :key="$index" :name="item.dataKey">
                {{ item.dataValue }}
              </ui-tag-select-option>
            </ui-tag-select>
          </FormItem>
          <FormItem class="btn-group">
            <Button type="primary" @click="startSearch">查询</Button>
            <Button @click="resetHandle">重置</Button>
          </FormItem>
        </div>
      </template>
      <!-- 视频档 -->
      <template v-if="dataType === 'video'">
        <FormItem label="视频身份:" prop="archiveNo">
          <Input placeholder="请输入" v-model="formData.archiveNo" maxlength="50" />
        </FormItem>
        <FormItem label="标签:" prop="">
          <div class="select-tag-button" @click="selectLabelHandle">请选择标签{{ formData.labelIds && formData.labelIds.length > 0 ? `{已选（${formData.labelIds.length}）}` : '' }}</div>
        </FormItem>
        <FormItem class="nullLabel" prop="">
          <Select v-model="formData.labelType" placeholder="请选择" style="width: 80px;" transfer>
            <Option value="2">并集</Option>
            <Option value="1">交集</Option>
          </Select>
        </FormItem>
        <FormItem class="btn-group video-search">
          <Button type="primary" @click="startSearch">查询</Button>
          <Button @click="resetHandle">重置</Button>
        </FormItem>
      </template>
    </Form>
    <!-- 选择标签 -->
    <LabelModal :labelType="2" @setCheckedLabel="setCheckedLabel" ref="labelModal" />
  </div>
</template>
<script>
import { mapGetters } from 'vuex'
import LabelModal from '@/views/holographic-archives/components/relationship-map/label-add'
export default {
  components: {
    LabelModal
  },
  props: {
    dataType: {
      type: [String, Number],
      default: ''
    }
  },
  data() {
    return {
      gender: '全部',
      formData: {
        nations: [],
        idcardNo: '',
        sex: '',
        name: '',
        labelIds: [],
        archiveNo: '',
        labelType: '2'
      }
    }
  },
  computed: {
    ...mapGetters({
      genderList: 'dictionary/getGenderList', //性别
      nationList: 'dictionary/getNationList' //民族
    })
  },
  mounted() {
    // this.formData.idcardNo = ''
  },
  methods: {
    // 选择标签
    selectLabelHandle() {
      this.$refs.labelModal.init(this.formData.labelIds)
    },
    // 已选标签
    setCheckedLabel(val) {
      this.formData.labelIds = JSON.parse(JSON.stringify(val))
    },
    // 查询
    startSearch() {
        this.$emit('searchForm', this.formData)
    },
    // 性别选择
    input(val) {
      this.formData.sex = val
    },
    // 重置
    resetHandle() {
      if (this.dataType === 'video') {
        this.formData.archiveNo = ''
      } else {
        this.$refs.form.resetFields()
        this.$refs.tagSelect.clearChecked()
      }
      this.formData.labelIds = []
      this.$refs.labelModal.removeAllHandle()

      this.startSearch()
    },
    setData (data) {
      if (this.dataType === 'video') {
        this.formData.archiveNo = ''
      } else {
        this.$refs.form.resetFields()
        this.$refs.tagSelect.clearChecked()
      }
      this.formData.labelIds = []
      this.$refs.labelModal.removeAllHandle()
      if (data) {
        if(data.archiveNo) this.formData.archiveNo = data.archiveNo
        if(data.nations) this.formData.nations = data.nations
        if(data.idcardNo) {
          if (typeof(data.idcardNo) != 'string') {
            this.formData.idcardNo = ''
          }else{
            this.formData.idcardNo = data.idcardNo
          }
        }
        if(data.sex) this.formData.sex = data.sex
        // if(data.name) this.formData.name = data.name
        if(data.name) {
          if (typeof(data.name) != 'string') {
            this.formData.name = ''
          }else{
            this.formData.name = data.name
          }
        }
        if(data.labelIds) this.formData.labelIds = data.labelIds
      }
      this.startSearch()
    }
  }
}
</script>
<style lang="less" scoped>
.search {
  width: 100%;
  .form {
    width: 100%;
  }
  .other-search-bottom {
    width: 100%;
    display: flex;
    justify-content: space-between;
  }
  .btn-group {
    margin-right: 0px;
  }
  .video-search {
    float: right;
  }
  /deep/.ivu-select-item-selected,
  .ivu-select-item-selected:hover {
    background-color: #fff !important;
    color: #2c86f8 !important;
  }
}
</style>
