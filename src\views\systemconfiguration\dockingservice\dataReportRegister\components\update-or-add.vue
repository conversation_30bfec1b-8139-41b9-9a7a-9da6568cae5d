<template>
  <ui-modal ref="modal" :title="title" :width="550" @query="submitHandle">
    <Form ref="formData" :model="formData" :rules="rules" :label-width="123">
      <FormItem label="接口名称" prop="intefaceName">
        <Input v-model="formData.intefaceName" placeholder="请输入接口名称"></Input>
      </FormItem>
      <FormItem label="接口类型" prop="intefaceType">
        <Select v-model="formData.intefaceType" clearable placeholder="请选择">
          <Option v-for="(item, index) in interfaceTypeList" :key="index" :value="item.dataKey">{{
            item.dataValue
          }}</Option>
        </Select>
      </FormItem>
      <FormItem label="接口地址" prop="intefaceUrl">
        <Input v-model="formData.intefaceUrl" placeholder="请输入接口地址"></Input>
      </FormItem>
      <FormItem label="本平台IP地址" prop="localIp" ref="localIp">
        <Input v-model="formData.localIp" placeholder="请输入本平台IP地址"></Input>
      </FormItem>
      <FormItem label="本平台端口号" prop="localPort">
        <Input type="number" v-model="formData.localPort" placeholder="请输入本平台端口号"></Input>
      </FormItem>
      <FormItem label="授权本平台账户" prop="username">
        <Input v-model="formData.username" placeholder="请输入授权本平台账户"></Input>
        <Input class="hide-input"></Input>
      </FormItem>
      <FormItem label="授权本平台密码" prop="password">
        <!--禁止自动填充-->
        <Input type="password" class="hide-input"></Input>
        <Input type="password" v-model="formData.password" maxlength="20" placeholder="授权本平台密码"></Input>
      </FormItem>
    </Form>
  </ui-modal>
</template>
<script>
import api from '@/config/api/cascadeIntefaceConfig';

export default {
  props: {
    interfaceTypeList: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      showItem: false,
      title: '',
      confirmLoading: null,
      formData: {
        'id': '',
        'intefaceName': '',
        'intefaceUrl': '',
        'localIp': '',
        'intefaceType': '',
        'localPort': '',
        'password': '',
        'username': '',
      },
      rules: {
        intefaceName: [{ required: true, message: '请输入接口名称', trigger: 'blur' }],
        intefaceType: [{ required: true, message: '请选择接口类型', trigger: 'change' }],
        intefaceUrl: [{ required: true, message: '请输入接口地址', trigger: 'blur' }],
        localIp: [
          { required: true, message: '请输入本平台IP地址', trigger: 'blur' },
          {
            validator: function (rule, value, callback) {
              if (
                /((2(5[0-5]|[0-4]\d))|[0-1]?\d{1,2})(\.((2(5[0-5]|[0-4]\d))|[0-1]?\d{1,2})){3}/gi.test(value) == false
              ) {
                callback(new Error('IP格式不正确'));
              } else {
                callback();
              }
            },
            trigger: 'blur',
          },
        ],
        localPort: [{ required: true, message: '请输入本平台端口号', trigger: 'blur' }],
        username: [{ required: true, message: '请输入授权本平台账户', trigger: 'blur' }],
        password: [{ required: true, message: '授权本平台密码', trigger: 'blur' }],
      },
    };
  },
  created() {},
  methods: {
    showModal(row) {
      this.$refs.modal.modalShow = true;
      let id = row ? row.id : '';
      this.title = id ? '编辑注册信息' : '接口注册';
      this.action = id ? 'update' : 'add';
      this.$refs.formData.resetFields();
      if (id) {
        row.localPort = row.localPort.toString();
        this.formData = { ...row };
      }
    },
    // 提交
    submitHandle() {
      this.$refs['formData'].validate((valid) => {
        if (valid && !this.confirmLoading) {
          this.confirmLoading = this.$Message.loading({ content: 'Loading...', duration: 0 });
          this.$http[this.action === 'add' ? 'post' : 'put'](
            this.action === 'add' ? api.supInterfaceAdd : api.supInterfaceUpdate,
            this.formData,
          )
            .then((res) => {
              let data = res.data;
              this.$Message.success(data.msg);
              this.$refs.modal.modalShow = false;
              this.$emit('refreshDataList');
            })
            .finally(() => {
              this.confirmLoading();
              this.confirmLoading = null;
            });
        }
      });
    },

    chechIp() {
      var ble = this.$util.common.checkIp(this.formData.localIp);
      if (ble) {
        this.$refs.localIp.validateState = 'success';
        this.$refs.localIp.validateMessage = '';
      } else {
        setTimeout(() => {
          this.$refs.localIp.validateState = 'error';
          this.$refs.localIp.validateMessage = 'IP格式不正确';
        }, 50);
      }
    },
  },
  watch: {
    'formData.localPort'(newVal, oldVal) {
      if (newVal.length > 8) {
        setTimeout(() => {
          this.formData.localPort = oldVal;
          // this.formData.localPort = newVal.slice(0,8)
        }, 50);
      }
    },
  },
};
</script>
<style lang="less" scoped>
/deep/ .ivu-modal-body {
  padding: 20px 60px 20px 50px;
}

/deep/ input::-webkit-outer-spin-button,
/deep/ input::-webkit-inner-spin-button {
  -webkit-appearance: none;
}
/deep/ input[type='number'] {
  -moz-appearance: textfield;
}
</style>
