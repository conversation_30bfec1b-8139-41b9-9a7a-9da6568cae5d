<template>
  <ui-modal v-model="visible" title="设置达标数量" :styles="styles" class="ui-modal" @query="query">
    <div class="area-filter mb-md ml-sm">
      <ui-label required class="inline" label="行政区划" :width="70">
        <Select class="width-md" v-model="form.regionType" placeholder="请选择行政区划">
          <Option value="province">省</Option>
          <Option value="city">市</Option>
          <Option value="region">区县</Option>
          <Option value="policeStation">派出所</Option>
        </Select>
      </ui-label>
      <ui-label required class="inline ml-lg" label="达标数量" :width="70">
        <InputNumber v-model="form.number" class="width-md" placeholder="请输入达标数量"> </InputNumber>
      </ui-label>
      <Button class="ml-md" type="primary" @click="clickBatchInput">批量填入</Button>
    </div>
    <div class="area-container">
      <div class="tree-title base-text-color">
        <span class="org-name">行政区划名称</span>
        <span class="car">达标数量</span>
        <span class="is-checked">是否考核</span>
        <span class="is-all">全部</span>
      </div>
      <div class="tree-wrapper">
        <ui-search-tree
          ref="uiTree"
          class="ui-search-tree"
          no-search
          :highlight-current="false"
          node-key="nodeKey"
          :tree-data="areaTreeData"
          :default-props="defaultProps"
          expandAll
          checkStrictly
        >
          <template #label="{ node, data }">
            <span class="vt-middle">{{ data.regionName }}</span>
            <div class="options inline fr">
              <Input
                v-show="data.check && edit"
                v-model="data.value"
                class="width-sm value vt-middle"
                :placeholder="`请输入达标数量`"
              ></Input>
              <span class="mr-100 vt-middle">
                <i-switch v-model="data.check" size="small" @on-change="check($event, node, data)"></i-switch>
              </span>
              <Checkbox
                v-model="data.checkAll"
                class="mr-50 vt-middle"
                @on-change="checkAll($event, node, data)"
                :style="{ visibility: !data.children ? 'hidden' : '' }"
                >{{ `${data.checkAll ? '取消' : '全部'} ` }}</Checkbox
              >
            </div>
          </template>
        </ui-search-tree>
        <loading v-if="loading"></loading>
      </div>
    </div>
  </ui-modal>
</template>

<script>
//数量达标率配置
export default {
  name: 'quantity-config',
  components: {
    UiSearchTree: require('@/components/ui-search-tree.vue').default,
  },
  props: {
    value: {},
    edit: {
      default: true,
    },
    defaultProps: {
      default: () => {
        return {
          label: 'regionName',
          children: 'children',
        };
      },
    },
    nodeKey: {
      default: 'regionCode',
    },
    //已配置的达标
    data: {
      required: true,
      default: () => [],
    },
    //行政区划
    regionData: {
      required: true,
      default: () => [],
    },
  },
  data() {
    return {
      styles: {
        width: '5rem',
      },
      visible: false,
      loading: false,
      checkedTreeData: [],
      areaTreeData: [],
      form: {
        regionType: 'province',
        number: null,
      },
      treeData: [],
      formData: [],
    };
  },
  created() {
    this.visible = true;
  },
  mounted() {
    // 操作dom 给只有自己的树转节点 添加类名
    document.querySelectorAll('.el-tree .is_parent').forEach((itemDom) => {
      itemDom.parentNode.parentNode.classList.add('has-child-panel');
    });
  },
  watch: {
    value(val) {
      this.visible = val;
    },
    visible(val) {
      this.$emit('input', val);
      if (val) {
        // this.init()
      } else {
        this.areaTreeData = [];
      }
    },
    data: {
      deep: true,
      immediate: true,
      handler: function (val) {
        this.formData = val;
      },
    },
    regionData: {
      deep: true,
      immediate: true,
      handler: function (val) {
        this.treeData = val || [];
        this.init();
      },
    },
  },
  methods: {
    clickBatchInput() {
      if (!this.form.number) {
        return this.$Message.error('请输入达标数量');
      }
      this.batchInput(this.areaTreeData);
    },
    /*
    1省 2直辖市 3省会市 4单列市 5其他地市 6直辖市的区 7直辖市的县 8其他各区县 9各派出所
    * */
    batchInput(data) {
      data.map((item) => {
        if (item.regionType === '1' && this.form.regionType === 'province') {
          this.$set(item, 'value', this.form.number);
        } else if (['2', '3', '4', '5'].includes(item.regionType) && this.form.regionType === 'city') {
          this.$set(item, 'value', this.form.number);
        } else if (['6', '7', '8'].includes(item.regionType) && this.form.regionType === 'region') {
          this.$set(item, 'value', this.form.number);
        } else if (item.regionType === '9' && this.form.regionType === 'policeStation') {
          this.$set(item, 'value', this.form.number);
        }
        if (item.children) {
          this.batchInput(item.children);
        }
      });
    },
    setSourceDataToTreeData(source, target) {
      target.map((item) => {
        source.map((value) => {
          if (item.regionCode === value.key) {
            this.$set(item, 'check', true);
            this.$set(item, 'regionCode', value.key);
            this.$set(item, 'regionName', value.name);
            this.$set(item, 'value', value.value);
          }
        });
      });
    },
    init() {
      let treeData = JSON.parse(JSON.stringify(this.treeData));
      this.setSourceDataToTreeData(this.formData, treeData);
      this.areaTreeData = this.$util.common.arrayToJson(treeData, 'regionCode', 'parentCode');
    },
    query() {
      this.checkedTreeData = [];
      this.getCheckedNodes(this.areaTreeData);
      let result = this.validateCheckedNodes(this.checkedTreeData);
      if (result) {
        this.$emit('query', this.checkedTreeData);
        this.visible = false;
      }
    },
    getCheckedNodes(data) {
      data.map((item) => {
        if (item.check) {
          this.checkedTreeData.push({
            key: item.regionCode,
            name: item.regionName,
            value: item.value || '',
          });
        }
        if (item.children) {
          this.getCheckedNodes(item.children);
        }
      });
    },
    validateCheckedNodes(data) {
      if (!data.length) {
        this.$Message.error('请选择行政区划并设置达标数量');
        return false;
      }
      for (let i = 0; i < data.length; i++) {
        let item = data[i];
        let value = item.value;
        let reg = /^[1-9]\d*$/;
        if (!value) {
          this.$Message.error(`${item.name}达标数量不能为空`);
          return false;
        }
        if (value && !reg.test(value)) {
          this.$Message.error(`${item.name}格式不正确`);
          return false;
        }
      }
      return true;
    },
    checkAll(val, node, data) {
      this.$set(node.data, 'check', data.checkAll);
      if (node.childNodes) {
        if (data.checkAll) {
          this.checkParent(node, data.checkAll);
        } else {
          this.checkChildren([node], data.checkAll);
        }
        node.childNodes.map((item) => {
          this.$set(item.data, 'check', data.checkAll);
        });
      }
    },
    check(val, node) {
      this.checkParent(node, val);
      this.checkChildren([node], val);
    },
    checkParent(node, check = true) {
      this.$set(node.data, 'check', check);
      if (node.parent) {
        if (check) {
          this.checkParent(node.parent, check);
        }
      }
    },
    checkChildren(node, check) {
      node &&
        node.map((item) => {
          this.$set(item.data, 'checkAll', false);
          this.$set(item.data, 'check', check);
          if (item.childNodes) {
            this.checkChildren(item.childNodes);
          }
        });
    },
  },
};
</script>

<style lang="less" scoped>
@import 'index';
</style>
