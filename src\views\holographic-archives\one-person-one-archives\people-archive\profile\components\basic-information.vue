<template>
  <div class="basic-information">
    <!-- <swiper
      ref="mySwiper"
      :options="swiperOption"
      class="my-swiper"
      @slideChange="slideChange"
      style="height: 100%"
    >
      <swiper-slide
        v-for="(item, index) in baseInfoList"
        :key="`${index}-${item.idcardNo}`"
        class="swiper-no-swiping"
      >
        <BasicInformation
          :labelType="labelType"
          :type="type"
          :baseInfo="item"
        ></BasicInformation>
      </swiper-slide>
      <div class="swiper-pagination" slot="pagination"></div>
    </swiper> -->
    <BasicInformation
      :labelType="labelType"
      :type="type"
      :baseInfo="baseInfo"
    ></BasicInformation>
  </div>
</template>
<script>
import { swiper, swiperSlide } from "vue-awesome-swiper";
import BasicInformation from "@/views/holographic-archives/components/relationship-map/basic-information";
import { mapGetters, mapMutations } from "vuex";
export default {
  props: {
    // 标签类型
    labelType: {
      type: Number,
      default: 0,
    },
    type: {
      type: String,
      default: "people",
    },
    // 基本信息
    baseInfo: {
      type: Object | Array,
    },
  },
  components: {
    swiper,
    swiperSlide,
    BasicInformation,
  },
  watch: {
    baseInfo: {
      deep: true,
      immediate: true,
      handler(val) {
        /**
         * 人脸返回的Object 车辆返回的Array 这里做一下兼容
         */
        if (Array.isArray(val)) {
          this.baseInfoList = val;
        } else {
          this.baseInfoList = [val];
        }
      },
    },
    baseInfoIndex(val) {
      this.$refs.mySwiper.swiper.slideTo(val, 500, false);
    },
  },
  computed: {
    ...mapGetters({
      baseInfoIndex: "archive/getBaseInfoIndex",
    }),
  },
  data() {
    return {
      baseInfoList: [],
      swiperOption: {
        effect: "fade",
        noSwiping: true,
        direction: "horizontal",
        pagination: {
          el: ".swiper-pagination",
          clickable: true,
        },
      },
    };
  },
  methods: {
    ...mapMutations({
      setBaseInfoIndex: "archive/setBaseInfoIndex",
    }),
    slideChange() {
      this.setBaseInfoIndex(this.$refs.mySwiper.swiper.activeIndex);
    },
  },
};
</script>
<style lang="less" scoped>
.basic-information {
  width: 350px;
  height: calc(~"100% - 88px");
  position: fixed;
  top: 78px;
  left: 10px;
  z-index: 101;
  /deep/ .ui-card {
    overflow: hidden !important;
    display: flex;
    flex-direction: column;
    flex: 1;
    height: 100%;
  }
  /deep/ .card-content {
    padding: 0 20px 20px !important;
    overflow-x: hidden;
    overflow-y: auto;
  }
}
</style>
