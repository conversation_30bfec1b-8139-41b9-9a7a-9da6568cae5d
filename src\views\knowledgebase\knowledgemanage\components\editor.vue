<template>
  <div class="editor-wrapper">
    <Toolbar :editor="editor" :defaultConfig="toolbarConfig" :mode="mode" />
    <Editor
      class="editor-message"
      ref="editorRef"
      v-model="editorHtml"
      :defaultConfig="editorConfig"
      :mode="mode"
      @onCreated="onCreated"
    />
  </div>
</template>
<script>
import { Editor, Toolbar } from '@wangeditor/editor-for-vue';
import '@wangeditor/editor/dist/css/style.css';
export default {
  components: { Editor, Toolbar },
  data() {
    return {
      editor: null,
      editorHtml: '', // 富文本的html，外部直接获取
      toolbarConfig: {
        toolbarKeys: [
          'headerSelect',
          'fontFamily',
          'fontSize',
          'color',
          'bgColor',
          'bold',
          'italic',
          'underline',
          'justifyLeft',
          'justifyRight',
          'justifyCenter',
          'justifyJustify',
          // "lineHeight",
          'emotion',
          'uploadImage',
          // 'codeSelectLang',
          'insertLink',
          'insertTable',
          'clearStyle',
          'undo',
          'redo',
          // 'fullScreen'
        ],
      },
      editorConfig: {
        placeholder: '请输入内容...',
        // autoFocus: false,
        // 所有的菜单配置，都要在 MENU_CONF 属性下
        MENU_CONF: {
          // 图片上传
          uploadImage: {
            server: '/qsdi-system-service/file/upload',
            fieldName: 'file',
            // 单个文件的最大体积限制，默认为 2M
            maxFileSize: 10 * 1024 * 1024, // 10M
            // 最多可上传几个文件，默认为 100
            maxNumberOfFiles: 10,
            // 选择文件时的类型限制，默认为 ['image/*'] 。如不想限制，则设置为 []
            allowedFileTypes: ['image/*'],
            // 自定义上传参数，例如传递验证的 token 等。参数会被添加到 formData 中，一起上传到服务端。
            meta: {
              // token: 'xxx',
              // otherKey: 'yyy'
              // file:''
            },
            // 将 meta 拼接到 url 参数中，默认 false
            metaWithUrl: false,

            // 自定义增加 http  header
            headers: {
              Authorization: `Bearer ${window.sessionStorage.token}`,
            },

            // 跨域是否传递 cookie ，默认为 false
            //withCredentials: true,

            // 超时时间，默认为 10 秒
            timeout: 30 * 1000,

            // 上传前
            onBeforeUpload(files) {
              return files;
            },
            // 自定义插入图片
            customInsert(res, insertFn) {
              // 因为自定义插入导致onSuccess与onFailed回调函数不起作用,自己手动处理
              // 先关闭等待的Message
              if (res.code !== 200) {
                this.$Message.warning('图片上传失败，请重新尝试');
                return;
              }
              insertFn(res.data.fileUrl, res.data.originalFilename, res.data.fileUrl);
            },
            // 上传进度的回调函数
            onProgress(progress) {
              console.log('progress', progress);
              // progress 是 0-100 的数字
            },
            // 上传错误，或者触发 timeout 超时
            onError(file, err, res) {
              console.log(`${file.name} 上传出错`, err, res);
            },
          },
        },
      },
      mode: 'simple', // or 'simple'
    };
  },
  props: {
    value: {},
  },
  watch: {
    editorHtml() {
      this.$emit('input', this.editorHtml);
    },
    value(val) {
      this.editorHtml = val;
    },
  },
  methods: {
    onCreated(editor) {
      this.editor = Object.seal(editor); // 一定要用 Object.seal() ，否则会报错
    },
  },
  mounted() {},
  beforeDestroy() {
    const editor = this.editor;
    if (editor == null) return;
    editor.destroy(); // 组件销毁时，及时销毁编辑器
  },
};
</script>
<style lang="less" scoped>
[data-theme='dark'] {
  .editor-wrapper {
    @{_deep}.w-e-bar {
      background: #0f2f59 !important;
      .w-e-bar-item {
        &:hover {
          background: 04182a !important;
        }
        .disabled {
          &:hover {
            background: 04182a !important;
          }
        }
        button {
          &:hover {
            background: 04182a !important;
          }
        }
      }
    }
  }
}
[data-theme='light'],
[data-theme='deepBlue'] {
  .editor-wrapper {
    border: 1px solid var(--border-input);
    @{_deep}.w-e-bar {
      background: #f9f9f9 !important;
      .w-e-bar-item {
        &:hover {
          color: var(--color-input);
          background: var(--bg-btn-primary-hover) !important;
        }
        .disabled {
          &:hover {
            background: var(--bg-btn-primary-hover) !important;
          }
        }
        button {
          &:hover {
            color: #fff;
            background: var(--bg-btn-primary-hover) !important;
          }
        }
      }
    }
  }
}

.editor-wrapper {
  display: flex;
  flex-direction: column;
  .editor-message {
    height: 300px;
    @{_deep}.w-e-text-container {
      background: var(--bg-input);
      padding: 0;
      color: var(--color-input);
    }
  }

  @{_deep}.select-button {
    &:hover {
      background: var(--bg-btn-primary-hover) !important;
    }
  }
}
</style>
