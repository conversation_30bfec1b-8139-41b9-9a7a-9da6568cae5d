<template>
  <ui-modal
    v-model="visible"
    :title="!isEdit ? '新增' : '编辑'"
    :r-width="1010"
    @onOk="comfirmHandle"
  >
    <div ref="personnelThematic" class="personnel-thematic-database">
      <Form
        ref="personnelForm"
        :model="personnelForm"
        :rules="ruleInline"
        inline
        class="personnel-form"
      >
        <div class="form-item-title card-border-color">
          <div class="title-line bg-primary"></div>
          <div class="title-text title-color">基本信息</div>
        </div>
        <div class="information-form essential-form">
          <FormItem prop="photoUrlList" class="img-formitem">
            <div class="essential-information-img">
              <div class="avatar-img card-border-color">
                <img
                  v-if="!previewImg"
                  src="@/assets/img/empty-page/null_img_icon.png"
                  class="avatar-null-img"
                  alt=""
                />
                <template v-else>
                  <img
                    v-if="isEdit"
                    v-viewer
                    :src="previewImg.photoUrl || previewImg"
                    alt
                  />
                  <img v-else v-viewer :src="previewImg" alt />
                </template>
              </div>
              <UploadImg
                choosed
                ref="uploadImg"
                :deleted="true"
                :isEdit="isEdit"
                :multipleNum="10"
                :choosedIndex="avatarIndex"
                :defaultList="personnelForm.photoUrlList"
                class="upload-img"
                @on-choose="chooseHandle"
              />
            </div>
          </FormItem>
          <div class="information-body">
            <div class="info-item">
              <FormItem prop="name">
                <div slot="label"><span class="label-text">姓名</span>:</div>
                <Input
                  v-model="personnelForm.name"
                  placeholder="请输入姓名"
                  class="input-200"
                />
              </FormItem>
              <FormItem prop="sex">
                <div slot="label"><span class="label-text">性别</span>:</div>
                <RadioGroup v-model="personnelForm.sex" class="input-200">
                  <Radio label="1">男</Radio>
                  <Radio label="2">女</Radio>
                </RadioGroup>
              </FormItem>
            </div>
            <div class="info-item">
              <FormItem prop="idCardNoType" label="证件类型:">
                <Select
                  v-model="personnelForm.idCardNoType"
                  placeholder="请选择证件类型"
                  class="input-200"
                  filterable
                >
                  <Option
                    v-for="(item, $index) in identityTypeList"
                    :key="$index"
                    :value="item.dataKey"
                    >{{ item.dataValue }}</Option
                  >
                </Select>
              </FormItem>
              <FormItem prop="idCardNo" label="证件号码:">
                <Input
                  v-model="personnelForm.idCardNo"
                  placeholder="请输入证件号码"
                  class="input-200"
                />
                <i
                  class="iconfont icon-sousuo"
                  title="查询"
                  @click="handleSearch"
                />
              </FormItem>
            </div>
            <div class="info-item">
              <FormItem prop="phoneNo" label="联系电话:">
                <Input
                  v-model="personnelForm.phoneNo"
                  placeholder="请输入联系电话"
                  class="input-200"
                />
              </FormItem>
              <FormItem prop="national">
                <div slot="label"><span class="label-text">民族</span>:</div>
                <Select
                  v-model="personnelForm.national"
                  placeholder="请选择民族"
                  class="input-200"
                  filterable
                >
                  <Option
                    v-for="(item, $index) in nationTypeList"
                    :key="$index"
                    :value="item.dataKey"
                    >{{ item.dataValue }}</Option
                  >
                </Select>
              </FormItem>
            </div>
            <div class="info-item">
              <FormItem prop="religious" label="宗教信仰:">
                <Input
                  v-model="personnelForm.religious"
                  placeholder="请输入宗教信仰"
                  class="input-200"
                />
              </FormItem>
              <FormItem prop="nativePlace">
                <div slot="label"><span class="label-text">籍贯</span>:</div>
                <Input
                  v-model="personnelForm.nativePlace"
                  placeholder="请输入籍贯"
                  class="input-200"
                />
              </FormItem>
            </div>
            <div class="info-item">
              <FormItem prop="marriageStatus" label="婚姻状态:">
                <Select
                  v-model="personnelForm.marriageStatus"
                  placeholder="请选择婚姻状态"
                  class="input-200"
                >
                  <Option
                    v-for="(item, $index) in marriageList"
                    :key="$index"
                    :value="item.dataKey"
                    >{{ item.dataValue }}</Option
                  >
                </Select>
              </FormItem>
              <FormItem prop="bloodType">
                <div slot="label"><span class="label-text">血型</span>:</div>
                <Input
                  v-model="personnelForm.bloodType"
                  placeholder="请输入血型"
                  class="input-200"
                />
              </FormItem>
            </div>
            <!-- <div class="info-item">
              <FormItem prop="fugitiveNo" label="在逃编号:">
                <Input v-model="personnelForm.fugitiveNo" placeholder="请输入在逃编号" class="input-200"/>
              </FormItem>
              <FormItem prop="degreeEducation" label="文化程度:">
                <Select v-model="personnelForm.degreeEducation" placeholder="请选择文化程度" class="input-200"></Select>
              </FormItem>
            </div> -->
            <div class="info-item">
              <FormItem prop="professional">
                <div slot="label"><span class="label-text">职位</span>:</div>
                <Input
                  v-model="personnelForm.professional"
                  placeholder="请输入职位"
                  class="input-200"
                />
              </FormItem>

              <FormItem prop="bizLabels">
                <div slot="label">
                  <span class="label-text">{{ labelName }}</span
                  >:
                </div>
                <Select
                  v-model="personnelForm.bizLabels"
                  placeholder="请选择"
                  multiple
                  filterable
                  transfer
                  class="input-200"
                >
                  <OptionGroup
                    v-for="item in faceLibBizList"
                    :label="item.bizTypeName"
                    :key="item.id"
                  >
                    <Option
                      v-for="label in item.labels"
                      :key="label.id"
                      :value="label.id.toString()"
                      >&nbsp;&nbsp;{{ label.labelName }}</Option
                    >
                  </OptionGroup>
                </Select>
              </FormItem>
            </div>
            <div class="info-item" v-if="isCommunity">
              <FormItem prop="communityId">
                <div slot="label"><span class="label-text">区域</span>:</div>
                <ui-treeSelect
                  class="input-520 dialog-input filter-tree"
                  v-model="personnelForm.communityId"
                  filterable
                  check-strictly
                  placeholder="选择区域"
                  show-checkbox
                  transfer
                  :expand-on-click-node="false"
                  node-key="id"
                  :treeData="placeTreeData"
                />
              </FormItem>
            </div>
            <div class="info-item">
              <FormItem prop="registeredResidence" label="户籍地址:">
                <Input
                  v-model="personnelForm.registeredResidence"
                  placeholder="请输入户籍地址"
                  class="input-520"
                />
              </FormItem>
            </div>
            <div class="info-item">
              <FormItem prop="address" label="居住地址:">
                <Input
                  v-model="personnelForm.address"
                  placeholder="请输入居住地址"
                  class="input-520"
                />
              </FormItem>
            </div>
          </div>
        </div>
      </Form>
      <ui-loading v-if="loading"></ui-loading>
    </div>
  </ui-modal>
</template>
<script>
import { mapActions, mapGetters } from "vuex";
import { updateLibPerson, addLibPerson } from "@/api/monographic/juvenile.js";
import UploadImg from "@/components/ui-upload-img-static-library";
import { getBizTypes } from "@/api/data-warehouse.js";
import { queryDeviceInfoPageList } from "@/api/dataGovernance";
import { getConfigPlaces } from "@/api/monographic/community-management";
import UiTreeSelect from "@/components/ui-tree-select";
export default {
  components: {
    UploadImg,
    UiTreeSelect,
  },
  props: {
    isCommunity: {
      type: Boolean,
      default: false,
    },
    // 当前库对象
    currentRow: {
      type: Object,
      default: {},
    },
    // 证件类型字典
    identityTypeList: {
      type: Array,
      default: [],
    },
    // 民族类型字典
    nationTypeList: {
      type: Array,
      default: [],
    },
    noAdd: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    const idCardNo = (rule, value, callback) => {
      if (value === "") {
        callback(new Error("请输入证件号码"));
      }
      // if (!/^(^[1-9]\d{7}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])\d{3}$)|(^[1-9]\d{5}[1-9]\d{3}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])((\d{4})|\d{3}[Xx])$)$/.test(value)) {
      // 	callback(new Error('证件号码格式错误'));
      // }
      if (!/^(^[0-9]*$)|([A-Za-z]+$)/.test(value)) {
        callback(new Error("请输入数字或字母"));
      }
      callback();
    };
    const phoneNo = (rule, value, callback) => {
      if (!/^1(3|4|5|7|8)\d{9}$/.test(value)) {
        callback(new Error("手机号码格式错误"));
      }
      callback();
    };
    return {
      visible: false,
      isEdit: true, //false-新增, true-编辑
      avatarIndex: "",
      personnelForm: {
        imageList: [],
        photoUrlList: [],
        name: "",
        sex: "1",
        idCardNoType: "",
        idCardNo: "",
        phoneNo: "",
        national: "",
        religious: "",
        marriageStatus: "",
        bloodType: "",
        fugitiveNo: "",
        degreeEducation: "",
        professional: "",
        nativePlace: "",
        address: "",
        licensePlate: "",
        licensePlateColor: "",
        vehicleColor: "",
        RFIDNumber: "",
        MACNumber: "",
        IMEINumber: "",
        IMSINumber: "",
        remark: "",
        labels: "",
        bizLabels: [],
        communityId: "",
      },
      previewImg: "",
      ruleInline: {
        name: [{ required: true, message: "请输入姓名", trigger: "blur" }],
        sex: [{ required: true, message: "请选择性别", trigger: "blur" }],
        idCardNoType: [
          { required: true, message: "请选择证件类型", trigger: "change" },
        ],
        idCardNo: [
          { required: true, validator: idCardNo, trigger: "blur" },
          { required: true, message: "请输入证件号码", trigger: "blur" },
        ],
        // phoneNo: [
        // 	{ required: false, message: '请选择证件类型', trigger: 'change' },
        // 	{ validator: phoneNo, trigger: 'change' },
        // ],
      },
      photoChange: false,
      originalPhoto: [],
      loading: false,
      faceLibBizList: [],
      placeTreeData: [],
    };
  },
  computed: {
    ...mapGetters({
      cardTypeList: "dictionary/getCardTypeList", //证件类型
      marriageList: "dictionary/getMarriageList", //婚姻状态
      licensePlateColorList: "dictionary/getLicensePlateColorList", //车牌颜色
      bodyColorList: "dictionary/getBodyColorList", //车辆颜色
    }),
    labelName() {
      return this.isCommunity ? "标签" : "前科类型";
    },
  },
  watch: {
    currentRow: {
      handler(val) {
        if (val?.bizType) {
          this.getBizLabelList(val);
        }
      },
      immediate: true,
    },
  },
  mounted() {
    this.getPlaceList();
  },
  methods: {
    ...mapActions({
      getDictData: "dictionary/getDictAllData",
    }),
    show(isEdit, item) {
      this.isEdit = isEdit;
      this.visible = true;
      this.photoChange = false;
      this.previewImg = "";
      this.avatarIndex = "";
      this.originalPhoto = [];
      this.$nextTick(() => {
        this.$refs.personnelThematic.scrollTop = 0;
        this.$refs.personnelForm.resetFields();
        if (this.isEdit) {
          // 编辑
          this.avatarIndex = 0;
          const info = JSON.parse(JSON.stringify(item));
          const { bizExtend = {}, ...obj } = info;
          this.personnelForm = { ...bizExtend, ...obj };
          this.previewImg = item.photoUrlList ? item.photoUrlList[0] : "";
          this.originalPhoto = item.photoUrlList;
          this.$forceUpdate();
        }
      });
    },
    async getPlaceList() {
      const { data } = await getConfigPlaces();
      const tree = {};
      data?.forEach((item) => {
        const firstLevel = item.firstLevel;
        const secondLevel = item.secondLevel;

        if (!tree[firstLevel]) {
          tree[firstLevel] = {
            id: firstLevel,
            title: item.firstLevelName,
            disabled: true,
            children: {},
          };
        }

        if (!tree[firstLevel].children[secondLevel]) {
          tree[firstLevel].children[secondLevel] = {
            id: secondLevel,
            disabled: true,
            title: item.secondLevelName,
            children: [],
          };
        }
        tree[firstLevel].children[secondLevel].children.push({
          ...item,
          title: item.name,
        });
      });

      const treeArray = Object.values(tree).map((firstLevelNode) => {
        firstLevelNode.children = Object.values(firstLevelNode.children);
        return firstLevelNode;
      });
      this.placeTreeData = treeArray;
    },
    async getBizLabelList(val) {
      const bizType = val?.bizType;
      if (!bizType) return;
      const resp = await getBizTypes({ bizTypeIds: bizType?.split(",") });
      this.faceLibBizList = resp?.data || [];
    },
    // 选择证件照
    chooseHandle(item) {
      this.photoChange = true;
      this.previewImg = item;
    },
    // 确认
    comfirmHandle() {
      this.$refs.personnelForm.validate((valid) => {
        if (!this.previewImg) {
          this.$Message.warning("请上传照片");
          return;
        }
        if (valid) {
          this.personnelForm.faceLibId = this.currentRow.id;
          const bizExtend = {};
          if (this.isCommunity) {
            bizExtend.communityId = this.personnelForm.communityId;
          }
          // 新增
          if (!this.isEdit) {
            let param = { ...this.personnelForm, bizExtend };
            // param.bizLabels = this.currentRow.bizType?.includes("1")
            //   ? param.bizLabel.split(",")
            //   : [];
            if (this.noAdd) {
              this.$emit("resultData", { ...param });
              this.visible = false;
              return;
            }
            addLibPerson(param).then((res) => {
              this.visible = false;
              this.$Message.success("新增成功");
              this.$parent.init();
            });
          } else {
            //编辑
            let param = {};
            if (this.photoChange) {
              this.originalPhoto.forEach((item) => {
                item.delStatus = 2;
              });
              param = {
                ...this.personnelForm,
                bizExtend,
                photoUrlList: [
                  { ...this.personnelForm.photoUrlList[0], delStatus: 1 },
                  ...this.originalPhoto,
                ],
              };
            } else {
              param = {
                ...this.personnelForm,
                bizExtend,
              };
            }
            // param.bizLabels = this.currentRow.bizType?.includes("1")
            //   ? param.bizLabel.split(",")
            //   : [];
            if (this.noAdd) {
              this.$emit("resultData", { ...param });
              this.visible = false;
              return;
            }
            updateLibPerson(param).then((res) => {
              this.visible = false;
              this.$Message.success("编辑成功");
              this.$parent.init();
            });
          }
        }
      });
    },
    handleSearch() {
      if (!this.personnelForm.idCardNo) return;
      this.loading = true;
      let params = {
        idCardNo: this.personnelForm.idCardNo,
        idCardNoExactQuery: true,
      };
      queryDeviceInfoPageList(params)
        .then((res) => {
          let item = res.data.entities[0] || {};
          let {
            idCardNoType,
            name,
            sex,
            phoneNo,
            national,
            religious,
            nativePlace,
            marriageStatus,
            bloodType,
            professional,
            registeredResidence,
            address,
            photoUrlList,
          } = item;
          this.personnelForm = {
            ...this.personnelForm,
            idCardNoType,
            name,
            sex,
            phoneNo,
            national,
            religious,
            nativePlace,
            marriageStatus,
            bloodType,
            professional,
            registeredResidence,
            address,
            photoUrlList: photoUrlList.map((v) => v.photoUrl),
          };
          this.previewImg = photoUrlList[0].photoUrl || "";
        })
        .catch((err) => {
          console.error(err);
        })
        .finally(() => {
          this.loading = false;
        });
    },
  },
};
</script>
<style lang="less" scoped>
.input-200 {
  width: 200px;
}
.input-520 {
  width: 520px;
}
/deep/ .ivu-modal-body {
  padding: 0 !important;
}
.personnel-thematic-database {
  overflow-x: hidden;
  overflow-y: auto;
  padding: 20px;
  max-height: 680px;
  .personnel-form {
    padding: 0 30px;
    box-sizing: border-box;
    .form-item-title {
      display: flex;
      align-items: center;
      padding-bottom: 4px;
      border-bottom: 1px solid #fff;
      .title-line {
        width: 3px;
        height: 16px;
        margin-right: 6px;
      }
      .title-text {
        font-size: 14px;
        line-height: 20px;
        font-weight: bold;
        font-family: "MicrosoftYaHei-Bold";
      }
    }
    .information-form {
      display: flex;
      justify-content: space-between;
      margin: 20px 0 30px 0;
      .essential-information-img {
        width: 240px;
        margin-right: 64px;
        display: flex;
        flex-direction: column;
        .avatar-img {
          width: 240px;
          height: 318px;
          border: 1px solid #fff;
          & > img {
            width: 100%;
            height: 100%;
            object-fit: contain;
            cursor: pointer;
          }
          .avatar-null-img {
            cursor: unset;
          }
        }
        .upload-img {
          margin-top: 10px;
          justify-content: flex-start;
          /deep/ .upload-item {
            width: 54px;
            height: 54px;
            .ivu-icon-ios-add {
              font-size: 30px;
              font-weight: bold;
            }
            .upload-text {
              line-height: 18px;
              display: none;
            }
          }
        }
      }
      .information-body {
        flex: 1;
        .info-item {
          display: flex;
          justify-content: space-between;
          /deep/ .ivu-form-item {
            display: inline-flex;
            margin-right: 0;
            margin-bottom: 10px;
            .ivu-form-item-label {
              display: flex;
              align-items: center;
              justify-content: end;
              padding-right: 10px;
              white-space: nowrap;
              .label-text {
                text-align-last: justify;
                display: inline-block;
              }
            }
            .ivu-form-item-label::before {
              margin: 0;
            }
            .ivu-radio-wrapper {
              margin-right: 30px;
            }
            .icon-sousuo {
              position: absolute;
              cursor: pointer;
              margin-left: 5px;
              color: #2c86f8;
            }
          }
        }
      }
    }
    .essential-form {
      margin-bottom: 0;
      /deep/ .ivu-form-item {
        .ivu-form-item-label {
          width: 80px !important;
          .label-text {
            width: 58px;
          }
        }
      }
      .img-formitem {
        margin: 0 !important;
      }
    }
    .vehicle-form {
      margin: 20px 0;
      /deep/ .ivu-form-item {
        .ivu-form-item-label {
          width: 74px;
        }
      }
      .info-item {
        justify-content: unset !important;
        .license-plate-number {
          margin-right: 36px !important;
        }
        .license-plate-color {
          margin-right: 46px !important;
        }
      }
    }
    .other-form {
      margin: 20px 0;
      /deep/ .ivu-form-item {
        .ivu-form-item-label {
          width: 74px;
          .label-text {
            width: 58px;
          }
        }
      }
      .info-item {
        justify-content: unset !important;
        .rfid-number {
          margin-right: 36px !important;
        }
        .mac-number {
          margin-right: 46px !important;
        }
      }
    }
  }
}
/deep/ .ivu-form-item-error-tip {
  z-index: 9;
}
</style>
