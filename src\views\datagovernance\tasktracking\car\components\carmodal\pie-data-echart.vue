<template>
  <div style="width: 500px; height: 180px" ref="echart2"></div>
</template>
<script>
export default {
  name: 'tasktracking',
  props: {
    pieMoreEchartData: {
      type: Object,
      default() {},
    },
  },
  data() {
    return {};
  },
  mounted() {
    // this.$nextTick(() => {
    //   this.init();
    // })
  },
  watch: {
    pieMoreEchartData: {
      deep: true,
      handler() {
        this.init();
      },
    },
  },
  methods: {
    init() {
      const that = this;
      let myChart = this.$echarts.init(this.$refs.echart2);
      var option = {
        tooltip: {
          trigger: 'item',
        },
        color: [
          {
            type: 'linear',
            x: 1,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: '#05CE98', // 0% 处的颜色
              },
              {
                offset: 1,
                color: '#32A19E', // 100% 处的颜色
              },
            ],
          },
          {
            x: 1,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: '#C02E02', // 0% 处的颜色
              },
              {
                offset: 1,
                color: '#4D1F08', // 100% 处的颜色
              },
            ],
          },
        ],
        legend: {
          orient: 'vertical',
          right: '20',
          top: '30%',
          textStyle: {
            fontSize: 12,
            color: '#fff',
          },
          formatter: function (name) {
            var target;
            that.pieMoreEchartData.data.map((val) => {
              if (val.name === name) {
                target = val.value;
              }
            });
            return name + ' ' + target;
          },
        },
        grid: {
          left: '10%',
          right: '50%',
          bottom: '10%',
          top: '10%',
          containLabel: true,
        },
        title: {
          text: this.pieMoreEchartData.title,
          x: 'center',
          y: 'center',
          textStyle: {
            fontSize: 12,
            color: '#fff',
          },
          subtext: String(that.pieMoreEchartData.count),
          subtextStyle: {
            fontSize: 12,
            color: '#19C176',
          },
        },
        series: [
          {
            name: '',
            type: 'pie',
            radius: ['60%', '80%'],
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: 10,
              borderColor: '#fff',
              borderWidth: 0,
            },
            labelLine: {
              show: false,
            },
            label: {
              show: false,
            },
            data: this.pieMoreEchartData.data,
          },
        ],
      };

      myChart.setOption(option, true);
      // window.onresize = function(){
      //   myChart.resize();
      // }
    },
    resizeFn() {
      this.init();
      // let myChart = this.$echarts.init(this.$refs.echart2);
      // myChart.resize();
    },
  },
  components: {},
};
</script>
<style lang="less" scoped>
.echart {
  position: absolute;
  width: 100%;
  height: 100%;
}
</style>
