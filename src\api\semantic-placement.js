import request from "@/libs/request";
import { compareService } from "./Microservice";

// 语义布控任务管理

// 语义布控任务新增
export function addLLMCompareTask(data) {
  return request({
    url: compareService + "/llm/compare/task/add",
    method: "post",
    data,
  });
}

// 文件任务按照任务下载资源
export function downloadFileByTaskId(taskId) {
  return request({
    url: compareService + `/llm/compare/task/downloadFileByTaskId/${taskId}`,
    method: "post",
    responseType: "blob",
  });
}

// 语义布控任务编辑
export function modifyLLMCompareTask(data) {
  return request({
    url: compareService + `/llm/compare/task/modify`,
    method: "put",
    data,
  });
}

// 查询语义布控任务分页列表
export function getLLMCompareTaskPageList(data) {
  return request({
    url: compareService + `/llm/compare/task/pageList`,
    method: "post",
    data,
  });
}

// 语义布控任务删除
export function removeLLMCompareTask(ids) {
  return request({
    url: compareService + `/llm/compare/task/remove/${ids}`,
    method: "delete",
  });
}

// 删除任务中的资源
export function removeTaskResourceLLMCompareTask(ids) {
  return request({
    url: compareService + `/llm/compare/task/removeTaskResource/${ids}`,
    method: "delete",
  });
}

// 开始任务
export function startLLMCompareTask(ids) {
  return request({
    url: compareService + `/llm/compare/task/start/${ids}`,
    method: "put",
  });
}

// 停止任务
export function stopLLMCompareTask(ids) {
  return request({
    url: compareService + `/llm/compare/task/stop/${ids}`,
    method: "put",
  });
}

// 获取语义布控任务详细信息
export function getLLMCompareTaskView(id) {
  return request({
    url: compareService + `/llm/compare/task/view/${id}`,
    method: "get",
  });
}

// 分页查询语义布控告警列表
export function alarmPageList(data) {
  return request({
    url: compareService + `/llm/compare/alarm/pageList`,
    method: "post",
    data,
  });
}
// 批量处置告警记录
export function alarmBatchHandle(data) {
  return request({
    url: compareService + `/llm/compare/alarm/batchHandle`,
    method: "post",
    data,
  });
}
// 查询报警处置信息
export function getAlarmHandleList(alarmId) {
  return request({
    url: compareService + `/llm/compare/alarm/getAlarmHandleList/${alarmId}`,
    method: "get",
  });
}

// 语义布控算法新增
export function addLLMCompareAlgorithm(data) {
  return request({
    url: compareService + `/llm/compare/algorithm/add`,
    method: "post",
    data,
  });
}

// 语义布控算法编辑
export function modifyLLMCompareAlgorithm(data) {
  return request({
    url: compareService + `/llm/compare/algorithm/modify`,
    method: "put",
    data,
  });
}

// 分页查询语义布控算法列表
export function getLLMCompareAlgorithmPageList(data) {
  return request({
    url: compareService + `/llm/compare/algorithm/pageList`,
    method: "post",
    data,
  });
}

// 语义布控算法删除
export function removeLLMCompareAlgorithmPageList(ids) {
  return request({
    url: compareService + `/llm/compare/algorithm/remove/${ids}`,
    method: "delete",
  });
}

// 获取基础模型拉列表
export function selectBaseModelList() {
  return request({
    url: compareService + `/llm/compare/algorithm/selectBaseModelList`,
    method: "get",
  });
}

// 获取算法下拉列表
export function getLLMCompareAlgorithm(data) {
  return request({
    url: compareService + `/llm/compare/algorithm/selectList`,
    method: "post",
    data,
  });
}

// 文件资源新增
export function addLLMCompareResource(data) {
  return request({
    url: compareService + `/llm/compare/resource/add`,
    method: "post",
    data,
  });
}

// 文件资源批量新增
export function batchAddLLMCompareResource(data) {
  return request({
    url: compareService + `/llm/compare/resource/batchAdd`,
    method: "post",
    data,
  });
}

// 视频资源帧信息分页查询
export function getResourceFrameList(data) {
  return request({
    url: compareService + `/llm/compare/resource/getResourceFrameList`,
    method: "post",
    data,
  });
}

// 文件资源编辑
export function modifyLLMCompareResource(data) {
  return request({
    url: compareService + `/llm/compare/resource/modify`,
    method: "put",
    data,
  });
}

// 分页查询文件资源列表
export function getLLMCompareResourcePageList(data) {
  return request({
    url: compareService + `/llm/compare/resource/pageList`,
    method: "post",
    data,
  });
}

// 文件资源删除
export function removeLLMCompareResource(ids) {
  return request({
    url: compareService + `/llm/compare/resource/remove/${ids}`,
    method: "delete",
  });
}
