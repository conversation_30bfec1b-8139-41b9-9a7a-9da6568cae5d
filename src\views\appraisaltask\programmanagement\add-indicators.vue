<template>
  <div>
    <ui-modal class="add-indicators-modal" :title="modalAction.title" v-model="visible" :styles="styles" @query="query">
      <div class="indicator_frame">
        <div class="fl left_con">
          <div class="icon_header">
            <p class="fl base-text-color f-14">
              指标列表（
              <span class="color-failed f-14">{{ tableData && tableData.length }}</span>
              &nbsp;条）
            </p>
          </div>
          <ui-card
            ref="uiCard"
            :data="indexTypeList"
            v-model="activeIndex"
            @on-change="handleClickCard"
            :countShow="true"
          ></ui-card>
        </div>
        <div class="fr right_con">
          <div class="over-flow icon_header flex-row mb-sm mt-sm">
            <div class="search-box d_flex">
              <ui-label label="关键词:">
                <Input
                  v-model="searchData.searchValue"
                  class="width-md"
                  placeholder="请输入关键词"
                  clearable
                  @on-enter="handleSearch"
                >
                </Input>
              </ui-label>
              <Button type="primary" @click="handleSearch" class="ml-lg">查询</Button>
              <Button @click="handleReset" class="ml-sm">重置</Button>
            </div>
            <p class="base-text-color f-14">
              已选
              <span class="color-failed f-14">{{ multipleSelection.length || 0 }}</span>
              个指标
            </p>
          </div>
          <el-table
            ref="elTable"
            :data="showTableList"
            row-key="id"
            class="reset-el-table"
            height="450"
            stripe
            @selection-change="handleSelectionChange"
          >
            <el-table-column reserve-selection type="selection" width="55"> </el-table-column>
            <el-table-column label="序号" type="index" width="50"> </el-table-column>
            <el-table-column prop="indexName" label="指标名称" width="300">
              <template #default="{ row }">
                <Tooltip max-width="400" :content="row.indexName">
                  <div class="text_hid">{{ row.indexName }}</div>
                </Tooltip>
              </template>
            </el-table-column>
            <el-table-column prop="dataTargetValue" label="达标值" width="100"> </el-table-column>
            <el-table-column prop="indexDefinition" label="计算方法" width="400">
              <template #default="{ row }">
                <Tooltip max-width="400" :content="row.indexDefinition">
                  <div class="text_hid">{{ row.indexDefinition }}</div>
                </Tooltip>
              </template>
            </el-table-column>
            <el-table-column prop="evaluationCriterion" label="评价标准">
              <template #default="{ row }">
                <Tooltip max-width="400" :content="row.evaluationCriterion">
                  <div class="text_hid">{{ row.evaluationCriterion }}</div>
                </Tooltip>
              </template>
            </el-table-column>
          </el-table>
          <loading v-if="loading"></loading>
        </div>
      </div>
    </ui-modal>
  </div>
</template>

<script>
import governanceevaluation from '@/config/api/governanceevaluation';
import { mapGetters } from 'vuex';
export default {
  props: {
    value: {
      required: true,
      type: Boolean,
    },
    modalAction: {},
    modalId: {},
    index_data: {},
    itemList: {},
  },
  data() {
    return {
      multipleSelection: [],
      visible: false,
      saveModalLoading: false,
      styles: {
        width: '9rem',
      },
      tableData: [],
      activeIndex: 'all',
      checkList: {},
      loading: false,
      tableList: [],
      searchData: {
        searchValue: '',
      },
      showTableList: [],
    };
  },
  inject: ['programThis'],
  methods: {
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    //切换指标
    handleClickCard(row) {
      this.searchData.searchValue = '';
      this.tableList = this.tableData.filter((item) => {
        if (row.id === 'all') {
          return true;
        } else {
          return item.indexModule == row.id;
        }
      });
      this.showTableList = this.tableList;
    },
    async getindexShow() {
      let params = {
        schemeId: this.$parent.activeModuleMessageId,
      };
      // if (!!this.activeIndex) {
      //   params.indexModule = this.activeIndex
      // }
      try {
        this.loading = true;
        let {
          data: { data },
        } = await this.$http.get(governanceevaluation.getPageOptionalIndexes, { params });
        this.tableData = data;
      } catch (err) {
        console.log(err);
      } finally {
        this.loading = false;
      }
    },
    async query() {
      try {
        this.saveModalLoading = true;
        if (!this.multipleSelection.length) {
          return this.$Message.error('请选择指标');
        }
        let indexIds = this.multipleSelection.map((item) => item.id);
        let checkList = {
          id: this.$parent.activeModuleMessageId,
          version: this.$parent.activeVersion,
          indexIds,
        };
        let { data } = await this.$http.post(governanceevaluation.getContingencyIndexes, checkList);
        this.$Message.success(data.msg);
        this.visible = false;
        this.programThis.refreshSchemeCount();
        this.$emit('init');
      } catch (err) {
        console.log(err);
      } finally {
        this.saveModalLoading = false;
      }
    },
    //匹配条件检索指标
    async handleSearch() {
      //按指标名称、指标ID、显示名称、名称简写检索指标
      const matchProperty = ['indexName', 'indexActualName', 'indexId', 'id'];
      let searchvalue = this.searchData.searchValue.trim();
      if (searchvalue !== '') {
        this.showTableList = this.tableList.filter((item) => {
          return matchProperty.some((key) => item[key]?.toString().includes(searchvalue));
        });
      } else {
        this.showTableList = this.tableList;
      }
    },
    handleReset() {
      this.searchData.searchValue = '';
      this.handleSearch();
    },
  },
  watch: {
    visible(val) {
      this.$emit('input', val);
    },
    async value(val) {
      this.visible = val;
      if (val) {
        this.$refs.elTable.clearSelection();
        await this.getindexShow();
        await this.handleClickCard(
          this.indexTypeList.find((item) => item.id == this.activeIndex) || this.indexTypeList[0],
        );
      }
    },
  },
  computed: {
    ...mapGetters({
      algorithmVendorType: 'algorithm/algorithmVendorType',
    }),
    indexTypeList() {
      this.global.indexTypeList.map((it) => {
        let list = this.tableData.filter((item) => {
          return item.indexModule == it.id;
        });
        it.count = list.length;
      });
      let all = [{ id: 'all', title: '全部指标', icon: 'icon-quanbu', count: this.tableData.length }];
      return [...all, ...this.global.indexTypeList];
    },
  },
  components: {
    uiCard: require('@/views/appraisaltask/components/ui-card.vue').default,
  },
};
</script>
<style lang="less" scoped>
.text_hid {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
}
.indicator_frame {
  display: inline-block;
  border-top: 1px solid var(--border-modal-footer);
  width: 100%;
  height: 100%;
  .icon_header {
    height: 50px;
    line-height: 50px;
  }
  .left_con {
    display: inline-block;
    width: 300px;
    height: 100%;
    border-right: 1px solid var(--border-modal-footer);

    padding: 0 20px;
  }
  .right_con {
    width: calc(~'100% - 300px');
    height: 100%;
    padding: 0 20px;
    position: relative;
    display: flex;
    flex-direction: column;
    .page {
      width: 100%;
      height: 50px;
    }
    .reset-el-table {
      @{_deep}.el-table__header {
        .el-table-column--selection {
          // .cell {
          //   padding-left: 0.072917rem;
          //   padding-right: 0.072917rem;
          // }
        }
        .is-leaf {
          border-bottom: none !important;
        }
      }
    }
  }
  .tab-indicators {
    .ivu-select-input {
      height: 30px !important;
      line-height: 30px !important;
    }
    .ivu-input {
      height: 30px !important;
    }
  }
}
.add-indicators-modal {
  @{_deep} .ivu-modal-body {
    padding: 0;
    height: 650px;
    margin-top: 20px;
  }
}
.ml-65 {
  margin-left: 65px;
}
</style>
