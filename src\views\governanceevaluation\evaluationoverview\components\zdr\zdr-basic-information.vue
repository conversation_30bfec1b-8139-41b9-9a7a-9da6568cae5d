<template>
  <!-- 基础信息填报准确率 -->
  <div class="basic-information" ref="contentScroll">
    <div class="information-header">
      <statistics class="statistics" :statistics-list="statisticsList" :isflexfix="false"> </statistics>
      <div class="information-echart">
        <div class="echart-box" v-ui-loading="{ loading: echartsLoading, tableData: echartData }">
          <draw-echarts
            v-if="echartData.length != 0"
            :echart-option="determinantEchart"
            :echart-style="ringStyle"
            ref="attributeChart"
            class="charts"
          ></draw-echarts>
          <span class="next-echart" v-if="echartData.length > 10">
            <i class="icon-font icon-youjiantou1 f-12" @click="scrollRight('attributeChart', echartData, [], 10)"></i>
          </span>
        </div>
      </div>
    </div>
    <div class="information-main">
      <div class="abnormal-title">
        <div class="fl">
          <i class="icon-font icon-jiancejieguoxiangqing f-16 color-filter"></i>
          <span class="f-16 color-filter ml-sm">异常数据列表</span>
        </div>
      </div>
      <div class="file-model">
        <div>
          <ui-label class="inline" label="姓 名" :width="50">
            <Input v-model="searchData.customParameters.name" class="width-md" placeholder="请输入姓名"></Input>
          </ui-label>
          <ui-label class="inline ml-lg" label="证 件 号" :width="60">
            <Input v-model="searchData.customParameters.idCard" class="width-md" placeholder="请输入证件号"></Input>
          </ui-label>
          <ui-label class="inline" label=" " :width="30">
            <Button type="primary" class="mr-sm" @click="startSearch">查询</Button>
            <Button type="default" class="mr-lg" @click="reset()">重置</Button>
          </ui-label>
        </div>
        <div class="inline">
          <Button type="primary" class="btn_search" :loading="exportLoading" @click="getExport">
            <span class="inline ml-xs">导出</span>
          </Button>
        </div>
      </div>
      <!-- <select-tabs class="list_item" :list="selectTabs" @selectInfo="selectInfo"></select-tabs> -->
      <ui-select-tabs class="tabs-ui" @selectInfo="selectInfo" :list="selectTabs"> </ui-select-tabs>
      <div class="list">
        <ui-table
          :maxHeight="contentClientHeight"
          :table-columns="tableColumns"
          :table-data="tableData"
          :loading="loading"
        >
          <template #photo="{ row }">
            <div @click="viewBig(row)" class="ui-images">
              <ui-image :src="row.photo" />
            </div>
          </template>
          <template #option="{ row }">
            <ui-btn-tip
              icon="icon-chakanyichangxiangqing"
              content="不合格原因"
              @click.native="checkReason(row)"
            ></ui-btn-tip>
          </template>
        </ui-table>
        <loading v-if="loading"></loading>
      </div>
      <ui-page class="page" :page-data="searchData" @changePage="changePage" @changePageSize="changePageSize"></ui-page>
    </div>
    <look-scene v-model="bigPictureShow" :img-list="imgList"></look-scene>
    <nonconformance
      ref="nonconformance"
      title="查看不合格原因"
      :tableColumns="reasonTableColumns"
      :tableData="reasonTableData"
      :reasonPage="reasonPage"
      :reasonLoading="reasonLoading"
      :isPage="false"
      @handlePageChange="handlePageChange"
      @handlePageSizeChange="handlePageSizeChange"
    ></nonconformance>
  </div>
</template>

<style lang="less" scoped>
.basic-information {
  position: relative;
  overflow-y: auto;
  &::-webkit-scrollbar {
    display: none; /* Chrome Safari */
  }
  @{_deep} .ivu-table {
    &-tip,
    &-overflowX,
    &-body {
      //min-height: 380px !important;
      min-height: calc(100vh - 698px) !important;
    }
  }
  .information-header {
    margin-top: 10px;
    height: 252px;
    display: flex;
    .information-statistics {
      display: flex;
      width: 765px;
      height: 100%;
      background: var(--bg-sub-content);
      margin-right: 10px;
      padding: 20px 5px 0 15px;

      li {
        position: relative;
        &:nth-child(2) {
          margin-right: 0;
        }
      }
    }
    .information-echart {
      display: inline-block;
      width: calc(100% - 760px);
      height: 100%;
      background: var(--bg-sub-content);
      .echart-box {
        width: 100%;
        height: 100%;
        .charts {
          width: 98% !important;
          height: 100% !important;
        }
      }
      .next-echart {
        width: 24px;
        height: 24px;
        background: #02162b;
        border: 1px solid #10457e;
        opacity: 1;
        border-radius: 4px;
        top: 120px;
        right: 5px;
        position: absolute;
        text-align: center;
        line-height: 24px;

        .icon-youjiantou1 {
          color: var(--color-primary);
          font-size: 12px;
          vertical-align: top !important;
        }
        &:active {
          width: 24px;
          height: 24px;
          background: #02162b;
          border: 1px solid var(--color-primary);
          opacity: 1;
          border-radius: 4px;
          .icon-youjiantou1 {
            color: #4e9ef2;
            font-size: 12px;
            vertical-align: top !important;
          }
        }
        &:hover {
          width: 24px;
          height: 24px;
          background: #02162b;
          border: 1px solid #146ac7;
          opacity: 1;
          border-radius: 4px;
          .icon-youjiantou1 {
            color: var(--color-primary);
            font-size: 12px;
            vertical-align: top !important;
          }
        }
      }
    }
  }
  .information-main {
    //height: calc(100% - 252px);
    .abnormal-title {
      height: 48px;
      line-height: 48px;
      .color-filter {
        color: rgba(43, 132, 226, 1);
        vertical-align: middle;
      }
    }
    .file-model {
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 100%;
      height: 58px;
      border-top: 1px solid var(--devider-line);
    }

    .list {
      margin-top: 10px;
      .no-data {
        position: absolute;
        top: 50%;
        left: 50%;
      }
      .ui-images {
        width: 56px !important;
        height: 56px !important;
      }
    }
  }
  .export {
    @{_deep}.ivu-select-dropdown {
      position: absolute !important;
      left: 1647px !important;
      top: 364px !important;
    }
  }
}
</style>

<script>
import evaluationoverview from '@/config/api/evaluationoverview';
import { mapGetters } from 'vuex';
import dataZoom from '@/mixins/data-zoom';
import downLoadTips from '@/mixins/download-tips';
export default {
  name: 'basic-information',
  mixins: [dataZoom, downLoadTips],
  data() {
    return {
      ringStyle: {
        width: '96%',
        height: '250px',
      },
      determinantEchart: {},
      echartData: [],
      taskType: '',
      statisticsList: [
        {
          name: 'ZRD总量',
          value: 0,
          icon: 'icon-ZRDzongliang',
          iconColor: 'icon-bg1',
          liBg: 'li-bg1',
          type: 'number',
          textColor: 'color1',
          key: 'total',
        },
        {
          name: '实际检测ZDR数量',
          value: 0,
          icon: 'icon-shijijianceZDRshuliang',
          iconColor: 'icon-bg2',
          liBg: 'li-bg2',
          type: 'number',
          textColor: 'color2',
          key: 'actualDetectionCount',
        },
        {
          name: '抓拍数量合格设备',
          value: 0,
          icon: 'icon-xinxihegeZDRshu',
          iconColor: 'icon-bg3',
          liBg: 'li-bg3',
          type: 'number',
          textColor: 'color3',
          key: 'qualifiedCount',
        },
        {
          name: '抓拍数量异常设备',
          value: 0,
          icon: 'icon-xinxibuhegeZDRshu',
          iconColor: 'icon-bg4',
          liBg: 'li-bg4',
          type: 'number',
          textColor: 'color4',
          key: 'unQualifiedCount',
        },
        {
          name: '基础信息填报准确率',
          value: 0,
          icon: 'icon-jichuxinxitianbaozhunqueshuai',
          iconColor: 'icon-bg5',
          liBg: 'li-bg5',
          type: 'percentage',
          textColor: 'color5',
          key: 'resultValue',
        },
      ],
      loading: false,
      tableColumns: [
        { type: 'index', width: 70, title: '序号', align: 'center' },
        {
          title: '人员照片',
          key: 'photo',
          slot: 'photo',
          minWidth: 150,
          tooltip: true,
        },
        { title: '姓名', key: 'name', minWidth: 150, tooltip: true },
        { title: '性别', key: 'gender', minWidth: 150, tooltip: true },
        { title: '民族', key: 'nation', minWidth: 150, tooltip: true },
        { title: '证件类型', key: 'cardType', minWidth: 150, tooltip: true },
        { title: '证件号', key: 'idCard', minWidth: 150, tooltip: true },
        { title: '重点人员类型', key: 'type', minWidth: 150, tooltip: true },
        { title: '居住地址', key: 'homeAddress', minWidth: 150, tooltip: true },
        {
          title: '操作',
          slot: 'option',
          fixed: 'right',
          width: 100,
          align: 'center',
        },
      ],
      tableData: [],
      searchData: {
        totalCount: 0,
        pageNum: 1,
        pageSize: 20,
        customParameters: { errorMessages: [], name: '', idCard: '' },
      },
      reasonTableColumns: [
        // { type: "selection", width: 70 },
        { type: 'index', width: 50, title: '序号', align: 'center' },
        { title: '不合格原因', key: 'errorMessage' },
        { title: '不合格字段', key: 'propertyName' },
        { title: '检测规则', key: 'detectionName' },
        { title: '实际结果', key: 'propertyValue' },
      ],
      reasonTableData: [],
      reasonPage: { totalCount: 0, pageNum: 1, pageSize: 20 },
      exportLoading: false,
      reasonLoading: false,
      selectTabs: [],
      statisticalList: {},
      paramsList: {},
      exportName: '',
      imgList: [require('@/assets/img/navigation-page/systemmanagement.png')],
      bigPictureShow: false,
      deviceInfoId: '',
      contentClientHeight: 0,
      echartsLoading: false,
    };
  },

  mounted() {
    const proportion = window.screen.height / 1080;
    const clientHeight = this.$refs.contentScroll.clientHeight;
    this.contentClientHeight = clientHeight > 0 ? clientHeight - 175 * proportion : 0;
  },
  filters: {
    separateNum(str) {
      str = String(str);
      let counter = 0;
      let result = '';
      for (let j = str.length - 1; j >= 0; j--) {
        counter++;
        result = str.charAt(j) + result;
        if (!(counter % 3) && j != 0) {
          result = ',' + result;
        }
      }

      return result;
    },
  },
  methods: {
    viewBig(item) {
      this.imgList = [item.identityPhoto];
      this.bigPictureShow = true;
    },

    async getExport() {
      this.exportLoading = true;
      let params = {
        indexId: this.paramsList.indexId,
        batchId: this.paramsList.batchId,
        access: this.paramsList.access,
        displayType: this.paramsList.displayType,
        orgRegionCode: this.paramsList.orgRegionCode,
        customParameters: this.searchData.customParameters,
      };
      try {
        this.$_openDownloadTip();
        const res = await this.$http.post(evaluationoverview.exportDeviceDetailData, params);
        await this.$util.common.transformBlob(res.data.data);
        this.exportLoading = false;
      } catch (err) {
        console.log(err);
      }
    },
    startSearch() {
      this.searchData.pageNum = 1;
      this.getTableData();
    },

    // 表格
    async getTableData() {
      try {
        this.loading = true;
        this.tableData = [];
        let params = {
          indexId: this.paramsList.indexId,
          batchId: this.paramsList.batchId,
          access: this.paramsList.access,
          displayType: this.paramsList.displayType,
          orgRegionCode: this.paramsList.orgRegionCode,
          customParameters: this.searchData.customParameters,
          pageSize: this.searchData.pageSize,
          pageNumber: this.searchData.pageNum,
        };
        let res = await this.$http.post(evaluationoverview.getDetailData, params);
        this.copySearchDataMx(this.searchData);
        const datas = res.data.data;
        this.tableData = datas.entities || [];
        this.searchData.totalCount = datas.total;
        this.loading = false;
      } catch (error) {
        console.log(error);
        this.loading = false;
      } finally {
        this.loading = false;
      }
    },
    reset() {
      this.searchData.pageNum = 1;
      this.searchData.customParameters.name = '';
      this.searchData.customParameters.idCard = '';
      this.getTableData();
      this.selectInfo([]);
      this.searchData.customParameters.errorMessages = [];
    },
    // 异常列表
    async getSelectTabs() {
      try {
        let params = {
          indexId: this.paramsList.indexId,
          batchId: this.paramsList.batchId,
          access: this.paramsList.access,
          displayType: this.paramsList.displayType,
          orgRegionCode: this.paramsList.orgRegionCode,
        };
        let res = await this.$http.post(evaluationoverview.getAbnormalLabel, params);
        this.selectTabs = res.data.data.map((item) => {
          let obj = {};
          obj.name = item;
          return obj;
        });
      } catch (error) {
        console.log(error);
      }
    },

    changePage(val) {
      this.searchData.pageNum = val;
      this.getTableData();
    },
    changePageSize(val) {
      this.searchData.pageSize = val;
      this.getTableData();
    },
    // 不合格原因
    checkReason(row) {
      this.deviceInfoId = row.id;
      this.reasonTableData = row.detailVos || [];
      this.$refs.nonconformance.init();
    },
    handlePageChange(val) {
      this.reasonPage.pageNum = val;
      this.getReason();
    },
    handlePageSizeChange(val) {
      this.reasonPage.pageSize = val;
      this.getReason();
    },

    selectInfo(val) {
      this.searchData.customParameters.errorMessages = val.map((item) => {
        return item.name;
      });
      this.getTableData();
    },
    // 柱状图统计

    async getGraphsInfo() {
      this.echartsLoading = true;
      let data = {
        indexId: this.paramsList.indexId,
        batchId: this.paramsList.batchId,
        access: this.paramsList.access,
        displayType: this.paramsList.displayType,
        orgRegionCode: this.paramsList.orgRegionCode,
      };
      try {
        let res = await this.$http.post(evaluationoverview.getGraphsInfo, data);
        this.echartData = res.data.data;
        this.initRing();
        this.echartsLoading = false;
      } catch (err) {
        console.log(err);
        this.echartsLoading = false;
      }
    },

    initRing() {
      this.barData = this.echartData.map((row) => {
        return {
          propertyColumn: row.propertyName,
          value: row.count,
          deviceRate: row.proportion || '0%',
        };
      });
      let opts = {
        xAxis: this.echartData.map((row) => row.propertyName),
        data: this.barData,
      };
      this.determinantEchart = this.$util.doEcharts.overviewColumn(opts);
      if (this.echartData.length != 0) {
        setTimeout(() => {
          this.setDataZoom('attributeChart', [], 10);
        });
      }
    },
    statistical() {
      this.statisticsList.map((val) => {
        val.value = this.statisticalList[val.key] || 0;
        if (val.key === 'resultValue' && this.statisticalList.qualified === '1') {
          val.qualified = true;
        } else if (val.key === 'resultValue' && this.statisticalList.qualified !== '1') {
          val.qualified = false;
        }
      });
    },
  },
  computed: {
    ...mapGetters({
      comprehensiveConfig: 'common/getComprehensiveConfig',
    }),
  },
  props: {
    paramsData: {
      type: Object,
      default: () => {},
    },
    statisticalData: {
      type: Object,
      default: () => {},
    },
  },
  watch: {
    statisticalData: {
      handler(val) {
        if (val) {
          this.statisticalList = val;
          this.statistical();
        }
      },
      deep: true,
      immediate: true,
    },

    paramsData: {
      handler(val) {
        if (val.indexId) {
          this.paramsList = val;
          this.getTableData(); //表格
          this.getSelectTabs();
          this.getGraphsInfo(); //柱状图
        }
      },
      deep: true,
      immediate: true,
    },
  },

  components: {
    statistics: require('@/components/icon-statistics').default,
    UiTable: require('@/components/ui-table.vue').default,
    DrawEcharts: require('@/components/draw-echarts').default,
    // selectTabs: require('@/views/governanceevaluation/evaluationoverview/components/basics/select-tabs.vue')
    //   .default,
    UiSelectTabs: require('@/components/ui-select-tabs.vue').default,
    nonconformance: require('@/views/appraisaltask/detectiontask/evaluationmanagement/components/nonconformance.vue')
      .default,
  },
};
</script>
