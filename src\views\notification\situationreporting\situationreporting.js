import global from '@/util/global';
const tabListArr = [
  {
    text: '系统报备',
    type: '1',
    permission: 'report',
  },
  {
    text: '报备审核',
    type: '2',
    permission: 'examine',
  },
];
const statisticsList = [
  {
    name: '报备总数',
    key: 'count',
    count: 0,
    icon: 'icon-baobeizongshu',
    liBg: 'var(--bg-card-cyan)',
    iconColor: 'var(--icon-card-gradient-cyan)',
    textColor: 'var(--font-card-cyan)',
    boxShadow: 'var(--bg-card-cyan-shadow)',
    type: 'number', // number数字 percentage 百分比
  },
  {
    name: '待审核总数',
    key: 'waitToDoneCount',
    waitToDoneCount: 0,
    icon: 'icon-daishenhezongshu',
    liBg: 'var(--bg-card-orange)',
    boxShadow: 'var(--bg-card-orange-shadow)',
    iconColor: 'var(--icon-card-gradient-orange)',
    textColor: 'var(--font-card-orange)',
    type: 'number', // number数字 percentage 百分比
  },
  {
    name: '审核通过总数',
    key: 'passCount',
    passCount: 0,
    icon: 'icon-shenhetongguozongshu',
    liBg: 'var(--bg-card-green)',
    boxShadow: 'var(--bg-card-green-shadow)',
    iconColor: 'var(--icon-card-gradient-green)',
    textColor: 'var(--font-card-green)',
    type: 'number', // number数字 percentage 百分比
  },
  {
    name: '审核未通过总数',
    key: 'notPassCount',
    notPassCount: 0,
    icon: 'icon-shenheweitongguozongshu',
    liBg: 'var(--bg-card-light-pink)',
    boxShadow: 'var(--bg-card-light-pink-shadow)',
    iconColor: 'var(--icon-card-gradient-light-pink)',
    textColor: 'var(--font-card-light-pink)',
    type: 'number', // number数字 percentage 百分比
  },
  {
    name: '审核通过率',
    key: 'dataPassRate',
    dataPassRate: 0,
    icon: 'icon-shenhetongguoshuai',
    liBg: 'var(--bg-card-blue)',
    boxShadow: 'var(--bg-card-blue-shadow)',
    iconColor: 'var(--icon-card-gradient-blue)',
    textColor: 'var(--font-card-blue)',
    type: 'percentage', // number数字 percentage 百分比
  },
];
const reportTableColumns = [
  {
    type: 'selection',
    align: 'center',
    width: 50,
  },
  {
    title: '序号',
    type: 'index',
    align: 'center',
    width: 50,
  },
  {
    title: '报备名称',
    key: 'reportName',
    align: 'left',
    tooltip: true,
    minWidth: 200,
  },
  {
    title: '报备区划',
    key: 'civilName',
    align: 'left',
    tooltip: true,
    minWidth: 130,
  },
  {
    title: '报备类型',
    key: 'reportTypeText',
    align: 'left',
    tooltip: true,
    minWidth: 200,
  },
  {
    title: '报备生效时间',
    slot: 'beginEndTime',
    align: 'left',
    tooltip: true,
    minWidth: 290,
  },
  {
    title: '报备备注信息',
    key: 'reportRemark',
    align: 'left',
    tooltip: true,
    minWidth: 200,
  },
  {
    title: '报备人',
    key: 'reportUserName',
    align: 'left',
    tooltip: true,
    minWidth: 130,
  },
  {
    title: '报备人联系方式',
    key: 'reviewPhoneNumber',
    align: 'left',
    tooltip: true,
    minWidth: 200,
  },
  {
    title: '上报时间',
    key: 'createTime',
    align: 'left',
    tooltip: true,
    minWidth: 150,
  },
  {
    title: '审核时间',
    key: 'reviewTime',
    align: 'left',
    tooltip: true,
    minWidth: 150,
  },
  {
    title: '审核人及单位',
    key: 'reviewUserCivilName',
    align: 'left',
    tooltip: true,
    minWidth: 200,
  },
  {
    title: '审核人联系方式',
    key: 'reviewPhoneNumber',
    align: 'left',
    tooltip: true,
    minWidth: 200,
  },
  {
    title: '审核状态',
    slot: 'status',
    align: 'left',
    tooltip: true,
    minWidth: 110,
    fixed: 'right',
    className: 'statuspadding',
  },
  {
    title: '操作',
    slot: 'action',
    align: 'left',
    tooltip: true,
    minWidth: 110,
    fixed: 'right',
    // className: 'table-action-padding' // 操作栏列-单元格padding设置
  },
];
const auditTableColumns = [
  {
    type: 'selection',
    align: 'center',
    width: 50,
  },
  {
    title: '序号',
    type: 'index',
    align: 'center',
    width: 50,
  },
  {
    title: '报备名称',
    key: 'reportName',
    align: 'left',
    tooltip: true,
    minWidth: 200,
  },
  {
    title: '报备区划',
    key: 'civilName',
    align: 'left',
    tooltip: true,
    minWidth: 130,
  },
  {
    title: '报备类型',
    key: 'reportTypeText',
    align: 'left',
    tooltip: true,
    minWidth: 200,
  },
  {
    title: '报备生效时间',
    slot: 'beginEndTime',
    align: 'left',
    tooltip: true,
    minWidth: 290,
  },
  {
    title: '报备备注信息',
    key: 'reportRemark',
    align: 'left',
    tooltip: true,
    minWidth: 200,
  },
  {
    title: '报备人',
    key: 'reportUserName',
    align: 'left',
    tooltip: true,
    minWidth: 200,
  },
  {
    title: '报备人联系方式',
    key: 'reviewPhoneNumber',
    align: 'left',
    tooltip: true,
    minWidth: 200,
  },
  {
    title: '上报时间',
    key: 'createTime',
    align: 'left',
    tooltip: true,
    minWidth: 150,
  },
  {
    title: '审核通过时间',
    key: 'reviewTime',
    align: 'left',
    tooltip: true,
    minWidth: 150,
  },
  {
    title: '审核人及单位',
    key: 'reviewUserCivilName',
    align: 'left',
    tooltip: true,
    minWidth: 200,
  },
  {
    title: '审核人联系方式',
    key: 'reviewPhoneNumber',
    align: 'left',
    tooltip: true,
    minWidth: 200,
  },
  {
    title: '审核备注信息',
    key: 'reviewRemark',
    align: 'left',
    tooltip: true,
    minWidth: 200,
  },
  {
    title: '审核状态',
    slot: 'status',
    align: 'left',
    tooltip: true,
    minWidth: 100,
    fixed: 'right',
    className: 'statuspadding',
  },
  {
    title: '操作',
    slot: 'action',
    align: 'left',
    tooltip: true,
    minWidth: 125,
    fixed: 'right',
    // className: 'table-action-padding' // 操作栏列-单元格padding设置
  },
];
const reportTypes = [
  {
    key: '1',
    label: '共享联网平台',
  },
  {
    key: '2',
    label: '人脸视图库',
  },
  {
    key: '3',
    label: '车辆视图库',
  },
  {
    key: '4',
    label: '资产库',
  },
  {
    key: '5',
    label: '重点人员库',
  },
];
const statusArr = [
  {
    value: '1',
    text: '待审核',
    color: 'var(--color-warning)',
  },
  {
    value: '2',
    text: '审核通过',
    color: 'var(--color-success)',
  },
  {
    value: '3',
    text: '审核不通过',
    color: 'var(--color-failed)',
  },
];
const reportModalTableColumns = [
  {
    title: '序号',
    type: 'index',
    align: 'center',
    width: 50,
  },
  {
    title: '设备编码',
    key: 'deviceId',
    align: 'left',
    tooltip: true,
    minWidth: 180,
  },
  {
    title: '设备名称',
    key: 'deviceName',
    align: 'left',
    tooltip: true,
    minWidth: 150,
  },
  {
    title: '摄像机功能类型',
    key: 'sbgnlxText',
    align: 'left',
    tooltip: true,
    minWidth: 120,
  },
  {
    title: '设备状态',
    key: 'phyStatusText',
    align: 'left',
    tooltip: true,
    minWidth: 80,
  },
  {
    title: '报备原因',
    slot: 'reportReason',
    key: 'reportReason',
    align: 'left',
    minWidth: 130,
  },
  {
    title: '报备开始时间',
    key: 'beginTime',
    slot: 'beginTime',
    align: 'left',
    minWidth: 180,
  },
  {
    title: '报备截止时间',
    slot: 'endTime',
    key: 'endTime',
    align: 'left',
    minWidth: 180,
  },
];
let tableColumns = [
  {
    type: 'selection',
    align: 'center',
    width: 50,
  },
  {
    title: '序号',
    type: 'index',
    align: 'center',
    width: 50,
  },
  {
    title: `${global.filedEnum.deviceId}`,
    key: 'deviceId',
    align: 'left',
    width: 170,
  },
  {
    title: `${global.filedEnum.deviceName}`,
    key: 'deviceName',
    align: 'left',
    minWidth: 200,
    tooltip: true,
  },
  {
    title: '组织机构',
    key: 'orgName',
    align: 'left',
    minWidth: 200,
    tooltip: true,
  },
  {
    title: '行政区划',
    key: 'civilName',
    align: 'left',
    width: 120,
    tooltip: true,
  },
  {
    title: `${global.filedEnum.ipAddr}`,
    key: 'ipAddr',
    align: 'left',
    minWidth: 130,
    tooltip: true,
  },
  {
    title: `${global.filedEnum.longitude}`,
    slot: 'longitude',
    align: 'left',
    width: 110,
    tooltip: true,
  },
  {
    title: `${global.filedEnum.latitude}`,
    slot: 'latitude',
    align: 'left',
    width: 110,
    tooltip: true,
  },
  {
    title: `${global.filedEnum.sbdwlx}`,
    key: 'sbdwlxText',
    align: 'left',
    width: 130,
    tooltip: true,
  },
  {
    title: `${global.filedEnum.sbgnlx}`,
    key: 'sbgnlxText',
    align: 'left',
    width: 130,
    tooltip: true,
  },
  // {
  //   title: '检测状态',
  //   slot: 'checkStatusText',
  //   align: 'left',
  //   width: 90,
  //   minWidth: 200,
  //   tooltip: true,
  // },
  {
    width: 100,
    title: '基础信息状态',
    slot: 'baseCheckStatus',
    align: 'left',
  },
  {
    width: 100,
    title: '视图数据状态',
    slot: 'viewCheckStatus',
    align: 'left',
  },
  {
    width: 100,
    title: '视频数据状态',
    slot: 'videoCheckStatus',
    align: 'left',
  },
  {
    title: '设备状态',
    slot: 'phyStatus',
    align: 'left',
    width: 100,
  },
];
const fileColumns = [
  {
    title: '序号',
    type: 'index',
    align: 'center',
    width: 50,
  },
  {
    title: '设备编码',
    key: 'deviceId',
    align: 'left',
    // width: 170
  },
  {
    title: '设备名称',
    key: 'deviceName',
    align: 'left',
    // width: 170
  },
  {
    title: '所属单位',
    key: 'orgName',
    align: 'left',
    // width: 170
  },
  {
    title: '行政区划',
    key: 'civilName',
    align: 'left',
    // width: 170
  },
  {
    title: '摄像机功能类型',
    key: 'sbgnlxText',
    align: 'left',
    // width: 170
  },
  {
    title: '设备状态',
    slot: 'sblwzt',
    key: 'sblwzt',
    align: 'left',
    // width: 170
  },
  {
    title: '操作',
    slot: 'action',
    align: 'left',
    tooltip: true,
    width: 50,
  },
];
export {
  tabListArr,
  statisticsList,
  reportTableColumns,
  auditTableColumns,
  reportTypes,
  statusArr,
  reportModalTableColumns,
  tableColumns,
  fileColumns,
};
