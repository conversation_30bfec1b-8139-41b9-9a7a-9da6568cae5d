<template>
  <div class="tooltip-container">
    <div class="tooltip-title-box">
      <p>{{ toolTipData[0].axisValue }}</p>
    </div>
    <div class="tooltip-content-box">
      <p class="legend-box">
        <span> 治理轨迹总数： </span>
        <span class="font-num">
          {{ toolTipData[0].data.governNum }}
        </span>
      </p>
    </div>
    <div class="tooltip-content-box" v-for="(item, index) in toolTipData" :key="index">
      <p class="legend-box">
        <span class="block" :style="{ 'background-color': item.data.color }"> </span>
        <span> {{ item.seriesName }}轨迹数量： </span>
        <span :style="{ color: item.data.color }" class="font-num">
          {{ item.value }}
        </span>
      </p>
    </div>
  </div>
</template>

<script>
export default {
  name: 'online-changes-tooltip',
  components: {},
  props: {},
  data() {
    return {};
  },
  methods: {},
};
</script>

<style lang="less" scoped>
.tooltip-container {
  border-radius: 8px 8px 8px 8px;
  border: 1px solid #1981f5;
  min-width: 230px;
  padding: 10px;
  color: #fff;
  .tooltip-content-box,
  .legend-box,
  .tooltip-title-box {
    display: flex;
    align-items: center;
    flex-direction: row;
    color: #ffffff;
    .font-vs {
      font-weight: 600;
      color: #1b86ff;
      margin: 0 15px;
    }
  }
  .tooltip-content-box {
    justify-content: space-between;
  }
  .block {
    display: inline-block;
    width: 6px;
    height: 6px;
    margin-right: 10px;
  }
  .font-num {
    font-weight: 600;
  }
}
</style>
