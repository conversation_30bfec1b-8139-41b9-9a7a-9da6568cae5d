<template>
  <div class="echarts-div" v-scroll="335">
    <div class="statistics">
      <div class="round">
        <i class="icon-font icon-fuwujiance"></i>
      </div>
      <div class="ml-sm">
        <div class="secondary">服务数量</div>
        <div class="table-text-content total-camera f-16">
          {{ statistics.count }}
        </div>
      </div>
    </div>
    <div>
      <draw-echarts
        :echart-option="echartOnlineRing"
        :echart-style="ringStyle"
        ref="ringChart"
        class="charts"
      ></draw-echarts>
    </div>
    <div>
      <draw-echarts
        :echart-option="echartOfflineRing"
        :echart-style="ringStyle"
        ref="ringChart"
        class="charts"
      ></draw-echarts>
    </div>
  </div>
</template>

<script>
import maintain from '@/config/api/maintain';
export default {
  name: 'charts',
  data() {
    return {
      statistics: {
        count: 0,
        onlineCount: 0,
        offlineCount: 0,
      },
      echartOnlineRing: {},
      echartOfflineRing: {},
      ringStyle: {
        width: '100%',
        height: '230px',
      },
    };
  },
  mounted() {
    this.initStatisc();
  },
  methods: {
    async initStatisc() {
      try {
        let res = await this.$http.get(maintain.getServiceStatistics);
        Object.assign(this.statistics, res.data.data);
        let params = {
          onlinePercent: Math.round((this.statistics.onlineCount / this.statistics.count) * 100),
          offlinePercent: Math.round((this.statistics.offlineCount / this.statistics.count) * 100),
        };
        this.initRing(params);
      } catch (err) {
        console.log(err);
      }
    },
    /**
     * 设备
     * echarts图表必须初始化
     **/
    initRing(params) {
      let onlineOpts = {
        color: new this.$echarts.graphic.LinearGradient(0, 0, 0, 1, [
          {
            offset: 0,
            color: '#0CC8CF',
          },
          {
            offset: 1,
            color: '#19CF91',
          },
        ]),
        percent: params.onlinePercent,
        status: '在线',
        statusColor: '#17CE98',
      };
      let offlineOpts = {
        color: new this.$echarts.graphic.LinearGradient(0, 0, 0, 1, [
          {
            offset: 0,
            color: '#DB520B',
          },
          {
            offset: 1,
            color: '#CF1919',
          },
        ]),
        percent: params.offlinePercent,
        status: '离线',
        statusColor: '#D01E17',
      };
      this.echartOnlineRing = this.$util.doEcharts.equipmentMonitoringRing(onlineOpts);
      this.echartOfflineRing = this.$util.doEcharts.equipmentMonitoringRing(offlineOpts);
    },
  },
  components: {
    DrawEcharts: require('@/components/draw-echarts.vue').default,
  },
};
</script>

<style lang="less" scoped>
.echarts-div {
  .statistics {
    display: flex;
    align-items: center;
    padding: 10px;
    i {
      font-size: 20px;
      color: #b47829;
    }
  }
  .round {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 40px;
    height: 40px;
    background: rgba(180, 120, 41, 0.2);
    border-radius: 50%;
  }
  .total-camera {
    color: var(--color-display-text);
  }
  .secondary {
    font-size: 12px;
    color: var(--color-label);
  }
}
</style>
