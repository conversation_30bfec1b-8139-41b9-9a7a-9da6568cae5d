<template>
  <div class="card">
    <div class="top">
      <ui-btn-tip class="sfgl-icon" content="身份关联" icon="icon-guanlian" transfer @click.stop.native="handleRelevance()" />
    </div>
    <div class="contrast">
      <div class="block border">
        <div class="desc">抓拍照片</div>
        <img
          v-if="itemInfo.faceImg"
          :src="itemInfo.faceImg"
          v-viewer="option"
          :data-src="itemInfo.illegalImg"
        />
        <img v-else :src="defaultImg" />
      </div>
      <div class="block" style="padding: 12px">
        <ui-image class="animation" :src="c5"></ui-image>
        <div class="num c5" v-if="itemInfo.similarity">{{(itemInfo.similarity * 100).toFixed(2)}}%</div>
      </div>
      <div class="block border">
        <div class="desc">证件照片</div>
        <img
          v-if="itemInfo.photo"
          :src="itemInfo.photo"
          v-viewer="option"
          :data-src="itemInfo.photo"
        />
        <img v-else :src="defaultImg" />
      </div>
    </div>
    <div class="info">
      <div class="left">
        <div class="p">
          <div class="title">抓拍时间:</div>
          <div class="val">{{ itemInfo.absTime }}</div>
        </div>
        <div class="p">
          <div class="title">抓拍地点:</div>
          <div class="val">{{ itemInfo.deviceName }}</div>
        </div>
        <div class="p">
          <div class="title">状&emsp;&emsp;态:</div>
          <div class="val">{{ itemInfo.associationIdCardStatus | commonFiltering(associationIdCardStatusList) }}</div>
        </div>
        <div class="p">
          <div class="title">姓&emsp;&emsp;名:</div>
          <div class="val">{{ itemInfo.name }}</div>
        </div>
        <div class="p">
          <div class="title">身份证号:</div>
          <div class="val">{{ itemInfo.idCardNo }}</div>
        </div>
      </div>
    </div>
    <!-- 身份关联 -->
    <identityModal ref="identityModal" @refresh="$emit('refresh')"></identityModal>
  </div>
</template>
<script>
import { mapActions, mapGetters } from 'vuex';
import identityModal from "./identity-modal.vue"
import c5 from "@/assets/img/target/c-five.png";

export default {
  components: { identityModal },
  props: {
    itemInfo: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      c5,
      option: {
        url: "data-src",
      },
      defaultImg: require("@/assets/img/default-img/real_name_default.png"),
    };
  },
  computed: {
		...mapGetters({
			associationIdCardStatusList: 'dictionary/getAssociationIdCardStatusList', // 状态
		})
	},
  async created() {
		await this.getDictData();
	},
  mounted() {},
  methods: {
    ...mapActions({
			getDictData: 'dictionary/getDictAllData'
		}),
    handleRelevance() {
      this.$refs.identityModal.show(this.itemInfo)
    }
  },
};
</script>
<style lang="less" scoped>
.card {
  position: relative;
  height: 250px;
  box-shadow: 0px 3px 5px 0px rgba(147, 171, 206, 0.7016);
  overflow: hidden;
  border-radius: 3px;
  border: 1px solid #ededed;
  background: #f9f9f9;
  .top {
    position: relative;
    display: flex;
    justify-content: flex-end;
    padding: 0 6px;
    .sfgl-icon {
      color: #2c86f8;
    }
  }
  .contrast {
    height: 100px;
    display: flex;
    justify-content: space-between;
    padding: 0 10px;
    // margin-top: 10px;
    .block {
      position: relative;
      width: 100px;
      img {
        width: 100px;
        height: 100%;
        cursor: pointer;
      }
      .animation {
        /deep/ .ui-image-div {
          border: 0;
          background: transparent;
        }
      }
      .desc {
        position: absolute;
        z-index: 9;
        background: rgba(0, 0, 0, 0.5);
        color: #fff;
        padding: 0 6px;
      }
      .num {
        position: absolute;
        width: 100%;
        height: 100%;
        top: 0;
        left: 0;
        align-items: center;
        display: flex;
        justify-content: center;
        color: #2c86f8;
      }
      .c1 {
        color: #ea4a36;
      }
      .c2 {
        color: #e77811;
      }
      .c3 {
        color: #ee9f00;
      }
      .c4 {
        color: #36be7f;
      }
      .c5 {
        color: #2c86f8;
      }
    }
    .border {
      border: 1px solid #ebebeb;
    }
  }

  .info {
    display: flex;
    padding: 12px;
    font-size: 12px;
    .left {
      flex: 1;
      width: 0;
      .p {
        display: flex;
        .title {
          color: rgba(0, 0, 0, 0.6);
          margin-right: 10px;
          line-height: 22px;
          white-space: nowrap;
        }
        .val {
          color: rgba(0, 0, 0, 0.9);
          flex: 1;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }
  }
}
</style>