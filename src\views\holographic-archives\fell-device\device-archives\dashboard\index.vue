<template>
  <section class="content">
    <div class="content-top m-b20">
      <!-- 左侧 -->
      <div class="content-left">
        <!-- 基本信息 -->
        <BasicInformation :baseInfo="baseInfo" @videoPlay="videoPlay" />
      </div>
      <div class="content-middle">
        <!--标签图谱-->
        <label-cloud-view
          :labels="baseInfo.labels || []"
          :type="4"
          :info="baseInfo"
          :photoUrl="
            baseInfo.imageUrls && baseInfo.imageUrls.length > 0
              ? baseInfo.imageUrls[0].photoUrl
              : ''
          "
        />
      </div>
      <!-- 右侧 -->
      <div class="content-right">
        <template>
          <!-- <ui-card title="关系统计" class="m-b20" :padding="0" v-if="graphObj">
            <div
              slot="extra"
              class="play-btn mr-20 color-primary cursor-p"
              @click="toRelationGraph"
            >
              关系图谱
            </div>
            <graph
              v-if="hasGraphData"
              @finish="graphLoaded"
              class="right"
              :relation-can-operate="false"
            ></graph>
            <ui-empty v-else></ui-empty>
          </ui-card> -->
          <!-- 数据采集趋势 -->
          <ui-card title="数据采集趋势" class="m-b20" :padding="0">
            <LineEchart
              :title="{}"
              :xAxis="dataTrendX"
              :yAxis="dataTrendY"
              :series="dataTrendSeries"
              :grid="dataTrendGrid"
            />
          </ui-card>

          <div class="capture">
            <div class="ui-card">
              <div class="card-head">
                <p class="capture-title face-capture capture-active">
                  <span>数据采集记录</span>
                </p>
              </div>
              <div class="card-content">
                <snapRecord
                  :deviceType="deviceType"
                  :list="captureList"
                  v-if="captureList.length !== 0"
                ></snapRecord>
                <ui-loading v-if="captureLoading" />
                <ui-empty v-if="captureList.length === 0"></ui-empty>
              </div>
            </div>
            <span class="more-btn mr-10 more-box" @click.stop="captureMore">
              更多<i class="iconfont icon-more"></i>
            </span>
          </div>
        </template>
      </div>
    </div>
    <!-- 底部布局 -->
    <div class="content-bottom">
      <!-- 左侧 -->
      <div class="content-left">
        <ui-card title="图上位置">
          <MapBase
            ref="map"
            class="map"
            v-if="baseInfo.geoPoint"
            :mapLayerConfig="{ showLatestLocation: true }"
            :chooseMapPosition="{
              lat: baseInfo.geoPoint.lat,
              lon: baseInfo.geoPoint.lon,
              address: baseInfo.detailAddress,
            }"
          />
        </ui-card>
      </div>
      <!-- 右侧 -->
      <div class="content-right">
        <ui-card title="数据采集统计" padding="10,20">
          <date-gather ref="dateGatherRef"></date-gather>
        </ui-card>
      </div>
    </div>
    <ui-modal
      v-model="videoVisible"
      title="视频播放"
      :r-width="750"
      footerHide
      @onCancel="handleCancel"
      @onOk="onVideoPlayCancel"
    >
      <!-- <easyPlayer :videoUrl="videoUrl" fluent stretch ref="easyPlay" /> -->
    </ui-modal>
  </section>
</template>
<script>
import { mapActions, mapGetters } from "vuex";
import { swiper, swiperSlide } from "vue-awesome-swiper";
import snapRecord from "./components/snap-record";
import BasicInformation from "./components/basic-information.vue";
import labelCloudView from "@/views/holographic-archives/components/label-cloud-view/index";
import dateGather from "./components/date-gather.vue";
import MapBase from "@/components/map/index";
import relativeGraphMixin from "@/views/holographic-archives/mixins/relativeGraphMixin2.js";
import LineEchart from "@/components/echarts/line-echart";
import {
  playVideo,
  getBaseStationDataList,
  baseStationDataAnalysis,
  dataAnalysisDetails,
  getETCDeviceDataList,
  getETCDeviceAnalysisDetails,
  getWIFIDeviceDataList,
  getWIFIDeviceAnalysisDetails,
  getRFIDDeviceDataList,
  getRFIDDeviceAnalysisDetails,
  getGPSDeviceDataList,
  getGPSDeviceAnalysisDetails,
  playing,
  stopPlaying,
} from "@/api/device";
import { getDateTime } from "@/util/modules/common";
export default {
  mixins: [relativeGraphMixin],
  components: {
    swiper,
    swiperSlide,
    snapRecord,
    labelCloudView,
    dateGather,
    MapBase,
    BasicInformation,
    LineEchart,
  },
  props: {
    // 基本信息
    baseInfo: {
      type: Object,
      default: {},
    },
  },
  data() {
    return {
      captureLoading: false, //抓拍记录加载
      heatLoading: false, //日分析加载
      deviceId: "", //设备id
      deviceType: 2,
      type: "capture", //1/抓拍,2/视频
      videoVisible: false, //播放视频弹框
      videoUrl: "", //视频播放路径
      tabType: 3, //3人脸2车辆抓拍分析
      captureType: 3, //3人脸2车辆抓拍记录
      captureList: [], //车辆抓拍/人脸抓拍记录
      analysisDay: {}, //抓拍日分析
      timeType: 1, //1月份2日
      videoQualityLoading: false,
      onlineVideoLoading: false,
      historyVideo: [], //历史录像
      videoQuality: [], //视频流质量分析
      onlineInfo: {}, //在线情况分析
      alarmData: [],
      dataObj: {
        imgUrl: require("@/assets/img/device/ctsxt.png"),
      },
      dataTrendX: {
        type: "category",
        axisLine: {
          lineStyle: {
            color: "#D3D7DE",
          },
        },
        boundaryGap: true,
        axisTick: {
          show: false,
        },
        axisLabel: {
          color: "rgba(0, 0, 0, 0.35)",
        },
        data: [],
      },
      dataTrendY: {
        name: "",
        axisLine: {
          show: true,
          lineStyle: {
            color: "#D3D7DE",
          },
        },
        splitLine: {
          lineStyle: {
            type: "dashed",
            color: "#D3D7DE",
          },
        },
      },
      dataTrendGrid: {
        left: "2%",
        top: "10%",
        right: "0.1%",
        bottom: 20,
        containLabel: true,
      },
      dataTrendSeries: [
        {
          type: "line",
          lineStyle: {
            width: 2,
            color: "#2C86F8", // 线条颜色
          },
          data: [],
        },
      ],
    };
  },
  watch: {
    baseInfo: {
      handler(val) {
        if (val.deviceId) {
          this.sbgnlxType = val.sbgnlx.split("/");
          if (this.sbgnlxType.includes("2") || this.sbgnlxType.includes("3")) {
            this.type = "capture";
          } else {
            this.type = "video";
          }
        }
      },
      deep: true,
      immediate: true,
      dataListReq: null, // 数据采集记录
      analysisDetailReq: null, // 数据采集统计
    },
  },
  mounted() {
    this.getDictData();
    this.deviceId = this.$route.query.archiveNo;
    this.deviceType = this.$route.query.deviceType;
    this.initPageData();
    this.$nextTick(() => {
      this.$refs.map.mapidlerWheel();
    });
  },
  methods: {
    ...mapActions({
      getDictData: "dictionary/getDictAllData",
    }),
    initPageData() {
      switch (this.deviceType) {
        case "3":
          this.dataListReq = getWIFIDeviceDataList;
          this.analysisDetailReq = getWIFIDeviceAnalysisDetails;
          break;
        case "4":
          this.dataListReq = getBaseStationDataList;
          this.analysisDetailReq = dataAnalysisDetails;
          break;
        case "5":
          this.dataListReq = getRFIDDeviceDataList;
          this.analysisDetailReq = getRFIDDeviceAnalysisDetails;
          break;
        case "6":
          this.dataListReq = getGPSDeviceDataList;
          this.analysisDetailReq = getGPSDeviceAnalysisDetails;
          break;
        case "16":
          this.dataListReq = getETCDeviceDataList;
          this.analysisDetailReq = getETCDeviceAnalysisDetails;
          break;
      }
      this.queryDataGather();
      // this.queryStatisGather();
      this.queryDataAnalysisDetails();
    },
    toRelationGraph() {
      this.MixinToRelationGraph();
    },
    queryDataGather() {
      this.captureLoading = true;
      this.dataListReq(this.deviceId)
        .then((res) => {
          this.captureList = res.data;
        })
        .finally(() => {
          this.captureLoading = false;
        });
    },
    /* queryStatisGather() {
      console.log(getDateTime(-7), getDateTime(0));
      let params = {
        deviceId: this.deviceId,
        startTime: getDateTime(-7),
        endTime: getDateTime(0),
      };
      baseStationDataAnalysis(params).then((res) => {
        this.alarmData = res.data;
        this.$refs.activityTrack.init(this.alarmData);
      });
    }, */
    queryDataAnalysisDetails() {
      let params = {
        deviceId: this.deviceId,
        startTime: getDateTime(-7),
        endTime: getDateTime(0),
      };
      this.analysisDetailReq(params).then((res) => {
        this.dateGatherList = res.data;
        this.$refs.dateGatherRef.init(this.dateGatherList);

        this.dataTrendX.data = res.data?.map((item) => item.date.substring(5));
        this.dataTrendSeries[0].data = res.data?.map((item) => item.totals);
      });
    },
    // 点击视频播放
    async videoPlay() {
      this.videoVisible = true;
      // 接口请求地址
      const { deviceId } = this.baseInfo;
      console.log(this.baseInfo);
      playing({ deviceId }).then((res) => {
        console.log(res);
        this.videoUrl = res.data.ws_hls;
      });
    },
    onVideoPlayCancel() {},
    // 停止视频播放
    handleCancel() {
      const { deviceId } = this.baseInfo;
      stopPlaying(deviceId).then((res) => {
        this.videoUrl = "";
      });
    },
    // 点击更多到详情
    captureMore() {
      // 设备档案 设备采集记录更多跳转搜索中心
      const deviceTypeToMenu = {
        3: "wifiContent",
        5: "RFIDContent",
        4: "electricContent",
        6: "gpsContent",
        16: "etcContent",
      };
      let deviceInfo = {
        deviceId: this.baseInfo.deviceId,
        deviceName: this.baseInfo.deviceName,
      };
      this.$router.push({
        path: "/wisdom-cloud-search/search-center",
        query: {
          sectionName: deviceTypeToMenu[this.deviceType],
          archiveDeviceList: JSON.stringify([deviceInfo]),
        },
      });
    },
  },
};
</script>
<style lang="less" scoped>
.switch-btn {
  position: absolute;
  top: 30px;
  left: 20px;
  font-size: 14px;
  cursor: pointer;
  span {
    margin-left: 3px;
  }
}
.content {
  width: 100%;
  height: 100%;
  padding: 16px 20px 20px;
  .content-top {
    width: 100%;
    display: flex;
    height: 65%;
    .content-left,
    .content-right {
      width: 500px;
      display: flex;
      flex-direction: column;
      height: 100%;
      .capture {
        position: relative;
        .more-box {
          position: absolute;
          top: 10px;
          right: 10px;
        }
      }
      .ui-card,
      .ui-tabs {
        flex: 1;
      }
      /deep/ .card-content {
        height: 234px;
      }
      /deep/ .ivu-tabs-content {
        height: 236px;
      }
    }
    .content-middle {
      overflow: hidden;
      flex: 1;
      display: flex;
      width: 100%;
      flex-direction: column;
    }
  }
  .content-bottom {
    width: 100%;
    height: 33%;
    display: flex;
    .content-left {
      margin-right: 20px;
      width: 26.6%;
      height: 100%;
      .ui-card {
        flex: 1;
        height: 100%;
      }
      /deep/ .card-content {
        height: 236px;
      }
    }
    .content-right {
      width: 72.4%;
      height: 100%;
      display: flex;
      position: relative;
      .ui-card,
      .ui-tabs {
        flex: 1;
      }
      .ui-tabs {
        /deep/.data-time {
          left: 18% !important;
        }
        /deep/.online-search {
          left: 22%;
        }
      }
      /deep/ .card-content {
        height: 246px;
        // padding: 20px !important;
      }
      /deep/ .ivu-tabs-content {
        height: 246px;
        padding: 20px 0 10px;
      }
    }
  }
  .play-btn {
    margin-top: 5px;
    margin-right: 15px;
    font-size: 14px;
    cursor: pointer;
    line-height: 20px;
    display: flex;
    align-items: center;
    i {
      font-size: 16px;
    }
  }
  .capture-title {
    font-size: 16px;
    cursor: pointer;
    line-height: 30px;
    text-align: center;
    background: #d3d7de;
    color: #666;
    transform: skewX(-18deg);
    padding: 0 23px;
    left: -6px;
    span {
      transform: skewX(18deg);
      display: inline-block;
    }
  }
  .face-capture {
    position: relative;
  }
  .car-capture {
    position: absolute;
    left: 100px;
  }
  .car-record {
    position: absolute;
    left: 135px;
  }
  .capture-active {
    background: linear-gradient(180deg, #5ba3ff 0%, #2c86f8 100%);
    color: #fff;
    font-weight: bold !important;
  }
}
/deep/.ivu-modal-wrap .ivu-modal .ivu-modal-content .ivu-modal-body {
  height: 440px;
}
</style>
