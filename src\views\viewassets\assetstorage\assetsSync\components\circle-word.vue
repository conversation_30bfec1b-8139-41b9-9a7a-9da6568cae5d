<template functional>
  <div class="circle-word ml-xs" :style="{ color: props.wordColor, borderColor: props.wordColor }">
    {{ props.word }}
  </div>
</template>
<script></script>
<style lang="less" scoped>
.circle-word {
  position: relative;
  color: var(--color-bluish-green-text);
  display: inline-block;
  border: 1px solid var(--color-bluish-green-text);
  border-radius: 12px;
  height: 20px;
  width: 20px;
  text-align: center;
  line-height: 20px;
}
</style>
