<!--
    * @FileDescription: 选择文件
    * @Author: H
    * @Date: 2024/2/28
 * @LastEditors: zhengmingming <EMAIL>
 * @LastEditTime: 2025-04-15 18:19:46
 -->
<template>
  <ui-modal
    v-model="modalShow"
    :r-width="dialogData.rWidth"
    :title="dialogData.title"
    list-content
    @onCancel="handleCancel"
    @onOk="confirmHandle"
  >
    <div class="select-label-container modal-list-has-footer-content">
      <div class="left-tree">
        <div class="title">文件列表</div>
        <div class="tree-box">
          <el-tree
            ref="tree"
            :data="data"
            show-checkbox
            :props="treeProps"
            node-key="id"
            :expand-on-click-node="false"
            @check="handleCheckChange"
          >
            <span
              class="custom-tree-node"
              slot-scope="{ node, data }"
              :key="data.id"
            >
              <template>
                <div class="label" :title="node.data.name">
                  <i
                    class="iconfont color-blue"
                    :class="
                      node.data.isLeaf ? 'icon-chakanshipin' : 'icon-fenju'
                    "
                  ></i>
                  {{ node.data.name }}
                </div>
              </template>
            </span>
          </el-tree>
        </div>
      </div>
      <div class="preview-select-label-content">
        <div class="info-bar">
          <span
            >已经选：<span>{{ selectData.length }}</span> 个</span
          >
          <span class="del-btn" @click="removeAllHandle">
            <i class="iconfont icon-shanchu"></i>清空
          </span>
        </div>
        <div class="label-container">
          <ul>
            <li v-for="(item, index) in selectData" :key="index">
              <i class="iconfont color-blue icon-chakanshipin"></i>
              <span>{{ item.name }}</span>
              <i
                class="iconfont icon-shanchu1 del-btn"
                @click.prevent="selectDel(item, index)"
              ></i>
            </li>
          </ul>
        </div>
      </div>
    </div>
  </ui-modal>
</template>
<script>
export default {
  props: {
    data: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      modalShow: false,
      dialogData: {
        title: "选择文件",
        rWidth: 1000,
      },
      treeProps: {
        label: "name",
        isLeaf: "isLeaf",
        children: "children",
      },
      selectData: [],
    };
  },
  methods: {
    /**
     * 显示model
     */
    async show(list = []) {
      this.modalShow = true;
      this.selectData = [...list];
      this.$refs.tree.setCheckedKeys(list.map((v) => v.id));
    },
    async handleCheckChange(data, checkObj) {
      let isChecked = checkObj.checkedKeys.includes(data.id);
      let files = [];
      if (!data.isLeaf) {
        files = data.children;
      } else {
        files = [data];
      }
      if (isChecked) {
        files.forEach((v) => this.selectData.push(v));
      } else {
        files.forEach((v) => {
          this.selectData = this.selectData.filter((i) => i.id != v.id);
        });
      }
    },
    handleCancel() {},
    confirmHandle() {
      this.$emit("selectData", [...this.selectData]);
      this.modalShow = false;
    },
    /**
     * 右侧清空
     */
    removeAllHandle() {
      this.selectData = [];
      this.$refs.tree.setCheckedKeys([]);
    },
    /**
     * 表格右侧删除
     */
    selectDel(row, index) {
      this.selectData.splice(index, 1);
      this.$refs.tree.setChecked(row.id, false);
    },
  },
};
</script>
<style lang="less" scoped>
.select-label-container {
  height: 500px;
  display: flex;
  border: 1px solid #d3d7de;
}
.color-blue {
  color: #2c86f8;
}
.left-tree {
  height: 100%;
  flex: 1;
  border-right: 1px solid #d3d7de;
  display: flex;
  flex-direction: column;
  .title {
    height: 40px;
    line-height: 40px;
    font-size: 14px;
    font-weight: 600;
    padding-left: 10px;
    color: rgba(0, 0, 0, 0.8);
    background: #f9f9f9;
  }
  .tree-box {
    overflow: scroll;
    flex: 1;
  }
  .custom-tree-node {
    .label {
      width: 400px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
}
.preview-select-label-content {
  width: 250px;
  padding: 10px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: auto;
  .info-bar {
    color: #515a6e;
    display: flex;
    justify-content: space-between;
    font-size: 14px;
    align-items: center;
    padding-bottom: 10px;
    .icon-shanchu {
      font-size: 14px;
      margin-right: 5px;
    }
  }
  .label-container {
    padding-top: 10px;
    ul {
      width: 100%;
      li {
        width: 100%;
        display: flex;
        span {
          flex: 1;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          padding: 0 5px;
        }
      }
    }
  }
  .del-btn {
    cursor: pointer;
  }
}
</style>
