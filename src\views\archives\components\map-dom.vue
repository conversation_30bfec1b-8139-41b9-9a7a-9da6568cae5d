<template>
  <div class="dom-wrapper">
    <div class="dom">
      <section class="dom-content">
        <p class="dom-content-p">
          <span class="label">{{ global.filedEnum.longitude }}：</span>
          <span class="message">{{ mapDomData.lon || '未知' }}</span>
        </p>
        <p class="dom-content-p">
          <span class="label">{{ global.filedEnum.latitude }}：</span>
          <span class="message">{{ mapDomData.lat || '未知' }}</span>
        </p>
      </section>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    mapDomData: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  data() {
    return {};
  },
};
</script>
<style lang="less" scoped>
[data-theme='light'],
[data-theme='deepBlue'] {
  .dom {
    background: #ffffff;
    border: 0;
    box-shadow: 0px 0px 6px 0px rgba(11, 51, 94, 0.15);
    margin-left: 3px;
    .dom-content {
      .dom-content-p {
        .label {
          color: rgba(0, 0, 0, 0.35);
        }
        .message {
          color: rgba(0, 0, 0, 0.8);
        }
      }
    }
  }
  .dom:before,
  .dom:after {
    border-color: #ffffff transparent transparent;
    filter: drop-shadow(0px 0px 0px rgba(147, 171, 206, 0.7016));
  }
}
.dom-wrapper {
  position: relative;
  padding: 10px 28px 25px 0;
  height: 100%;
}

.dom {
  background: rgba(0, 41, 97, 0.9);
  border: 1px solid #0068b7;
  width: 200px;
  border-radius: 5px;
  box-shadow: 0 0 20px #0769ce inset;
  position: relative;
  .dom-content {
    padding: 5px 0;
    font-size: 14px;
    .dom-content-p {
      margin-top: 6px;
      width: 200px;
      vertical-align: text-top;
      display: flex;
      position: relative;
      .label {
        display: inline-block;
        color: #ffffff;
        white-space: nowrap;
        width: 28px;
        margin-left: 11px;
      }
      .message {
        display: inline-block;
        color: #b4ceef;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        margin-left: 12px;
      }
    }
  }
}
.dom:before,
.dom:after {
  content: '';
  display: block;
  border-width: 8px;
  position: absolute;
  bottom: -16px;
  left: 11px;
  border-style: solid dashed dashed;
  border-color: #0254a7 transparent transparent;
  font-size: 0;
  line-height: 0;
}
</style>
