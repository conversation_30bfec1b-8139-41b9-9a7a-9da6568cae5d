<!--
    * @FileDescription: etc: 高速出入， 高速出行，通行卡过车，卡主身份信息
    * @Author: H
    * @Date: 2024/04/22
 * @LastEditors: duansen
 * @LastEditTime: 2024-11-01 14:07:35
 -->
<template>
  <section class="main-container">
    <div class="search-bar">
      <searchEtc
        ref="searchEtc"
        @search="searchInfo"
        :mapOnData="mapOnData"
      ></searchEtc>
    </div>
    <div class="table-container">
      <div class="data-export">
        <div class="export-box" v-if="!mapOnData">
          <Button class="mr" @click="handleExport($event)" size="small">
            <ui-icon type="daoru" color="#2C86F8"></ui-icon>
            导出
          </Button>
          <exportBox
            ref="exportbox"
            v-if="exportShow"
            :needPic="false"
            @confirm="confirm"
            @cancel="exportShow = false"
          ></exportBox>
        </div>
        <Button class="mr" @click="handleSort('absTime')" size="small">
          <Icon type="md-arrow-round-down" v-if="!timeUpDown" />
          <Icon type="md-arrow-round-up" v-else />
          时间排序
        </Button>
        <Button @click="dataAboveMapHandler" size="small" style="float: right">
          <ui-icon type="dongtai-shangtudaohang" color="#2C86F8"></ui-icon>
          数据上图
        </Button>
      </div>
      <div class="table-content">
        <ui-table
          :columns="columns"
          :data="tableList"
          :loading="loading"
          @on-selection-change="selectionChangeHandle"
        >
          <template #carType="{ row }">
            <div>{{ row.carType | commonFiltering(vehicleTypeList) }}</div>
          </template>
          <template #plateColor="{ row }">
            <div>
              {{ row.plateColor | commonFiltering(licensePlateColor) }}
            </div>
          </template>
          <template #driveStatus="{ row }">
            <div>
              {{ row.driveStatus | commonFiltering(driveStatus) }}
            </div>
          </template>
          <template #action="{ row }">
            <div class="btn-tips">
              <ui-btn-tip
                content="定位"
                icon="icon-location"
                class="mr-20 primary"
                @click.native="openDirectModel(row)"
              ></ui-btn-tip>
            </div>
          </template>
        </ui-table>
      </div>
      <!-- <ui-empty v-if="tableList.length == 0 && loading == false"></ui-empty> -->
      <!-- <ui-loading v-if="loading"></ui-loading> -->
      <!-- 分页 -->
      <ui-page
        :current="pageInfo.pageNumber"
        :total="total"
        countTotal
        :page-size="pageInfo.pageSize"
        :page-size-opts="[40, 80, 120]"
        @pageChange="pageChange"
        @pageSizeChange="pageSizeChange"
      >
      </ui-page>
      <direction-model ref="directionModel"></direction-model>
      <hl-modal
        v-model="modalShow"
        title="提示"
        :r-width="500"
        @onCancel="loadCancel"
      >
        <div class="content">
          <p class="tipLoad">数据打包中，请等候......</p>
          <p>大约尚需{{ maybeTime }}秒</p>
        </div>
      </hl-modal>
      <ui-modal
        v-model="wranModalShow"
        title="提示"
        :r-width="500"
        @onCancel="onCancel"
        @onOk="onOk"
      >
        <div class="content">
          <p>当前存在打包任务，请确认是否离开！</p>
        </div>
      </ui-modal>
    </div>
  </section>
</template>
<script>
import searchEtc from "../components/search-etc";
import {
  etcHighwayExitOrEntryVehicleTrack,
  etcHighwayVehicleTrack,
  etcVehicleTrack,
  etcVehicleTrackCardOwner,
  etcHighwayExitOrEntryVehicleTrackDownload,
  etcHighwayVehicleTrackDownload,
  etcVehicleTrackCardOwnerDownload,
  etcVehicleTrackDownload,
  taskView,
} from "@/api/wisdom-cloud-search";
import { myMixins } from "../../components/mixin/index.js";
import directionModel from "../components/direction-model";
import { queryDataByKeyTypes } from "@/api/user.js";
import { mapMutations, mapGetters, mapActions } from "vuex";
import hlModal from "@/components/modal/index.vue";
import exportBox from "../../components/export/export-box.vue";
export default {
  mixins: [myMixins], //全局的mixin
  name: "gpsContent",
  components: {
    searchEtc,
    directionModel,
    hlModal,
    exportBox,
  },
  props: {
    mapOnData: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      queryParam: {
        sortField: "absTime",
        order: "desc",
      },
      tableList: [],
      pageInfo: {
        pageNumber: 1,
        pageSize: 40,
      },
      total: 0,
      loading: false,
      timeUpDown: false,
      columns: [],
      columns1: [
        { type: "selection", width: 70, align: "center" },
        { title: "ETC编号", key: "obuId" },
        { title: "设备编号", key: "deviceId", tooltip: true },
        { title: "设备名称", key: "deviceName" },
        { title: "车牌号", key: "plateNo" },
        { title: "车牌颜色", slot: "plateColor" },
        { title: "采集时间", key: "absTime", tooltip: true },
        { title: "核定载重", key: "approvedLoad" },
        { title: "车轴数", key: "axle" },
        { title: "载重", key: "load" },
        { title: "车辆类型", slot: "carType" },
        { title: "收费站编码", key: "cashNo" },
        { title: "入高速口状态", slot: "driveStatus" },
        { title: "入高速口时间", key: "driveTime", tooltip: true },
        { title: "操作", slot: "action", width: 100 },
      ],
      columns2: [
        { type: "selection", width: 70, align: "center" },
        { title: "ETC编号", key: "obuId" },
        { title: "设备编号", key: "deviceId" },
        { title: "设备名称", key: "deviceName" },
        { title: "车辆类型", slot: "carType" },
        { title: "采集时间", key: "absTime" },
        { title: "累计实收金额", key: "amountPaid" },
        { title: "累计应收金额", key: "amountRec" },
        { title: "入收费站编码", key: "cashNo" },
        { title: "出收费站编码", key: "cashNoOut" },
        { title: "入高速口状态", key: "driveStatus" },
        { title: "出高速口状态", key: "driveStatusOut" },
        { title: "入高速口时间", key: "driveTime" },
        { title: "出高速口时间", key: "driveTimeOut" },
        { title: "累计里程", key: "mileAcc" },
        { title: "交易成功次数", key: "paidCnt" },
        { title: "入收费路网编码", key: "roadNo" },
        { title: "出收费路网编码", key: "roadNoOut" },
        { title: "操作", slot: "action", width: 100 },
      ],
      columns3: [
        { type: "selection", width: 70, align: "center" },
        { title: "ETC编号", key: "obuId" },
        { title: "ETC厂商", key: "obu" },
        { title: "ETC发行地", key: "operate" },
        { title: "车牌号", key: "plateNo" },
        { title: "车牌颜色", slot: "plateColor" },
        { title: "车辆类型", slot: "carType" },
        { title: "采集时间", key: "absTime" },
        { title: "厂商类型", key: "algorithmVendorType" },
        { title: "到期时间", key: "cardEnd" },
        { title: "卡号", key: "cardNo" },
        { title: "启用时间", key: "cardStart" },
        { title: "卡片类型", key: "cardType" },
        { title: "卡片版本", key: "cardVer" },
        { title: "设备编号", key: "deviceId" },
        { title: "设备名称", key: "deviceName" },
        { title: "下载数量", key: "downloadSize" },
        { title: "累计里程", key: "mileAcc" },
        { title: "抓拍结束时间", key: "endDate" },
        { title: "操作", slot: "action", width: 100 },
      ],
      columns4: [
        { type: "selection", width: 70, align: "center" },
        { title: "ETC编号", key: "obuId" },
        { title: "站点编号", key: "stationNo" },
        { title: "车牌号", key: "plateNo" },
        { title: "车牌颜色", slot: "plateColor" },
        { title: "采集时间", key: "absTime" },
        { title: "持卡人证件号码", key: "usrCardno" },
        { title: "持卡人姓名", key: "usrName" },
        { title: "车辆类型", slot: "carType" },
        { title: "厂商类型", key: "algorithmVendorType" },
        { title: "到期时间", key: "cardEnd" },
        { title: "卡号", key: "cardNo" },
        { title: "启用时间", key: "cardStart" },
        { title: "卡片类型", key: "cardType" },
        { title: "卡片版本", key: "cardVer" },
        { title: "设备编号", key: "deviceId" },
        { title: "设备名称", key: "deviceName" },
        { title: "下载数量", key: "downloadSize" },
        { title: "累计里程", key: "mileAcc" },
        { title: "抓拍结束时间", key: "endDate" },
        { title: "操作", slot: "action", width: 100 },
      ],
      requestList: {
        0: etcHighwayExitOrEntryVehicleTrack,
        1: etcHighwayVehicleTrack,
        2: etcVehicleTrack,
        3: etcVehicleTrackCardOwner,
      },
      downloadList: {
        0: etcHighwayExitOrEntryVehicleTrackDownload,
        1: etcHighwayVehicleTrackDownload,
        2: etcVehicleTrackDownload,
        3: etcVehicleTrackCardOwnerDownload,
      },
      gpsTypeList: [],
      selectionList: [],
      modalShow: false,
      wranModalShow: false,
      exportShow: false,
      downTaskId: "",
      loadIntervel: null,
      timeInterval: null,
      maybeTime: 0,
    };
  },
  watch: {
    exportShow(val) {
      //点击空白处隐藏
      if (val) {
        document.addEventListener("click", () => {
          this.exportShow = false;
        });
      } else {
        document.addEventListener("click", () => {});
      }
    },
  },
  activated() {
    if (this.mapOnData) {
      // 轨迹搜索上图返回加载数据
      this.selectionList = [];
      this.queryList();
    }
  },
  computed: {
    ...mapGetters({
      getMaxLayer: "countCoverage/getMaxLayer",
      getNum: "countCoverage/getNum",
      getNewAddLayer: "countCoverage/getNewAddLayer",
      getListNum: "countCoverage/getListNum",
      upImageData: "map/getUpImageData",
      vehicleTypeList: "dictionary/getVehicleTypeList",
      licensePlateColor: "dictionary/getLicensePlateColor",
      driveStatus: "dictionary/getDriveStatus",
    }),
    // 已上图数据
    alreadyUpImageIds() {
      return this.upImageData.map((e) => e.recordId);
    },
  },
  destroyed() {
    clearInterval(this.loadIntervel);
    clearInterval(this.timeInterval);
  },
  async mounted() {
    this.columns = this.columns1;
    this.getDictDataPageList();
    await this.getDictData();
  },
  methods: {
    ...mapActions({
      getDictData: "dictionary/getDictAllData",
    }),
    //所属类型字典数据
    getDictDataPageList() {
      queryDataByKeyTypes(["ivcp_gps_type"]).then((res) => {
        this.gpsTypeList = res.data[0].ivcp_gps_type;
      });
    },
    //所属类型字典数据转换
    tranDictType(type) {
      let typeName = "";
      this.gpsTypeList.forEach((item) => {
        if (item.dataKey == type) {
          typeName = item.dataValue;
        }
      });
      return typeName;
    },
    // 排序
    handleSort(val) {
      this.queryParam.sortField = val;
      this.timeUpDown = !this.timeUpDown;
      this.queryParam.order = this.timeUpDown ? "asc" : "desc";
      this.queryList();
    },
    queryList() {
      this.queryParam = {
        ...this.queryParam,
        ...this.$refs.searchEtc.getQueryParams(),
      };
      this.loading = true;
      this.tableList = [];
      this.requestList[this.queryParam.type]({
        ...this.queryParam,
        ...this.pageInfo,
      })
        .then((res) => {
          const { total, entities } = res.data;
          this.total = total;
          this.tableList = entities;
          if (this.tableList.length) {
            this.tableList.forEach((e, idx) => {
              if (
                this.alreadyUpImageIds.length &&
                this.alreadyUpImageIds.includes(e.recordId)
              ) {
                e.isChecked = true;
                e._checked = true; // iview table的手动选中
                this.selectionList.push(e);
                this.$set(this.tableList, idx, e);
              }
            });
          }
        })
        .catch((err) => {
          console.error(err);
        })
        .finally(() => {
          this.loading = false;
        });
    },
    searchInfo() {
      const { type } = this.$refs.searchEtc.getQueryParams();
      switch (type) {
        case "0":
          this.columns = this.columns1;
          break;
        case "1":
          this.columns = this.columns2;
          break;
        case "2":
          this.columns = this.columns3;
          break;
        case "3":
          this.columns = this.columns4;
          break;
      }
      this.pageInfo.pageNumber = 1;
      this.queryList();
    },
    // 页数改变
    pageChange(size) {
      this.pageInfo.pageNumber = size;
      this.queryList();
    },
    // 页数量改变
    pageSizeChange(size) {
      this.pageInfo.pageNumber = 1;
      this.pageInfo.pageSize = size;
      this.queryList();
    },
    ...mapMutations({
      setNum: "countCoverage/setNum",
      setList: "countCoverage/setList",
    }),
    selectionChangeHandle(val) {
      this.selectionList = val.map((item) => {
        return {
          ...item,
          isChecked: true,
        };
      });
    },
    dataAboveMapHandler() {
      let seleNum = this.selectionList.filter(
        (e) => !this.alreadyUpImageIds.includes(e.recordId)
      );
      if (!seleNum.length) {
        this.$Message.warning("请选择上图数据");
        return;
      }
      // 判断是否有坐标信息
      let listHasLocation = seleNum.filter((item) => {
        return item.geoPoint && item.geoPoint.lat && item.geoPoint.lon;
      });
      if (!listHasLocation.length) {
        this.$Message.warning("无经纬度信息，无法上图");
        return;
      }
      if (listHasLocation.length < seleNum.length) {
        this.$Message.warning(
          `已过滤${seleNum.length - listHasLocation.length}条无经纬度信息的数据`
        );
      }
      let newNumLayer = this.getNum.layerNum + this.getNewAddLayer.layer + 1; //图层
      let newNumPoints =
        this.getNum.pointsNum +
        this.getNewAddLayer.pointsInLayer +
        listHasLocation.length; //点位
      if (Number(this.getMaxLayer.maxNumberOfLayer) < newNumLayer) {
        this.$Message.warning("已达到图层最大创建数量");
        return;
      }
      if (Number(this.getMaxLayer.maxNumberOfPointsInLayer) < newNumPoints) {
        this.$Message.warning("已达到上图最大点位总量");
        return;
      }
      let num = JSON.stringify(this.getListNum);
      this.setList(num++);
      this.setNum({ layerNum: newNumLayer, pointsNum: newNumPoints });
      listHasLocation.map((item) => {
        item.delePoints = true;
        item.deleType = "etc";
      });
      this.$emit("dataAboveMapHandler", {
        type: "etc",
        deleIdent: "etc-" + this.getListNum,
        list: listHasLocation,
      });
    },
    openDirectModel(row) {
      this.$refs.directionModel.show(row);
    },
    // 导出
    handleExport($event) {
      $event.stopPropagation();
      this.exportShow = !this.exportShow;
    },
    loadCancel() {
      this.modalShow = false;
      this.wranModalShow = true;
    },
    onCancel() {
      this.modalShow = true;
      this.wranModalShow = false;
    },
    onOk() {
      this.modalShow = false;
      this.wranModalShow = false;
      clearInterval(this.loadIntervel);
      clearInterval(this.timeInterval);
    },
    downdata() {
      this.loadIntervel = setInterval(() => {
        taskView(this.downTaskId)
          .then((res) => {
            if (res.data) {
              this.downStatus = res.data.status;
              if (res.data.status != 0) {
                clearInterval(this.timeInterval);
              }
              if (res.data.status == 1) {
                let filePath = res.data.path;
                let urllength = filePath.split("/");
                let filename = urllength[urllength.length - 1];
                let flieType = filename.indexOf("zip") > 0 ? "zip" : "xlsx";
                let url = "http://" + document.location.host;
                Toolkits.ocxUpDownHttp(
                  "lis",
                  `${flieType}`,
                  `${url}${filePath}`,
                  `${filename}`
                );
                this.onOk();
              } else if (res.data.status == 2) {
                this.onOk();
                this.$Message.warning("打包失败当前任务结束！");
              }
            } else {
              this.onOk();
            }
          })
          .catch(() => {});
      }, 2000);
    },
    confirm(param) {
      let funparams = this.queryParam;
      let params = {};
      if (param.type == "1") {
        let list = this.selectionList;
        if (list.length > 0) {
          let ids = list.map((item) => item.recordId);
          params = {
            ids,
            downloadPics: param.downloadPics,
            downloadSize: null,
            ...funparams,
          };
          this.exportShow = false;
          this.modalShow = true;
        } else {
          this.$Message.warning("请选择需要导出的数据！");
          return;
        }
      } else {
        params = {
          ids: [],
          downloadPics: param.downloadPics,
          downloadSize: param.downloadSize,
          ...funparams,
        };
        this.exportShow = false;
        this.modalShow = true;
      }
      this.downloadList[this.queryParam.type](params)
        .then((res) => {
          this.downTaskId = res.data.taskId;
          this.maybeTime = res.data.maybeTime;
          this.timeInterval = setInterval(() => {
            if (this.maybeTime == 0) {
              clearInterval(this.timeInterval);
            } else {
              this.maybeTime -= 1;
            }
          }, 1000);
          this.downdata();
        })
        .finally(() => {});
    },
  },
};
</script>
<style lang="less" scoped>
@import "style/index";
</style>
