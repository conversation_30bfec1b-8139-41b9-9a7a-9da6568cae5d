<template>
  <ui-modal v-model="visible" title="选择组织机构目录" :styles="styles" class="ui-modal" @query="query">
    <div class="area-container">
      <div class="tree-wrapper">
        <ui-search-tree
          ref="uiTree"
          class="ui-search-tree"
          no-searchtree-wrapper
          :highlight-current="false"
          node-key="nodeKey"
          :tree-data="orgTreeData"
          :default-props="defaultProps"
          expandAll
          checkStrictly
        >
          <template #label="{ node, data }">
            <span>{{ data.orgName }}</span>
            <Button
              type="text"
              @click="checkAll(node, data)"
              class="fr mr-sm btn-mini"
              :style="{ visibility: !data.children ? 'hidden' : '' }"
              >{{ `${data.checkAll ? '取消' : '全部'} ` }}</Button
            >
            <Checkbox v-model="data.check" class="fr mr-sm" @on-change="check($event, node, data)"></Checkbox>
          </template>
        </ui-search-tree>
        <loading v-if="loading"></loading>
      </div>
    </div>
  </ui-modal>
</template>

<script>
import { mapGetters } from 'vuex';
export default {
  name: 'regionalization-select',
  components: {
    UiSearchTree: require('@/components/ui-search-tree.vue').default,
  },
  props: {
    value: {},
    edit: {
      default: true,
    },
    defaultProps: {
      default: () => {
        return {
          label: 'orgName',
          children: 'children',
        };
      },
    },
    nodeKey: {
      default: 'id',
    },
    data: {},
  },
  data() {
    return {
      styles: {
        width: '4.5rem',
      },
      visible: false,
      loading: false,
      checkedTreeData: [],
      orgTreeData: [],
    };
  },
  created() {
    this.visible = true;
  },
  computed: {
    ...mapGetters({
      rawTreeData: 'common/getInitialOrgList',
    }),
  },
  watch: {
    value(val) {
      this.visible = val;
    },
    visible(val) {
      this.$emit('input', val);
      if (val) {
        // this.init()
      } else {
        this.orgTreeData = [];
      }
    },
    data: {
      deep: true,
      immediate: true,
      handler: function () {
        this.init();
      },
    },
  },
  methods: {
    setSourceDataToTreeData(source, target) {
      source.map((item) => {
        target.map((value) => {
          if (item === value.orgCode) {
            this.$set(value, 'check', true);
          }
        });
      });
    },
    init() {
      let treeData = JSON.parse(JSON.stringify(this.rawTreeData));
      this.setSourceDataToTreeData(this.data, treeData);
      this.orgTreeData = this.$util.common.arrayToJson(treeData, 'id', 'parentId');
    },
    query() {
      this.checkedTreeData = [];
      this.getCheckedNodes(this.orgTreeData);
      this.$emit('query', this.checkedTreeData);
      this.visible = false;
    },
    getCheckedNodes(data) {
      data.map((item) => {
        if (item.check) {
          this.checkedTreeData.push(item.orgCode);
        }
        if (item.children) {
          this.getCheckedNodes(item.children);
        }
      });
    },
    checkAll(node, data) {
      if (node.childNodes) {
        if (data.checkAll) {
          this.$set(data, 'checkAll', false);
        } else {
          this.$set(data, 'checkAll', true);
          this.checkParent(node, true);
        }
        node.childNodes.map((item) => {
          this.$set(item.data, 'check', data.checkAll);
        });
      }
    },
    check(val, node) {
      this.checkParent(node, val);
    },
    checkParent(node, check = true) {
      this.$set(node.data, 'check', check);
      if (node.parent) {
        if (check) {
          this.checkParent(node.parent, check);
        }
      }
    },
  },
};
</script>

<style lang="less" scoped>
@import 'index';
.btn-mini {
  height: 24px;
  @{_deep} .ivu-input {
    height: 24px;
    line-height: 24px;
  }
}
@{_deep} .ivu-modal-body {
  padding: 16px 50px 50px 50px;
}
.w100 {
  width: 100px;
}
</style>
