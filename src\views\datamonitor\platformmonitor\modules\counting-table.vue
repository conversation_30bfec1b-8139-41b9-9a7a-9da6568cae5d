<template>
  <div class="counting-table-box">
    <ui-table
      class="ui-table auto-fill"
      :table-columns="computedTableColumns"
      :table-data="tableData"
      :loading="tableLoading"
    >
      <!-- <template #changeRate="{ row }">
        <p class="flex-aic">
          <span class="mr-xs">{{ row.changeRate }}</span>
          <i class="icon-font icon-shangsheng font-green"></i>
          <i class="icon-font icon-xiajiang font-warning"></i>
        </p>
      </template> -->
    </ui-table>
  </div>
</template>
<script>
import { countingTableColumns } from '../utils/enum';
export default {
  name: 'countingtable',
  props: {
    tableData: {
      type: Array,
      default: () => [],
    },
    tableLoading: {
      type: Boolean,
      default: false,
    },
    commonSearchData: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  computed: {
    computedTableColumns() {
      const emitTableFunc = ({ row }, handlerType = 'viewThisMonthCatch') => {
        this.$emit('tableItemClick', { row }, handlerType);
      };
      let formatNowMonth = this.$util.common.formatDate(new Date(), 'yyyy-MM');
      let formatSearchMonth = this.$util.common.formatDate(this.commonSearchData.searchTime, 'yyyy-MM');
      if (formatNowMonth !== formatSearchMonth) {
        return countingTableColumns(emitTableFunc).filter((column) => column.key !== 'captureNumByDay');
      } else {
        return countingTableColumns(emitTableFunc);
      }
    },
  },
  data() {
    return {};
  },
  methods: {},
  components: {
    UiTable: require('@/components/ui-table.vue').default,
  },
};
</script>
<style lang="less" scoped></style>
