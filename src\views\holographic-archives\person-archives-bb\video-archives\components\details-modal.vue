<!--
    * @FileDescription: 人员档案 - 视频档案-详情
    * @Author: H
    * @Date: 2023/03/7
    * @LastEditors: 
    * @LastEditTime: 
 -->
<template>
    <div class="dom-wrapper">
        <div class="dom" @click="($event) => $event.stopPropagation()">
            <header>
				<span>档案详情</span>
				<Icon type="md-close" size="14" @click.native="() => $emit('close', $event)" />
			</header>
            <section class="dom-content">
                <div class="info-box">
                    <div class="info-box-left">
                        <div class="info-photo"></div>
                        <div class="details-list">
                            <div class="wrapper-content">
								<span class="label">档案编号</span>
								<ui-textOver-tips class="message" refName="deviceName" :content="datailsInfo.deviceName || '--'"></ui-textOver-tips>
							</div>
                            <div class="wrapper-content">
								<span class="label">轨迹数量</span>
								<ui-textOver-tips class="message" refName="deviceName" :content="datailsInfo.deviceName || '--'"></ui-textOver-tips>
							</div>
                            <div class="wrapper-content">
								<span class="label">最近抓拍</span>
								<ui-textOver-tips class="message" refName="deviceName" :content="datailsInfo.deviceName || '--'"></ui-textOver-tips>
							</div>
                            <div class="wrapper-content">
								<span class="label">抓拍地点</span>
								<ui-textOver-tips class="message" refName="deviceName" :content="datailsInfo.deviceName || '--'"></ui-textOver-tips>
							</div>
                        </div>
                    </div>
                    <div class="info-box-right">
                        <div class="search">

                        </div>
                        <div class="table-content">
                            <div class="list-card box-1" v-for="(item, index) in dataList" :key="index">
                                <div class="img-content">
                                    <div class="similarity" v-if="item.idScore">
                                        <span class="num" :class="{'gerling-num':queryParam.algorithmVendorType == 'GLST'}" v-if="item.idScore">{{ item.idScore }}%</span>
                                    </div>
                                    <template>
                                        <ui-image :src="item.traitImg" alt="动态库" />
                                    </template>
                                </div>
                                <!-- 动态库 -->
                                <div class="bottom-info">
                                    <time>
                                        <Tooltip content="抓拍时间" placement="right" transfer theme="light">
                                            <i class="iconfont icon-time"></i>
                                        </Tooltip>
                                        {{ item.absTime }}
                                    </time>
                                    <p>
                                        <Tooltip content="抓拍地点" placement="right" transfer theme="light">
                                            <i class="iconfont icon-location"></i>
                                        </Tooltip>
                                        <ui-textOver-tips refName="detailAddress" :content="item.deviceName"></ui-textOver-tips>
                                    </p>
                                </div>
                            </div>
                            <div class="empty-card-1" v-for="(item, index) of 9 - (dataList.length % 9)" :key="index + 'demo'"></div>
                        </div>
                        <ui-empty v-if="dataList.length === 0 && !loading"></ui-empty>
                        <ui-loading v-if="loading"></ui-loading>
                        <ui-page :current="pageData.pageNumber" :total="total" countTotal :page-size="pageData.pageSize" :page-size-opts="[10, 20, 50, 100]" @pageChange="pageChange" @pageSizeChange="pageSizeChange"> </ui-page>
                    </div>
                </div>
            </section>
        </div>
    </div> 
</template>
<script> 
import { originalProfileQueryDetailList } from '@/api/person-archives-bb';
export default {
    data() {
        return {
            dataList: [],
            loading: false,
            total: 0,
            pageData: {
                pageNumber: 1,
                pageSize: 20,
            },
            datailsInfo: {}
        }
    },
    methods: {
        show() {
            let params = {
                beginDate: "2024-03-05 00:00:00",
                deviceIds: [],
                endDate: "2024-03-12 15:34:18",
                ...this.pageData,
                profileId: "f8551846-fb45-4993-b752-7a4f85552992",
            }
            originalProfileQueryDetailList(params)
            .then(res => {

            })
        },
    },

}
</script>
<style lang="less" scoped>

</style>