<template>
  <div class="video-device-connect-internet">
    <div class="statistics">
      <statistics-card
        v-for="(item, index) in data"
        :data="item"
        :key="index"
        class="mr-md"
        :type="`bg-${index + 1}`"
        :iconType="`icon-${index + 1}`"
      ></statistics-card>
      <icon-statistics
        style="width: 388px"
        :statistics-list="abnormalList"
        :isflexfix="false"
        :listyle="listyle"
      ></icon-statistics>
    </div>
    <div style="height: 30%">
      <city-report-chart></city-report-chart>
    </div>
    <div class="mt-sm" style="height: 30%">
      <subordinate-chart></subordinate-chart>
    </div>
    <div class="mt-sm" style="height: 30%">
      <line-chart></line-chart>
    </div>
  </div>
</template>

<script>
export default {
  name: 'VideoDeviceConnectInternet',
  components: {
    IconStatistics: require('@/components/icon-statistics').default,
    StatisticsCard: require('@/views/governanceevaluation/evaluationoResult/components/statistics-card.vue').default,
    SubordinateChart: require('@/views/governanceevaluation/evaluationoResult/components/subordinate-chart.vue')
      .default,
    CityReportChart: require('@/views/governanceevaluation/evaluationoResult/components/city-report-chart.vue').default,
    LineChart: require('@/views/governanceevaluation/evaluationoResult/components/line-chart.vue').default,
  },
  props: {},
  data() {
    return {
      data: [
        {
          '设备总数': 12338888888313,
          '省满分数': 223424,
          '不达标地市数': 34535345,
        },
        {
          '测试实施撒旦发射点发': 123321313,
          '省满分数': 223424,
          '不达标地市数': 34535345,
        },
        {
          '设备总数': 123321313,
          '省满分数': 223424,
          '不达标地市数': 34535345,
        },
      ],
      abnormalList: [
        {
          key: 'total',
          name: '检测地市数量',
          value: 0,
          icon: 'icon-gongdanzongliang',
          iconColor: 'icon-bg1',
          liBg: 'li-bg1',
          type: 'number', // number数字 percentage 百分比
          textColor: 'color1',
        },
      ],
    };
  },
  computed: {},
  watch: {},
  filter: {},
  mounted() {},
  methods: {},
};
</script>

<style lang="less" scoped>
.video-device-connect-internet {
  position: relative;
  height: 100%;
  width: 100%;
  overflow: hidden;
  .statistics {
    display: flex;
  }
}
</style>
