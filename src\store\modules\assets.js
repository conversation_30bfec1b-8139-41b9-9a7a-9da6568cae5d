import axios from 'axios';
import user from '@/config/api/user';
export default {
  namespaced: true,
  state: {
    aysc_check_status: [],
    aysc_storage_status: [],
    aysc_compare_status: [],
    aysc_storage_type: [],
  },
  getters: {
    aysc_check_status(state) {
      return state.aysc_check_status;
    },
    aysc_storage_status(state) {
      return state.aysc_storage_status;
    },
    aysc_compare_status(state) {
      return state.aysc_compare_status;
    },
    aysc_storage_type(state) {
      return state.aysc_storage_type;
    },
  },
  mutations: {
    setAlldicData(state, { data, params }) {
      data.map((val) => {
        params.map((key) => {
          if (val[key]) {
            state[key] = val[key];
          }
        });
      });
    },
  },
  actions: {
    // 批量获取字典值 原algorithm.dictData接口统一改为批量接口
    async getAlldicData({ commit, state }) {
      if (state.aysc_check_status.length) return;
      const params = ['aysc_check_status', 'aysc_storage_status', 'aysc_compare_status', 'aysc_storage_type'];
      await axios.post(user.queryDataByKeyTypes, params).then((res) => {
        commit('setAlldicData', {
          data: res.data.data,
          params: params,
        });
      });
    },
  },
};
