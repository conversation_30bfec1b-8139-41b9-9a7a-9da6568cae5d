<template>
  <ui-modal v-model="visible" title="结果详情" footer-hide>
    <div class="title-total base-text-color">
      <div class="f-16 mb-sm">
        布控照片（共<span class="font-warning">{{ imageData.length }}</span
        >张）
      </div>
    </div>
    <div class="image-bar mt-lg" v-ui-loading="{ loading, tableData: imageData }">
      <image-card
        v-for="(item, index) in imageData"
        :key="index"
        class="base-text-color"
        :src="item.url"
        isClick
        @click="onClickImage(item)"
        :active="currentImageData.id === item.id"
      >
        <div class="ellipsis">
          <span class="icon-font icon-zhanghuxinxi color-gray mr-sm vt-middle f-14"></span>
          <span>{{ item.name }}</span>
        </div>
        <div class="ellipsis">
          <span class="icon-font icon-shenfenzheng color-gray mr-sm vt-middle f-14"></span>
          <span>{{ item.idCard }}</span>
        </div>
        <div class="ellipsis">
          <span class="icon-font icon-dingwei1 color-gray mr-sm vt-middle f-14"></span>
          <span>{{ item.civilName }}</span>
        </div>
      </image-card>
    </div>
    <div class="title-total base-text-color mt-sm">
      <div class="f-16 mb-sm">布控预警</div>
    </div>
    <div class="warning mt-lg" v-ui-loading="{ loading: monitorLoading, tableData: monitorImageData }">
      <image-card v-for="(item, index) in monitorImageData" :key="index" class="base-text-color inline" :src="item.url">
        <div class="ellipsis font-gray">
          <span class="icon-font icon-shijian color-gray mr-sm vt-middle f-14"></span>
          <span>{{ item.shotTime }}</span>
        </div>
        <div class="ellipsis font-gray">
          <span class="icon-font icon-dizhi color-gray mr-sm vt-middle f-14"></span>
          <span>{{ item.address }}</span>
        </div>
      </image-card>
    </div>
    <ui-page class="page" :page-data="searchData" @changePage="changePage" @changePageSize="changePageSize"> </ui-page>
  </ui-modal>
</template>
<script>
import evaluationoverview from '@/config/api/evaluationoverview';

export default {
  props: {
    value: {},
  },
  data() {
    return {
      monitorLoading: false,
      loading: false,
      visible: false,
      searchData: {
        totalCount: 0,
        pageNum: 1,
        pageSize: 20,
      },
      imageData: [],
      monitorImageData: [],
      currentImageData: {},
    };
  },
  created() {},
  mounted() {},
  methods: {
    onClickImage(item) {
      if (this.currentImageData.id === item.id) return;
      this.currentImageData = item;
      this.getMonitorImage(this.currentImageData);
    },
    async init() {
      try {
        this.loading = true;
        this.imageData = [];
        const { regionCode, orgCode, statisticType, batchId, indexId } = this.$route.query;
        let { currentRow } = this.$parent;
        let params = {
          batchId,
          displayType: statisticType,
          indexId,
          orgRegionCode: statisticType === 'REGION' ? regionCode : orgCode,
          customParameters: {
            monitorIds: currentRow.detail.monitorIds,
          },
        };
        let {
          data: { data },
        } = await this.$http.post(evaluationoverview.getDetailData, params);
        this.imageData = data || [];
        if (this.imageData.length > 0) {
          await this.onClickImage(this.imageData[0]);
        }
      } catch (e) {
        console.log(e);
      } finally {
        this.loading = false;
      }
    },
    async getMonitorImage(item) {
      try {
        this.monitorLoading = true;
        this.monitorImageData = [];
        const { regionCode, orgCode, statisticType, batchId, indexId } = this.$route.query;
        let { currentRow } = this.$parent;
        let params = {
          pageNumber: this.searchData.pageNum,
          pageSize: this.searchData.pageSize,
          batchId,
          displayType: statisticType,
          indexId,
          orgRegionCode: statisticType === 'REGION' ? regionCode : orgCode,
          customParameters: {
            monitorId: item.id,
            receiveIds: currentRow.detail.receiveIds,
          },
        };
        let {
          data: { data },
        } = await this.$http.post(evaluationoverview.getPolyData, params);
        this.searchData.totalCount = data.total || 0;
        this.monitorImageData = data.entities || [];
      } catch (e) {
        console.log(e);
      } finally {
        this.monitorLoading = false;
      }
    },
    changePage(val) {
      this.searchData.pageNum = val;
      this.getMonitorImage(this.currentImageData);
    },
    changePageSize(val) {
      this.searchData.pageNum = 1;
      this.searchData.pageSize = val;
      this.getMonitorImage(this.currentImageData);
    },
    reset() {
      this.currentImageData = {};
    },
  },
  watch: {
    visible(val) {
      this.$emit('input', val);
    },
    value(val) {
      this.visible = val;
      if (val) {
        this.reset();
        this.init();
      }
    },
  },
  computed: {},

  components: {
    ImageCard:
      require('@/views/governanceevaluation/evaluationoResult/result-modules/InterfaceStability/components/image-card')
        .default,
  },
};
</script>
<style lang="less" scoped>
.color-gray {
  color: #8797ac;
}
.title-total {
  &:after {
    position: absolute;
    left: 0;
    content: '';
    width: 100%;
    height: 1px;
    background: #07355e;
  }
}
.image-bar {
  min-height: 282px;
  display: flex;
  overflow-x: auto;
}
.warning {
  height: 380px;
  overflow-y: auto;
}
.font-gray {
  color: #8797ac;
}
@{_deep} .ivu-modal {
  width: 76.9% !important;
  .ivu-modal-body {
    position: relative;
  }
}
</style>
