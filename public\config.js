BASE_URL = "";

// 最大展示数量
maxTotal = 10000;

gisUrl = "192.168.1.146:83";

/** 播放器配置 begin */

//开发环境
developmentEnvironment = "320100"; // 南京code
playerType = "pvg"; // 播放器类型：pvg、lwpt
liveType = "pvgplus"; // pvg: pvg67、pvgplus, 联网平台: rtsp、flv
vodType = "pvgplus"; // pvg: pvg67、pvgplus, 联网平台: hls、flv
desencode = 1; // pvg参数是否需要解密
isCustomControl = false; // 是否需要通过调接口来控制播放进度（联网平台设备录像时需要）
vodStorage = "device"; // 录像类型：平台录像'platform'， 设备录像'device'
// lwptIp = '*************'; // 联网平台ip、token
//lwptToken = 'eyJhbGciOiAiSFMyNTYiLCAidHlwIjogIkpXVCJ9.eyJ1c2VyIjoiYWRtaW4iLCJwZXJtaXQiOjI2ODYzMjk1OSwibGV2ZWwiOjAsImlzdCI6MTY5Mzg2MzAxN30.33HKJWrb9QTziTZ-b7VLIinNpfWdsG8qSUsvCL9v5h4'
// lwptDevice = ''
// lwptProt = '18080'
// lwptToken = 'eyJhbGciOiAiSFMyNTYiLCAidHlwIjogIkpXVCJ9.eyJ1c2VyIjoiYWRtaW4iLCJwZXJtaXQiOjI2ODYzMjk1OSwibGV2ZWwiOjAsImlzdCI6MTcwNDU4NzkxOH0.ePkbANO0QAGO3cdT09FahHkX6jxE2gpn_Qt-x5eNTK4'

// 蚌埠环境
// playerType = "pvg" // 播放器类型：pvg、lwpt
// liveType = "pvgplus" // pvg: pvg67、pvgplus, 联网平台: rtsp、flv
// vodType = "pvgplus" // pvg: pvg67、pvgplus, 联网平台: hls、flv
// desencode = 1 // pvg参数是否需要解密
// isCustomControl = false // 是否需要通过调接口来控制播放进度（联网平台设备录像时需要）
// vodStorage = "device" // 联网平台录像类型：平台录像'platform'， 设备录像'device'
// lwptIp = '************'; // 联网平台ip、port、token
// lwptProt = '8080';
// lwptDevice = '34030000002000000110'; // 联网平台下级平台ID
// lwptToken = 'eyJhbGciOiAiSFMyNTYiLCAidHlwIjogIkpXVCJ9.eyJ1c2VyIjoiYWRtaW4iLCJwZXJtaXQiOjI2ODYzMjk1OSwibGV2ZWwiOjAsImlzdCI6MTczNDQ4NzA4Nn0.gaUz3qG6ZLQblhOQCbbZ15AkuM_1rvh4Udt1jX7fswE'

/** 播放器配置 end */

//  qizhi默认显示新版小智lingxi旧版小智
machine = "qizhi";
// 灵析id
appid = 2331;

imgProxyPrefix = ""; // /proxyImg/?oriurl="; // https 图片服务需要代理

applicationCode = "3002"; // 应用编码 3002 00000010  00000024

/** 视图解析配置 begin */
serverIps = ["*************"];
serverPort = 6030;
diskDirectory = "C:/upload/";
defaultFileSpeed = 10; //文件默认倍速
startuploadUrl = "http://127.0.0.1:9124/download/startupload?param=";
fileStructureFormat =
  "Data Files (*.avi;*.wmv;*.mbf;*.mp4;*.dav;*.bmp;*.ts;)|*.avi;*.wmv;*.mbf;*.mp4;*.dav;*.bmp;*.ts;||";
pfsInfo = {
  server: {
    password: "admin",
    port: 9000,
    ip: "*************",
    userName: "admin",
  },
};
/** 视图解析配置 end */

/** 语音解析服务地址 start */
gradioServerApi = "http://*************:7860/";
/** end */

/** 专题库地址 */
monographicIp = location.origin; // location.protocol + "//" + location.hostname;

/*  主应用地址 */
baseAppIp = location.origin; // location.protocol + "//" + location.hostname + ":83";

/*  版权信息 */
copyright = [
  "南京启数智能系统有限公司",
  "Copyright©2020-2022 南京启数智能系统有限公司 版权所有",
];

/*  minio信息,上传，下载 */
minioInfo = {
  "ip":"*************",
  "port":9000,
  "accessKey":"CwLa5im33N3uE4cUCopK",  //minio accessKey
  "secretKey":"PIXSyvaGIfIBwEYhrOYive7GMyyh7OtO6fRSK1Vp", //minio secretKey
}