<template>
  <div ref="echart" class="echarts"></div>
</template>

<script>
import * as echarts from 'echarts'
/**
 *  参考  https://www.makeapie.com/editor.html?c=x0oZWoncE
 */
export default {
  name: 'HomeTitle',
  components: {},
  props: {
    list: { // 数据列表
      type: Array,
      default: () => {
        return [
          {
            name: '规则任务',
            type: '1',
            data: []
          },
          {
            name: '模型任务',
            type: '2',
            data: []
          }
        ]
      }
    },
    names: {
      type: Array,
      required: true,
      default: () => ['未开始', '运行中', '已终止', '运行成功 ', '运行出错']
    },
    colors: {
      type: Array,
      default: () => [
        { startColor: 'rgba(191, 94, 29, 0)', endColor: '#F2AF1C' },
        { startColor: 'rgba(31, 63, 229, 0)', endColor: '#5D9FF4' }
      ]
    }
  },
  data () {
    return {
      myEchart: null
      // colorList: [
      //   { startColor: 'rgba(46, 53, 55, 0)', endColor: '#797F82' },
      //   { startColor: 'rgba(191, 94, 29, 0)', endColor: '#F2AF1C' },
      //   { startColor: 'rgba(31, 63, 229, 0)', endColor: '#5D9FF4' },
      //   { startColor: 'rgba(13, 113, 13, 0)', endColor: '#60C80B' },
      //   { startColor: 'rgba(116, 13, 13, 0)', endColor: '#D45321' }
      // ]
    }
  },
  mounted () {
    this.init()
  },
  deactivated () {
    this.removeResizeFun()
  },
  beforeDestroy () {
    this.removeResizeFun()
  },
  methods: {
    init () {
      const _that = this
      this.myEchart = echarts.init(this.$refs.echart)
      var option = {
        tooltip: {
          trigger: 'axis',
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          borderColor: 'rgba(0, 0, 0, 0.8)',
          textStyle: {
            color: '#fff'
          }
        },
        grid: {
          top: '14%',
          right: '0%',
          left: '0%',
          bottom: '0%',
          containLabel: true
        },
        legend: {
          top: '0%',
          right: '0%',
          icon: 'rect',
          itemWidth: 8,
          itemHeight: 8,
          textStyle: {
            color: 'rgba(255, 255, 255, 0.75)',
            padding: [0, 0, 0, 6]
          }
        },
        color: ['#F2AF1C', '#5D9FF4'],
        xAxis: [
          {
            type: 'category',
            boundaryGap: true, // 边界间隙，起始点跟轴之间的距离
            axisLabel: {
              formatter: '{value}',
              fontSize: 12,
              textStyle: {
                color: 'rgba(255, 255, 255, 0.75)'
              }
            },
            axisLine: {
              lineStyle: {
                color: '#07355E',
                width: 2
              }
            },
            axisTick: {
              show: false
            },
            data: this.names
          }
        ],
        yAxis: [
          {
            boundaryGap: false,
            type: 'value',
            minInterval: 1,
            axisLabel: {
              textStyle: {
                color: 'rgba(255, 255, 255, 0.75)'
              }
            },
            splitLine: {
              show: true,
              lineStyle: {
                type: 'dashed',
                color: '#07355E'
              }
            },
            axisTick: {
              show: false
            }
          }
        ],
        series: [{
          name: this.list[0].name,
          taskType: this.list[0].type,
          type: 'bar',
          data: this.list[0].data,
          barWidth: '16px',
          itemStyle: {
            normal: {
              color: function (params) {
                return new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                  offset: 0,
                  color: _that.colors[0].endColor // 0% 处的颜色
                }, {
                  offset: 1,
                  color: _that.colors[0].startColor // 100% 处的颜色
                }], false)
              },
              barBorderRadius: [8, 8, 0, 0]
            }
          }
        }, {
          name: this.list[1].name,
          taskType: this.list[1].type,
          type: 'bar',
          data: this.list[1].data,
          barWidth: '16px',
          itemStyle: {
            normal: {
              color: function (params) {
                return new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                  offset: 0,
                  color: _that.colors[1].endColor // 0% 处的颜色
                }, {
                  offset: 1,
                  color: _that.colors[1].startColor // 100% 处的颜色
                }], false)
              },
              barBorderRadius: [8, 8, 0, 0]
            }
          }
        }]
      }
      this.myEchart.setOption(option)
      this.myEchart.on('click', function (params) {
        const query = {
          taskType: params.data.type,
          status: params.data.status
        }
        _that.$router.push({
          path: '/label-analysis/label-task',
          query: {
            taskInfo: JSON.stringify(query)
          }
        })
      })
      window.addEventListener('resize', () => this.myEchart.resize())
    },

    removeResizeFun () {
      window.removeEventListener('resize', () => this.myEchart.resize())
    }
  }
}
</script>

<style lang="less" scoped>
// 高度100%
.echarts {
  width: 100%;
  height: 100%;
}
</style>
