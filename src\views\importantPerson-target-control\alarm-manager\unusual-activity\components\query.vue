<template>
  <Form ref="formData" :inline="true" :label-width="80">
    <FormItem label="姓名:" prop="name">
      <Input v-model="queryParam.name" placeholder="请输入"></Input>
    </FormItem>
    <FormItem label="身份证号:" prop="idCardNo">
      <Input v-model="queryParam.idCardNo" placeholder="请输入"></Input>
    </FormItem>
    <FormItem label="前科类型:" prop="criminalRecordType">
      <Select
        v-model="queryParam.criminalRecordType"
        placeholder="请选择"
        :multiple="false"
        transfer
        :max-tag-count="1"
        style="width: 200px"
      >
        <OptionGroup
          v-for="item in faceLibBizList"
          :label="item.bizTypeName"
          :key="item.id"
        >
          <Option
            v-for="label in item.labels"
            :key="label.id"
            :value="label.id.toString()"
            >{{ label.labelName }}</Option
          >
        </OptionGroup>
      </Select>
    </FormItem>
    <FormItem label="民族:" prop="nation">
      <Select
        v-model="queryParam.nation"
        placeholder="请选择"
        :multiple="false"
        transfer
        :max-tag-count="1"
      >
        <Option
          :value="item.dataKey"
          v-for="item in nationTypeList"
          :key="item.dataKey"
          >{{ item.dataValue }}</Option
        >
      </Select>
    </FormItem>
    <FormItem label="性别:" prop="sex">
      <Select
        v-model="queryParam.sex"
        placeholder="请选择"
        :multiple="false"
        transfer
        :max-tag-count="1"
      >
        <Option
          :value="item.dataKey"
          v-for="item in sexTypeList"
          :key="item.dataKey"
          >{{ item.dataValue }}</Option
        >
      </Select>
    </FormItem>
  </Form>
</template>
<script>
import { mapActions, mapGetters } from "vuex";
import { getBizTypes } from "@/api/data-warehouse.js";
export default {
  data() {
    return {
      queryParam: {
        name: "", // 布控目标
        idCardNo: "", // 身份证号
        nation: "",
        sex: "",
        criminalRecordType: "",
      },
      faceLibBizList: [], // 前科类型
    };
  },
  computed: {
    ...mapGetters({
      identityTypeList: "dictionary/getIdentityTypeList", // 证件类型
      nationTypeList: "dictionary/getNationTypeList", //民族类型
      sexTypeList: "dictionary/getGenderList", // 性别
      partiTypeList: "dictionary/getPartiTypeList", // 案件类型 这个字典值后边可能要单独拎出来一个给未成年人用
    }),
  },
  async created() {
    await this.getDictData();
    await this.getBizLabelList({ bizType: "1" }); // 初始化前科类型data
  },
  mounted() {
    if (this.$route.query.idCardNo) {
      this.queryParam.idCardNo = this.$route.query.idCardNo;
    }
  },
  methods: {
    ...mapActions({
      getDictData: "dictionary/getDictAllData",
    }),
    async getBizLabelList(val) {
      const bizType = val?.bizType;
      if (!bizType) return;
      const resp = await getBizTypes({ bizTypeIds: bizType?.split(",") });
      this.faceLibBizList = resp?.data || [];
    },

    /**
     * @description: 重置
     */
    reset() {
      this.queryParam.name = "";
      this.queryParam.idCardNo = "";
      this.queryParam.nation = "";
      this.queryParam.sex = "";
      this.queryParam.criminalRecordType = "";
    },

    /**
     * @description: 获取查询参数，暴露给父组件
     * @return {object}
     */
    getQueryParams() {
      return this.queryParam;
    },
  },
};
</script>
<style lang="less" scoped>
.separtor {
  margin: 0 10px;
  width: 16px;
  height: 1px;
  background: rgba(0, 0, 0, 0.45);
}
.flex-box {
  display: flex;
  align-items: center;
}
.select-record-kind {
  /deep/.ivu-select-item {
    width: 210px;
    text-overflow: ellipsis;
    overflow: hidden;
  }
}
</style>
