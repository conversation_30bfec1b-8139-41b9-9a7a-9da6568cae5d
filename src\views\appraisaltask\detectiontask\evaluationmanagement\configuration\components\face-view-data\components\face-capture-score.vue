<!-- 人脸抓拍图片评分率（FACE_QUALITY_PASS_RATE） -->
<template>
  <div class="complete-rule-form">
    <common-form
      :label-width="180"
      class="common-form"
      ref="commonForm"
      :module-action="moduleAction"
      :form-data="formData"
      :form-model="formModel"
      :task-index-config="taskIndexConfig"
      @updateFormData="updateFormData"
      @handleDetect="getDetect"
    >
      <div slot="extract" v-if="formData.detectMode !== '4'">
        <FormItem label="每设备抽取图片" prop="captureNum">
          <InputNumber
            v-model="formData.captureNum"
            class="input-width"
            placeholder="请输入抽取图片"
            clearable
          ></InputNumber>
        </FormItem>
        <FormItem label="" :class="{ 'mt-minus-sm': formData.captureNum }" prop="isMissPic">
          <Checkbox v-model="formData.isMissPic" label="" :true-value="1" :false-value="0">
            <span>图片数量不足，则设备不合格</span>
          </Checkbox>
        </FormItem>
        <FormItem
          prop="deviceQueryForm.dayByCapture"
          label="图片抽取范围"
          :rules="[{ validator: validateDayByCapture, trigger: 'blur', required: true }]"
        >
          <span class="base-text-color mr-xs">近</span>
          <InputNumber
            v-model.number="formData.deviceQueryForm.dayByCapture"
            :min="0"
            :precision="0"
            class="mr-sm width-mini"
          ></InputNumber>
          <span class="base-text-color">天，</span>
          <InputNumber
            v-model.number="formData.deviceQueryForm.startByCapture"
            :min="0"
            :max="23"
            :precision="0"
            class="mr-sm width-mini"
          ></InputNumber>
          <span class="base-text-color mr-sm">点至</span>
          <InputNumber
            v-model.number="formData.deviceQueryForm.endByCapture"
            :min="0"
            :max="23"
            :precision="0"
            class="mr-sm width-mini"
          ></InputNumber>
          <span class="base-text-color mr-sm">点</span>
        </FormItem>
      </div>
      <div slot="waycondiction" class="mt-xs" v-if="['1', '3'].includes(formData.detectMode)">
        <div>
          <span class="base-text-color">检测条件：</span>
          <div>
            <Checkbox v-model="formData.deviceQueryForm.detectPhyStatus" true-value="1" false-value="0"
              >设备可用</Checkbox
            >
          </div>
          <div>
            <FormItem
              prop="deviceQueryForm.dayByFilterOnline"
              label=""
              :rules="[{ validator: validateDayByFilterOnline, trigger: 'blur' }]"
            >
              <Checkbox class="mb-sm" v-model="formData.deviceQueryForm.filterOnline">设备有流水</Checkbox>
              <span class="base-text-color">近</span>
              <InputNumber
                v-model.number="formData.deviceQueryForm.dayByFilterOnline"
                :min="0"
                :precision="0"
                class="mr-sm width-mini"
              ></InputNumber>
              <span class="base-text-color">天，</span>
              <InputNumber
                v-model.number="formData.deviceQueryForm.startByFilterOnline"
                :min="0"
                :max="23"
                :precision="0"
                class="mr-sm width-mini"
              ></InputNumber>
              <span class="base-text-color mr-sm">点至</span>
              <InputNumber
                v-model.number="formData.deviceQueryForm.endByFilterOnline"
                :min="0"
                :max="23"
                :precision="0"
                class="mr-sm width-mini"
              ></InputNumber>
              <span class="base-text-color mr-sm">点</span>
              <div class="capture-vehicle">
                <span class="base-text-color mr-sm">抓拍人脸不少于</span>
                <InputNumber
                  v-model.number="formData.deviceQueryForm.countByFilterOnline"
                  :min="0"
                  :precision="0"
                  class="mr-sm width-mini"
                ></InputNumber>
                <span class="base-text-color">张</span>
                <p class="color-failed">说明：系统只检测满足条件的设备。</p>
              </div>
            </FormItem>
          </div>
        </div>
      </div>
    </common-form>
    <Form ref="modalData" :model="formData" :rules="ruleCustom" class="form-content" :label-width="180">
      <FormItem label="合格图片质量分阈值" prop="qualityScore">
        <span class="base-text-color">>=</span>
        <InputNumber
          v-model.number="formData.qualityScore"
          class="width-input ml-xs mr-xs"
          :max="100"
          :min="0"
          placeholder="请输入0-100的数字"
        ></InputNumber>
        <span class="base-text-color">分</span>
      </FormItem>
      <FormItem label="更新人脸图片合格状态">
        <RadioGroup v-model="formData.isUpdatePhyStatus">
          <Radio :label="1">是</Radio>
          <Radio :label="0">否</Radio>
        </RadioGroup>
      </FormItem>
    </Form>
  </div>
</template>

<script>
export default {
  components: {
    CommonForm:
      require('@/views/appraisaltask/detectiontask/evaluationmanagement/configuration/components/common-form/index.vue')
        .default,
  },
  props: {
    indexType: {
      type: String,
      default: '',
    },
    configInfo: {
      required: true,
      type: Object,
    },
    formModel: {
      required: true,
      type: String,
      default: 'edit',
    },
    moduleAction: {
      type: Object,
      default: () => {},
    },
    // 所有配置详情
    taskIndexConfig: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    const validateQualityScore = (rule, value, callback) => {
      if (!value && value !== 0) {
        callback(new Error('请输入合格图片质量分阈值'));
      } else {
        callback();
      }
    };
    return {
      formData: {
        captureNum: null, //抓拍数量
        isMissPic: 0, //是否以抓拍图片数量判定设备不合格
        qualityScore: null, // 合格图片质量分阈值
        isUpdatePhyStatus: 0,
      },
      deviceQueryForm: {
        detectPhyStatus: '0',
        filterOnline: null,
        dayByFilterOnline: 2,
        countByFilterOnline: 1,
        startByFilterOnline: 0,
        endByFilterOnline: 23,
        dayByCapture: 2,
        startByCapture: 0,
        endByCapture: 23,
      },
      validateDayByFilterOnline: (rule, value, callback) => {
        let { filterOnline, dayByFilterOnline, startByFilterOnline, endByFilterOnline, countByFilterOnline } =
          this.formData.deviceQueryForm;
        if (
          filterOnline &&
          (!dayByFilterOnline ||
            (!startByFilterOnline && startByFilterOnline !== 0) ||
            (!endByFilterOnline && endByFilterOnline !== 0) ||
            !countByFilterOnline)
        ) {
          callback(new Error('设备有流水参数不能为空'));
        }
        if (filterOnline && startByFilterOnline > endByFilterOnline) {
          callback(new Error('设备有流水开始时间不能大于结束时间'));
        }
        callback();
      },
      validateDayByCapture: (rule, value, callback) => {
        let { dayByCapture, startByCapture, endByCapture } = this.formData.deviceQueryForm;
        if (!dayByCapture || (!startByCapture && startByCapture !== 0) || (!endByCapture && endByCapture !== 0)) {
          callback(new Error('图片抽取范围参数不能为空'));
        }
        if (startByCapture > endByCapture) {
          callback(new Error('图片抽取范围开始时间不能大于结束时间'));
        }
        callback();
      },
      ruleCustom: {
        qualityScore: [{ validator: validateQualityScore, trigger: 'blur', required: true }],
      },
    };
  },
  computed: {},
  watch: {
    indexType: {
      handler() {
        const { regionCode } = this.moduleAction;
        this.formData = {
          ...this.formData,
          regionCode, // 检测对象
        };
      },
      immediate: true,
    },
    formModel: {
      handler(val) {
        if (val === 'edit') {
          this.formData = {
            ...this.configInfo,
            deviceQueryForm: {
              ...this.deviceQueryForm,
              ...this.configInfo.deviceQueryForm,
            },
          };
        } else {
          const { regionCode } = this.moduleAction;
          this.formData = {
            ...this.formData,
            regionCode: regionCode, // 检测对象
            detectMode: '1',
            quantityConfig: [],
            deviceQueryForm: {
              ...this.deviceQueryForm,
            }, //默认值
            visitTimeout: null,
          };
        }
      },
      immediate: true,
    },
  },
  async created() {},
  methods: {
    getDetect() {
      this.formData.deviceQueryForm.detectPhyStatus = '0';
      this.formData.deviceQueryForm.filterOnline = false;
    },
    updateFormData(val) {
      this.formData = {
        ...val,
        deviceQueryForm: {
          detectPhyStatus: this.formData.deviceQueryForm.detectPhyStatus,
          filterOnline: this.formData.deviceQueryForm.filterOnline,
          dayByFilterOnline: this.formData.deviceQueryForm.dayByFilterOnline,
          countByFilterOnline: this.formData.deviceQueryForm.countByFilterOnline,
          startByFilterOnline: this.formData.deviceQueryForm.startByFilterOnline,
          endByFilterOnline: this.formData.deviceQueryForm.endByFilterOnline,
          dayByCapture: this.formData.deviceQueryForm.dayByCapture,
          startByCapture: this.formData.deviceQueryForm.startByCapture,
          endByCapture: this.formData.deviceQueryForm.endByCapture,
          ...val.deviceQueryForm,
        },
      };
    },
    async handleSubmit() {
      let modalDataValid = await this.$refs.modalData.validate();
      let commonFormValid = await this.$refs['commonForm'].handleSubmit();
      return commonFormValid && modalDataValid;
    },
  },
};
</script>

<style lang="less" scoped>
.complete-rule-form {
  .input-width {
    width: 380px;
  }
  .ipt-width-50 {
    width: 50px;
  }
  .params-suffix {
    color: #fff;
    margin-left: 5px;
  }
  .quality-score {
    @{_deep} .ivu-form-item-content {
      margin-left: 0 !important;
      display: flex;
      align-items: center;
    }
  }
}
</style>
