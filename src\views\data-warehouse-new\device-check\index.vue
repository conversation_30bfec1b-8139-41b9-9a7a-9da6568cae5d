<template>
  <div class="layout">
    <tabsPage
      ref="tabsPage"
      :selectLi="selectLi"
      :list="alarmList"
      @change="tabChange"
    />
    <component :ref="isAndRef" :is="isAndRef" />
  </div>
</template>
<script>
import tabsPage from "./components/tabs.vue";
import dataReportingStatus from "./data-reporting-status/index.vue";
import dataDetaction from "./data-detection/index.vue";
export default {
  name: "device-check",
  components: {
    dataReportingStatus,
    dataDetaction,
    tabsPage,
  },
  data() {
    return {
      isAndRef: "dataDetaction",
      alarmList: [
        { name: "数据监测", value: 1 },
        { name: "设备24小时数据上报状态", value: 2 },
      ],
      selectLi: 1,
    };
  },
  computed: {},
  created() {},
  async mounted() {},
  methods: {
    init() {
      var name = this.isAndRef;
      this.$nextTick(() => {
        this.$refs[name].init();
      });
    },
    tabChange(row) {
      if (row.value == 1) {
        this.isAndRef = "dataDetaction";
      } else {
        this.isAndRef = "dataReportingStatus";
      }
      this.selectLi = row.value;
      this.$nextTick(() => {
        this.$refs[this.isAndRef]?.init();
      });
    },
  },
};
</script>
<style lang="less" scoped>
.layout {
  width: 100%;
  //   height: 100%;
  height: inherit;
  display: flex;
  flex-direction: column;
  .tabs {
    height: 100px;
  }
  .container {
    flex: 1;
    display: flex;
    flex-direction: column;
  }
}
</style>
