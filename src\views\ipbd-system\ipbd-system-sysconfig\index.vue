<template>
  <div class="layout">
    <!-- <div class="button">
      <Button type="primary" @click="save()">保存</Button>
    </div> -->
    <div class="content">
      <div class="module">
        <div class="title">
          <h2>全局设置</h2>
          <Button type="primary" @click="save(1)">保存</Button>
        </div>
        <div class="row">
          <div class="left">水印显示</div>
          <div class="right">
            <div>
              <span class="span">选择展示信息</span>
              <CheckboxGroup v-model="formData.globalConfig.showInfo">
                <Checkbox label="姓名"></Checkbox>
                <Checkbox label="警号"></Checkbox>
              </CheckboxGroup>
            </div>
            <div class="btn">
              <i-switch v-model="formData.globalConfig.watermarkSwitch" />
            </div>
          </div>
        </div>
        <div class="row">
          <div class="left">以图搜图</div>
          <div class="right2">
            <div class="childLeft">
              <span>搜索结果TOP</span>
              <Input v-model="formData.globalConfig.searchForPicturesTopN" placeholder="请输入" style="width: 150px" />
            </div>
            <div class="childRight">
              <span>默认相似度</span>
              <!-- <Input search enter-button="22" placeholder="请输入" style="width: auto" /> -->
              <Input placeholder="请输入" v-model="formData.globalConfig.searchForPicturesDefaultSimilarity" style="width: auto">
                <!-- <span slot="append">%</span> -->
              </Input>
              <div class="position">%</div>
            </div>
          </div>
        </div>
        <div class="row">
          <div class="left">数据脱敏</div>
          <div class="right2">
            <div class="childLeft">
              <span>身份证号脱敏</span>
              <i-switch v-model="formData.globalConfig.idCardSensitiveSwitch" />
            </div>
            <div class="childRight">
              <span>手机号脱敏</span>
              <i-switch v-model="formData.globalConfig.phoneNumberSensitiveSwitch" />
            </div>
          </div>
        </div>
        <div class="row">
          <div class="left">地图设置</div>
          <div class="right2">
            <div class="childLeft">
              <span>默认层级</span>
              <Input placeholder="请输入" v-model="formData.globalConfig.mapLayerLevel" style="width: auto">
              </Input>
              <div class="position">层</div>
            </div>
            <div class="childRight">
              <span>中心坐标</span>
              <span class="link-active-color" @click="showChooseMap" v-if="!formData.globalConfig.mapCenterPoint">请点击选择中心坐标</span>
              <span class="link-active-color" @click="showChooseMap" v-else>{{formData.globalConfig.mapCenterPoint}}</span>
              <!-- <Input v-model="formData.globalConfig.mapCenterPoint" placeholder="请输入" style="width: 150px" /> -->
            </div>
          </div>
        </div>

      </div>
      <div class="module">
        <div class="title">
          <h2>图上作战</h2>
          <Button type="primary" @click="save(2)">保存</Button>
        </div>
        <div class="row">
          <div class="left">搜周边</div>
          <div class="right">
            <div>
              <span class="span">默认范围</span>
            </div>
            <div class="btn">
              <Input v-model="formData.mapConfig.maxPeripheryDistance" placeholder="请输入" style="width: auto">
              </Input>
              <div class="position">M</div>
            </div>
          </div>
        </div>
        <div class="row">
          <div class="left">数据上图</div>
          <div class="right2">
            <div class="childLeft">
              <span>图层最大创建数量</span>
              <Input v-model="formData.mapConfig.maxNumberOfLayer" placeholder="请输入" style="width: 150px" />
            </div>
            <div class="childRight">
              <span>上图最大点位总量</span>
              <Input v-model="formData.mapConfig.maxNumberOfPointsInLayer" placeholder="请输入" style="width: auto" />
            </div>
          </div>
        </div>
      </div>
      <div class="module">
        <div class="title">
          <h2>智慧云搜</h2>
          <Button type="primary" @click="save(5)">保存</Button>
        </div>
        <div class="row">
          <div class="left">静态库配置</div>
          <div class="right">
            <Select v-model="formData.searchConfig.faceList" multiple>
                <Option v-for="(item,index) in formData.searchConfig.faceLibConfig" :value="item.field_name" :key="index">{{ item.field_name_cn }}</Option>
            </Select>
          </div>
        </div>
      </div>
      <div class="module">
        <div class="title">
          <h2>全息档案</h2>
          <Button type="primary" @click="save(3)">保存</Button>
        </div>
        <div class="row">
          <div class="left">行为规律</div>
          <div class="right">
            <div>
              <span>白天时间</span>
            </div>
            <div class="btn">
              <TimePicker v-model="formData.archivesConfig.timeInfo" format="HH" type="timerange" placement="bottom-end" placeholder="选择时间"></TimePicker>
            </div>
          </div>
        </div>
        <div class="row">
          <div class="left">实名人档</div>
          <div class="right">
            <div class="span">背景信息</div>
            <Select v-model="formData.archivesConfig.realNameArcBackground" multiple>
                <Option v-for="(item,index) in resourceList" :value="item.id" :key="index">{{ item.resourceNameCn + "  -  " + item.resourceName }}</Option>
            </Select>
          </div>
        </div>
        <div class="row">
          <div class="left">车辆档案</div>
          <div class="right">
            <div class="span">背景信息</div>
            <Select v-model="formData.archivesConfig.vehicleArcBackground" multiple>
                <Option v-for="(item,index) in resourceList" :value="item.id" :key="index">{{ item.resourceNameCn + "  -  " + item.resourceName }}</Option>
            </Select>
          </div>
        </div>
        <!-- <div class="row">
          <div class="left">背景信息</div>
          <div class="right2">
            <div class="childLeft">
              <span>实名人档</span>
              <Button type="primary">配置</Button>
            </div>
            <div class="childRight">
              <span>车辆档案</span>
              <Button type="primary">配置</Button>
            </div>
          </div>
        </div> -->
        <div class="row">
          <div class="left">人-IMSI拟合</div>
          <div class="right2">
            <div class="childLeft">
              <span>最大距离差</span>
              <Input v-model="formData.archivesConfig.maxHumanIMSIFittingDistance" placeholder="请输入" style="width: auto">
              </Input>
              <div class="position">米</div>
              <!-- <Input v-model="value" placeholder="请输入" style="width: 150px" /> -->
            </div>
            <div class="childRight">
              <span>最大时间差</span>
              <Input v-model="formData.archivesConfig.maxHumanIMSIFittingTime" placeholder="请输入" style="width: auto">
              </Input>
              <div class="position">秒</div>
              <!-- <Input suffix="ios-search" placeholder="请输入" style="width: auto" /> -->
            </div>
          </div>
        </div>
      </div>
      <div class="module">
        <div class="title">
          <h2>数智立方</h2>
          <Button type="primary" @click="save(4)">保存</Button>
        </div>
        <div class="row">
          <div class="left">我的图谱</div>
          <div class="right">
            <div>
              <span>最大创建数量</span>
            </div>
            <div class="btn">
              <Input v-model="formData.relationConfig.maxNumberOfCanvases" placeholder="请输入" style="width: auto" />
            </div>
          </div>
        </div>
      </div>

      <div style="height: 20px"></div>
    </div>
    <choose-center-map ref="centerMapShowRef" @getCenterPoint='getCenterPoint' :defaultCenterPoint='formData.globalConfig.mapCenterPoint'></choose-center-map>
  </div>
</template>
<script>
import { mapActions, mapGetters } from 'vuex'
import { getManageResourceList } from '@/api/dataGovernance'
import { getParamDataByKeys, paramUpdate, queryParamDataByKeys } from '@/api/config'
import chooseCenterMap from './choose-center-map.vue'
export default {
  components: {
    chooseCenterMap
  },
  props: {},
  data() {
    return {
      formData: {
        globalConfig: {
          nameWatermarkSwitch: false,
          workCodeWatermarkSwitch: false,
          dayStartTime: "",
          dayEndTime: ""
        },       // 全局设置
        mapConfig: {},          // 图上作战
        archivesConfig: {               // 全息档案
          timeInfo: [],
          realNameArcBackground: [],    // 实名人档
          vehicleArcBackground: [],     // 车辆档案
        },
        relationConfig: {},     // 数智立方
        searchConfig: {
          faceList: [],
          faceLibConfig: []
        },       // 智慧云搜
      },
      resourceList: [],
    };
  },
  computed: {},
  watch: {},
  filter: {},
  created() {},
  mounted() {
    this.init()
    this.resourceListFn()
  },
  methods: {
    ...mapActions({
      getSystemAllData: 'systemParam/getSystemAllData'
    }),
    init () {
      var param =["ICBD_GLOBAL_CONFIG", "ICBD_MAP_CONFIG", "ICBD_ARCHIVES_CONFIG", "ICBD_RELATION_CONFIG", "ICBD_SEARCH_CONFIG" ]
      queryParamDataByKeys(param).then(res => {
        // console.log(res)
        var list = res.data
        list.forEach(item => {
          switch (item.paramKey) {
            case "ICBD_GLOBAL_CONFIG":
              this.formData.globalConfig = JSON.parse(item.paramValue)
              break;
            case "ICBD_MAP_CONFIG":
              this.formData.mapConfig = JSON.parse(item.paramValue)
              break;
            case "ICBD_ARCHIVES_CONFIG":
              this.formData.archivesConfig = JSON.parse(item.paramValue)
              break;
            case "ICBD_RELATION_CONFIG":
              this.formData.relationConfig = JSON.parse(item.paramValue)
              break;
            case "ICBD_SEARCH_CONFIG":
              this.formData.searchConfig = JSON.parse(item.paramValue)
              break;
            default:
              break;
          }
        })
        // console.log(this.formData)
      })
    },
    /**
     * 获取已添加管理资源
     */
    resourceListFn () {
      var param = {
        // catalogId: ["20", "3"]
      }
      getManageResourceList(param).then(res => {
        console.log(res)
        this.resourceList = res.data
      })
    },

    save (val) {
      if (this.formData.globalConfig.showInfo.includes('姓名')) {
        this.formData.globalConfig.nameWatermarkSwitch = true;
      }else{
        this.formData.globalConfig.nameWatermarkSwitch = false;
      }
      if (this.formData.globalConfig.showInfo.includes('警号')) {
        this.formData.globalConfig.workCodeWatermarkSwitch = true;
      }else{
        this.formData.globalConfig.workCodeWatermarkSwitch = false;
      }

      if (this.formData.globalConfig.timeInfo.length > 0) {
        this.formData.globalConfig.dayStartTime = this.formData.globalConfig.timeInfo[0]
        this.formData.globalConfig.dayEndTime = this.formData.globalConfig.timeInfo[1]
      }

      if (this.formData.searchConfig.faceList.length > 0) {
        this.formData.searchConfig.faceLibConfig.forEach(item => {
          if (this.formData.searchConfig.faceList.includes(item.field_name)) {
            item.cloud_search = 1
          }else {
            item.cloud_search = 0
          }
        })
      }else {
        this.formData.searchConfig.faceLibConfig.forEach(item => {
          item.cloud_search = 0
        })
      }

      console.log('表单内容', this.formData)
      var paramKey = ""
      var paramValue=""
      switch (val) {
        case 1:
          paramKey = "ICBD_GLOBAL_CONFIG"
          paramValue = JSON.stringify(this.formData.globalConfig)
          break;
        case 2:
          paramKey = "ICBD_MAP_CONFIG"
          paramValue= JSON.stringify(this.formData.mapConfig)
          break;
        case 3:
          paramKey = "ICBD_ARCHIVES_CONFIG"
          paramValue= JSON.stringify(this.formData.archivesConfig)
          break;
        case 4:
          paramKey = "ICBD_RELATION_CONFIG"
          paramValue= JSON.stringify(this.formData.relationConfig)
          break;
        case 5:
          paramKey = "ICBD_SEARCH_CONFIG"
          paramValue= JSON.stringify(this.formData.searchConfig)
          break;
        default:
          break;
      }

      var param = {
        paramKey: paramKey,
        paramType: "icbd",
        paramValue: paramValue
      }
      paramUpdate(param).then(res => {
        this.$Message.success('修改成功')
        this.getSystemAllData(1)
      })
    },
    showChooseMap(){
      this.$refs.centerMapShowRef.init()
    },
    getCenterPoint(centerPoint){
      console.log(centerPoint)
      this.formData.globalConfig.mapCenterPoint = centerPoint
    }
  },
};
</script>
<style lang="less" scoped>
  .layout {
    position: relative;
    width: 100%;
    height: 100%;
    background: #fff;
    display: flex;
    justify-content: center;
    overflow-y: auto;
    padding-bottom: 20px;
    .button {
      position: fixed;
      right: 30px;
      top: 110px;
      cursor: pointer;
    }
    .content {
      width: 50%;
      .module {
        margin-top: 30px;
        .title {
          display: flex;
          justify-content: space-between;
        }
        .row {
          display: flex;
          justify-content: start;
          font-size: 14px;
          height: 50px;
          line-height: 50px;
          margin-top: 20px;
          .left{
            width: 80px;
          }
          .right {
            flex: 1;
            background: #F9F9F9;
            padding: 0 10px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            .span{
              float: left;
              margin-right: 20px;
              display: inline-block;
            }
            .ivu-checkbox-group {
              float: left;
            }
            .ivu-select {
              flex: 1;
            }
            .btn {
              position: relative;
              display: flex;
              align-items: center;
              .position {
                position: absolute;
                right: 10px;
              }
            }
          }
          .right2 {
            flex: 1;
            display: flex;
            justify-content: space-between;
            .childLeft {
              position: relative;
              width: 49%;
              background: #F9F9F9;
              display: flex;
              justify-content: space-between;
              align-items: center;
              padding: 0 10px;
              .position {
                position: absolute;
                right: 20px;
              }
            }
            .childRight {
              position: relative;
              width: 49%;
              background: #F9F9F9;
              display: flex;
              justify-content: space-between;
              align-items: center;
              padding: 0 10px;
              .position {
                position: absolute;
                right: 20px;
              }
            }
          }
        }
      }
    }
  }

  /deep/ .ivu-input-group-append {
    width: 30px;
  }

  /deep/ .ivu-input {
    width: 150px;
  }
   .link-active-color {
    color: #1A74E7;
    cursor: pointer;
  }
</style>
