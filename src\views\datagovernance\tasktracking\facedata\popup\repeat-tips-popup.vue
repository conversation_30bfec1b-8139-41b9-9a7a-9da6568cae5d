<template>
  <ui-modal title="重复图像" v-model="visible" :styles="styles" :footer-hide="true">
    <p class="repeat-type mb-md">
      <span class="mr-xs">重复类型:</span>
      <span class="active-color">短时大量抓拍图片</span>
    </p>
    <section class="message-box mb-lg">
      <p class="mr-lg">
        <i class="icon-font icon-peizhi-1 mr-xs"></i>
        <span>南山区粤海街道东3巷6号编号110号机</span>
      </p>
      <p class="mr-lg">
        <i class="icon-font icon-peizhi-1 mr-xs"></i>
        <span>2021-06-04 15:31:14~10:31:14</span>
      </p>
      <p>
        <i class="icon-font icon-peizhi-1 mr-xs"></i>
        2021-06-04 15:31:14~10:31:14
        <span class="font-red">2</span>
        秒内连续抓拍
        <span class="font-table-action">100</span>
        条同一目标
      </p>
    </section>
    <face-list :face-list="faceList" height="3.3rem" :has-hover="false"> </face-list>
    <div class="no-data" v-if="!faceList.length">
      <i class="no-data-img icon-font icon-zanwushuju1"></i>
    </div>
    <ui-page class="page" :pageData="pageData" @changePage="changePage" @changePageSize="changePageSize"> </ui-page>
  </ui-modal>
</template>
<style lang="less" scoped>
.message-box {
  display: flex;
  color: #f5f5f5;
}
.repeat-type {
  color: #f5f5f5;
  .active-color {
    color: var(--color-bluish-green-text);
  }
}
</style>
<script>
export default {
  props: {
    value: {},
    faceList: {
      default() {
        return [
          {},
          {},
          {},
          {},
          {},
          {},
          {},
          {},
          {},
          {},
          {},
          {},
          {},
          {},
          {},
          {},
          {},
          {},
          {},
          {},
          {},
          {},
          {},
          {},
          {},
          {},
          {},
          {},
          {},
          {},
          {},
          {},
          {},
          {},
          {},
        ];
      },
    },
  },
  data() {
    return {
      visible: false,
      styles: {
        //top: "0.25rem",
        width: '85%',
      },
      pageData: {
        pageNum: 1,
        pageSize: 20,
        totalCount: 10,
      },
      // nomalLabelList: [
      //     { name: '抓拍时间', value: '2021-04-11  15:10:20'},
      //     { name: '入库时间', value: '2021-04-11  15:10:20'},
      //     { name: '结论', value: '抓拍时间晚于入库时间，抓拍时间异常 '},
      // ]
    };
  },
  created() {},
  mounted() {},
  methods: {
    changePage() {},
    changePageSize() {},
  },
  watch: {
    visible(val) {
      this.$emit('input', val);
    },
    value(val) {
      this.visible = val;
    },
  },
  components: {
    FaceList: require('@/components/face-list.vue').default,
  },
};
</script>
