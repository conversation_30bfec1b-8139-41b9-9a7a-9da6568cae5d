/*
 * @Author: zhengmingming <EMAIL>
 * @Date: 2024-09-09 11:29:16
 * @LastEditors: liyuhang1 <EMAIL>
 * @LastEditTime: 2025-03-12 18:12:52
 * @FilePath: \icbd-view\src\router\router.permission.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import router from "./index";
import store from "@/store";
import { LoadingBar } from "view-design";
import {
  getToken,
  setToken,
  getAuthor,
} from "@/libs/configuration/util.common";

const allowList = ["Login", "Register", "ForgetPassword"]; // no redirect allowList
const loginRoutePath = "/login";
let hasToken = false;
import { getUserInfo } from "@/libs/configuration/util.permission";

router.beforeEach((to, from, next) => {
  LoadingBar.start(); // start progress bar
  if (to.name === "error_404") {
    next();
    return;
  }
  init(to, from, next);
});

function init(to, from, next) {
  // const url = window.location.search;
  // if (url.includes("refresh_token")) {
  //   const token = url.substr(url.indexOf("refresh_token") + 14);
  //   hasToken = true;
  //   setToken(token);
  //   window.location.href = window.location.href.replace(url, "");
  // }
  if (to.query.refresh_token) {
    let tokenRe = to.query.refresh_token;
    hasToken = true;
    setToken(tokenRe);
    next({ path: to.path, query: { ...to.query, refresh_token: undefined } });
    return;
  }
  const token = getToken();

  /* has token */
  if (token && token !== undefined) {
    if (to.path === loginRoutePath) {
      getUserInfo("from-login", {
        query: "",
      });
      next();
      LoadingBar.finish();
    } else {
      if (store.getters.addRouters.length === 0 || !store.getters.hasGetInfo) {
        // request login userInfo
        getUserInfo(to, from, next);
      } else {
        const routeTile = store.getters.routeTile.find(
          (item) => item.path === to.path
        );
        if (
          routeTile &&
          routeTile.permission &&
          !store.getters.btnPermission.includes(routeTile.permission)
        ) {
          next({ path: "/404" });
        } else {
          next();
        }
      }
    }
  } else {
    if (allowList.includes(to.name)) {
      // 在免登录名单，直接进入
      next();
    } else {
      // 带token进入不需要先走到login中
      if (hasToken) {
        getUserInfo("from-login", {
          query: "",
        });
        next();
        LoadingBar.finish();
      } else {
        next({ path: loginRoutePath, query: { redirect: to.fullPath } });
      }

      LoadingBar.finish(); // if current page is login will not trigger afterEach hook, so manually handle it
    }
  }
}

router.afterEach((to) => {
  // 多页控制 打开新的页面
  LoadingBar.finish();
  // setTitle(router.app)
  window.scrollTo(0, 0);
});
