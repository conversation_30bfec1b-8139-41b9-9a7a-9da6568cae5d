<template>
  <div class="servermonitoring">
    <div class="left-div">
      <charts></charts>
    </div>
    <div class="right-div auto-fill">
      <search-list class="search-module" @startSearch="startSearch"></search-list>
      <ui-table class="auto-fill" :table-columns="tableColumns" :table-data="tableData" :loading="loading">
        <template #system="{ row }">
          <div v-if="!!row.system">
            {{ row.system.arch || '' + ' ' + row.system.name + ' ' + row.system.version }}
          </div>
        </template>
        <template #cpu="{ row }">
          <Progress
            style="width: 100%"
            :percent="!!row.cpu && row.cpu.usePercent ? row.cpu.usePercent : 0"
            :stroke-color="
              !!row.cpu && row.cpu.usePercent > 90 ? 'var(--color-progress-warning)' : 'var(--color-progress-success)'
            "
            :hide-info="true"
          />
          <div>{{ (!!row.cpu && row.cpu.usePercent ? row.cpu.usePercent : 0) + '%' }}</div>
        </template>
        <template #memory="{ row }">
          <Progress
            style="width: 100%"
            :percent="!!row.memory && row.memory.usePercent ? row.memory.usePercent : 0"
            :stroke-color="
              !!row.memory && row.memory.usePercent > 90 ? 'var(--color-progress-warning)' : 'var(--color-warning)'
            "
            :hide-info="true"
          />
          <div>
            {{
              row.memory
                ? Number(row.memory.use || 0).toFixed(1) +
                  'GB/' +
                  (row.memory.total || 0) +
                  'GB/' +
                  (row.memory.usePercent || 0) +
                  '%'
                : '0GB/0GB/0%'
            }}
          </div>
        </template>
        <template #disc="{ row }">
          <Progress
            style="width: 100%"
            :percent="!!row.disc && row.disc.totalUsePercent ? row.disc.totalUsePercent : 0"
            :stroke-color="
              !!row.disc && row.disc.totalUsePercent > 90 ? 'var(--color-progress-warning)' : '--color-progress-default'
            "
            :hide-info="true"
          />
          <div>
            {{
              !!row.disc
                ? (row.disc.totalUse || 0) +
                  'GB/' +
                  (row.disc.total || 0) +
                  'GB/' +
                  (row.disc.totalUsePercent || 0) +
                  '%'
                : '0GB/0GB/0%'
            }}
          </div>
        </template>
        <template #GPU="{ row }">
          <div class="gpu-row-box">
            <div v-if="row.gpu?.length">
              <Progress
                style="width: 140px"
                :percent="row._viewGpu.percent"
                :stroke-color="row._viewGpu.percent > 90 ? 'var(--color-progress-warning)' : '#3FE3AC'"
                :hide-info="true"
              />
              <!-- xxx/4卡/15GB/0% -->
              <p>{{ row._viewGpu.text }}</p>
              <div class="gpu-warning-box" v-if="row._warningGpuLength">{{ row._warningGpuLength }}个报警</div>
            </div>
            <div v-else>--</div>
          </div>
        </template>
        <template #status="{ row }">
          <div
            class="status-tag"
            :style="`background: ${!!row.status ? 'var(--color-success)' : 'var(--color-failed)'}`"
          >
            {{ !!row.status ? '在线' : '离线' }}
          </div>
        </template>
        <template #action="{ row }">
          <ui-btn-tip
            class="operatbtn"
            icon="icon-chakanxiangqing"
            content="查看详情"
            @click.native="getDetail(row)"
          ></ui-btn-tip>
        </template>
      </ui-table>
    </div>
    <view-server v-model="viewShow" :view-data="viewData" @update="update"></view-server>
  </div>
</template>
<script>
import maintain from '@/config/api/maintain';
import { tableColumns } from './utils/enum';
export default {
  name: 'servermonitoring',
  data() {
    return {
      minusTable: 90,
      loading: false,
      viewShow: false,
      orgList: [],
      copyData: {},
      tableColumns: tableColumns(false),
      tableData: [],
      viewData: {},
      searchData: {},
    };
  },
  activated() {
    this.initList();
  },
  methods: {
    startSearch(data) {
      this.searchData = data;
      this.initList();
    },
    async initList() {
      try {
        this.loading = true;
        let res = await this.$http.post(maintain.getServerList, this.searchData);
        const list = res.data.data;
        this.tableData =
          list?.map((item) => {
            return item.gpu?.length > 1
              ? { ...item, _viewGpu: this.returnGpuItem(item), _warningGpuLength: this.returnWarningGpuLen(item) }
              : { ...item, _disableExpand: true, _viewGpu: this.returnGpuItem(item), _warningGpuLength: 0 };
          }) || [];
      } catch (err) {
        console.log(err);
      } finally {
        this.loading = false;
      }
    },
    view(row) {
      this.viewData = row;
      this.viewShow = true;
    },
    update() {
      this.viewData = this.tableData.find((row) => row.serverId === this.viewData.serverId);
    },
    getDetail(row) {
      this.view(row);
    },
    //计算Gpu百分比
    returnGpuItem(row) {
      const defaultReturn = {
        percent: 0,
        text: '--',
      };
      if (!row.gpu?.length) {
        return defaultReturn;
      }
      let item = row.gpu[0];
      try {
        const percent = (item.gpuMemUsed / item.gpuMemTotal) * 100;
        const formatPercent = Number.isInteger(percent) ? percent : percent.toFixed(2) * 1;
        return {
          percent: formatPercent,
          text: `${item.model}/${item.gpuCount}卡/${item.gpuMemTotal}GB/${formatPercent}%`,
        };
      } catch (err) {
        console.log(err);
        return defaultReturn;
      }
    },
    returnWarningGpuLen(row) {
      if (row.gpu.length) {
        return row.gpu.filter((item) => item.gpuMemUsed / item.gpuMemTotal > 0.9).length;
      } else {
        return 0;
      }
    },
  },
  watch: {},
  computed: {},
  props: {},
  components: {
    Charts: require('./components/charts.vue').default,
    UiTable: require('@/components/ui-table.vue').default,
    ViewServer: require('./view-server.vue').default,
    SearchList: require('./components/search-list.vue').default,
  },
};
</script>
<style lang="less" scoped>
.servermonitoring {
  display: flex;
  padding: 0 10px 0 0px;
  height: 100%;
  background: var(--bg-content);
  overflow: hidden;
}

.left-div {
  width: 250px;
  height: 100%;
  border-right: 1px solid var(--border-color);
  padding: 0 10px;
  .option-div {
    border: 1px solid rgba(255, 255, 255, 0.1);
    padding: 2px 10px;
  }
  .button-div {
    margin-top: 30px;
  }
  .offline {
    margin-top: 30px;
  }
}
.right-div {
  flex: 1;
  height: 100%;
  padding: 20px 0 20px 20px;
}
.search-module {
  margin-bottom: 20px;
}
.status-tag {
  width: 60px;
  height: 22px;
  line-height: 22px;
  text-align: center;
  border-radius: 4px;
  color: #fff;
}
// 更改进度条
@{_deep} .ivu-progress {
  .ivu-progress-outer {
    .ivu-progress-inner {
      border: 1px solid #8a9eb6;
      background: transparent;
      padding: 1px;
      .ivu-progress-bg {
        height: 8px !important;
      }
    }
  }
}
@{_deep} .ivu-table-expanded-cell {
  padding: 10px 50px;
}
.gpu-row-box {
  position: relative;
  .gpu-warning-box {
    position: absolute;
    top: 5px;
    right: 10px;
    width: 54px;
    height: 25px;
    background-image: url('../../../../assets/img/systemconfiguration/warning-chat.png');
    background-size: 100% 100%;
    background-position: center center;
    display: flex;
    justify-content: center;
    color: var(--color-content);
    font-size: 12px;
  }
}
</style>
