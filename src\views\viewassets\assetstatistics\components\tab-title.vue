<template>
  <div class="title-container f-14">
    <span class="square vt-middle inline" :class="icon"></span>
    <span
      @click="clickItem(item)"
      class="default-color ml-xs mr-md pointer"
      :class="getClass(item)"
      v-for="item in tabData"
      :key="item.id"
      >{{ item.label }}</span
    >
    <span class="fr">
      <slot name="filter"></slot>
    </span>
  </div>
</template>

<script>
export default {
  name: 'tabTitle',
  components: {},
  props: {
    icon: {},
    data: {},
    value: {},
  },
  data() {
    return {
      tabData: [],
      activeValue: '',
    };
  },
  computed: {},
  watch: {
    data: {
      handler(val) {
        this.tabData = val || [];
      },
      deep: true,
      immediate: true,
    },
    value: {
      handler(val) {
        this.activeValue = val;
      },
      deep: true,
      immediate: true,
    },
  },
  filter: {},
  mounted() {},
  methods: {
    getClass(item) {
      if (item.id === this.activeValue && this.tabData.length === 1) {
        return 'color-green';
      }
      return item.id === this.activeValue && this.tabData.length > 1 ? 'link-text-active' : '';
    },
    clickItem(val) {
      this.activeValue = val.id;
      this.$emit('input', val.id);
      this.$emit('on-change', val.id, val);
    },
  },
};
</script>

<style lang="less" scoped>
.square {
  width: 4px;
  height: 15px;
  background: var(--color-display-title-before);
}
.title-container {
  position: relative;
  padding: 0 10px 0 10px;
  height: 41px;
  line-height: 41px;
  width: 100%;
  background: var(--bg-sub-echarts-title);
  .default-color {
    color: #8392a6;
  }
  .color-green {
    color: var(--color-display-title);
  }
  .link-text-active {
    color: var(--color-display-title);
    position: relative;
    &::before {
      position: absolute;
      content: '';
      width: 50px;
      height: 2px;
      background: var(--color-display-title-before);
      bottom: -13px;

      left: 50%;
      transform: translate(-50%);
    }
  }
}
</style>
