<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>qsdi-root-pom</artifactId>
        <groupId>com.qsdi</groupId>
        <version>1.1.0-RELEASE</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.qsdi</groupId>
    <version>1.0.5-RELEASE</version>
    <artifactId>ivdg-view</artifactId>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <!-- <plugin>
                <groupId>com.spotify</groupId>
                <artifactId>docker-maven-plugin</artifactId>
            </plugin> -->
        </plugins>
    </build>

</project>
