<template>
<div class="areaconfig-manger">
    <div class="areaconfig-mange-container">
        <!-- 地图模式 -->
        <mapCustom ref="mapBase" mapType="area-recommend" sectionName="capture"/>
        <!-- 左面信息展示框 -->
        <leftBox ref="searchBar" label="疑似关注人员高频活动区域推荐" @search="handleSearch" v-show="searchBox"></leftBox>
        <detailsBox ref="detailBox" 
            label="疑似关注人员高频活动区域推荐"
            @backSearch="handleBackSearch"
            @handleDetail="handleDetail"
            :listFun="personHighFreqRegional"
            v-show="!searchBox"></detailsBox>
        <!-- 右侧结果 -->
        <right-list-one :appearList="appearList" :total="total" :loading="loading" :loadingText="loadingText" v-if="rightShowList" @handleReachBottom="handleReachBottom" @show-pic="showPic"></right-list-one>
    </div>
</div>
</template>

<script>
import mapCustom from '@/views/model-market/components/map/index.vue'
import { mapGetters } from 'vuex'
import leftBox from './components/leftBox.vue';
import detailsBox from './components/detailsBox.vue';
import RightListOne from './components/RightListOne.vue';
import { personHighFreqRegional } from '@/api/recommend';
import { getPortraitCapture } from '@/api/realNameFile'

export default {
    name: "areaconfig",
    components: {
        mapCustom,
        leftBox,
        detailsBox,
        RightListOne
    },
    props: {
      taskParams: {
        type: Object,
        default: () => ({})
      }
    },
    data() {
        return {
            searchBox: true,
            dataList: [], //列表
            current: {},
            appearList: [],
            isLast: false,
            loading: false,
            total: 0,
            loadingText: '加载中',
            rightShowList: false,
            personHighFreqRegional,
            pageInfo: {
                pageNumber: 1,
                pageSize: 20,
            },
            detailsParams: {}
        }
    },
    computed: {
      ...mapGetters({
				userInfo: "userInfo"
			}),
    },
    async created() {
        this.$nextTick(() => {
            // 推荐中心查看
            console.log('taskParams: ', this.taskParams)
            if (!Toolkits.isEmptyObject(this.taskParams)) {
                let params = {...this.taskParams.params}
                delete params.dayNum
                delete params.deviceGbIdList
                if (this.taskParams.queryStartTime) this.$refs.searchBar.queryParams.startTime = this.taskParams.queryStartTime
                if (this.taskParams.queryEndTime) this.$refs.searchBar.queryParams.endTime = this.taskParams.queryEndTime
                this.$refs.searchBar.queryParams = { ...this.$refs.searchBar.queryParams, ...params }
                this.$refs.searchBar.handleSearch()
            }
        })
	},
    methods: {
        handleSearch(params){
            console.log(params, 'params')
            this.searchBox = false;
            this.$nextTick(() => {
                this.$refs.detailBox.handleList({...params})
            })
        },
        handleBackSearch(){
            this.searchBox = true;
            this.handleCancel()
        },
        handleCancel() {
            this.rightShowList = false;
            this.$refs.mapBase.clearSelectDraw()
        },
        handleDetail(item, params) {
            this.rightShowList = true;
            this.pageInfo.pageNumber = 1
            this.appearList = []
            this.$refs.mapBase.showPolygon(item.region)
            this.$refs.mapBase.closeMapDom();
            this.$refs.mapBase.resetMarkerbasicPoint();
            let devices = []
            try {
                devices = JSON.parse(item.region.deviceParam)
            } catch {}
            this.detailsParams = {
                endDate: params.endTime,
                startDate: params.startTime,
                archiveNo: params.vid,
                deviceIds: devices.map(v => v.deviceGbId)
            }
            this.getRightList()
        },
        showPic(item,index){
          this.$refs.mapBase.chooseNormalPoint(
                this.basicPoints,
                "capture",
                index
            );
        },
        getRightList(){
            this.loading = true;
            let params = {...this.detailsParams, ...this.pageInfo}
            this.$refs.mapBase.closeMapDom();
            this.$refs.mapBase.resetMarkerbasicPoint();
            getPortraitCapture(params)
            .then(res => {
                let list = res.data.entities || []
                this.appearList = this.appearList.concat(list)
                this.basicPoints = this.appearList.map((item) => {
                    item.showIconBorder = true;
                    return item;
                });
                this.$refs.mapBase.sprinkleNormalPoint(this.basicPoints, true);
                this.$refs.mapBase.setCenter(this.basicPoints[0]);
                if (list && list.length) {
                    this.pageInfo.pageNumber += 1
                    this.total = res.data.total
                } else {
                    this.isLast = true
                }
            })
            .finally(() =>{
                this.loading = false;
            })
        },
        handleReachBottom() {
            this.loadingText = '加载中'
            if (this.isLast) {
                this.loadingText = '已经是最后一页了'
                return
            }
            return this.getRightList()
        }
    }
};
</script>

<style lang="less">
.areaconfig-manger {
    height: 100%;
    width: 100%;
    position: relative;
}
.areaconfig-mange-container {
    height: 100%;
}
</style>
