<template>
  <div class="relationship-info" v-if="modalShow">
    <ui-modal
      width="1000"
      v-model="modalShow"
      :footer-hide="true"
      :title="labelCn"
    >
      <div class="relationship-content">
        <div class="relationship-content-header">
          <UiCard
            class="mt10 mb20"
            :card-data="fromNode"
            :property-list="currentNodePropertyList"
          />
          <div class="relationship-content-center">
            <img :src="cube" alt="#" />
          </div>
          <UiCard
            class="mt10 mb20"
            :card-data="toNode"
            :property-list="currentNodePropertyList"
          />
        </div>

        <UiCardList
          v-if="relationData.length > 0"
          :showTab="showTab"
          :propertyDicMap="propertyDicMap"
          :relationData="relationData"
        />
      </div>
      <Spin size="large" fix v-if="spinShow"></Spin>
    </ui-modal>
  </div>
</template>
<script>
import imgloading from "@/assets/img/car1.webp";
import relationship from "@/assets/img/relationship.png";
import cube from "@/assets/img/number-cube/cube.png";
import relationshipright from "@/assets/img/relationship-right.png";
import {
  relationDetail2,
  previewMiningRelationDetail,
} from "@/api/number-cube";
import { queryDataByKeyTypes } from "@/api/user";
import dayjs from "dayjs";

export default {
  components: {
    UiCard: require("./ui-card").default,
    UiCardList: require("./ui-card-list").default,
  },
  props: {
    value: {
      type: Boolean,
      default: false,
    },
    fromNode: {
      type: Object,
      default: () => {},
    },
    toNode: {
      type: Object,
      default: () => {},
    },
    propertyList: {},
    relationMiningExecLogId: {
      type: String,
      default: () => "",
    },
  },
  data() {
    return {
      modalShow: false,
      imgloading,
      relationship,
      cube,
      relationshipright,
      relationData: [],
      groupFieldList: [],
      detailFieldList: [],
      currentNodePropertyList: [],
      detailType: "",
      labelCn: "",
      spinShow: false,
      showTab: false,
      propertyDicMap: {},
    };
  },
  computed: {
    propertyNameObject() {
      let propertyNameObject = {};
      this.propertyList.forEach((ele) => {
        propertyNameObject[ele.name] = ele.nameCn;
      });
      return propertyNameObject;
    },
  },
  watch: {
    modalShow(val) {
      this.$emit("input", val);
    },
    value(val) {
      this.modalShow = val;
    },
  },
  filter: {},
  created() {},
  mounted() {},
  methods: {
    /**
     * 关系详情
     */
    init(isExcavating = false) {
      this.currentNodePropertyList = [];
      Object.keys(this.fromNode.ext.properties).forEach((key) => {
        const filterProperty = key in this.propertyNameObject;
        if (filterProperty) {
          this.currentNodePropertyList.push({
            label: this.propertyNameObject[key],
            value: this.fromNode.ext.properties[key],
          });
        }
      });
      this.relationData = [];
      this.relationDetail(isExcavating);
    },
    getEntity(arr, vid, splitEntityFieldList) {
      // getEntity 是个数组，获取拼接属性的字符串和实体id比较
      return (
        arr.filter((el) =>
          vid.includes(splitEntityFieldList.map((key) => el[key])?.join(""))
        ) || []
      );
    },
    queryDataByKeyTypes(dicTypes) {
      if (dicTypes.length === 0) return;
      queryDataByKeyTypes(dicTypes).then((res) => {
        this.propertyDicMap = res.data.reduce(
          (obj, item) => ({ ...obj, ...item }),
          {}
        );
      });
    },
    getItemGroupData(list, groupFields) {
      const groupDataMap = list.reduce((group, item) => {
        const groupId = groupFields.map((el) => item[el.fieldName])?.join("_");
        if (groupId) {
          group[groupId] = [...(group[groupId] || []), item];
        }
        return group;
      }, {});
      return groupDataMap;
    },
    getEntityGroup(item, sourceId, targetId, splitEntityFieldList) {
      const sourceEntity = this.getEntity(item, sourceId, splitEntityFieldList);
      const targetEntity = this.getEntity(item, targetId, splitEntityFieldList);
      return [
        {
          index: 0,
          itemData: [...sourceEntity],
        },
        {
          index: 0,
          itemData: [...targetEntity],
        },
      ];
    },
    getGroupData(
      detailList,
      sourceId,
      targetId,
      splitEntityFieldList,
      groupFields
    ) {
      if (detailList.length === 0) return { groupData: [], data: [] };
      let is2DArr = false;
      const groupDataMap = detailList.reduce((group, item) => {
        // 关系挖掘-人脸场所出现二次分组
        const info = item?.[0] || {};
        // 关系挖掘-人脸场所出现通过groupFields进行字段分组，其他的根据edgeId分组
        const groupId = groupFields
          ? groupFields.map((el) => info[el.fieldName])?.join("_")
          : info.edgeId;
        if (item.length > 1) is2DArr = true;
        if (groupId) {
          group[groupId] = [...(group[groupId] || []), item];
        }
        return group;
      }, {});
      return Object.keys(groupDataMap).map((key) => {
        const groupData = groupDataMap[key] || [];
        let delGroupData = groupData;
        if (is2DArr) {
          delGroupData = groupData.map((item) => {
            // [[[source],[target]]]
            // 卡片展示一行是一个对比组，所以source，target都是数组，一般都是长度1的数组，特殊情况就是关系挖掘-人脸场所，可以单组切换
            return this.getEntityGroup(
              item,
              sourceId,
              targetId,
              splitEntityFieldList
            );
          });
        }
        const newGroupData = delGroupData?.[0]?.[0];

        return {
          groupData: newGroupData?.itemData
            ? newGroupData?.itemData[0]
            : newGroupData,
          data: delGroupData || [],
        };
      });
    },
    getGroupField(associationLabelName, miningGroupField) {
      if (!associationLabelName) return associationLabelName;
      return miningGroupField?.[associationLabelName];
    },
    relationDetail(isExcavating) {
      let sourceId = "";
      let targetId = "";
      // ---------isExcavating为true 挖掘中关系详情预览，只需更换接口参数，返回数据不变------------
      let apiDetail = "";
      let param = {};
      if (isExcavating) {
        let excavaDetail = this.toNode.edge.ext;
        sourceId = this.toNode.edge.source;
        targetId = this.toNode.edge.target;
        apiDetail = previewMiningRelationDetail;
        param = {
          graphInstanceId: excavaDetail.metadata.qsdi_graph_instance_id,
          relationMiningExecLogId: this.relationMiningExecLogId,
          targetEntityId: targetId,
        };
        this.labelCn = excavaDetail.labelCn;
        this.showTab = true;
        this.spinShow = true;
      } else {
        apiDetail = relationDetail2;
        const edgeDetail = this.toNode.edge.ext.detail || this.toNode.edge.ext;
        const { label, relationSource, labelCn, metadata } = edgeDetail;
        sourceId = this.fromNode.id;
        targetId = this.toNode.id;
        param = {
          graphInstanceId: metadata.qsdi_graph_instance_id,
          relationName: label,
          sourceEntityId: sourceId,
          targetEntityId: targetId,
        };
        this.labelCn = labelCn;
        this.showTab = relationSource === 3;
        this.spinShow = true;
      }
      apiDetail(param)
        .then((res) => {
          const data = res.data || [];
          if (data.length === 0) return;
          const dicTypeCodes = [];
          this.relationData = data.map((item) => {
            const {
              dataList,
              groupFieldList = [],
              detailFieldList = [],
              associationLabelName,
              miningGroupField,
              splitEntityFieldList,
            } = item;

            //  如果是挖掘关系，则根据miningGroupField关联分组，其它则保持groupFieldList
            const newGroupFieldList = associationLabelName
              ? this.getGroupField(associationLabelName, miningGroupField)
              : groupFieldList;

            [...newGroupFieldList, ...detailFieldList]?.forEach((item) => {
              item.dicTypeCode && dicTypeCodes.push(item.dicTypeCode);
            });

            return {
              ...item,
              dataList: undefined,
              groupFieldList: newGroupFieldList,
              detailList: this.getGroupData(
                this.sortData(dataList || [], newGroupFieldList || []),
                sourceId,
                targetId,
                splitEntityFieldList,
                associationLabelName ? newGroupFieldList : null
              ),
            };
          });
          this.queryDataByKeyTypes(dicTypeCodes);
        })
        .finally(() => {
          this.spinShow = false;
        });
    },
    sortData(dataList, fieldList) {
      const info = fieldList.find((item) => item.fieldType === "time");
      if (!info) return dataList;
      return dataList.sort((a, b) =>
        dayjs(b?.[info.fieldName]).isBefore(dayjs(a?.[info.fieldName]))
      );
    },
  },
};
</script>
<style lang="less" scoped>
.relationship-info {
  position: relative;
  .relationship-content-header {
    display: flex;
    flex-wrap: nowrap;
    justify-content: space-between;
    align-items: center;
    .relationship-content-center {
      div {
        color: #2c86f8;
        text-align: center;
      }
    }
  }
}
.pl20 {
  padding-left: 20px;
}
.pr20 {
  padding-right: 20px;
}
.pr6 {
  padding-right: 6px;
}
.pr30 {
  padding-right: 30px;
}
.inlineb {
  display: inline-block;
}
</style>
