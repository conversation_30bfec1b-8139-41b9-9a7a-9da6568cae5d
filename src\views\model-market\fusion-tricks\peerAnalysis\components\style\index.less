// 头部名称
.title{
    font-size: 16px;
    font-weight: bold;
    color: rgba(0,0,0,0.9);
    height: 40px;
    position: relative;
    line-height: 40px;
    padding-left: 20px;
    border-bottom: 1px solid #D3D7DE;
    display: flex;
    justify-content: space-between;
    align-items: center;
    top: 0;
    // z-index: 999;
    background: #fff;
    &:before {
        content: '';
        position: absolute;
        width: 3px;
        height: 20px;
        top: 50%;
        transform: translateY(-50%);
        left: 10px;
        background: #2c86f8;
    }
    span{
        color: #2C86F8; 
    }
    /deep/.ivu-icon-ios-close{
        font-size: 30px;
        cursor: pointer;
    }
}

// 图片滑动

.swiper-container {
    width: 100%;
    height: 100%;
    .swiper-wrapper .swiper-slide {
        width: 80px;
        height: 80px;
        text-align: center;
        & > img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            cursor: pointer;
        }
    }
}
.swiper-pagination {
    bottom: 0px;
    width: 100%;
}
/deep/.swiper-pagination-bullet {
    width: 6px;
    height: 6px;
    margin-left: 2px;
    background: #fff;
    opacity: 1;
}
/deep/.swiper-pagination-bullet-active {
    background: #2c86f8;
}
.right-img-list{
    .swiper-wrapper .swiper-slide{
        width: 100px;
        height: 100px;
    }  
}
.loading, .endlist{
    color: #2C86F8;
    text-align: center;
}

.footer{
    display: flex;
    justify-content: center;
    cursor: pointer;
    padding: 10px 0;
    color: #2C86F8;
    &:hover{
        color: #4597FF;
    }
    img{
        transform: rotate(0deg);
        transition: transform 0.2s;
        margin-right: 5px;
    }
}
.packArrow{
    img{
        transform: rotate(180deg);
        transition: transform 0.2s;
    }
}