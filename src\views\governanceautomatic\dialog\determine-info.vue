<template>
  <ui-modal v-model="visible" :title="title" width="42.1rem" @onCancel="handleReset">
    <Form ref="formInfo" :model="formValidate" :rules="ruleValidate" :label-width="100">
      <FormItem label="治理内容：" prop="governanceContent">
        <Select v-model="formValidate.governanceContent" placeholder="请选择治理内容">
          <Option v-for="(item, index) in governanceContents" :key="index" :value="item.code">{{ item.label }}</Option>
          <!-- <Option value="1">设备信息自动获取</Option> -->
        </Select>
      </FormItem>
      <FormItem v-if="type === '0'" label="数据对象：">
        <RadioGroup v-model="formValidate.dataobj" @on-change="handleRadioChange('dataobj', 'businessIdList')">
          <Radio label="0">全部设备</Radio>
          <Radio label="1">自定义设备</Radio>
        </RadioGroup>
      </FormItem>
      <FormItem
        v-if="formValidate.dataobj === '1'"
        label=""
        prop="businessIdList"
        :rules="[
          {
            required: formValidate.dataobj === '1' || false,
            message: '请选择设备',
            trigger: 'change',
            type: 'array',
          },
        ]"
      >
        <div class="device-content">
          <div class="camera fl" @click="selectCamera">
            <span class="font-blue camera-text" v-if="formValidate.businessIdList.length"
              >已选择{{ formValidate.businessIdList.length }}条设备</span
            >
            <span v-else>
              <i class="icon-font icon-xuanzeshexiangji inline font-blue f-14 mr-sm"></i>
              <span class="font-blue camera-text">请选择设备</span>
            </span>
          </div>
        </div>
      </FormItem>
      <FormItem v-if="type === '0'" label="治理时间：">
        <RadioGroup v-model="formValidate.immediately" @on-change="handleRadioChange('immediately', 'time')">
          <Radio label="0">立即执行</Radio>
          <Radio label="1">自定义时间</Radio>
        </RadioGroup>
        <FormItem
          v-if="formValidate.immediately === '1'"
          label=""
          prop="time"
          :rules="[
            {
              required: formValidate.immediately === '1' ? true : false,
              message: '请选择治理时间',
              trigger: 'change',
              type: 'date',
            },
          ]"
        >
          <DatePicker
            v-model="formValidate.time"
            type="datetime"
            format="yyyy-MM-dd HH:mm:ss"
            placeholder="请选择时间"
            style="width: 100%"
            @on-change="handleChangeTime"
          ></DatePicker>
        </FormItem>
      </FormItem>
    </Form>
    <governinfo
      ref="governinfo"
      v-if="formValidate.governanceContent === '2'"
      :editInfos="editInfos"
      @getInfos="getInfos"
    ></governinfo>
    <select-device
      ref="SelectDevice"
      :isfromTheme="false"
      :selectedList="formValidate.businessIdList"
      @getDeviceIdList="getDeviceIdList"
    ></select-device>
    <template slot="footer">
      <Button type="primary" @click="previous">上一步</Button>
      <Button type="primary" @click="submit">确&nbsp;定</Button>
    </template>
  </ui-modal>
</template>
<script>
import governanceautomatic from '@/config/api/governanceautomatic';
import user from '@/config/api/user';
export default {
  props: {
    type: {
      type: String,
      default: '0',
    },
  },
  data() {
    return {
      visible: false,
      title: '新增检测任务-确定治理信息',
      formValidate: {
        governanceContent: '1',
        dataobj: '0',
        immediately: '1',
        time: '',
        businessType: '1', // 视图基础数据
        businessIdList: [],
      },
      ruleValidate: {
        governanceContent: [
          {
            required: true,
            message: '请选择治理内容',
            trigger: 'change',
          },
        ],
      },
      // businessIdList: [], // 所选设备
      governanceContents: [],
      formdata: {},
      governanceTime: '',
      infos: {},
      editInfos: {},
      isEdit: false,
      dictData: {},
    };
  },
  created() {},
  methods: {
    async init(data, editData, isEdit) {
      this.visible = true;
      this.editInfos = editData;
      this.isEdit = isEdit;
      this.$nextTick(() => {
        this.title = isEdit ? '编辑检测任务-确定治理信息' : '新增检测任务-确定治理信息';
        this.formdata = data;
        this.getGovernanceContent();
        this.formValidate.governanceContent = editData.governanceContent || '1';
        this.formValidate.immediately = editData.governanceTime ? '1' : '0';
        this.formValidate.time = editData.governanceTime || '';
        this.governanceTime = editData.governanceTime || '';
        this.formValidate.businessType = '1';
        this.formValidate.dataobj =
          editData.businessDataOrConfigList && editData.businessDataOrConfigList.length ? '1' : '0';
        if (editData.businessDataOrConfigList && editData.businessDataOrConfigList.length) {
          this.formValidate.businessIdList = editData.businessDataOrConfigList.map((item) => Number(item.businessId));
        } else {
          this.formValidate.businessIdList = [];
        }
      });
      await this.getDictData();
    },
    selectCamera() {
      this.$refs.SelectDevice.init(this.dictData);
    },
    getDeviceIdList(data) {
      this.formValidate.businessIdList = data;
      this.$nextTick(() => {
        this.$refs.formInfo.validateField('businessIdList');
      });
    },
    previous() {
      this.visible = false;
    },
    handleRadioChange(key, name) {
      if (this.formValidate[key] === '1') {
        this.$nextTick(() => {
          this.$refs.formInfo.validateField(name);
        });
      }
    },
    // 根据资源类型获取治理内容
    async getGovernanceContent() {
      try {
        let res = await this.$http.get(governanceautomatic.getGovernanceContentBysourceType, {
          params: { sourceType: this.formdata.sourceType },
        });
        this.governanceContents = res.data.data;
      } catch (error) {
        console.log(error);
      }
    },
    handleChangeTime(date) {
      this.governanceTime = date;
    },
    getInfos(data) {
      this.infos = data;
    },
    async submit() {
      let governinfoValid = true;
      const valid = await this.$refs['formInfo'].validate((valid) => valid);
      if (this.formValidate.governanceContent === '2') {
        governinfoValid = await this.$refs.governinfo.valids();
      }
      if (!valid || !governinfoValid) {
        this.$Message.error('请将信息填写完整！');
        return false;
      }
      let params = {};
      params = {
        taskName: this.formdata.taskName, // 任务名称
        sourceType: this.formdata.sourceType, // 资源类型
        governanceContent: this.formValidate.governanceContent, // 治理内容
        governanceTime: this.formValidate.immediately === '1' ? this.governanceTime : null, // 治理时间
        businessType: this.formValidate.businessType, // 业务数据类型
        businessIdList: this.formValidate.businessIdList, // 传所选设备 为空则默认全部设备
      };
      if (this.formValidate.governanceContent === '2') {
        params = Object.assign(params, this.infos);
      }
      if (this.isEdit) {
        params.id = this.editInfos.id;
      }
      this.updateTask(params);
    },
    async updateTask(params) {
      try {
        await this.$http.post(governanceautomatic.edit, params);
        this.$Message.success('任务更新成功！');
        this.$emit('close');
        this.handleReset();
      } catch (error) {
        console.log(error);
      }
    },
    handleReset() {
      this.$refs['formInfo'].resetFields();
      this.visible = false;
    },
    async getDictData() {
      try {
        const params = ['propertySearch_sbdwlx', 'sxjgnlx_receive', 'check_status'];
        let res = await this.$http.post(user.queryDataByKeyTypes, params);
        const datas = res.data.data;
        params.forEach((key) => {
          let obj = datas.find((row) => {
            return !!row[key];
          });
          this.$set(this.dictData, key, obj[key]);
        });
      } catch (error) {
        console.log(error);
      }
    },
  },
  watch: {},
  components: {
    SelectDevice: require('@/views/datagovernance/governancetheme/viewprocess/components/select-device.vue').default,
    governinfo: require('../components/governinfo.vue').default,
  },
};
</script>
<style lang="less" scoped>
.device-content {
  display: flex;
  justify-content: center;
  width: 100%;
  // margin: 15px 0;
}
.camera {
  width: 230px;
  margin: 0 auto;
  padding: 0;
  height: 34px;
  line-height: 32px;
  background: rgba(43, 132, 226, 0.1);
  border: 1px dashed var(--color-primary);
  &:hover {
    background: rgba(43, 132, 226, 0.2);
    border: 1px dashed #3c90e9;
  }
  .icon-xuanzeshexiangji {
    line-height: 30px;
  }
}
@{_deep} .ivu {
  &-modal-body {
    padding: 20px 50px !important;
  }
  &-form-item {
    margin-bottom: 15px !important;
  }
}
</style>
