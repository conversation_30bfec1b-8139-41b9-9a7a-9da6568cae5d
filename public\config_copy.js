// 最大展示数量
maxTotal = 10000

/** 播放器配置 begin */

// 开发环境
playerType = 'lwpt' // 播放器类型：pvg、lwpt
liveType = 'rtsp' // pvg: pvg67、pvgplus, 联网平台: rtsp、flv
vodType = 'flv' // pvg: pvg67、pvgplus, 联网平台: hls、flv
desencode = 1 // pvg参数是否需要解密
isCustomControl = true // 是否需要通过调接口来控制播放进度（联网平台设备录像时需要）
vodStorage = 'platform' // 录像类型：平台录像'platform'， 设备录像'device'
lwptIp = '*************' // 联网平台ip、token
//lwptToken = 'eyJhbGciOiAiSFMyNTYiLCAidHlwIjogIkpXVCJ9.eyJ1c2VyIjoiYWRtaW4iLCJwZXJtaXQiOjI2ODYzMjk1OSwibGV2ZWwiOjAsImlzdCI6MTY5Mzg2MzAxN30.33HKJWrb9QTziTZ-b7VLIinNpfWdsG8qSUsvCL9v5h4'
lwptProt = '18080'
lwptToken = 'eyJhbGciOiAiSFMyNTYiLCAidHlwIjogIkpXVCJ9.eyJ1c2VyIjoiYWRtaW4iLCJwZXJtaXQiOjI2ODYzMjk1OSwibGV2ZWwiOjAsImlzdCI6MTcwMjI2ODI0MH0.AnshVD8OLmQWmR3CBQENu3Cjvy0FmP3_GXHOwvAKKc4'

// 蚌埠环境
// playerType = "lwpt" // 播放器类型：pvg、lwpt
// liveType = "flv" // pvg: pvg67、pvgplus, 联网平台: rtsp、flv
// vodType = "flv" // pvg: pvg67、pvgplus, 联网平台: hls、flv
// desencode = 1 // pvg参数是否需要解密
// isCustomControl = true // 是否需要通过调接口来控制播放进度（联网平台设备录像时需要）
// vodStorage = "device" // 联网平台录像类型：平台录像'platform'， 设备录像'device'
// lwptIp = '************'; // 联网平台ip、port、token
// lwptProt = '8080';
// lwptDevice = '34030000002000000110'; // 联网平台下级平台ID
// lwptToken = 'eyJhbGciOiAiSFMyNTYiLCAidHlwIjogIkpXVCJ9.eyJ1c2VyIjoiYWRtaW4iLCJwZXJtaXQiOjI2ODYzMjk1OSwibGV2ZWwiOjAsImlzdCI6MTcwMjg4NDEwM30.ttcIy5gC9s5KFD28IyqN_or5eX6qmdxzYD_c7-NBgEA'

/** 播放器配置 end */
