<template>
  <div class="table-module auto-fill">
    <div class="btn-list over-flow">
      <div class="fl">
        <Checkbox v-model="checkAll" @on-change="handleAllCheck"> 全选 </Checkbox>
        <span class="base-text-color f-14">
          <span class="ml-lg">已选中</span>
          <span>
            <span class="check-num">
              {{ (checkAll ? this.pageData.totalCount : chooseTableData.length) | formatNum }}
            </span>
            <span>条</span>
          </span>
        </span>
        <span v-show="active === 'pendingStorage'" class="ml-lg line"></span>
        <RadioGroup class="ml-lg" v-show="active === 'pendingStorage'" v-model="sign" @on-change="changeSign">
          <Radio label="1">标记差异字段</Radio>
          <Radio class="ml-lg" label="2">标记不合格字段</Radio>
        </RadioGroup>
      </div>
      <div class="fr">
        <Button type="primary" @click="exportDevice">
          <i class="icon-font icon-daochu mr-xs f-12"></i>
          <span>导出</span>
        </Button>
        <Button
          type="primary"
          class="ml-sm"
          v-show="active === 'pendingStorage'"
          :loading="comparisonLoading"
          @click="detectionComparison"
        >
          <i class="icon-font icon-jiance-01 mr-xs"></i>
          <span>资产检测与比对</span>
        </Button>
        <Button
          type="primary"
          class="ml-sm"
          v-show="active === 'pendingStorage'"
          :loading="deleteLoading"
          @click="batchDeleteFill"
        >
          <i class="icon-font icon-piliangshanchu mr-xs f-12"></i>
          <span>批量删除</span>
        </Button>
        <Button type="primary" class="ml-sm" v-show="active === 'inStock'" @click="batchEdit">
          <i class="icon-font icon-bianji3 mr-xs f-12"></i>
          <span>批量编辑</span>
        </Button>
        <Button type="primary" class="ml-sm" v-show="active === 'inStock'" @click="synchroModalShow">
          <i class="icon-font icon-xinxizidongtongbu mr-xs f-12"></i>
          <span>信息自动同步</span>
        </Button>
      </div>
    </div>
    <ui-table
      reserveSelection
      class="ui-table auto-fill mt-sm"
      row-key="id"
      :table-columns="tableColumns"
      :table-data="tableData"
      :loading="loading"
      :is-all="checkAll"
      :default-store-data="defaultStoreData"
      @storeSelectList="storeSelectList"
    >
      <template #deviceId="{ row }">
        <span :class="{ 'check-num': checkField(row, 'deviceId') }">{{ row.deviceId }}</span>
        <span
          v-if="row.sign !== '0' && active === 'pendingStorage'"
          :class="['ml-xs', 'sign', row.sign === '1' ? 'new' : row.sign === '2' ? 'same' : 'diff']"
          >{{ row.sign === '1' ? '新' : row.sign === '2' ? '同' : '异' }}</span
        >
      </template>
      <template #deviceName="{ row }">
        <Tooltip :content="row.deviceName" :disabled="!row.deviceName">
          <div :class="{ 'check-num': checkField(row, 'deviceName') }" class="width-sm inline ellipsis">
            {{ row.deviceName }}
          </div>
        </Tooltip>
      </template>
      <template #orgName="{ row }">
        <span :class="{ 'check-num': checkField(row, 'orgName') }">{{ row.orgName }}</span>
      </template>
      <template #civilName="{ row }">
        <span :class="{ 'check-num': checkField(row, 'civilName') }">{{ row.civilName }}</span>
      </template>
      <!-- 经纬度保留8位小数-->
      <template #longitude="{ row }">
        <span :class="{ 'check-num': checkField(row, 'longitude') }">{{ row.longitude | filterLngLat }}</span>
      </template>
      <template #latitude="{ row }">
        <span :class="{ 'check-num': checkField(row, 'latitude') }">{{ row.latitude | filterLngLat }}</span>
      </template>
      <template #ipAddr="{ row }">
        <span :class="{ 'check-num': checkField(row, 'ipAddr') }">{{ row.ipAddr }}</span>
      </template>
      <template #macAddr="{ row }">
        <span :class="{ 'check-num': checkField(row, 'macAddr') }">{{ row.macAddr }}</span>
      </template>
      <template #sbgnlx="{ row }">
        <Tooltip :content="row.sbgnlxText" :disabled="row.sbgnlxText.split('/').length < 2">
          <div :class="{ 'check-num': checkField(row, 'sbgnlx') }" class="width-xs inline ellipsis">
            {{ row.sbgnlxText }}
          </div>
        </Tooltip>
      </template>
      <template #sbdwlx="{ row }">
        <span :class="{ 'check-num': checkField(row, 'sbdwlx') }">{{ row.sbdwlxText }}</span>
      </template>
      <template #sbcjqy="{ row }">
        <Tooltip :content="row.sbcjqyText" :disabled="!row.sbcjqyText">
          <div :class="{ 'check-num': checkField(row, 'sbcjqy') }" class="width-xs inline ellipsis">
            {{ row.sbcjqyText }}
          </div>
        </Tooltip>
      </template>
      <template #phyStatus="{ row }">
        <span class="check-status-font" :class="row.phyStatus === '1' ? 'font-success' : 'font-failed'">
          {{ row.phyStatus | filterType(phystatusList) }}
        </span>
      </template>
      <template #checkStatus="{ row }">
        <div>
          <span
            class="check-status"
            :class="row.checkStatus === '1' ? 'bg-success' : row.checkStatus === '2' ? 'bg-failed' : ''"
          >
            {{ row.checkStatusText }}
          </span>
        </div>
      </template>
      <template #contrastStatus="{ row }">
        <div>
          <span
            class="check-status-font"
            :class="row.contrastStatus === '1' ? '' : row.contrastStatus === '2' ? 'font-success' : 'font-failed'"
          >
            {{ row.contrastStatusText }}
          </span>
        </div>
      </template>
      <template #examineStatus="{ row }">
        <div>
          <span
            class="check-status-font"
            :class="row.examineStatus === '1' ? 'font-success' : row.examineStatus === '2' ? 'font-failed' : ''"
          >
            {{ row.examineStatusText }}
          </span>
        </div>
      </template>
      <template #isOnline="{ row }">
        <div>
          <span
            class="check-status-font"
            :class="row.isOnline === '1' ? 'font-success' : row.isOnline === '2' ? 'font-failed' : ''"
          >
            {{ row.isOnlineText }}
          </span>
        </div>
      </template>

      <template #baseCheckStatus="{ row }">
        <div :class="row.rowClass">
          <span
            class="check-status"
            :class="row.baseCheckStatus === '0' ? 'bg-success' : row.baseCheckStatus === '1' ? 'bg-failed' : ''"
          >
            {{ row.baseCheckStatus === '1' ? '不合格' : row.baseCheckStatus === '0' ? '合格' : '--' }}
          </span>
        </div>
      </template>
      <template #action="{ row }">
        <div class="action">
          <ui-btn-tip icon="icon-bianji2" content="编辑" @handleClick="edit(row)"></ui-btn-tip>
          <ui-btn-tip
            v-if="active === 'inStock'"
            icon="icon-xinxitongbu"
            content="信息同步"
            @handleClick="synchroResultModalShow(row)"
          ></ui-btn-tip>
          <ui-btn-tip
            v-if="active === 'pendingStorage'"
            icon="icon-yichangyuanyin"
            content="异常原因"
            :styles="{ color: 'var(--color-failed)', 'font-size': '14px' }"
            :disabled="row.checkStatus !== '2'"
            @handleClick="abnormal(row)"
          ></ui-btn-tip>
          <ui-btn-tip
            v-if="active === 'inStock'"
            icon="icon-yichangyuanyin"
            content="异常原因"
            :styles="{ color: 'var(--color-failed)', 'font-size': '14px' }"
            :disabled="row.checkStatus === '1000' || row.checkStatus === '0000'"
            @handleClick="abnormal(row)"
          ></ui-btn-tip>
          <ui-btn-tip
            v-if="active === 'pendingStorage'"
            icon="icon-chayixiangqing"
            content="差异详情"
            :styles="{ color: 'var(--color-table-btn-default)', 'font-size': '14px' }"
            :disabled="row.contrastStatus !== '2'"
            @handleClick="differenceDetails(row)"
          ></ui-btn-tip>
          <ui-btn-tip
            v-if="active === 'inStock'"
            icon="icon-caozuojilu"
            content="操作记录"
            :styles="{ color: 'var(--color-table-btn-default)', 'font-size': '14px' }"
            @handleClick="operationRecord(row)"
          ></ui-btn-tip>
          <OperationBtnMore
            v-if="active === 'pendingStorage'"
            :btn-list="getBtnList(row)"
            :row-data="row"
            @handleOperations="handleOperations"
          ></OperationBtnMore>
        </div>
      </template>
    </ui-table>
    <ui-page class="page" :page-data="pageData" @changePage="changePage" @changePageSize="changePageSize"> </ui-page>
  </div>
</template>
<script>
import { mapGetters } from 'vuex';
import equipmentassets from '@/config/api/equipmentassets';
const optionColumn = [
  {
    width: 100,
    title: '填报人',
    key: 'modifier',
    align: 'left',
    tooltip: true,
  },
  {
    width: 150,
    title: '填报时间',
    key: 'modifyTime',
    align: 'left',
  },
  {
    width: 100,
    title: '审核人',
    key: 'examineUser',
    align: 'left',
    tooltip: true,
  },
  {
    width: 150,
    title: '审核时间',
    key: 'examineTime',
    align: 'left',
  },
  {
    width: 140,
    title: '操作',
    slot: 'action',
    align: 'left',
    fixed: 'right',
    className: 'table-action-padding' /*操作栏列-单元格padding设置*/,
  },
];
export default {
  props: {
    active: {
      type: String,
      default: 'pendingStorage',
    },
    searchParams: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      checkAll: false,
      loading: false,
      comparisonLoading: false,
      deleteLoading: false,
      sign: '1',
      searchData: {
        pageNumber: 1,
        pageSize: 20,
      },
      pageData: {
        pageNum: 1,
        pageSize: 20,
        totalCount: 0,
      },
      tableColumns: [],
      tableBasicColumns: [
        { type: 'selection', fixed: 'left', align: 'center', width: 50 },
        {
          title: '序号',
          type: 'index',
          fixed: 'left',
          align: 'center',
          width: 50,
        },
        {
          title: this.global.filedEnum.deviceId,
          slot: 'deviceId',
          fixed: 'left',
          align: 'left',
          minWidth: 210,
          tooltip: true,
        },
        {
          title: this.global.filedEnum.deviceName,
          slot: 'deviceName',
          align: 'left',
          minWidth: 200,
          tooltip: true,
        },
        {
          width: 120,
          title: '组织机构',
          slot: 'orgName',
          align: 'left',
          tooltip: true,
        },
        {
          width: 100,
          title: '行政区划',
          slot: 'civilName',
          align: 'left',
          tooltip: true,
        },
        {
          width: 120,
          title: `${this.global.filedEnum.longitude}`,
          key: 'longitude',
          align: 'left',
          slot: 'longitude',
          tooltip: true,
        },
        {
          width: 120,
          title: `${this.global.filedEnum.latitude}`,
          key: 'latitude',
          align: 'left',
          slot: 'latitude',
          tooltip: true,
        },
        {
          width: 130,
          title: this.global.filedEnum.ipAddr,
          slot: 'ipAddr',
          align: 'left',
          tooltip: true,
        },
        {
          width: 150,
          title: `${this.global.filedEnum.macAddr}`,
          slot: 'macAddr',
          align: 'left',
          tooltip: true,
        },
        {
          minWidth: 130,
          title: `${this.global.filedEnum.sbgnlx}`,
          slot: 'sbgnlx',
          tooltip: true,
        },
        {
          minWidth: 120,
          title: `${this.global.filedEnum.sbdwlx}`,
          slot: 'sbdwlx',
          tooltip: true,
        },
        {
          minWidth: 130,
          title: `${this.global.filedEnum.sbcjqy}`,
          slot: 'sbcjqy',
          tooltip: true,
        },
        {
          minWidth: 100,
          title: `${this.global.filedEnum.phyStatus}`,
          slot: 'phyStatus',
          tooltip: true,
        },
      ],
      tableData: [],
      defaultStoreData: [],
      chooseTableData: [],
    };
  },
  created() {},
  methods: {
    // 全选
    handleAllCheck() {
      this.chooseTableData = [];
      this.tableData = this.tableData.map((item) => {
        this.$set(item, '_checked', this.checkAll);
        this.$set(item, '_disabled', this.checkAll);
        return item;
      });
    },
    storeSelectList(selection) {
      this.chooseTableData = selection;
    },
    async init() {
      try {
        this.loading = true;
        let { url, params } = this.getParams();
        let res = await this.$http.post(url, params);
        this.pageData.totalCount = res.data.data.total;
        this.tableData = res.data.data.entities;
      } catch (err) {
        console.log(err);
      } finally {
        this.loading = false;
      }
    },
    getParams() {
      let url = '',
        params = {};
      switch (this.active) {
        case 'pendingStorage':
          params = Object.assign(
            {
              checkOrDiffer: this.sign,
            },
            this.searchData,
            this.searchParams,
          );
          url = equipmentassets.fillList;
          break;
        case 'inStock':
          params = Object.assign({}, this.searchData, this.searchParams);
          url = equipmentassets.getPageDeviceList;
          break;
      }
      return {
        url,
        params,
      };
    },
    getBtnList(row) {
      return [
        { name: '删除', funct: 'deleteFill' },
        {
          name: '审核记录',
          funct: 'auditRecords',
          disabled: row.examineStatus === '0',
        },
      ];
    },
    search() {
      this.searchData.pageNumber = 1;
      this.pageData.pageNum = 1;
      this.init();
    },
    changePage(val) {
      this.searchData.pageNumber = val;
      this.init();
    },
    reset() {
      this.searchData = {
        pageNumber: 1,
        pageSize: 20,
      };
      this.pageData = {
        pageNum: 1,
        pageSize: 20,
        totalCount: 0,
      };
    },
    changePageSize(val) {
      this.searchData.pageSize = val;
      this.search();
    },
    changeSign() {
      this.search();
    },
    detectionComparison() {
      if (!this.chooseTableData.length && !this.checkAll) {
        this.$Message.error('请选择设备');
        return;
      }
      this.$UiConfirm({
        content: `您将要比对这${this.checkAll ? this.pageData.totalCount : this.chooseTableData.length}项，是否确认？`,
        title: '警告',
      })
        .then(async () => {
          try {
            this.comparisonLoading = true;
            const res = await this.$http.post(
              equipmentassets.checkAndContrast,
              Object.assign({ ids: this.chooseTableData.map((row) => row.id) }, this.searchParams),
            );
            this.$Message.success(res.data.msg);
            this.search();
          } catch (err) {
            console.log(err);
            this.$Message.error(err.data.msg);
          } finally {
            this.defaultStoreData = [];
            this.chooseTableData = [];
            this.comparisonLoading = false;
          }
        })
        .catch((res) => {
          console.log(res);
        });
    },
    exportDevice() {
      this.$emit('exportDevice', this.chooseTableData);
    },
    synchroModalShow() {
      this.$router.push({
        name: 'governanceautomatic',
        params: {
          governanceContent: '2',
        },
      });
    },
    synchroResultModalShow(row) {
      this.$emit('synchroResultModalShow', row);
    },
    batchDeleteFill() {
      if (!this.chooseTableData.length && !this.checkAll) {
        this.$Message.error('请选择设备');
        return;
      }
      this.deleteFun(this.chooseTableData.map((row) => row.id));
    },
    deleteFill(row) {
      this.deleteFun([row.id]);
    },
    deleteFun(ids) {
      this.$UiConfirm({
        content: `您要删除这${this.checkAll ? this.pageData.totalCount : ids.length}项，是否确认?`,
        title: '警告',
      })
        .then(async () => {
          try {
            this.deleteLoading = true;
            const res = await this.$http.post(
              equipmentassets.deleteFill,
              Object.assign(
                {
                  ids: ids,
                },
                this.searchParams,
              ),
            );
            this.$Message.success(res.data.msg);
            this.search();
          } catch (err) {
            console.log(err);
            this.$Message.error(err.data.msg);
          } finally {
            this.defaultStoreData = [];
            this.chooseTableData = [];
            this.deleteLoading = false;
          }
        })
        .catch((res) => {
          console.log(res);
        });
    },
    // 已入库批量编辑
    batchEdit() {
      if (!this.chooseTableData.length && !this.checkAll) {
        this.$Message.error('请选择设备');
        return;
      }
      this.$emit('batchEdit', this.chooseTableData, Object.assign({}, this.searchData, this.searchParams));
    },
    edit(row) {
      this.$emit('edit', row);
    },
    checkField(row, field) {
      if (this.sign === '1') {
        if (!row.differList) return;
        return row.differList.includes(field);
      } else {
        if (!row.accuracyList) return;
        return row.accuracyList.includes(field);
      }
    },
    differenceDetails(row) {
      this.$emit('differenceDetails', row);
    },
    abnormal(row) {
      this.$emit('abnormal', row);
    },
    auditRecords(row) {
      this.$emit('auditRecords', row);
    },
    handleOperations({ data, methods }) {
      this[methods](data);
    },
    operationRecord(row) {
      this.$emit('operationRecord', row);
    },
  },
  watch: {
    searchParams: {
      handler() {
        this.pageData.totalCount = 0;
        this.search();
      },
      deep: true,
    },
    active: {
      handler(val) {
        if (!val) return;
        this.sign = '1';
        this.tableColumns = [];
        this.tableData = [];
        let columns = [];
        switch (val) {
          case 'pendingStorage':
            columns = [
              { title: ' ', key: '', width: 10, align: 'center', fixed: 'right' },
              {
                width: 100,
                title: '入库检测状态',
                slot: 'checkStatus',
                align: 'left',
                fixed: 'right',
              },
              {
                width: 100,
                title: '比对状态',
                slot: 'contrastStatus',
                align: 'left',
                fixed: 'right',
              },
              {
                width: 100,
                title: '审核状态',
                slot: 'examineStatus',
                align: 'left',
                fixed: 'right',
              },
            ];
            break;
          case 'inStock':
            columns = [
              { title: ' ', key: '', width: 10, align: 'center', fixed: 'right' },
              {
                width: 100,
                title: '在线状态',
                slot: 'isOnline',
                align: 'left',
                fixed: 'right',
              },
              {
                width: 100,
                title: '基础信息状态',
                slot: 'baseCheckStatus',
                align: 'left',
                fixed: 'right',
              },
              {
                width: 100,
                title: '审核状态',
                slot: 'examineStatus',
                align: 'left',
                fixed: 'right',
              },
            ];
            break;
        }
        this.tableColumns = [...this.tableBasicColumns, ...columns, ...optionColumn];
      },
      immediate: true,
    },
  },
  computed: {
    ...mapGetters({
      phystatusList: 'algorithm/propertySearch_phystatus',
    }),
  },
  components: {
    UiTable: require('@/components/ui-table.vue').default,
    OperationBtnMore: require('@/components/operation-btn-more.vue').default,
  },
};
</script>
<style lang="less" scoped>
.table-module {
  .ui-table,
  .btn-list {
    padding: 0 20px;
  }
  .check-num {
    color: var(--color-failed);
  }
  .line {
    height: 18px;
    width: 2px;
    display: inline-block;
    background-color: var(--devider-line);
    vertical-align: middle;
  }
  .sign {
    display: inline-block;
    width: 18px;
    height: 18px;
    font-size: 12px;
    line-height: 18px;
    text-align: center;
    border-radius: 50%;

    &.new {
      color: var(--color-bluish-green-text);
      border: 1px solid var(--color-bluish-green-text);
      background-color: var(--bg-bluish-green-text);
    }
    &.diff {
      color: #e85128;
      border: 1px solid #e85128;
      background-color: rgba(232, 81, 40, 0.2);
    }
    &.same {
      color: #21d83a;
      border: 1px solid #21d83a;
      background-color: rgba(33, 216, 58, 0.2);
    }
  }
  .action {
    @{_deep}.ivu-tooltip {
      margin-right: 10px;
      &:last-of-type {
        margin-right: 0;
      }
    }
  }
  @{_deep} .check-status-font {
    padding: 0 !important;
  }
}
</style>
