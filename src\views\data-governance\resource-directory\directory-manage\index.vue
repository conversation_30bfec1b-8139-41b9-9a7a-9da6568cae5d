<template>
    <div class="directory">
        <!-- 左侧查询目录树 -->
        <div class="left mr-10">
            <div class="search-top">
                <Input placeholder="请输入目录名称" v-model="catalogName">
                    <Icon type="ios-search" class="font-16 cursor-p" slot="suffix" maxlength="50" @click.prevent="searchName(catalogName)" />
                </Input>
                <i class="iconfont icon-jia auxiliary-color ml-15 add-dictType" @click="addDirectory"></i>
            </div>
            <el-tree ref="treeList" :data="tree" class="tree-box" :highlight-current="true" default-expand-all :expand-on-click-node="false"
                :current-node-key="flag" node-key="flag" @node-click="handleNodeClick">
                <div slot-scope="{ node, data }" class="slot-node">
                    <span class="node-title">
                        <span>
                            <i class="iconfont color-bule mr-5" :class="data.catalogName ? (node.expanded ? 'icon-folder-open-fill' : 'icon-folder-fill') : 'icon-file-text-fill'"></i>
                            <span :title="data.catalogName ? data.catalogName : data.resourceNameCn" class="ellipsis name-width">{{ data.catalogName ? data.catalogName : data.resourceNameCn }}</span>
                        </span>
                        <span class="node-action">
                            <i v-if="data.catalogName" class="iconfont color-white icon-bianji" @click="edit(data)"></i>
                            <i v-if="data.children.length === 0" class="iconfont color-white icon-shanchu ml-10" @click="remove(data)"></i>
                        </span>
                    </span>
                </div>
            </el-tree>
        </div>
        <!-- 右侧资源配置列表 -->
        <div class="box container">
            <div class="right-title" v-show="muenShow">
                <p>{{ tabTitle.catalogName }}</p>
                <Button @click="edit(tabTitle)"> <i class="iconfont icon-bianji font-16" />编辑</Button>
            </div>
            <div class="right-box" v-show="muenShow">
                <div class="box-children">
                    <div class="box-children-title">
                        <i class="iconfont icon-folder-fill font-10" />
                        <p class="little-title">子分组( <span class="num">{{ childrenList.length }}</span> )</p>
                    </div>
                    <div class="box-upDown" :class="{packArrow: muenUpDown[0]}" @click="handlePackup(0)">
                        <img :src="packUrl" alt="">
                        <p>{{ muenUpDown[0] ? '展开' : '收起'}} </p>
                    </div>
                </div> 
                <ul class="box-ul" :class="{'box-ul-pack': muenUpDown[0]}">
                    <li class="box-li" v-for="(item, index) in childrenList" @click="handleChild(item, index)" :key="index"> 
                        <div class="li-left">
                            <i class="iconfont icon-folder-fill font-16" />
                            <p>{{ item.catalogName }}</p>
                        </div>
                    </li>
                </ul>
                <div class="box-children box-bottom">
                    <div class="box-children-title"> 
                        <i class="iconfont icon-file-text-fill font-10" />
                        <p class="little-title">资源数据( <span class="num">{{ resourceList.length }}</span> )</p>
                    </div>
                    <div class="box-upDown" :class="{packArrow: muenUpDown[1]}" @click="handlePackup(1)">
                        <img :src="packUrl" alt="">
                        <p>{{ muenUpDown[1] ? '展开' : '收起'}} </p>
                    </div>
                </div>
                <div class="box-table" :class="{'box-table-pack': muenUpDown[1]}"> 
                    <ui-table class="" :columns="resourceColumns" @on-row-click="handleRowClick" :height="resourceList.length < 10 ? '500': ''" :data="resourceList">
                        <template #resourceNameCn="{ row }">
                            <div class="href">
                              {{ row.resourceNameCn }}
                            </div>
                        </template>
                        <template #action="{ row }">
                            <div class="btn-tips">
                                <ui-btn-tip content="删除" icon="icon-shanchu" class="mr-20 primary" @click.native="remove(row)"></ui-btn-tip>
                            </div>
                        </template>
                    </ui-table>
                </div>
            </div>
            <div class="right" v-show="!muenShow">
                <div class="btns">
                    <Button type="default" @click="handleDataPreview" class="mr-10"><i class="iconfont icon-eye font-16" />数据预览</Button>
                    <Upload action="*" :beforeUpload="(file)=>uploadFile(file)" :limit="1">
                        <Button type="default"><i class="iconfont icon-export font-16" />数据导入</Button>
                    </Upload>
                </div>
                <ui-table class="auto-fill table" :columns="columns" :data="tableData" :loading="loading">
                    <template #isShow="{ row }">
                        <span>{{ row.isShow == '1' ? '是' : '否' }}</span>
                    </template>
                    <template #isSearch="{ row }">
                        <span>{{ row.isSearch == '1' ? '是' : '否' }}</span>
                    </template>
                    <template #searchType="{ row }">
                        <span>{{ row.searchType | commonFiltering(searchTypeList) }}</span>
                    </template>
                    <template #dictionaryCode="{ row }">
                        <span>{{ getValue(row.dictionaryCode) }}</span>
                        <!-- <span>{{ row.dictionaryCode | commonFiltering(dictTypedata) }}</span> -->
                    </template>
                    <template #cloudSearch="{ row }">
                        <span>{{ row.cloudSearch == 1 ? '是' : '否' }}</span>
                    </template>
                </ui-table>
            </div>
        </div>
        <!-- 新增/编辑 -->
        <addModal ref="addModal" @refreshDataList="refreshDataList" :list="directoryList" />
        <!-- 数据预览 -->
        <dataPreviewModal ref="dataPreViewModal" @refreshDataList="refreshDataList" />
    </div>
</template>
<script>
import { mapActions, mapGetters } from 'vuex'
import addModal from './components/addModal'
import dataPreviewModal from './components/dataPreviewModal';
import { getConfigurPageList, getSelectCatalogList, removeCatalog, getDictTypeList, importResourceData, dataPreview } from '@/api/dataGovernance'
export default {
    components: { addModal, dataPreviewModal },
    props: {},
    data() {
        return {
            loading: false,
            catalogName: '', //目录名称
            resourceType: 0, //树节点选中
            params: {
                pageNumber: 1,
                pageSize: 20
            },
            flag: 0,
            total: 0,
            tableData: [], //列表
            dictTypedata: [], //对应字典
            directoryList: [], //上级目录
            tree: [], //目录树
            previewData: [ ],  // 预览的数据
            columns: [
                { title: '序号', width: 90, type: 'index', key: 'index' },
                { title: '字段名称', key: 'fieldName' },
                { title: '中文名称', key: 'fieldNameCn' },
                { title: '是否显示字段', slot: 'isShow' },
                { title: '是否作为检索条件', slot: 'isSearch' },
                { title: '检索类型', slot: 'searchType' },
                { title: '对应字典', slot: 'dictionaryCode' },
                { title: '字段显示排序', key: 'fieldSort' },
                { title: '查询条件显示排序', key: 'searchSort' },
                { title: '是否作为云搜检索字段', slot: 'cloudSearch', width: 180 }
            ],
            resourceColumns: [
                { title: '序号', width: 90, type: 'index', key: 'index' },
                { title: '资源名称', slot: 'resourceNameCn' },
                // { title: '创建人', key: 'name' },
                // { title: '创建时间', key: 'isShow' },
                { title: '操作', slot: 'action', width: 100},
            ],
            childrenList: [],
            resourceList: [],
            tabTitle: '',
            packUrl: require('@/assets/img/model/icon/arrow.png'),
            muenUpDown: [false, false],
            muenShow: true
        }
    },
  computed: {
    ...mapGetters({
      searchTypeList: 'dictionary/getSearchTypeList' //检索类型
    })
  },
  watch: {
    tree(val) {
         if (val) {
            this.$nextTick(() => {
                document.querySelector('.el-tree-node__content').click();
            })
        }
    }
  },
  created() {
    this.getDictData()
    this.getList()
    this.getSelectCatalogList()
    this.getDictTypeList()
  },
  methods: {
    ...mapActions({
      getDictData: 'dictionary/getDictAllData'
    }),
    // 列表
    getList() {
      this.loading = true
      getConfigurPageList({ id: this.resourceType }).then(res => {
        this.tableData = res.data
        this.loading = false
      })
    },
    // 获取所有目录
    getSelectCatalogList() {
      let data = {
        catalogName: this.catalogName
      }
      getSelectCatalogList(data).then(res => {
        let list = res.data
        this.directoryList = list
        this.tree = this.getTree(list)
      })
    },
    // 获取字典
    getDictTypeList() {
      let data = {
        params: {
          pageNumber: 1,
          pageSize: 10
        }
      }
      getDictTypeList(data).then(res => {
        this.dictTypedata = res.data
      })
    },
    getValue (val) {
      if (val == "") {
        return val
      }
      var obj = this.dictTypedata.find(item => item.typeKey == val)
      if (obj) {
        return obj.typeValue
      }else {
        return val
      }
    },
    // 递归修改目录树字段
    getTree(tree = []) {
      let arr = []
      if (!!tree && tree.length !== 0) {
        tree.forEach(item => {
          // 目录树数据自定义递归处理
          let catalog = item.list || []
          let resource = item.resourceList || []
          resource.forEach(item => {
            item.flag = this.flag++
          })
          let obj = {
            catalogName: item.catalogName || '',
            resourceNameCn: item.resourceNameCn || '',
            id: item.id || '',
            flag: this.flag++,
            children: this.getTree([...catalog, ...resource])
          }
          arr.push(obj)
        })
      }
      return arr
    },
    // 选中左侧树节点
    handleNodeClick(data) {
        this.tableData = [];
        this.childrenList = [];
        this.resourceList = [];
        if (data.resourceNameCn) {
            this.muenShow = false;
            this.resourceType = data.id
            this.getList()
        } else {
            this.muenShow = true;
            this.tabTitle = data;
            data.children.map(item => {
                if (item.catalogName) {
                    this.childrenList.push(item) 
                } else {
                    this.resourceList.push(item)
                }
            })
        }
    },
    // 点击子组件
    handleChild(item, index) {
        this.muenShow = true;
        this.childrenList = [];
        this.resourceList = [];
        this.tabTitle = item;
        this.resourceType = item.id
        this.flag = item.flag
        this.$refs.treeList.setCurrentKey(item.id);
        item.children.map(ite => {
            if (ite.catalogName) {
                this.childrenList.push(ite) 
            } else {
                this.resourceList.push(ite)
            }
        })
    },
    //资源数据 
    handleRowClick(row) {
        console.log(row, 'row')
        this.muenShow = false;
        this.resourceType = row.id
        this.flag = row.flag
        this.$refs.treeList.setCurrentKey(row.flag);
        this.getList()
    },
    // 查询目录树
    searchName() {
      this.getSelectCatalogList()
    },
    // 新增目录
    addDirectory() {
      this.$refs.addModal.show()
    },
    // 编辑目录
    edit(data) {
      this.$refs.addModal.show(data)
    },
    // 删除目录
    remove(data) {
      this.$Modal.confirm({
        title: '提示',
        closable: true,
        content: `确定删除吗？`,
        onOk: () => {
          removeCatalog(data.id).then(res => {
            this.refreshDataList()
          })
        }
      })
    },
    // 目录树更新
    refreshDataList() {
      this.getList()
      this.getSelectCatalogList()
    },
    handlePackup(index) {
        this.$set(this.muenUpDown, index, !this.muenUpDown[index])
    },
    // 数据预览
    handleDataPreview() {
      this.$refs.dataPreViewModal.show(this.resourceType)
    },
    // 数据导入
    uploadFile(file) {
      if (!/\.(xlsx|xls)$/.test(file.name)) {
        this.$Message.error('仅支持Excel文件格式的导入')
        return false
      }
      let fileData = new FormData()
      fileData.append("file", file)
      importResourceData({resourceId:this.resourceType, file:fileData}).then(res => {
        this.$Message.success('导入成功')
      })
    }
  }
}
</script>
<style lang="less" scoped>
.directory {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: space-between;
    .left {
        width:290px;
        height: 100%;
        background-color: #fff;
        box-shadow: 0px 3px 5px 0px rgba(147, 171, 206, 0.7);
        border-radius: 4px;
        .search-top {
            margin: 16px 15px 23px;
            display: flex;
            justify-content: flex-start;
            align-items: center;
            .add-dictType {
                cursor: pointer;
                font-size: 20px;
                border-radius: 4px;
                padding: 1px 5.5px;
                border: 1px solid #d3d7de;
            }
        }
        .tree-box{
            overflow: auto;
            height: calc( ~'100% - 74px' );
        }
        /deep/ .el-tree-node__content {
            height: 34px;
            font-size: 14px;
            .color-bule {
                color: #2c86f8;
            }
            .color-white {
                color: #fff;
            }
        }
        /deep/.el-tree-node.is-current > .el-tree-node__content {
            background: #2c86f8 !important;
            color: #fff;
            .color-bule {
                color: #fff;
            }
        }
        /deep/ .el-tree-node__content:hover {
            background-color: #2c86f8;
            color: #fff;
            .color-bule {
                color: #fff;
            }
        }
        .slot-node {
            width: 100%;
            .node-title {
                display: flex;
                justify-content: space-between;
                align-items: center;
                .name-width {
                    display: block;
                    width: 140px;
                }
                span {
                    display: flex;
                    align-items: center;
                }
            }
            .node-action {
                display: inline-block;
                margin-right: 10px;
                i {
                    font-size: 14px;
                }
            }
        }
    }
    .right {
        display: flex;
        flex: 1;
        height: 100%;
        flex-direction: column;
        padding: 16px 20px 0 20px;
        .btns{
          display: flex;
        }
    }
    .container{
        padding: 0;
    }
    .box{
        overflow: hidden;
        padding-bottom: 10px;
        .right-title{
            font-size: 16px;
            font-weight: bold;
            color: rgba(0,0,0,0.9);
            height: 56px;
            position: relative;
            line-height: 56px;
            padding-left: 30px;
            padding-right: 20px;
            // border-bottom: 1px solid #D3D7DE;
            display: flex;
            justify-content: space-between;
            align-items: center;
            top: 0;
            z-index: 1;
            background: rgba(44,134,248,0.05);
            border-top-left-radius: 4px;
            border-top-right-radius: 4px;
            &:before {
                content: '';
                position: absolute;
                width: 5px;
                height: 20px;
                top: 50%;
                transform: translateY(-50%);
                left: 20px;
                background: #2c86f8;
            }
            span{
                color: #2C86F8; 
            }
        }
        .right-box{
            height: calc( ~'100% - 56px');
            overflow-x: auto;
            padding: 10px 20px;
            display: flex;
            flex-direction: column;
            .iconfont{
                color: #2C86F8;
            }
            .box-children{
                display: flex;
                align-items: center;
                justify-content: space-between;
                box-shadow: inset 0px -1px 0px 0px #D3D7DE;
                padding: 10px 0;
                .box-children-title{
                    display: flex;
                    align-items: center;
                    .icon-folder-fill{
                        font-size: 14px;
                    }
                    .little-title{
                        font-weight: bold;
                        font-size: 14px;
                        color: rgba(0,0,0,0.8);
                        margin-left: 5px;
                        .num{
                            color: rgba(44, 134, 248, 1);
                        }
                    }
                    
                }
            }
            .box-ul{
                padding-top: 10px;
                height: auto;
                transition: height 0.2s ease-out;
                .box-li{
                    background: #F9F9F9;
                    border-radius: 4px;
                    margin-top: 10px;
                    display: flex;
                    padding: 11px 20px;
                    cursor: pointer;
                    .icon-folder-fill{
                        font-size: 18px;
                        margin-right: 21px;
                    }
                    .li-left{
                        font-size: 14px;
                        display: flex;
                        align-items: center;
                    }
                }
            }
            .box-ul-pack{
                height: 10px;
                transition: height 0.2s ease-out;
                overflow: hidden; 
            }
            .box-table{
                flex: 1;
                display: flex;
                height: auto;
                transition: height 0.2s ease-out;
            }
            .href {
              cursor: pointer;
            }
            .box-table-pack{
                height: 0px;
                transition: height 0.2s ease-out;
                overflow: hidden;
                .ui-table{
                    height: 0;
                }
            }
            .box-bottom{
                margin: 30px 0 16px 0;
                .icon-file-text-fill{
                    font-size: 14px;
                }
            }
            .box-upDown{
                display: flex;
                justify-content: center;
                cursor: pointer;
                color: #2C86F8;
                &:hover{
                    color: #4597FF;
                }
                img{
                    transform: rotate(0deg);
                    transition: transform 0.2s;
                    margin-right: 5px;
                }
            }
            .packArrow{
                img{
                    transform: rotate(180deg);
                    transition: transform 0.2s;
                }
            }
        }
    }
}
</style>
