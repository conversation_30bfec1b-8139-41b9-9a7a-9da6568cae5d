<!--
    * @FileDescription: 碰撞分析
    * @Author: H
    * @Date: 2024/06/27
 * @LastEditors: zhengmingming <EMAIL>
 * @LastEditTime: 2025-02-12 11:23:36
 -->
<template>
  <div class="leftBox">
    <div class="search_box" :class="{ 'search_box-pack': packUpDown }">
      <div class="title">
        <p>碰撞分析</p>
      </div>
      <div
        class="search_box_condition"
        :class="{ 'search_box_condition-pack': packUpDown }"
      >
        <div class="search_form">
          <div class="search_wrapper">
            <div class="form_title">
              <p class="search_strut">选择范围:</p>
            </div>
            <div class="search_content">
              <li
                class="area-list"
                v-for="(item, index) in toolMap"
                :key="index"
                @click="handleSeleArea(item, index)"
                :class="{ 'search-content-active': selectAreaTool === index }"
              >
                <img :src="item.icon" alt="" />
              </li>
            </div>
          </div>
        </div>
        <ul class="search_ul">
          <li
            class="search_li"
            v-for="(item, index) in queryForm"
            :key="index"
            @click="handleListClick(item, index)"
          >
            <div class="collect-top">
              <div @click.stop>
                <Checkbox
                  v-model="item.select"
                  @on-change="handleChangeCheck($event, item, index)"
                ></Checkbox>
                <span>碰撞区域{{ index + 1 }}</span>
              </div>
              <div>
                <p>
                  设备数：<span class="device-num">{{
                    item.deviceList.length
                  }}</span>
                </p>
              </div>
              <Icon type="ios-close" @click.stop="handleDele(index)" />
            </div>
            <div class="select-time">
              <i class="iconfont icon-time"></i>
              <span>{{ item.startAbsTime }} - {{ item.endAbsTime }}</span>
            </div>
          </li>
          <div class="tip-box">
            <p class="tip-text">
              <i class="iconfont icon-tishi"></i>请选择碰撞区域
            </p>
          </div>
        </ul>
      </div>
      <div class="footer-box">
        <div class="btn-group" v-if="queryForm.length > 1">
          <Button type="primary" class="btnwidth" @click="handleSearch"
            >碰撞</Button
          >
        </div>
        <div
          class="footer"
          :class="{ packArrow: packUpDown }"
          @click="handlePackup"
        >
          <img :src="packUrl" alt="" />
          <p>{{ packUpDown ? "展开条件" : "收起条件" }}</p>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { getConfigDate } from "@/util/modules/common";
export default {
  name: "",
  props: {
    typeName: {
      type: String,
      default: "face",
    },
  },
  components: {},
  data() {
    return {
      queryForm: [],
      circleColor: ["#2C86F8", "#1FAF81", "#F29F4C", "#A786FF", "#48BAFF"],
      packUpDown: false,
      packUrl: require("@/assets/img/model/icon/arrow.png"),
      toolMap: [
        {
          title: "圆形框选",
          icon: require("@/assets/img/model/icon/circle.png"),
          fun: this.selectDraw,
          value: "circle",
          funName: "selectDrawCircleByDiameter",
        },
        {
          title: "矩形框选",
          icon: require("@/assets/img/model/icon/rectangle.png"),
          fun: this.selectDraw,
          value: "rectangle",
          funName: "selectRectangle",
        },
        {
          title: "多边形框选",
          icon: require("@/assets/img/model/icon/polygon.png"),
          fun: this.selectDraw,
          value: "polygon",
          funName: "selectPolygon",
        },
      ],
      tabTitle: {},
      optionList: ["", "", "", "", ""],
      isClick: false,
      selectAreaTool: -1, // 当前选中的框选工具
      currentIndex: 0,
    };
  },
  watch: {},
  computed: {},
  created() {},
  mounted() {},
  methods: {
    handleListClick(item, index) {
      this.$emit("areaClick", item, index);
    },
    handleSearch() {
      let list = this.queryForm.filter((item) => item.select);
      if (list.length < 2) {
        this.$Message.warning("勾选区域小于2，请重新选择");
        return;
      }
      let params = [];
      list.forEach((ite) => {
        let deviceIds = ite.deviceList.map((item) => item.deviceGbId);
        params.push({
          startAbsTime: ite.startAbsTime,
          endAbsTime: ite.endAbsTime,
          deviceIds: deviceIds,
        });
      });
      this.$emit("search", JSON.stringify(params));
    },
    // 选择时间
    handleFast(index, fastIndex) {
      this.queryForm[index].fastIndex = fastIndex;
      let arr = "";
      switch (fastIndex) {
        case 0:
          arr = getConfigDate(-1);
          this.queryForm[index].timeDate = [
            new Date(arr[1] + " 00:00:00"),
            new Date(arr[1] + " 23:59:59"),
          ];
          break;
        case 1:
          arr = getConfigDate(-6);
          this.queryForm[index].timeDate = [
            new Date(arr[0] + " 00:00:00"),
            new Date(arr[1] + " 23:59:59"),
          ];
          break;
        case 2:
          arr = getConfigDate(-29);
          this.queryForm[index].timeDate = [
            new Date(arr[0] + " 00:00:00"),
            new Date(arr[1] + " 23:59:59"),
          ];
          break;
        case 3:
          arr = getConfigDate(-89);
          this.queryForm[index].timeDate = [
            new Date(arr[0] + " 00:00:00"),
            new Date(arr[1] + " 23:59:59"),
          ];
          break;
      }
    },
    // 选择区域
    handleSeleArea(item, tool) {
      if (this.selectAreaTool === tool) {
        this.selectAreaTool = -1;
        this.$emit("cancelDraw");
        this.currentIndex > this.queryForm.length - 1 &&
          this.$emit("deleDraw", this.currentIndex);
        return;
      }
      this.selectAreaTool = tool;
      if (this.queryForm.length == 4) {
        this.$Message.warning("最多选择4个碰撞区域！");
        return;
      }
      // rectangle 矩形
      // circle    圆形
      // polygon   多边形
      let index = this.queryForm.length;
      this.currentIndex = index;
      this.$emit("selectDraw", item.funName, index, this.queryForm);
    },
    // 删除
    handleDele(index) {
      this.queryForm.splice(index, 1);
      this.optionList[index] = "";
      this.$nextTick(() => {
        const offset = document.querySelector(".search_box").offsetHeight;
        const scroll = document.querySelector(
          ".search_box_condition"
        ).scrollHeight;
        const box = document.querySelector(".time-space-colli").offsetHeight;
        if (offset - 40 >= scroll) {
          document.querySelector(".leftBox").style.height = "auto";
          document.querySelector(".search_box").style.height = "auto";
        }
      });
      this.$emit("deleDraw", index);
    },
    handlePackup() {
      this.packUpDown = !this.packUpDown;
    },
    handleStartChange(event, index) {
      this.queryForm[index].startAbsTime = event;
      this.queryForm[index].endAbsTime = "";
      let time = this.time(3, event);
      this.optionList[index] = {
        disabledDate(date) {
          return (
            (date && date.valueOf() < new Date(event).getTime() - 8640000) ||
            (date && date.valueOf() > new Date(time).getTime())
          );
        },
      };
    },
    handleEndChange(event, index) {
      this.queryForm[index].endAbsTime = event;
    },
    // 新增搜索条件
    handleAdd(data) {
      this.selectAreaTool = -1;
      this.queryForm.push({
        startAbsTime: data.startDate,
        endAbsTime: data.endDate,
        deviceList: data.list,
        select: true,
      });
      this.$nextTick(() => {
        const offset = document.querySelector(".search_box").offsetHeight;
        const scroll = document.querySelector(
          ".search_box_condition"
        ).scrollHeight;
        const box = document.querySelector(".time-space-colli").offsetHeight;
        if (offset + 20 >= box) {
          document.querySelector(".leftBox").style.height = "100%";
          document.querySelector(".search_box").style.height =
            "calc( 100% - 20px)";
        } else {
          document.querySelector(".leftBox").style.height = "auto";
        }
        if (offset - 40 <= scroll) {
          document.querySelector(".search_box").style.height =
            "calc( 100% - 20px)";
        }
      });
    },
    handleUpdate(data, index) {
      if (data.list.length == 0) {
        // 没有设备，删除该区域
        this.handleDele(index);
        return;
      }
      this.$set(this.queryForm, index, {
        startAbsTime: data.startDate,
        endAbsTime: data.endDate,
        deviceList: data.list,
        select: true,
      });
    },
    handleChangeCheck(event, row, index) {
      this.$emit("checkChange", event, row, index);
    },
    time(monthData = 3, times = "") {
      let date = times ? new Date(times) : new Date();
      let year = date.getFullYear();
      let month = date.getMonth() + 1;
      let day = date.getDate();
      let countDown = 12 - monthData;
      if (month <= countDown) {
        month += monthData;
      } else if (month > countDown) {
        year += 1;
        month = month - 12 + monthData;
      }
      if (month < 10) {
        month = "0" + month;
      }
      if (day < 10) {
        day = "0" + day;
      }
      let time = year + "-" + month + "-" + day + " " + "00:00:00";
      return time;
    },
  },
};
</script>

<style lang="less" scoped>
@import "./style/index";
.leftBox {
  position: absolute;
  top: 10px;
  left: 10px;
  // height: 100%;
  .search_box {
    background: #fff;
    box-shadow: 0px 3px 5px 0px rgba(0, 0, 0, 0.3);
    border-radius: 4px;
    width: 370px;
    max-height: calc(~"100% - 20px");
    overflow: hidden;
    .search_box_condition {
      padding: 17px 20px;
      max-height: calc(~"100% - 120px");
      overflow-y: auto;
      box-sizing: border-box;
      .search_ul {
        .search_li {
          margin: 0px 0 20px;
          border: 1px solid #a5b0b64f;
          padding: 10px;
          cursor: pointer;
          &:hover {
            border: 1px solid #187ae4;
          }
          .collect-top {
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid #a5b0b64f;
            margin-bottom: 5px;
            /deep/.ivu-icon-ios-close {
              font-size: 30px;
              cursor: pointer;
              color: #888888;
            }
            .device-num {
              color: #187ae4;
            }
          }
          .select-time {
            display: flex;
            align-items: center;
            span {
              margin-left: 5px;
            }
          }

          .search_title {
            display: flex;
            align-items: center;
            justify-content: space-between;
            &_left {
              display: flex;
              align-items: center;
            }
            .list_icon {
              width: 12px;
              height: 12px;
              background: rgba(44, 134, 248, 0.1028);
              opacity: 1;
              border: 3px solid #2c86f8;
              border-radius: 10px;
            }
            .list_text {
              font-size: 14px;
              font-weight: 700;
              color: rgba(0, 0, 0, 0.75);
              line-height: 20px;
              margin-left: 10px;
            }
            /deep/.ivu-icon-ios-close {
              font-size: 30px;
              cursor: pointer;
              color: #888888;
            }
          }
        }
        .tip-box {
          padding: 5px;
          border: 1px solid #a5b0b6;
          color: #a5b0b6;
          .tip-text {
            text-align: center;
            /deep/ .icon-tishi {
              color: #f29f4c !important;
            }
          }
        }
        .search_add {
          display: flex;
          justify-content: space-between;
          .add_left {
            color: #2c86f8;
            font-size: 14px;
            font-weight: 400;
            cursor: pointer;
            &:hover {
              color: #4597ff;
            }
          }
          .add_right {
            font-size: 14px;
            color: #f29f4c;
            /deep/ .icon-tishi {
              color: #f29f4c !important;
            }
          }
        }
      }
      .search_form {
        .search_wrapper {
          margin: 10px 0;
          display: flex;
          align-items: center;
          /deep/.ivu-date-picker {
            width: 100% !important;
          }
          .form_title {
            font-size: 14px;
            font-weight: 400;
            color: rgba(0, 0, 0, 0.4);
            margin-right: 10px;
          }
          .search_content {
            width: 250px;
            display: flex;
            align-items: center;
            /deep/.ivu-date-picker {
              width: 100% !important;
            }
            &-li {
              font-size: 14px;
              color: rgba(0, 0, 0, 0.9);
              font-weight: 400;
              cursor: pointer;
              width: 65px;
              text-align: center;
              padding: 3px 0;
              margin-right: 15px;
            }
            &-li-active {
              color: #fff;
              background: #2c86f8;
              border-radius: 2px;
            }
            .area-list {
              width: 34px;
              height: 34px;
              background: #ffffff;
              border-radius: 4px;
              border: 1px solid #d3d7de;
              cursor: pointer;
              color: rgba(0, 0, 0, 0.6);
              margin-right: 10px;
              display: flex;
              justify-content: center;
              align-items: center;
              img {
                opacity: 1;
              }
            }
            .active-area-list {
              border: 1px dashed #2c86f8;
              background: rgba(44, 134, 248, 0.1);
              color: rgba(44, 134, 248, 1);
              img {
                opacity: 0.6;
              }
            }
            .sele-area-list {
              // cursor: not-allowed;
            }
            .selectNum {
              font-size: 14px;
              font-weight: 400;
              color: rgba(0, 0, 0, 0.9);
              span {
                color: rgba(44, 134, 248, 1);
              }
            }
          }
          .search-content-active {
            background: rgba(44, 134, 248, 0.1) !important;
            border: 1px dashed #2c86f8 !important;
            color: #2c86f8 !important;
          }
        }
      }
    }
    .search_box_condition-pack {
      height: 0px;
      transition: height 0.2s ease-out;
      overflow: hidden;
      padding: 0;
    }
    .footer-box {
      box-shadow: 0px 3px 5px 0px rgba(0, 0, 0, 0.298);
      background: #ffffff;
      border-radius: 4px 4px 4px 4px;
      .btn-group {
        padding: 8px 20px 0 20px;
        text-align: center;
        .btnwidth {
          width: 280px;
        }
      }
    }
  }
  .search_box-pack {
    // height: 120px;
    transition: height 0.2s ease-out;
    overflow: hidden;
  }
}
/deep/ .ivu-checkbox-group {
  display: flex;
  justify-content: space-between;
}
/deep/ .ivu-checkbox-inner {
  margin-right: 0;
}
</style>
