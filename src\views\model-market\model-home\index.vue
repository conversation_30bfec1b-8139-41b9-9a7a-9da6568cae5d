/** * 模型首页 */
<template>
  <div class="container">
    <template v-for="info in modelList">
      <div :key="info.englishName">
        <div class="title title-sec">
          <p class="english">{{ info.englishName }}</p>
          <p class="Chinese">{{ info.name }}</p>
        </div>
        <ul class="list_tab">
          <li
            class="components_list"
            v-for="(item, index) in info.list"
            :key="index"
            @click="handleDetail(item)"
          >
            <p class="components_list_title">{{ item.name }}</p>
            <p class="components_list_subtitle">{{ item.englishName }}</p>
            <div class="components_list_line"></div>
            <img :src="item.imgUrl" alt="" />
          </li>
          <li
            class="card-tab"
            v-for="(_, index) of 5 - (info.list.length % 5)"
            :key="index + 'demo'"
          ></li>
        </ul>
      </div>
    </template>
  </div>
</template>

<script>
import { modelList } from "./menuData";
export default {
  name: "model-home",
  components: {},
  data() {
    return {
      modelList,
    };
  },
  methods: {
    handleDetail(item) {
      if (item.page) {
        const { href } = this.$router.resolve({
          name: item.url,
        });
        window.open(
          `${location.protocol}//${location.host}/${item.page}${href}?noMenu=1`,
          "_blank"
        );
      } else {
        this.$router.push({
          name: item.url,
        });
      }
    },
  },
};
</script>

<style lang="less" scoped>
.container {
  background: url("~@/assets/img/model/dot.png") no-repeat, #fff;
  background-position: bottom;
  overflow: auto;
}

.title {
  font-size: 26px;
  font-weight: bold;
  color: rgba(0, 0, 0, 0.9);
  text-align: left;
  position: relative;
  padding-left: 43px;

  .english {
    height: 70px;
    line-height: 80px;
    font-size: 34px;
    font-weight: bold;
    color: rgba(0, 0, 0, 0.3);
    color: #d3d7de;
    opacity: 0.3;
  }

  .Chinese {
    position: absolute;
    left: 97px;
    top: 40%;
    transform: translate(-50%, -50%);
  }
}

.title-top {
  margin: 15px 0 61px;
}

.title-sec {
  margin: 0px 0 41px;
}

.img {
  background: url("~@/assets/img/model/pic1.png") no-repeat;
}

.list_tab {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  padding: 0 73px 0 43px;

  .components_list {
    width: 300px;
    height: 150px;
    background: #f9f9f9;
    box-shadow: 0px 3px 5px 0px rgba(147, 171, 206, 0.7);
    border-radius: 4px;
    position: relative;
    margin-bottom: 50px;
    padding-left: 20px;
    cursor: pointer;

    &:hover {
      transform: translateY(-10px);
      box-shadow: 0px 8px 12px 0px rgba(147, 171, 206, 0.7);
    }

    img {
      width: 160px;
      height: 160px;
      position: absolute;
      right: 0;
      bottom: 40px;
    }

    &_title {
      font-size: 20px;
      font-weight: bold;
      color: rgba(0, 0, 0, 0.9);
      margin-top: 30px;
    }

    &_subtitle {
      font-size: 10px;
      color: rgba(0, 0, 0, 0.6);
      margin: 10px 0 7px;
      width: 165px;
    }

    &_line {
      width: 40px;
      height: 6px;
      background: #2c86f8;
    }

    .child_ul {
      display: flex;
      margin-top: 15px;

      .child_li {
        font-size: 14px;
        color: #2c86f8;
        margin-right: 20px;

        &:hover {
          color: #4597ff;
        }
      }
    }
  }
}

.card-tab {
  width: 300px;
  height: 150px;
}
</style>
