<template>
  <div class="control-add container">
    <div class="page-title">{{ taskId ? "编辑" : "新增" }}布控</div>
    <div class="data">
      <div class="form">
        <Form
          ref="formValidate"
          :model="formData"
          :label-width="120"
          :rules="ruleValidate"
        >
          <div class="form-title">基本信息</div>
          <FormItem label="布控名称:" prop="taskName">
            <Input
              v-model="formData.taskName"
              placeholder="请输入"
              maxlength="20"
            ></Input>
          </FormItem>
          <FormItem
            label="时间类型:"
            class="form-item-time"
            prop="taskTimeType"
          >
            <div>
              <RadioGroup
                v-model="formData.taskTimeType"
                @on-change="handleRadioChange"
              >
                <Radio label="0">永久</Radio>
                <Radio label="1">自定义</Radio>
              </RadioGroup>
              <DatePicker
                class="mg20"
                v-if="formData.taskTimeType == 1"
                v-model="formData.timeSlotArr"
                @on-ok="handleDateOk"
                type="datetimerange"
                @on-change="dateChange"
                format="yyyy-MM-dd HH:mm:ss"
                placeholder="请选择自定义时间段"
                transfer
              >
              </DatePicker>
            </div>
          </FormItem>
          <FormItem label="有效时段:">
            <div class="validTime">
              <RadioGroup
                v-model="validTimeType"
                style="line-height: 34px"
                @on-change="validTimeTypeChange"
              >
                <Radio :label="1">全天</Radio>
                <Radio :label="2">时间段</Radio>
              </RadioGroup>
              <div class="validTime-time">
                <div
                  class="shortNote validTime-item"
                  v-for="(item, index) in formData.validTimeRangeFormList"
                  :key="index"
                >
                  <TimePicker
                    :disabled="validTimeType == 1"
                    v-model="item.beginTime"
                    :clearable="false"
                    format="HH:mm:ss"
                    style="width: 100px; margin-right: 10px"
                  ></TimePicker>
                  <TimePicker
                    :disabled="validTimeType == 1"
                    v-model="item.endTime"
                    :clearable="false"
                    format="HH:mm:ss"
                    style="width: 100px"
                  ></TimePicker>
                  <span
                    class="shanchu-icon"
                    @click="handleDeleValidTime(item, index)"
                    v-if="formData.validTimeRangeFormList.length > 1"
                  >
                    <i class="iconfont icon-shanchu1"></i>
                  </span>
                  <span
                    class="add-icon"
                    v-if="index + 1 == formData.validTimeRangeFormList.length"
                    @click="handleAddValidTime(item)"
                  >
                    <i class="iconfont icon-jia"></i>
                  </span>
                </div>
              </div>
            </div>
          </FormItem>
          <FormItem label="布控级别:" prop="taskLevel">
            <RadioGroup v-model="formData.taskLevel">
              <Radio label="1">一级</Radio>
              <Radio label="2">二级</Radio>
              <Radio label="3">三级</Radio>
            </RadioGroup>
          </FormItem>
          <div class="form-title">布控目标</div>
          <FormItem
            label="报警阀值:"
            class="slider-form-item"
            prop="alarmThreshold"
          >
            <div class="slider-content">
              <Slider
                v-model="formData.alarmThreshold"
                @on-change="handleSliderChange"
              ></Slider>
              <span>{{ formData.alarmThreshold }}%</span>
            </div>
          </FormItem>
          <FormItem label="布控类型:" prop="taskType">
            <RadioGroup
              v-model="formData.taskType"
              @on-change="handleTaskChange"
            >
              <Radio :disabled="isEdit" label="1">单体布控</Radio>
              <Radio :disabled="isEdit" label="2">库布控</Radio>
            </RadioGroup>
            <div></div>
            <formTable ref="formTable" v-if="formData.taskType == '1'" />
            <Select
              v-else
              v-model="formData.libIds"
              placeholder="请选择"
              filterable
              multiple
              :max-tag-count="10"
            >
              <Option :value="item.id" v-for="item in libList" :key="item.id">{{
                item.libName
              }}</Option>
            </Select>
          </FormItem>
          <div class="form-title">布控范围</div>
          <FormItem label="选择设备:" prop="scopeType">
            <RadioGroup
              v-model="formData.scopeType"
              @on-change="handleScopeChange"
            >
              <Radio label="1">所有</Radio>
              <Radio label="2">&nbsp;</Radio>
              <div
                class="select-tag-button"
                :class="{ 'select-tag-active': formData.scopeType != '2' }"
                @click="handleSelectDevice"
              >
                {{
                  deviceList.length == 0
                    ? `选择设备`
                    : `已选(${deviceList.length})`
                }}
              </div>
              <div
                class="frame-selection"
                :class="{ 'select-tag-active': formData.scopeType != '2' }"
                @click="mapToolVisibleHandlebar(true)"
              >
                <ui-icon type="gateway" :size="20" title="框选"></ui-icon>
              </div>
            </RadioGroup>
          </FormItem>
          <div class="form-title">报警通知</div>
          <FormItem label="通知用户:" prop="userScopeType">
            <div
              class="select-tag-button"
              :class="{ 'select-tag-active': formData.userScopeType != '2' }"
              @click="handleUser"
            >
              {{
                userList.length == 0 ? `选择用户` : `已选(${userList.length})`
              }}
            </div>
          </FormItem>
          <div class="form-title">通知方式</div>
          <FormItem label="短信通知人员:" prop="userScopeType">
            <div
              class="shortNote"
              v-for="(item, index) in formData.taskSmsFormList"
              :key="index"
            >
              <Input
                v-model="item.phone"
                placeholder="手机号码"
                @on-change="handleInputInform(item)"
                style="width: 200px"
                maxlength="20"
              ></Input>
              <RadioGroup v-model="item.sendTimeType">
                <Radio :label="1">即时发送</Radio>
                <Radio :label="2">时间段</Radio>
              </RadioGroup>
              <TimePicker
                :disabled="item.sendTimeType == 1"
                v-model="item.sendStartTime"
                format="HH:mm:ss"
                style="width: 100px; margin-right: 10px"
              ></TimePicker>
              <TimePicker
                :disabled="item.sendTimeType == 1"
                v-model="item.sendEndTime"
                format="HH:mm:ss"
                style="width: 100px"
              ></TimePicker>
              <span
                class="shanchu-icon"
                @click="handleDeleInform(item, index)"
                v-if="formData.taskSmsFormList.length > 1"
              >
                <i class="iconfont icon-shanchu1"></i>
              </span>
              <span
                class="add-icon"
                v-if="index + 1 == formData.taskSmsFormList.length"
                @click="handleAddInform(item)"
              >
                <i class="iconfont icon-jia"></i>
              </span>
            </div>
          </FormItem>
          <div class="form-title">布控申请</div>
          <FormItem label="申请理由:">
            <Input
              v-model="formData.requestReason"
              type="textarea"
              placeholder="请输入"
              maxlength="20"
            ></Input>
          </FormItem>
          <FormItem label="申请书:">
            <div class="uploadImg">
              <UploadImg
                choosed
                ref="uploadImg"
                :deleted="true"
                :isEdit="isEdit"
                :defaultList="defaultList"
                :multipleNum="1"
                class="upload-img"
                @on-choose="chooseHandle"
              />
            </div>
          </FormItem>
        </Form>
        <div class="btns">
          <Button @click="handleCancel">取消</Button>
          <Button class="margin" type="primary" @click="handleConfirm(0)"
            >提交</Button
          >
          <Button class="margin" type="primary" @click="handleConfirm(1)"
            >保存</Button
          >
        </div>
      </div>
    </div>
    <!-- 选择设备 -->
    <select-device
      ref="selectDevice"
      @selectData="handleSelectData"
      :showOrganization="true"
    ></select-device>
    <!-- 框选设备 -->
    <select-map-modal
      v-if="selectmapShow"
      ref="selectmap"
      @configdata="handleConfig"
    ></select-map-modal>
    <!-- 选择用户 -->
    <select-user
      @selectData="handleUserData"
      showOrganization
      ref="selectUser"
    ></select-user>
    <ui-loading v-if="loading"></ui-loading>
  </div>
</template>
<script>
import UploadImg from "@/components/ui-upload-img-static-library";
import formTable from "./components/form-table.vue";
import selectMapModal from "../components/select-map-modal.vue";
import { addTask, getTaskInfo, updateTask } from "@/api/monographic/juvenile.js";
import SelectUser from "@/components/select-modal/select-user.vue";
import { queryFaceLibList } from "@/api/monographic/juvenile.js";
import { queryDeviceInfoPageList } from "@/api/dataGovernance";
import { mapState, mapGetters, mapActions } from "vuex";
import { cloneDeep } from "lodash";
import { checkPhone } from "@/libs/configuration/util.common";
export default {
  components: { UploadImg, formTable, selectMapModal, SelectUser },
  data() {
    return {
      formData: {
        taskName: "",
        taskTimeType: "0",
        taskLevel: "1",
        taskType: "1",
        scopeType: "1",
        libIds: [],
        alarmThreshold: 0,
        userScopeType: "2",
        timeSlotArr: [],
        taskStatus: 0,
        taskSmsFormList: [
          {
            phone: "",
            sendTimeType: "",
            sendStartTime: "08:00:00",
            sendEndTime: "20:00:00",
          },
        ],
        requestReason: "",
        requestAttachmentUrl: "",
        validTimeRangeFormList: [
          {
            beginTime: "08:00:00",
            endTime: "20:00:00",
          },
        ],
      },
      validTimeType: 1,
      defaultList: [],
      selectmapShow: false,
      libList: [],
      deviceList: [],
      userList: [],
      ruleValidate: {
        taskName: [
          { required: true, message: "请输入布控名称", trigger: "blur" },
        ],
        taskTimeType: [
          { required: true, message: "请选择时间类型", trigger: "change" },
        ],
        taskLevel: [
          { required: true, message: "请选择布控级别", trigger: "change" },
        ],
        alarmThreshold: [{ required: true, message: "请选择报警域值" }],
        taskType: [
          { required: true, message: "请选择布控类型", trigger: "change" },
        ],
        scopeType: [
          { required: true, message: "请选择设备", trigger: "change" },
        ],
        userScopeType: [
          { required: true, message: "请选择通知用户", trigger: "change" },
        ],
        applyReason: [
          { required: true, message: "请输入审核意见", trigger: "blur" },
        ],
      },
      taskId: "",
      compareType: "2",
      deviceQueryConfig: {
        checkDeviceFlag: "0",
        sbgnlxList: [],
        deviceName: "",
        deviceId: "",
        detailAddress: "",
        orgCodeList: [],
      },
      loading: false,
      isEdit: false,
    };
  },
  computed: {
    ...mapGetters({
      targetObj: "systemParam/targetObj",
      userInfo: "userInfo",
    }),
  },
  async activated() {},
  async mounted() {
    await this.getDictData();
    this.taskId = this.$route.query.taskId || "";
    this.compareType = this.$route.query.compareType || "1";
    if (!this.taskId) {
      this.$nextTick(() => {
        this.formData.alarmThreshold = Number(
          this.targetObj.compareTaskConfig.minSimilarity
        );
      });
      this.isEdit = false;
    } else {
      this.isEdit = true;
    }
    this.resetForm();
    this.init();
    // 一键布控
    if (this.$route.query.images) {
      const images = JSON.parse(this.$route.query.images);
      this.$nextTick(() => {
        this.formData.taskType = "1";
        this.$refs.formTable.tableData = [
          {
            images,
          },
        ];
      });
    }
  },
  methods: {
    ...mapActions({
      getDictData: "dictionary/getDictAllData",
    }),
    handleDateOk() {},
    // 选择申请书
    chooseHandle(item) {
      this.formData.requestAttachmentUrl = item;
    },
    // 选择设备数据
    handleSelectData(list, params) {
      this.deviceQueryConfig = params;
      this.deviceList = [...list];
    },
    // 通知用户
    handleUserData(list) {
      this.userList = list;
    },
    handleConfig(value) {
      this.selectmapShow = false;
      value.map((item) => {
        item.select = true;
      });
      let deviceIds = [];
      if (this.deviceList.length == 0) {
        deviceIds = value;
      } else {
        let deviceId = new Map(
          this.deviceList.map((item) => [item.deviceGbId, item])
        );
        value.map((item) => {
          if (!deviceId.get(item.deviceGbId)) {
            deviceIds.push(item);
          }
        });
      }
      this.deviceList = [...this.deviceList, ...deviceIds];
    },
    //
    handleInputInform(row, index) {
      if (row.sendTimeType == "") {
        row.sendTimeType = 1;
      }
    },
    // 添加短信通知
    handleAddInform(row) {
      if (checkPhone(row.phone)) {
        this.formData.taskSmsFormList.push({
          phone: "",
          sendTimeType: 1,
          sendStartTime: "08:00:00",
          sendEndTime: "20:00:00",
        });
      } else {
        this.$Message.warning("请输入手机号");
      }
    },
    // 删除
    handleDeleInform(row, index) {
      this.formData.taskSmsFormList.splice(index, 1);
    },
    // 添加有效时段
    handleAddValidTime(row) {
      if (this.validTimeType == 2) {
        this.formData.validTimeRangeFormList.push({
          beginTime: "08:00:00",
          endTime: "20:00:00",
        });
      }
    },
    // 删除有效时段
    handleDeleValidTime(row, index) {
      this.formData.validTimeRangeFormList.splice(index, 1);
    },
    validTimeTypeChange(val) {
      if (val == 2) {
        this.formData.validTimeRangeFormList = [
          {
            beginTime: "08:00:00",
            endTime: "20:00:00",
          },
        ];
      }
    },
    // 确认
    handleConfirm(index) {
      this.$refs["formValidate"].validate((valid) => {
        if (valid) {
          // 时间类型 自定义
          let taskTimeB = "",
            taskTimeE = "";
          if (this.formData.taskTimeType == 1) {
            let time = this.formData.timeSlotArr.filter((item) => item);
            if (time.length > 0) {
              taskTimeB = this.$dayjs(this.formData.timeSlotArr[0]).format(
                "YYYY-MM-DD HH:mm:ss"
              );
              taskTimeE = this.$dayjs(this.formData.timeSlotArr[1]).format(
                "YYYY-MM-DD HH:mm:ss"
              );
            } else {
              taskTimeB = "";
              taskTimeE = "";
              this.$Message.warning("时间类型中自定义时间段未选择");
              return;
            }
          }
          // 布控有效时段
          if (this.validTimeType == 1) {
            this.formData.validTimeRangeFormList = [];
          }
          // 设备
          let deviceIds = this.deviceList.map((item) => item.deviceId);
          if (deviceIds.length == 0 && this.formData.scopeType == "2") {
            this.$Message.warning("设备未选择");
            return;
          }
          // 用户
          let userIds = this.userList.map((item) => item.id);
          let list = [];
          if (this.formData.taskType == "1") {
            // 单体布控
            let tableList = this.$refs.formTable.tableData;
            let cardIndex = [],
              photoIndex = [];
            let repetition = {};
            let repe = [];
            list = tableList.map((item, index) => {
              if (!item.idCardNo || item.idCardNo == "") {
                cardIndex.push(index + 1);
              } else {
                if (repetition[item.idCardNo]) {
                  repe.push(index + 1);
                } else {
                  repetition[item.idCardNo] = true;
                }
              }
              let imgs = [];
              item.images.map((ite) => {
                if (ite == "") {
                  imgs.push(ite);
                }
              });
              if (imgs.length == 5) {
                photoIndex.push(index + 1);
              }
              item.photoUrlList = item.images.map((ite) => ite.fileUrl);
              return {
                idCardNo: item.idCardNo,
                name: item.name,
                national: item.national,
                photoUrlList: item.photoUrlList.filter((item) => item),
                sex: item.sex,
              };
            });
            if (tableList.length == 0) {
              this.$Message.warning(`单体布控表格数据未填写`);
              return;
            }
            if (cardIndex.length > 0) {
              this.$Message.warning(`第${cardIndex.join(",")}项身份证未填写`);
              return;
            }
            if (repe.length > 0) {
              this.$Message.warning(`第${repe.join(",")}项身份证号为重复号码`);
              return;
            }
            if (photoIndex.length > 0) {
              this.$Message.warning(`第${photoIndex.join(",")}项照片未上传`);
              return;
            }
          } else {
            if (this.formData.libIds.length == 0) {
              this.$Message.warning(`库布控下拉框未选择`);
              return;
            }
          }
          let timeIndex = [];
          this.formData.taskSmsFormList.forEach((item) => {
            if (item.sendStartTime > item.sendEndTime) {
              timeIndex.push(index + 1);
            }
          });
          if (timeIndex.length > 0) {
            this.$Message.warning("短信通知人员结束时间必须大于当前时间");
            return;
          }
          let taskSmsFormList = this.formData.taskSmsFormList.map((item) => {
            if (item.sendTimeType == "1") {
              return {
                phone: item.phone,
                sendTimeType: item.sendTimeType,
              };
            } else {
              return item;
            }
          });
          let params = {
            userIds: userIds,
            deviceQueryConfig: {
              deviceIds: deviceIds,
              ...this.deviceQueryConfig,
            },
            personFormList: list,
            taskTimeB: taskTimeB,
            taskTimeE: taskTimeE,
            ...this.formData,
            taskSmsFormList,
            compareType: this.compareType,
          };
          this.loading = true;
          if (this.taskId) {
            params.taskId = this.taskId;
            let taskStatus = index == 0 ? 1 : 0;
            updateTask({ ...params, taskStatus: taskStatus })
              .then((res) => {
                this.$Message.success("编辑成功");
                this.handleCancel();
              })
              .finally(() => {
                this.loading = false;
              });
          } else {
            delete params.taskStatus;
            let taskStatus = index == 0 ? 1 : 0;
            addTask({ ...params, taskStatus: taskStatus })
              .then((res) => {
                this.$Message.success("新增成功");
                this.handleCancel();
              })
              .finally(() => {
                this.loading = false;
              });
          }
        }
      });
    },
    // 取消
    handleCancel() {
      this.$router.back()
      this.resetForm();
    },
    resetForm() {
      if (this.formData.taskType == "1") {
        this.$refs.formTable.tableData = [{}];
      }
      this.$refs["formValidate"].resetFields();
      this.formData.taskStatus = 0;
      this.deviceList = [];
      this.userList = [];
      this.setCurrentUser();
      this.formData.libIds = [];
      this.defaultList = [];
    },
    // 选择设备
    handleSelectDevice() {
      if (this.formData.scopeType != 2) {
        return;
      }
      this.$refs.selectDevice.show(this.deviceList);
    },
    // 框选设备
    mapToolVisibleHandlebar() {
      if (this.formData.scopeType != 2) {
        return;
      }
      this.selectmapShow = true;
      // this.$refs.selectmap.show();
    },
    setCurrentUser() {
      if (this.formData.userScopeType != 2) {
        return;
      }
      let userId = true;
      this.userList.map((item) => {
        if (item.id == this.userInfo.id) {
          userId = false;
        }
      });
      if (userId) {
        this.userList.push({ ...this.userInfo, select: true, disabled: true });
      }
    },
    // 选择用户
    handleUser() {
      this.$refs.selectUser.show(this.userList);
    },
    // 改变时间类型
    handleRadioChange() {
      this.formData.timeSlotArr = [];
    },
    // 滑动块
    handleSliderChange(val) {
      if (val < Number(this.targetObj.compareTaskConfig.minSimilarity)) {
        this.formData.alarmThreshold = Number(
          this.targetObj.compareTaskConfig.minSimilarity
        );
        this.$Message.warning("报警阀值不能小于系统配置中最小布控相似度");
      }
    },
    // 改变布控类型
    handleTaskChange() {
      this.formData.libIds = [];
      this.$refs.formTable.tableData = [{}];
    },
    // 改变设备
    handleScopeChange() {
      this.deviceList = [];
    },
    async init() {
      var params = {
        controlStatus: 1,
        libSource: 2,
      };
      let res = await queryFaceLibList(params);
      this.libList = res.data;
      if (this.taskId) {
        this.detailsQuery();
      }
    },
    // 详情查询
    detailsQuery() {
      this.loading = true;
      getTaskInfo(this.taskId)
        .then((res) => {
          let obj = res.data;
          let formkey = {
            taskTimeType: "",
            taskLevel: "",
            taskType: "",
            scopeType: "",
            userScopeType: "",
            taskStatus: "",
          };
          for (let key in this.formData) {
            if (obj[key]) {
              if (!formkey[key]) {
                this.$set(this.formData, key, obj[key].toString());
              } else {
                this.formData[key] = obj[key];
              }
            }
          }
          // 布控有效时段
          if (obj.validTimeRange) {
            obj.validTimeRangeFormList = JSON.parse(obj.validTimeRange);
            this.validTimeType = obj.validTimeRangeFormList.length ? 2 : 1;
            this.formData.validTimeRangeFormList = obj.validTimeRangeVoList;
          } else {
            this.validTimeType = 1;
          }
          if (obj.requestAttachmentUrl)
            this.defaultList = [obj.requestAttachmentUrl];
          this.formData.alarmThreshold = Number(this.formData.alarmThreshold);
          this.formData.timeSlotArr = [obj.taskTimeB, obj.taskTimeE];
          obj.smsVoList.forEach((item) => {
            item.sendStartTime = item.sendStartTime
              ? item.sendStartTime
              : "08:00:00";
            item.sendEndTime = item.sendEndTime ? item.sendEndTime : "20:00:00";
          });
          this.formData.taskSmsFormList = obj.smsVoList.length
            ? obj.smsVoList
            : [
                {
                  phone: "",
                  sendTimeType: "",
                  sendStartTime: "08:00:00",
                  sendEndTime: "20:00:00",
                },
              ];
          // 判断布控范围是不是选所有
          if (this.formData.scopeType == 2) {
            obj.deviceVoList.map((item) => {
              item.select = true;
            });
            this.deviceList = obj.deviceVoList;
          }
          if (this.formData.userScopeType == 2) {
            obj.userVoList.map((item) => {
              item.id = Number(item.userId);
              item.select = true;
            });
            this.userList = obj.userVoList;
          }
          if (this.formData.taskType == 1) {
            let params = {
              faceLibId: obj.libVoList[0].libId,
            };
            queryDeviceInfoPageList(params).then((res) => {
              let list = [];
              res.data.entities.map((item) => {
                item.photoUrlList.map((ite) => {
                  ite.fileUrl = ite.photoUrl;
                });
                // if (item.photoUrlList.length < 6) {
                // 	let num = 5 - item.photoUrlList.length
                // 	for (let i = 0; i < num; i++) {
                // 		item.photoUrlList.push('')
                // 	}
                // }
                if (item.photoUrlList.length > 1) {
                  item.photoUrlList = item.photoUrlList.slice(0, 1);
                }
                list.push({
                  id: item.id,
                  idCardNo: item.idCardNo,
                  name: item.name,
                  national: item.national,
                  sex: item.sex,
                  images: item.photoUrlList,
                });
              });
              if (this.formData.taskType == "1") {
                this.$refs.formTable.tableData = list;
              }
            });
          } else {
            this.formData.libIds = obj.libVoList.map((item) =>
              Number(item.libId)
            );
          }
        })
        .finally(() => {
          this.loading = false;
        });
    },
    dateChange(val1, val2) {
      if (val1[1].slice(-8) === "00:00:00") {
        val1[1] = val1[1].slice(0, -8) + "23:59:59";
        // 由于this.time是标准日期，因此必须使用这样的语句
        // this.$set(this.queryParam, 'timeSlotArr', val1)
      }
      this.formData.timeSlotArr = val1;
      this.$forceUpdate();
    },
  },
};
</script>
<style lang="less" scoped>
.container {
  position: relative;
  display: flex;
  flex-direction: column;
  // justify-content: center;

  .page-title {
    // margin-top: 16px;
    background: #fff;
    // height: 36px;
    z-index: 99;
    width: 100%;
    // margin-left: -20px;
    // border-bottom: 1px solid #e5e5e5;
    // padding-left: 20px;
    font-weight: 700;
    font-size: 15px;
    padding: 13px 0 13px 20px;
    box-shadow: 0px 1px 0px 0px #d3d7de;
    border-radius: 4px 4px 4px 4px;
  }
  .data {
    padding-top: 30px;
    overflow: auto;
  }

  .form {
    width: 60%;
    margin: 0 auto;
    .form-title {
      position: relative;
      height: 22px;
      line-height: 22px;
      font-weight: 600;
      padding-left: 10px;
      color: rgba(0, 0, 0, 0.8);
      font-size: 14px;
      margin-bottom: 20px;
      background: linear-gradient(
        to right,
        rgba(233, 243, 255, 1),
        rgba(233, 243, 255, 0)
      );
    }
    .form-title::after {
      content: "";
      width: 3px;
      height: 22px;
      background: #2c86f8;
      position: absolute;
      left: 0;
    }

    .form-item-time {
      /deep/ .ivu-form-item-content {
        display: flex;
      }
      /deep/ .ivu-date-picker-rel {
        width: 354px;
      }
    }
    .shortNote {
      display: flex;
      /deep/ .iconfont {
        font-size: 18px;
      }
      .shanchu-icon,
      .add-icon {
        cursor: pointer;
        margin-left: 5px;
      }
    }
    .validTime {
      display: flex;
      &-time {
        display: flex;
        flex-direction: column;
      }
      &-item {
        margin-bottom: 5px;
      }
    }
  }

  .btns {
    height: 80px;
    display: flex;
    justify-content: center;
    align-items: center;

    /deep/ .ivu-btn {
      margin: 0 6px;
    }
  }
}
.control-add {
  padding: 0;
}
/deep/ .ivu-radio-group {
  display: flex;
  margin-bottom: 12px;
  .ivu-radio-group-item {
    margin-left: 30px;
  }
  .ivu-radio-group-item:first-child {
    margin-left: 10px;
  }
}

.frame-selection {
  width: 34px;
  height: 34px;
  border-radius: 4px;
  border: 1px solid #d3d7de;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-left: 10px;
  cursor: pointer;

  .iconfont {
    margin-right: 0;
  }
}

.frame-selection:hover {
  border: 1px solid #2c86f8;

  .iconfont {
    color: #2c86f8 !important;
  }
}
.select-tag-active {
  cursor: not-allowed;
  i {
    cursor: not-allowed;
  }
}

.select-tag-button {
  width: 400px;
}
.uploadImg {
  .upload-img {
    justify-content: flex-start;
    /deep/ .upload-item {
      height: 200px !important;
      width: 200px !important;
    }
  }
}
</style>
