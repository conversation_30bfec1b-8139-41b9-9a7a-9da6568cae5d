#!/bin/bash
#set -x

cd `dirname $0`
HOME_DIR=`pwd`
#镜像版本
VERSION_INFO=1.0.0
#镜像仓库地址,使用外部传参指定
# ACTION=${1}
# IMAGE_FINAL_NAME=${2}
# DOCKER_DEFULT=registry.cn-hangzhou.aliyuncs.com/qsdi
DATE_YMD=`date +%Y-%m-%d`
DATE_HMS=`date +%H:%m:%S`
DATE_YMD_HMS="$DATE_YMD $DATE_HMS"

# URL=$(grep registry ${MAVEN_HOME}/conf/settings.xml | cut -d'>' -f2 | cut -d'<' -f1)
# USER=$(grep username ${MAVEN_HOME}/conf/settings.xml | grep -v admin | cut -d'>' -f2 | cut -d'<' -f1)
# PASSWORD=$(grep password ${MAVEN_HOME}/conf/settings.xml | grep -v admin | cut -d'>' -f2 | cut -d'<' -f1)
URL=''
USER=''
PASSWORD=''
ACTION=''
IMAGE_FINAL_NAME=''
DOCKER_DEFULT=registry.cn-hangzhou.aliyuncs.com/qsdi

while [ -n "$1" ]
do
        case "$1" in
                -a|--action) ACTION=$2; shift 2;;
                -i|--image) IMAGE_FINAL_NAME=$2; shift 2;;
                -r|--url) URL=$2; shift 2;;
                -u|--user) USER=$2; shift 2;;
                -p|--password) PASSWORD=$2; shift 2;;
                --) break ;;
                *) echo $1,$2,$URL,$USER,$PASSWORD; break ;;
        esac
done

echo 'action==' $ACTION
echo 'image==' $IMAGE_FINAL_NAME
echo 'url==' $URL
echo 'user==' $USER
echo 'password==' $PASSWORD
DOCKER_DEFULT=$URL/qsdi
echo 'docker_defult==' $DOCKER_DEFULT

docker login $URL -u $USER -p $PASSWORD

if [ $? -eq 0 ]; then
    echo "登陆Harbor仓库成功"
else
    echo "登陆Harbor仓库失败"
    return 1
fi

buildImage(){
    echo $DOCKER_DEFULT/$IMAGE_FINAL_NAME
    echo "build" = "$ACTION"
    mkdir -p docker/html
    cp -r ../dist/ html/
    docker rmi $DOCKER_DEFULT/$IMAGE_FINAL_NAME
     if [ "build" = "$ACTION" ];then
        exec docker build -t $DOCKER_DEFULT/$IMAGE_FINAL_NAME -f Dockerfile ./
   elif [ "push" = "$ACTION" ];then
        exec docker build -t $DOCKER_DEFULT/$IMAGE_FINAL_NAME -f Dockerfile ./ &
        sleep 10s
        exec docker push $DOCKER_DEFULT/$IMAGE_FINAL_NAME
    else
        echo "error: "
        exit 1;

    fi
}

buildImage

#cp -r ../dist/ html/
#docker build -t $DOCKER_DEFULT/$IMAGE_FINAL_NAME -f Dockerfile ./ & sleep 1s
#docker push $DOCKER_DEFULT/$IMAGE_FINAL_NAME