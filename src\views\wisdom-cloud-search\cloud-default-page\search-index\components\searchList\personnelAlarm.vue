<!--
    * @FileDescription: 人员报警
    * @Author: H
    * @Date: 2023/10/07
 * @LastEditors: du<PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2024-06-18 15:06:29
 -->
<template>
  <div class="personnel_alarm">
    <alarmCard
      :isShowCheckBox="false"
      :collectIcon="false"
      class="alarnRow"
      @click.native="detailFn(itemList)"
      @collection="collection"
      @refresh="tableListFn()"
      :alarmInfo="itemList"
      @singleChecked="singleChecked"
      :showOperate="false"
    />
  </div>
</template>

<script>
import alarmCard from "@/views/target-control/alarm-manager/people/components/alarm-card.vue";
export default {
  props: {
    itemList: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  components: {
    alarmCard,
  },
  data() {
    return {};
  },
  methods: {
    singleChecked() {},
    tableListFn() {},
    detailFn() {
      this.$emit("detailFn");
    },
    collection() {},
  },
};
</script>

<style lang='less' scoped>
.personnel_alarm {
  width: calc(~"20% - 10px");
  margin-left: 10px;
}
</style>