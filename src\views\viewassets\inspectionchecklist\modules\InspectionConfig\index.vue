<template>
  <div class="inspection-config-box">
    <div class="config-header">
      <i class="icon-font icon-he<PERSON><PERSON><PERSON><PERSON>u mr-sm f-18"></i>
      <span class="base-text-color f-16">合格要求</span>
    </div>
    <!-- 组件同资产库-选点分析配置 -->
    <quality ref="qualityRef" :loading="getConfigLoading" :quality-config="qualityConfig"></quality>
    <div class="config-header">
      <i class="icon-font icon-fenxijihua mr-sm f-18"></i>
      <span class="base-text-color f-16">分析计划</span>
    </div>
    <!-- 组件同资产库-选点分析配置 -->
    <analysis-plan
      ref="analysisPlanRef"
      :loading="getConfigLoading"
      :analysis-plan-config="analysisPlanConfig"
    ></analysis-plan>
  </div>
</template>
<script>
import viewassets from '@/config/api/viewassets.js';

export default {
  name: 'inspectionConfig',
  props: {},
  data() {
    return {
      configInfo: {},
      dataType: 2, //1选点分析 2巡检清单
      getConfigLoading: false,
      qualityConfig: [],
      analysisPlanConfig: {},
    };
  },
  methods: {
    async handleSubmit() {
      try {
        //合格要求配置
        this.configInfo['quality'] = this.$refs.qualityRef.convertInfo();
        let qualityParams = this.configInfo['quality'].map((item) => {
          return { ...item, dataType: this.dataType };
        });

        //分析计划
        this.configInfo['analysisPlan'] = this.$refs.analysisPlanRef.formData;
        let analysisPlanParams = {
          ...this.configInfo['analysisPlan'],
          dataType: this.dataType,
        };

        return {
          forms: qualityParams,
          cronForm: analysisPlanParams,
        };
      } catch (err) {
        console.log('err', err);
      }
    },
    async getConfig() {
      try {
        this.getConfigLoading = true;
        let {
          data: { data },
        } = await this.$http.post(viewassets.queryListByDeviceInspection);
        //合格要求
        this.analysisPlanConfig = this.$util.common.deepCopy(data.cronVo);
        // 分析计划
        this.qualityConfig = this.$util.common.deepCopy(data.vos);
      } catch (err) {
        console.log(err);
      } finally {
        this.getConfigLoading = false;
      }
    },
  },
  async mounted() {
    await this.getConfig();
  },
  components: {
    // 同src\views\viewassets\commonreport\pointanalysis\components\config\quality.vue
    Quality: require('@/views/viewassets/inspectionchecklist/modules/InspectionConfig/quality.vue').default,
    AnalysisPlan: require('@/views/viewassets/inspectionchecklist/modules/InspectionConfig/AnalysisPlan.vue').default,
  },
};
</script>

<style lang="less" scoped>
.inspection-config-box {
  overflow: auto;
  .config-header {
    background: var(--bg-collapse-item);
    padding: 17px 22px;
    .icon-font {
      color: var(--color-primary);
    }
  }
}
</style>
