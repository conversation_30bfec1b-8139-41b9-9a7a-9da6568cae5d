<template>
  <ui-modal v-model="visible" title="检测不合格原因" :styles="styles" footer-hide>
    <div>
      <ui-table
        class="ui-table mt-lg"
        :table-columns="tableColumns"
        :table-data="tableData"
        :minus-height="minusTable"
        :loading="loading"
      >
        <template #errorMessage="{ row }">
          <span class="font-red">{{ row.remark ? row.remark : row.errorMessage }}</span>
        </template>
        <template #errorType="{ row }">
          <span>{{ handleCheckStatus(row.checkStatus) }}</span>
        </template>
        <template #option="{ row }">
          <ui-btn-tip icon="icon-bianji2" content="编辑" @click.native="recordModalShow(row)"></ui-btn-tip>
        </template>
      </ui-table>
    </div>
    <ui-page
      class="page menu-content-background"
      :page-data="pageData"
      @changePage="changePage"
      @changePageSize="changePageSize"
    >
    </ui-page>
  </ui-modal>
</template>
<style lang="less" scoped>
.success {
  background: @color-success;
}
.error {
  background: @color-failed;
}
</style>
<script>
// import governancetheme from "@/config/api/governancetheme";
import equipmentassets from '@/config/api/equipmentassets';
export default {
  data() {
    return {
      visible: false,
      styles: {
        width: '6.5rem',
      },
      searchData: {
        deviceCode: null,
        page: true,
        pageNumber: 1,
        pageSize: 20,
      },
      pageData: {
        pageNum: 1,
        pageSize: 20,
        totalCount: 0,
      },
      loading: false,
      minusTable: 400,
      // tableColumns: [
      //     { type: "index", width: 70, title: "序号" },
      //     { title: "不合格原因", slot: "errorMessage" },
      //     { title: "检测规则名称", key: "componentName" },
      //     { title: "不合格字段", key: "propertyName" },
      //     { title: "实际结果", key: "propertyValue" },
      //     // { title: "操作", slot: "option" },
      // ],
      tableData: [],
    };
  },
  created() {},
  mounted() {},
  methods: {
    async init() {
      try {
        this.loading = true;
        this.copySearchDataMx(this.searchData);
        const url = this.fetchUrl ? this.fetchUrl : equipmentassets.queryUnqualifiedList;
        let res = await this.$http.post(url, this.searchData);
        this.tableData = res.data.data.entities;
        this.pageData.totalCount = res.data.data.total;
      } catch (err) {
        console.log(err);
      } finally {
        this.loading = false;
      }
    },
    search() {
      this.searchData.pageNum = 1;
      this.init();
    },
    clear() {
      this.resetSearchDataMx(this.searchData, this.search);
      this.search();
    },
    checkColor(row) {
      switch (row.checkStatus) {
        case 2:
          return 'success';
        case 3:
          return 'error';
      }
    },
    changePage(val) {
      this.searchData.pageNumber = val;
      this.init();
    },
    changePageSize(val) {
      this.searchData.pageSize = val;
      this.search();
    },
    handleCheckStatus(row) {
      const flag = {
        '1000': '待检测',
        '0000': '合格',
        '0001': '视频流不合格',
        '0010': '视图不合格',
        '0011': '视图、视频流不合格',
        '0100': '基础信息不合格',
        '0101': '基础信息、视频流不合格',
        '0110': '基础信息、视图不合格',
        '0111': '基础信息、视图、视频流不合格',
      };
      return flag[row] ? flag[row] : '--';
    },
    recordModalShow(row) {
      this.$emit('recordModalShow', this.viewData, row.checkColumnName);
    },
  },
  watch: {
    value(val) {
      if (val) {
        this.searchData.deviceCode = this.viewData.deviceId;
        // this.copySearch = this.$util.common.deepCopy(this.searchData);
        this.init();
      }
      this.visible = val;
    },
    visible(val) {
      this.$emit('input', val);
    },
  },
  computed: {
    tableColumns() {
      let columns = [];
      columns.push(
        { type: 'index', width: 50, title: '序号', align: 'center' },
        { title: '不合格原因', slot: 'errorMessage', tooltip: true },
        { title: '检测规则名称', key: 'ruleName', tooltip: true },
        { title: '不合格字段', key: 'checkColumnName', tooltip: true },
        { title: '异常类型', slot: 'errorType', tooltip: true },
        { title: '实际结果', key: 'checkColumnValue', tooltip: true },
      );
      if (this.needOption) {
        columns.push({
          title: '操作',
          slot: 'option',
          width: 80,
          align: 'center',
          fixed: 'right',
        });
      }
      return columns;
    },
  },
  props: {
    value: {
      required: true,
      type: Boolean,
    },
    viewData: {
      required: true,
      type: Object,
    },
    needOption: {
      type: Boolean,
      default: false,
    },
    fetchUrl: {
      type: String,
      default: null,
    },
  },
  components: {
    UiTable: require('@/components/ui-table.vue').default,
  },
};
</script>
