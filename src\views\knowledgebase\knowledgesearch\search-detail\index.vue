<template>
  <div class="search-detail height-full">
    <section class="search-detail-content">
      <ui-search-tree
        ref="uiTree"
        class="customize-tree mr-sm"
        placeholder="请输入目录名称"
        :show-checkbox="false"
        node-key="id"
        :tree-data="getCatalogTreeData"
        :current-node-key="currentNodeKey"
        :default-props="defaultProps"
        :defaultCheckedKeys="[]"
        expandAll
        checkStrictly
        @selectTree="selectTree"
      >
      </ui-search-tree>
      <div class="search-detail-content-list auto-fill">
        <div v-if="!detailShow" class="auto-fill list-wrapper">
          <section class="search-detail-top">
            <big-input
              v-model="searchData.queryKey"
              class="big-input"
              placeholder="请输入知识名称、内容或知识目录进行检索"
              @startSearch="startSearch"
            ></big-input>
            <Button type="text" @click="$emit('onBack')">&lt;&lt; 返回</Button>
          </section>
          <div class="detail-item">
            <list-item
              v-for="(item, index) in detailList"
              :key="index"
              :one-detail="item"
              @viewDetail="viewDetail"
            ></list-item>
          </div>
        </div>
        <div class="no-data" v-if="!detailList.length">
          <i class="no-data-img icon-font icon-zanwushuju1"></i>
          <div class="null-data-text">暂无数据</div>
        </div>
        <loading v-if="listLoading && !detailShow" fix></loading>
        <ui-page
          v-if="!detailShow"
          class="page"
          :page-data="pageData"
          @changePage="changePage"
          @changePageSize="changePageSize"
        >
        </ui-page>
        <item-detail v-if="detailShow" @onBack="detailShow = false" :item-data="currentActiveDetail"></item-detail>
      </div>
    </section>
  </div>
</template>

<script>
import { mapActions, mapGetters } from 'vuex';
import knowledgebase from '@/config/api/knowledgebase';
export default {
  name: 'searchDetail',
  props: {
    defaultSearchValue: {},
    params: {},
  },
  data() {
    return {
      searchData: {
        catalogueId: null,
        queryKey: '',
        pageNumber: 1,
        pageSize: 20,
      },
      pageData: {
        pageNum: 1,
        pageSize: 20,
        totalCount: 0,
      },
      listLoading: false,
      defaultProps: Object.freeze({
        id: 'id',
        label: 'name',
        children: 'children',
      }),
      currentNodeKey: 0,
      detailShow: false,
      detailList: [],
      currentActiveDetail: {},
    };
  },
  async created() {
    await this.setCatalogTreeData();
    this.searchData.queryKey = this.defaultSearchValue;
    this.init();
  },
  methods: {
    ...mapActions({
      setCatalogTreeData: 'knowledgebase/setCatalogTreeData',
    }),
    changePage(val) {
      this.searchData.pageNumber = val;
      this.init();
    },
    changePageSize(val) {
      this.searchData.pageNumber = 1;
      this.searchData.pageSize = val;
      this.search();
    },
    viewDetail(item) {
      this.currentActiveDetail = item;
      this.detailShow = true;
    },
    selectTree(data) {
      this.searchData.catalogueId = data.id;
      this.startSearch();
    },
    startSearch() {
      this.searchData.pageNumber = 1;
      this.pageData.pageNum = 1;
      this.init();
    },
    async init() {
      try {
        this.detailShow = false;
        this.listLoading = true;
        this.copySearchDataMx(this.searchData);
        let data = {
          ...this.searchData,
          ...this.params,
        };
        const res = await this.$http.post(knowledgebase.knowledgeSearchList, data);
        this.detailList = res.data.data.entities;
        this.pageData.totalCount = res.data.data.total;
      } catch (err) {
        console.log(err);
      } finally {
        this.listLoading = false;
      }
    },
  },
  watch: {
    params: {
      deep: true,
      handler() {
        this.init();
      },
    },
  },
  computed: {
    ...mapGetters({
      getCatalogTreeData: 'knowledgebase/getCatalogTreeData',
    }),
  },
  components: {
    UiSearchTree: require('@/components/ui-search-tree.vue').default,
    ListItem: require('./list-item.vue').default,
    ItemDetail: require('../../components/item-detail.vue').default,
    BigInput: require('../big-input.vue').default,
  },
};
</script>
<style lang="less" scoped>
.search-detail {
  .search-detail-top {
    padding: 20px 0;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    .big-input {
      width: 500px;
      @{_deep}.ivu-input {
        height: 35px !important;
        line-height: 35px;
      }
    }
  }
  .search-detail-content {
    display: flex;
    height: 100%;
    .customize-tree {
      width: 250px;
      height: 100%;
      border-right: 1px solid var(--border-color);
      padding: 20px 10px;
    }
    .search-detail-content-list {
      position: relative;
      padding: 0 10px 10px 10px;
      flex: 1;
      height: 100%;
      .detail-item{
        overflow-y: scroll;
      }
    }
  }
}
</style>
