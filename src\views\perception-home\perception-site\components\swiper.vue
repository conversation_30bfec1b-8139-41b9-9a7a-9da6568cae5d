<template>
  <div class="my-swiper-container" id="mySwiper">
    <swiper ref="mySwiper" :options="swiperOption" class="my-swiper">
      <template v-for="(item, index) in list">
        <swiper-slide :key="index">
          <div class="swiper-item">
            <div class="block">
              <div class="title">{{item.title}}</div>
              <div class="content">
                <div class="left">
                  <h1>
                    <count-to :start-val="0" :end-val="item.total" :duration="1000" class="h1"></count-to>
                  </h1>

                  <h6>总量</h6>
                </div>
                <div class="line"></div>
                <div class="right">
                  <h1>
                    <count-to :start-val="0" :end-val="item.todayAdd" :duration="1000" class="h1"></count-to>
                  </h1>
                  <h6>今日新增</h6>
                </div>
              </div>
            </div>
          </div>
        </swiper-slide>
      </template>
    </swiper>
    <div class="swiper-pagination" slot="pagination"></div>
    <!-- <div class="swiper-button-prev snap-prev" slot="button-prev"><i class="iconfont icon-caret-right"></i></div>
    <div class="swiper-button-next snap-next" slot="button-next"><i class="iconfont icon-caret-right"></i></div> -->
  </div>
</template>
<script>
import CountTo from 'vue-count-to'
import { swiper, swiperSlide } from 'vue-awesome-swiper'
export default {
  components: { CountTo, swiper, swiperSlide },
  props: {
    list: {
      type: Array,
      default: () => [
        { title: '系统数据', total: 10000, todayAdd: 1000},
        { title: '系统数据1', total: 20000, todayAdd: 2000},
        { title: '系统数据2', total: 30000, todayAdd: 3000},
      ]
    },
    // 3人脸/2车辆
    type: {
      type: Number,
      default: 3
    },
    // 卡片内部间距，单位 px
    padding: {
      type: Number,
      default: 20
    },
    // 禁用鼠标悬停显示阴影
    disHover: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      swiperResize: false,
      swiperOption: {
        effect: 'coverflow',
        slidesPerView: 2.05,
        centeredSlides: true,
        initialSlide: 1,
        // loop: false,
        loopAdditionalSlides: 3,
        speed: 1000,
        // autoplay: {
        //   delay: 100000,
        //   stopOnLastSlide: false,
        //   disableOnInteraction: false,
        //   autoplayDisableOnInteraction: false
        // },
        coverflowEffect: {
          rotate: 0, // slide做3d旋转时Y轴的旋转角度。默认50。
          stretch: 80, // 每个slide之间的拉伸值，越大slide靠得越紧。
          depth: 100, // slide的位置深度。值越大z轴距离越远，看起来越小。
          modifier: 1.2, // depth和rotate和stretch的倍率，相当于depth*modifier、rotate*modifier、stretch*modifier，值越大这三个参数的效果越明显。默认1。
          slideShadows: true // 开启slide阴影。默认 true。
        },
        pagination: {
          el: '.swiper-pagination',
          clickable: true
        },
        // navigation: {
        //   nextEl: '.snap-next',
        //   prevEl: '.snap-prev'
        // },
        observer: true,
        observeParents: true
      }
    }
  },
  mounted() {
    let _this = this
    this.$erd.listenTo(document.getElementById('mySwiper'), element => {
      _this.updateSliderHandle()
    })
  },
  methods: {
    updateSliderHandle() {
      const w = document.documentElement.clientWidth
      this.swiperOption.coverflowEffect.stretch = (w / 192) * 3
      this.swiperOption.coverflowEffect.depth = (w / 192) * 9
      this.swiperResize = true
      this.$nextTick(() => {
        this.swiperResize = false
      })
    }
  }
}
</script>
<style lang="less" scoped>
.my-swiper-container {
  width: 100%;
  position: relative;
  display: flex;
  justify-content: center;
  .my-swiper {
    margin: auto;
    padding: 15px 0;
    
    .swiper-item {
      width: 260px;
      height: 128px;
      background: #fff;
      box-shadow: 0px 6px 10px 0px rgba(0, 0, 0, 0.2);
      border-radius: 4px;
      border: 1px solid #d3d7de;
      box-sizing: border-box;
      // padding: 10px;
      overflow: hidden;
      display: flex;
      justify-content: center;
      // align-items: center;
      .block {
        width: 100%;
      }
      .title {
        height: 40px;
        line-height: 40px;
        text-align:center;
        font-size:14px;
        font-weight: 600;
        background: #F9F9F9;
      }
      .content {
        display: flex;
        justify-content: center;
        text-align:center;
        padding-top: 12px;
        .left {
          width: 55%;
          .h1 {
            color: #2C86F8;
          }
        }
        .right {
          flex: 1;
          .h1 {
            color: #1FAF8A;
          }
        }
        .line {
          width: 2px;
          height: 50px;
          margin: 10px;
          border-right: 1px solid #e3e3e3;
        }
      }
      .img-content {
        width: 100%;
        height: 120px;
        overflow: hidden;
        position: relative;
        img {
          width: 100%;
          height: 100%;
        }
        span {
          width: 100%;
          height: 30px;
          position: absolute;
          left: 0;
          bottom: 0px;
          color: #fff;
          text-align: center;
          line-height: 30px;
          background: rgba(0, 0, 0, 0.5);
        }
        .badge {
          position: absolute;
          width: 20px;
          height: 20px;
          border-radius: 4px;
          left: 0;
          top: 0;
          color: #fff;
          font-size: 12px;
          line-height: 21px;
          text-align: center;
        }
      }
      .bottom-info {
        padding-top: 10px;
        color: #000;
        font-size: 12px;
        .info,
        time {
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }
  }
  /deep/ .swiper-container-3d {
    .swiper-slide-shadow-left {
      background-image: linear-gradient(to left, rgba(255, 255, 255, 1), rgba(255, 255, 255, 0));
    }
    .swiper-slide-shadow-right {
      background-image: linear-gradient(to right, rgba(255, 255, 255, 1), rgba(255, 255, 255, 0));
    }
  }
  .swiper-button-prev,
  .swiper-button-next {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background: rgba(0, 0, 0, 0.5);
    text-align: center;
    line-height: 30px;
    margin-top: -15px;
    .iconfont {
      color: #fff;
      font-size: 18px;
    }
    &:hover {
      background: rgba(0, 0, 0, 0.7);
    }
    &:active {
      background: rgba(0, 0, 0, 1);
    }
  }
  .swiper-button-prev {
    transform: rotate(180deg);
    left: 12px;
  }
  .swiper-button-next {
    right: 12px;
  }
}
.swiper-pagination {
    bottom: 0;
    /deep/ .swiper-pagination-bullet {
      width: 70px;
      height: 6px;
      border-radius: 0;
      background: rgba(255, 255, 255, 1);
      opacity: 1;
      margin-right: 6px;
      &.swiper-pagination-bullet-active {
        background: #2c86f8;
      }
    }
  }
</style>
