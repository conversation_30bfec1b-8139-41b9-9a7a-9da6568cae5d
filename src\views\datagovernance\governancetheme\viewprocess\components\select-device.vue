<template>
  <div class="select-device">
    <ui-modal v-model="visible" :title="title" width="97.3rem" @onCancel="cancel">
      <div class="select-device-wrap">
        <!--        <div class="select-device-wrap-header">-->
        <!--          <div class="left">-->
        <!--            已选中 <span class="red">{{ this.checkIds.length }}</span> 条-->
        <!--          </div>-->
        <!--          <div class="right">-->
        <!--            <ui-label class="inline right-margin" label="关键词" :width="55">-->
        <!--              <Input-->
        <!--                v-model="searchData.keyWord"-->
        <!--                class="input-width"-->
        <!--                placeholder="请输入设备名称或编码"-->
        <!--              ></Input>-->
        <!--            </ui-label>-->
        <!--            <ui-label class="inline right-margin" :label="global.filedEnum.sbdwlx" :width="70">-->
        <!--              <Select-->
        <!--                class="input-width"-->
        <!--                v-model="searchData.sbdwlx"-->
        <!--                clearable-->
        <!--                placeholder="请选择"-->
        <!--              >-->
        <!--                <Option-->
        <!--                  v-for="(sbdwlxItem, bdindex) in dictData['propertySearch_sbdwlx']"-->
        <!--                  :key="'sbdwlx' + bdindex"-->
        <!--                  :value="sbdwlxItem.dataKey"-->
        <!--                  >{{ sbdwlxItem.dataValue }}</Option-->
        <!--                >-->
        <!--              </Select>-->
        <!--            </ui-label>-->
        <!--            <ui-label class="inline right-margin" :label="global.filedEnum.sbgnlx" :width="70">-->
        <!--              <Select-->
        <!--                class="input-width"-->
        <!--                v-model="searchData.sbgnlx"-->
        <!--                clearable-->
        <!--                placeholder="请选择"-->
        <!--              >-->
        <!--                <Option-->
        <!--                  v-for="(sbgnlxItem, bdindex) in dictData['sxjgnlx_receive']"-->
        <!--                  :key="'sbgnlx' + bdindex"-->
        <!--                  :value="sbgnlxItem.dataKey"-->
        <!--                  >{{ sbgnlxItem.dataValue }}</Option-->
        <!--                >-->
        <!--              </Select>-->
        <!--            </ui-label>-->
        <!--            <ui-label class="inline right-margin" label="检测状态" :width="70">-->
        <!--              <Select-->
        <!--                class="input-width"-->
        <!--                multiple-->
        <!--                v-model="searchData.checkStatuses"-->
        <!--                placeholder="请选择"-->
        <!--                clearable-->
        <!--              >-->
        <!--                <Option-->
        <!--                  v-for="(statusItem, statuindex) in dictData['check_status']"-->
        <!--                  :key="'status' + statuindex"-->
        <!--                  :value="statusItem.dataKey"-->
        <!--                  >{{ statusItem.dataValue }}</Option-->
        <!--                >-->
        <!--              </Select>-->
        <!--            </ui-label>-->
        <!--            <div class="inline">-->
        <!--              <Button type="primary" class="mr-sm" @click="search(searchData)">-->
        <!--                查询-->
        <!--              </Button>-->
        <!--              <Button-->
        <!--                type="default"-->
        <!--                @click="resetSearchDataMx(searchData, () => search(searchData))"-->
        <!--              >-->
        <!--                重置-->
        <!--              </Button>-->
        <!--            </div>-->
        <!--          </div>-->
        <!--          <Checkbox class="mr-sm" v-model="single">全选</Checkbox>-->
        <!--          <Checkbox class="mr-sm" v-model="single">排除</Checkbox>-->
        <!--          <div class="rmgroup">-->
        <!--            <RadioGroup>-->
        <!--              <Radio label="0">全量设备排除选中设备</Radio>-->
        <!--              <Radio label="1">筛选结果排除选中设备</Radio>-->
        <!--            </RadioGroup>-->
        <!--          </div>-->
        <!--        </div>-->
        <div class="select-device-wrap-content">
          <div class="select-device-wrap-content-left">
            <ui-search-tree
              placeholder="请输入组织机构名称或组织机构编码"
              node-key="id"
              :scroll="340"
              :maxHeight="700"
              :tree-data="treeData"
              :default-props="defaultProps"
              @selectTree="selectTree"
            >
            </ui-search-tree>
          </div>
          <div class="select-device-wrap-content-right auto-fill">
            <div class="select-device-wrap-header">
              <div class="search-top">
                <div class="right">
                  <ui-label class="inline right-margin" label="关键词" :width="55">
                    <Input v-model="searches.keyWord" class="input-width" placeholder="请输入设备名称或编码"></Input>
                  </ui-label>
                  <ui-label class="inline right-margin" :label="global.filedEnum.sbdwlx" :width="70">
                    <Select class="input-width" v-model="searches.sbdwlx" clearable placeholder="请选择">
                      <Option
                        v-for="(sbdwlxItem, bdindex) in dictData['propertySearch_sbdwlx']"
                        :key="'sbdwlx' + bdindex"
                        :value="sbdwlxItem.dataKey"
                        >{{ sbdwlxItem.dataValue }}</Option
                      >
                    </Select>
                  </ui-label>
                  <ui-label class="inline right-margin" :label="global.filedEnum.sbgnlx" :width="70">
                    <Select class="input-width" v-model="searches.sbgnlx" clearable placeholder="请选择">
                      <Option
                        v-for="(sbgnlxItem, bdindex) in dictData['sxjgnlx_receive']"
                        :key="'sbgnlx' + bdindex"
                        :value="sbgnlxItem.dataKey"
                        >{{ sbgnlxItem.dataValue }}</Option
                      >
                    </Select>
                  </ui-label>
                  <ui-label class="inline right-margin" label="检测状态" :width="70">
                    <Select
                      class="input-width"
                      multiple
                      :max-tag-count="1"
                      v-model="searches.checkStatuses"
                      placeholder="请选择"
                      clearable
                    >
                      <Option
                        v-for="(statusItem, statuindex) in dictData['check_status']"
                        :key="'status' + statuindex"
                        :value="statusItem.dataKey"
                        >{{ statusItem.dataValue }}</Option
                      >
                    </Select>
                  </ui-label>
                  <div class="inline">
                    <Button type="primary" class="mr-sm" @click="search(searchData)"> 查询 </Button>
                    <Button type="default" @click="resetSearchDataMx(searchData, () => search(searchData))">
                      重置
                    </Button>
                  </div>
                </div>
                <div class="left">
                  已选设备 <span class="red">{{ checkDevices }}</span> 条
                </div>
              </div>
              <div class="search-bottom">
                <Checkbox class="checks mr-lg align-flex" v-model="isAll" @on-change="handleAllCheck"> 全选</Checkbox>
                <Checkbox class="checks mr-sm align-flex" v-model="isExclude" @on-change="handleExcludeCheck">
                  排除</Checkbox
                >
                <RadioGroup v-if="isExclude" v-model="excludeType">
                  <Radio label="0">全量设备排除选中设备</Radio>
                  <Radio label="1">筛选结果排除选中设备</Radio>
                </RadioGroup>
              </div>
            </div>
            <ui-table
              class="ui-table auto-fill"
              :table-columns="tableColumns"
              :table-data="tableData"
              :loading="tableLoading"
              @oneSelected="handleSelected"
              @cancelSelectTable="handleOneCancelSelect"
              @onSelectAllTable="handleSelected"
              @cacelAllSelectTable="handleAllCancelSelect"
            >
              <!-- 经纬度保留8位小数-->
              <template #longitude="{ row }">
                <span>{{ row.longitude | filterLngLat }}</span>
              </template>
              <template #latitude="{ row }">
                <span>{{ row.latitude | filterLngLat }}</span>
              </template>
              <template #checkStatusText="{ row }">
                <span class="statustag" :style="{ 'background-color': statusArr[row.checkStatus] }">{{
                  statusObj[row.checkStatus]
                }}</span>
              </template>
            </ui-table>
            <ui-page
              class="page menu-content-background"
              :page-data="pageData"
              @changePage="handlePage"
              @changePageSize="handlePageSize"
            >
            </ui-page>
          </div>
        </div>
      </div>
      <template slot="footer">
        <Button type="primary" @click="save">确&nbsp;定</Button>
      </template>
    </ui-modal>
  </div>
</template>
<script>
import equipmentassets from '@/config/api/equipmentassets';
import { mapGetters } from 'vuex';
export default {
  props: {
    selectedList: {
      type: Array,
      default: () => {
        return [];
      },
    },
    isfromTheme: {
      type: Boolean,
      default: true,
    }, // 对否来自主题
  },
  data() {
    return {
      visible: false,
      title: '选择设备',
      selectOrgTree: {
        orgCode: null,
      },
      searchData: {
        orgCode: '',
        keyWord: '',
        sbdwlx: '', // 设备点位类型
        sbgnlx: '', // 设备功能类型
        checkStatuses: [0, 1, 2, 3], // 状态
        pageNumber: 1,
        pageSize: 20,
      },
      defaultProps: {
        label: 'orgName',
        children: 'children',
      },
      tableColumns: [
        { type: 'selection', align: 'center', width: 50 },
        { title: '序号', type: 'index', align: 'center', width: 50 },
        {
          title: `${this.global.filedEnum.deviceId}`,
          key: 'deviceId',
          align: 'left',
          width: 170,
        },
        {
          title: `${this.global.filedEnum.deviceName}`,
          key: 'deviceName',
          align: 'left',
          minWidth: 200,
          tooltip: true,
        },
        {
          title: '组织机构',
          key: 'orgName',
          align: 'left',
          minWidth: 120,
          tooltip: true,
        },
        {
          title: '行政区划',
          key: 'civilName',
          align: 'left',
          width: 120,
          tooltip: true,
        },
        {
          title: `${this.global.filedEnum.ipAddr}`,
          key: 'ipAddr',
          align: 'left',
          minWidth: 130,
          tooltip: true,
        },
        {
          title: `${this.global.filedEnum.longitude}`,
          key: 'longitude',
          slot: 'longitude',
          align: 'left',
          width: 110,
          tooltip: true,
        },
        {
          title: `${this.global.filedEnum.latitude}`,
          key: 'latitude',
          slot: 'latitude',
          align: 'left',
          width: 110,
          tooltip: true,
        },
        {
          title: this.global.filedEnum.sbdwlx,
          key: 'sbdwlxText',
          align: 'left',
          width: 130,
          tooltip: true,
        },
        {
          title: `${this.global.filedEnum.sbgnlx}`,
          key: 'sbgnlxText',
          align: 'left',
          width: 130,
          tooltip: true,
        },
        {
          title: '检测状态',
          slot: 'checkStatusText',
          align: 'left',
          width: 90,
          minWidth: 200,
          tooltip: true,
        },
      ],
      tableData: [],
      tableLoading: false,
      pageData: {
        pageNum: 1,
        pageSize: 20,
        totalCount: 0,
      },
      dictData: {},
      topicComponentId: '', // 组件ID
      checkIds: [],
      statusObj: {},
      statusArr: ['#09B5AF', 'var(--color-warning)', 'var(--color-success)', 'var(--color-failed)'],
      isAll: false,
      isExclude: false,
      excludeType: '0',
      checkDevices: 0,
      searches: {
        orgCode: '',
        keyWord: '',
        sbdwlx: '', // 设备点位类型
        sbgnlx: '', // 设备功能类型
        checkStatuses: [0, 1, 2, 3], // 状态
      },
    };
  },
  created() {},
  methods: {
    init(data, id = '') {
      this.cancel();
      this.dictData = data;
      this.topicComponentId = id;
      this.visible = true;
      this.copySearchDataMx(this.searchData);
      this.checkIds = this.selectedList;
      this.handleStatusDict();
      this.search();
    },
    selectedOrgTree(val) {
      this.orgCode = val.orgCode;
    },
    search() {
      this.searchData.orgCode = this.searches.orgCode;
      this.searchData.keyWord = this.searches.keyWord;
      this.searchData.sbdwlx = this.searches.sbdwlx;
      this.searchData.sbgnlx = this.searches.sbgnlx;
      this.searchData.checkStatuses = this.searches.checkStatuses;
      this.initTableList();
    },
    async initTableList() {
      try {
        this.tableLoading = true;
        let params = this.searchData;
        if (!this.searchData.checkStatuses.length) {
          params.checkStatuses = [0, 1, 2, 3];
        }
        if (this.isfromTheme) {
          params.topicComponentId = this.topicComponentId;
        }
        let res = await this.$http.post(equipmentassets.queryDeviceInfoPageList, params);
        this.tableData = res.data.data.entities.map((item) => {
          item._checked = this.checkIds.includes(item.id);
          switch (item.checkStatus) {
            case '':
              item.color = '';
          }
          return item;
        });
        this.pageData.totalCount = res.data.data.total;
        this.tableLoading = false;
      } catch (err) {
        console.log(err);
      }
    },
    selectTree(data) {
      this.searchData.orgCode = data.orgCode;
      this.search();
    },
    handleSelected(selection) {
      let selectIds = selection.map((item) => {
        return item.id;
      });
      this.checkIds = Array.from(new Set([...this.checkIds, ...selectIds]));
    },
    handleOneCancelSelect(selection, row) {
      const index = this.checkIds.indexOf(row.id);
      this.checkIds.splice(index, 1);
    },
    handleAllCancelSelect() {
      this.tableData.forEach((item) => {
        const index = this.checkIds.indexOf(item.id);
        if (index !== -1) {
          this.checkIds.splice(index, 1);
        }
      });
    },
    async handlePage(val) {
      this.searchData.pageNumber = val;
      await this.initTableList();
      this.hadleDataChecked();
    },
    handlePageSize(val) {
      this.searchData.pageSize = val;
      this.search();
      this.hadleDataChecked();
    },
    cancel() {
      this.checkIds = [];
      this.tableData = [];
      this.searches = {
        orgCode: '',
        keyWord: '',
        sbdwlx: '', // 设备点位类型
        sbgnlx: '', // 设备功能类型
        checkStatuses: [0, 1, 2, 3], // 状态
      };
      this.searchData.pageNumber = 1;
      this.searchData.pageSize = 20;
      this.pageData = {
        pageNum: 1,
        pageSize: 20,
        totalCount: 0,
      };
    },
    save() {
      this.visible = false;
      let selectResults = {
        isAll: this.isAll,
        isExclude: this.isExclude,
        checkIds: this.checkIds,
        excludeType: this.excludeType,
        checkDevices: this.checkDevices,
      };
      if (this.isExclude) {
        selectResults.keyWord = this.excludeType === '0' ? '' : this.searchData.keyWord;
        selectResults.sbdwlx = this.excludeType === '0' ? '' : this.searchData.sbdwlx;
        selectResults.sbgnlx = this.excludeType === '0' ? '' : this.searchData.sbgnlx;
        selectResults.orgCode = this.excludeType === '0' ? '' : this.searchData.orgCode;
        selectResults.checkStatuses = this.excludeType === '0' ? [0, 1, 2, 3] : this.searchData.checkStatuses;
      }
      this.$emit('getDeviceIdList', selectResults);
    },
    handleStatusDict() {
      this.statusObj = {};
      this.dictData['check_status'].forEach((item) => {
        this.statusObj[item.dataKey] = item.dataValue;
      });
    },
    handleAllCheck(val) {
      if (val) {
        this.isExclude = false;
        this.hadleDataChecked();
        this.checkDevices = this.pageData.totalCount;
      } else {
        this.checkDevices = 0;
        this.hadleDataChecked();
      }
    },
    handleExcludeCheck(val) {
      if (val) {
        this.isAll = false;
        this.hadleDataChecked();
        if (this.excludeType === '0') {
          this.checkDevices = this.checkIds.length ? this.pageData.totalCount - this.checkIds.length : 0;
        } else {
          this.checkDevices = this.checkIds.length ? this.pageData.totalCount - this.checkIds.length : 0;
        }
      } else {
        this.checkDevices = 0;
      }
    },
    hadleDataChecked() {
      this.tableData = this.tableData.map((item) => {
        this.$set(item, '_checked', this.isAll);
        this.$set(item, '_disabled', this.isAll);
        return item;
      });
    },
  },
  watch: {},
  computed: {
    ...mapGetters({
      treeData: 'common/getOrganizationList',
      defaultSelectedOrg: 'common/getDefaultSelectedOrg',
    }),
  },
  components: {
    UiSearchTree: require('@/components/ui-search-tree.vue').default,
    UiTable: require('@/components/ui-table.vue').default,
  },
};
</script>
<style lang="less" scoped>
.flex {
  display: flex;
  justify-content: center;
  align-items: center;
}
.align-flex {
  display: flex;
  align-items: center;
}
.right-margin {
  margin-right: 30px;
}
.select-device {
  width: 100%;
  &-wrap {
    width: 100%;
    &-header {
      padding: 20px;
      border-bottom: 1px solid var(--border-color);
      .search-top {
        width: 100%;
        .align-flex;
      }
      .search-bottom {
        width: 100%;
        margin-top: 20px;
        .align-flex;
      }
      .left {
        font-size: 14px;
        color: #ffffff;
      }
      .right {
        flex: 1;
      }
    }
    &-content {
      width: 100%;
      height: 750px;
      display: flex;
      border-top: 1px solid var(--border-color);
      &-left {
        width: 300px;
        height: 100%;
        padding: 0 10px;
        border-right: 1px solid var(--border-color);
      }
      &-right {
        flex: 1;
        height: 100%;
      }
    }
  }
  .input-width {
    width: 160px;
  }
  .red {
    color: #bc3c19;
  }
  @{_deep} .ivu-modal-body {
    padding: 30px 0 0 !important;
  }
}
.statustag {
  .flex;
  width: 56px;
  height: 19px;
  font-size: 14px;
  color: #ffffff;
  border-radius: 4px;
}
//.rmgroup {
//  width: 180px;
//  margin-right: 20px;
//}
@{_deep} .ivu {
  &-table-cell {
    padding: 0 5px !important;
  }
  &-modal-body {
    padding: 0 !important;
  }
}
@{_deep} .checks .ivu-checkbox {
  margin-right: 10px !important;
}
@{_deep} .el-tree-node > .el-tree-node__children {
  overflow: inherit;
}
</style>
