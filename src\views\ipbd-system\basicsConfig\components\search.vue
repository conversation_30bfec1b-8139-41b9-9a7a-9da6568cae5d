<template>
  <div class="search">
    <Form :inline="true" ref="form" :model="form">
      <FormItem label="表名称:" class="mr-20" prop="sourceTable">
        <Input placeholder="请输入" v-model="form.sourceTable" maxlength="50"></Input>
      </FormItem>
      <FormItem label="表注释:" class="mr-20" prop="sourceTableCn">
        <Input placeholder="请输入" v-model="form.sourceTableCn" maxlength="50"></Input>
      </FormItem>
      <FormItem class="btn-group">
        <Button type="primary" @click="startSearch">查询</Button>
        <Button @click="resetHandle">重置</Button>
      </FormItem>
    </Form>
  </div>
</template>
<script>
export default {
  components: {
  },
  props: {
    list: {
      //所有目录
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      visible: false,
      form: {
        sourceTable: '',
        sourceTableCn: '',
      }
    }
  },
  created() {},
  methods: {
    // 查询
    startSearch() {
      this.$emit('searchForm', this.form)
    },
    // 重置
    resetHandle() {
      this.$refs.form.resetFields()
      this.startSearch()
    },
    // 删除
    delet() {
      this.$emit('delet')
    },
    // 新增
    add() {
      this.$emit('add')
    },
    // 导出
    exportData() {
      this.$emit('exportData')
    }
  }
}
</script>
<style lang="less" scoped>
.search {
  .mr-30 {
    margin-right: 30px;
  }
  .dialog-input {
    width: 200px;
  }
  /deep/.ivu-select {
    width: 200px !important;
  }
  .btns {
    float: right;
    .ivu-btn {
      margin-left: 10px;
    }
  }
  /deep/ .filter-tree .el-tree-node__content {
    margin-top: 0px;
  }
}
</style>
