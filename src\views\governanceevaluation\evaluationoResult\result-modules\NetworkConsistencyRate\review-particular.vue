<template>
  <div class="auto-fill">
    <Particular
      class="particular"
      :icon-list="iconList"
      :table-columns="tableColumns"
      :table-data="tableData"
      :total-count="totalCount"
      :table-loading="tableLoading"
      :form-item-data="formItemData"
      :form-data="formData"
      @startSearch="startSearch"
      @handlePage="handlePage"
      @handlePageSize="handlePageSize"
    >
      <template #otherButton>
        <div class="operation-bar mb-md">
          <Button
            slot="export"
            type="primary"
            class="button-export ml-md"
            @click="onClickIndex"
            :loading="exportLoading"
          >
            <i class="icon-font icon-daochu font-white mr-xs"></i>
            <span class="ml-xs">导出</span>
          </Button>
        </div>
      </template>
      <!-- 表格插槽 -->
    </Particular>
    <export-data ref="exportModule" :export-loading="exportLoading" @handleExport="handleExport"> </export-data>
  </div>
</template>
<script>
/**
 * 本文件属于处理数据的中间层
 */
import { iconStaticsList, tableColumn, formItemData } from './util/enum/ReviewParticular.js';
import downLoadTips from '@/mixins/download-tips';
import dealWatch from '@/mixins/deal-watch';
import particularMixin from '@/views/governanceevaluation/evaluationoResult/util/Mixins/particularMixin';
import { mapGetters } from 'vuex';
export default {
  mixins: [downLoadTips, particularMixin, dealWatch],
  props: {},
  data() {
    return {
      iconList: iconStaticsList,
      tableColumns: [],
      formItemData: [],
      formData: {
        deviceId: '',
        deviceName: '',
        phyStatus: '',
        sbgnlx: '',
      },
      pageData: {
        pageNum: 1,
        pageSize: 20,
      },
      totalCount: 0,
      tableLoading: false,
      tableData: [],
      exportLoading: false,
      exportList: [
        { name: '导出设备总表', type: false },
        { name: '按异常原因导出分表', type: true },
      ],
    };
  },
  computed: {
    ...mapGetters({
      propertySearchSxjgnlx: 'algorithm/sxjgnlx_receive',
    }),
  },
  created() {
    let paramsList = this.$route.query;
    this.tableColumns = tableColumn;
    if (paramsList.statisticType === 'REGION') {
      this.tableColumns[3].title = '行政区划';
      this.tableColumns[3].key = 'civilName';
    } else {
      this.tableColumns[3].title = '组织机构';
      this.tableColumns[3].key = 'orgName';
    }

    this.startWatch(
      '$route.query',
      () => {
        this.initAll();
      },
      { deep: true, immediate: true },
    );
  },
  watch: {
    // 处理 摄像机功能类型
    propertySearchSxjgnlx: {
      handler(arr) {
        if (!arr?.length) return;
        let options = [];
        arr.forEach((item) => {
          options.push({ value: item.dataKey, label: item.dataValue });
        });
        this.formItemData = formItemData.map((item) => {
          if (item.key === 'sbgnlx') {
            item.options = options;
          }
          return item;
        });
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {
    initAll() {
      this.getTableList();
      this.getTableDataTotal();
      // 调用统计，并通知后端已更新[之前的逻辑]
      this.MixinGetStatInfo().then((data) => {
        this.iconList.forEach((item) => {
          if (item.fileName === 'resultValueFormat') {
            // 配置合格不合格图标
            this.MixinSpecialIcon(item, data.qualified);
          }
          item.count = data[item.fileName] || 0;
        });
      });
    },
    startSearch(params) {
      this.pageData.pageNum = 1;
      Object.assign(this.formData, params);
      this.formData = params;
      this.getTableList();
      this.getTableDataTotal();
    },
    getTableList() {
      this.tableData = [];
      this.MixinGetTableData().then((data) => {
        this.tableData = data.entities || [];
        // this.totalCount = data.total;
      });
    },
    getTableDataTotal() {
      // 通过接口单独获取总数
      this.MixinGetTableDataTotal().then((data) => {
        this.totalCount = data;
      });
    },
    handlePage(pageData) {
      this.pageData = pageData;
      this.getTableList();
    },
    handlePageSize(pageData) {
      this.pageData = pageData;
      this.getTableList();
    },
    selectInfo(infoList) {
      this.errorMessages = infoList.map((item) => {
        return item.name;
      });
      this.formData.errorMessages = this.errorMessages;
      this.pageData.pageNum = 1;
      this.getTableList();
      this.getTableDataTotal();
    },
    // 导出
    onClickIndex() {
      this.$refs.exportModule.init(this.$route.query.batchId);
    },
    handleExport(val) {
      this.MixinGetExport(val);
    },
  },
  components: {
    Particular: require('../../ui-pages/particular.vue').default,
    exportData: require('./components/export-data.vue').default,
  },
};
</script>
<style lang="less" scoped>
.particular {
  padding: 0 10px;
  .operation-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .tabs {
    flex: 1;
  }
}
</style>
