<template>
  <div class="safety-property">
    <Form ref="form" inline autocomplete="off" label-position="right">
      <FormItem :required="isRequired('isShare')" class="left-item" label="是否对外共享">
        <Select v-if="!isView" v-model="formCustom.isShare" class="width-md" clearable placeholder="请选择是否对外共享">
          <Option v-for="(item, index) in shareList" :key="index" :label="item.dataValue" :value="`${item.dataKey}`">
          </Option>
        </Select>
        <span v-else class="base-text-color">{{ formCustom.isShareText || '未知' }}</span>
        <div class="error-tip ellipsis" :title="errorData.isShare">
          {{ errorData.isShare }}
        </div>
      </FormItem>
      <FormItem :required="isRequired('safetyWay')" class="left-item" label="信令安全模式">
        <Select
          v-if="!isView"
          v-model="formCustom.safetyWay"
          class="width-md"
          clearable
          placeholder="请选择信令安全模式"
        >
          <Option v-for="(item, index) in safetyList" :key="index" :label="item.dataValue" :value="`${item.dataKey}`">
          </Option>
        </Select>
        <span v-else class="base-text-color">{{ formCustom.safetyWayText || '未知' }}</span>
        <div class="error-tip ellipsis" :title="errorData.safetyWay">
          {{ errorData.safetyWay }}
        </div>
      </FormItem>
      <FormItem :required="isRequired('registerWay')" class="left-item" label="注册方式">
        <Select v-if="!isView" v-model="formCustom.registerWay" class="width-md" clearable placeholder="请选择注册方式">
          <Option v-for="(item, index) in registerList" :key="index" :label="item.dataValue" :value="`${item.dataKey}`">
          </Option>
        </Select>
        <span v-else class="base-text-color">{{ formCustom.registerWayText || '未知' }}</span>
        <div class="error-tip ellipsis" :title="errorData.registerWay">
          {{ errorData.registerWay }}
        </div>
      </FormItem>
      <FormItem :required="isRequired('certNum')" class="left-item" label="证书序列号">
        <Input
          v-if="!isView"
          type="text"
          class="width-md"
          v-model="formCustom.certNum"
          placeholder="请输入证书序列号"
          :maxlength="300"
        ></Input>
        <span v-else class="base-text-color">{{ formCustom.certNum || '未知' }}</span>
        <div class="error-tip ellipsis" :title="errorData.certNum">
          {{ errorData.certNum }}
        </div>
      </FormItem>
      <FormItem :required="isRequired('certifiable')" class="left-item" label="证书有效标识">
        <Select
          v-if="!isView"
          v-model="formCustom.certifiable"
          class="width-md"
          clearable
          placeholder="请选择证书有效标识"
        >
          <Option
            v-for="(item, index) in certifiableList"
            :key="index"
            :label="item.dataValue"
            :value="`${item.dataKey}`"
          >
          </Option>
        </Select>
        <span v-else class="base-text-color">{{ formCustom.certifiableText || '未知' }}</span>
        <div class="error-tip ellipsis" :title="errorData.certifiable">
          {{ errorData.certifiable }}
        </div>
      </FormItem>
      <FormItem :required="isRequired('errCode')" class="left-item" label="无效原因码">
        <InputNumber
          v-if="!isView"
          type="number"
          class="width-md"
          v-model="formCustom.errCode"
          placeholder="请输入无效原因码"
          :min="0"
          :max="999999999"
          :precision="0"
        ></InputNumber>
        <span v-else class="base-text-color">{{ formCustom.errCode || '未知' }}</span>
        <div class="error-tip ellipsis" :title="errorData.errCode">
          {{ errorData.errCode }}
        </div>
      </FormItem>
      <FormItem :required="isRequired('certEndTime')" class="left-item" label="证书终止有效期">
        <DatePicker
          v-if="!isView"
          class="width-md"
          type="datetime"
          v-model="formCustom.certEndTime"
          @on-change="(formatTime, timeType) => changeTimeMx(formatTime, timeType, formCustom, 'certEndTime')"
          placeholder="请选择证书终止有效期"
        ></DatePicker>
        <span v-else class="base-text-color">{{ formCustom.certEndTime || '未知' }}</span>
        <div class="error-tip ellipsis" :title="errorData.certEndTime">
          {{ errorData.certEndTime }}
        </div>
      </FormItem>
      <FormItem :required="isRequired('secrecy')" class="left-item" label="保密属性">
        <Select v-if="!isView" v-model="formCustom.secrecy" class="width-md" clearable placeholder="请选择保密属性">
          <Option v-for="(item, index) in secrecyList" :key="index" :label="item.dataValue" :value="`${item.dataKey}`">
          </Option>
        </Select>
        <span v-else class="base-text-color">{{ formCustom.secrecyText || '未知' }}</span>
        <div class="error-tip ellipsis" :title="errorData.secrecy">
          {{ errorData.secrecy }}
        </div>
      </FormItem>
      <FormItem :required="isRequired('svcSpaceSupportMode')" class="left-item" label="空域编码能力">
        <Select
          v-if="!isView"
          v-model="formCustom.svcSpaceSupportMode"
          class="width-md"
          clearable
          placeholder="请选择空域编码能力"
        >
          <Option
            v-for="(item, index) in svcSpaceSupportModeList"
            :key="index"
            :label="item.dataValue"
            :value="`${item.dataKey}`"
          >
          </Option>
        </Select>
        <span v-else class="base-text-color">{{ formCustom.svcSpaceSupportModeText || '未知' }}</span>
        <div class="error-tip ellipsis" :title="errorData.svcSpaceSupportMode">
          {{ errorData.svcSpaceSupportMode }}
        </div>
      </FormItem>
      <FormItem :required="isRequired('svcTimeSupportMode')" class="left-item" label="时域编码能力">
        <Select
          v-if="!isView"
          v-model="formCustom.svcTimeSupportMode"
          class="width-md"
          clearable
          placeholder="请选择时域编码能力"
        >
          <Option
            v-for="(item, index) in svcTimeSupportModeList"
            :key="index"
            :label="item.dataValue"
            :value="`${item.dataKey}`"
          >
          </Option>
        </Select>
        <span v-else class="base-text-color">{{ formCustom.svcTimeSupportModeText || '未知' }}</span>
        <div class="error-tip ellipsis" :title="errorData.svcTimeSupportMode">
          {{ errorData.svcTimeSupportMode }}
        </div>
      </FormItem>
    </Form>
  </div>
</template>
<script>
export default {
  props: {
    defaultForm: {
      type: Object,
    },
    modalAction: {
      type: String,
    },
    errorData: {
      type: Object,
    },
    allDicData: {
      type: Object,
    },
    // 校验必填字段
    isRequired: {
      type: Function,
    },
  },
  data() {
    return {
      shareList: [
        // { label: "不共享", value: 0 },
        // { label: "共享", value: 1 },
      ],
      safetyList: [
        // { label: "不采用", value: 0 },
        // { label: "S/MIME签名方式", value: 2 },
        // { label: "S/MIME加密签名同时采用方式", value: 3 },
        // { label: "数字摘要方式", value: 4 },
      ],
      registerList: [
        // { label: "符合IETF RFC 3261标准的认证注册模式", value: 1 },
        // { label: "基于口令的双向认证注册模式", value: 2 },
        // { label: "基于证书的双向认证注册模式", value: 3 },
      ],
      certifiableList: [
        // { label: "无效", value: 0 },
        // { label: "有效", value: 1 },
      ],
      secrecyList: [
        // { label: "不涉密", value: 0 },
        // { label: "涉密", value: 1 },
      ],
      svcSpaceSupportModeList: [
        // { label: "不支持", value: 0 },
        // { label: "1级增强", value: 1 },
        // { label: "2级增强", value: 2 },
        // { label: "3级增强", value: 3 },
      ],
      svcTimeSupportModeList: [
        // { label: "不支持", value: 0 },
        // { label: "1级增强", value: 1 },
        // { label: "2级增强", value: 2 },
        // { label: "3级增强", value: 3 },
      ],
      formCustom: {
        isShare: '',
        isShareText: '',
        safetyWay: '',
        safetyWayText: '',
        registerWay: '',
        registerWayText: '',
        certNum: '',
        certifiable: '',
        certifiableText: '',
        errCode: null,
        certEndTime: '',
        secrecy: '',
        secrecyText: '',
        svcSpaceSupportMode: '',
        svcSpaceSupportModeText: '',
        svcTimeSupportMode: '',
        svcTimeSupportModeText: '',
      },
      formError: {},
    };
  },
  created() {},
  methods: {
    validate() {
      // this.$refs.form.validate((valid)=>{
      //   if (valid) {

      //   } else {

      //   }
      // })
      this.$emit('putData', this.formCustom);
      return true;
    },
    resetFields() {
      this.$refs.form.resetFields();
      this.formCustom = {
        isShare: '',
        isShareText: '',
        safetyWay: '',
        safetyWayText: '',
        registerWay: '',
        registerWayText: '',
        certNum: '',
        certifiable: '',
        certifiableText: '',
        errCode: null,
        certEndTime: '',
        secrecy: '',
        secrecyText: '',
        svcSpaceSupportMode: '',
        svcSpaceSupportModeText: '',
        svcTimeSupportMode: '',
        svcTimeSupportModeText: '',
      };
    },
  },
  watch: {
    defaultForm: {
      handler(val) {
        let length = Object.keys(val).length;
        this.$nextTick(() => {
          this.resetFields();
          if (length > 0) {
            Object.keys(val).forEach((key) => {
              if (this.formCustom.hasOwnProperty(key)) {
                this.formCustom[key] = val[key];
              }
            });
          }
        });
      },
      immediate: true,
    },
    errorData: {
      handler(val) {
        let length = Object.keys(val).length;
        if (length > 0) {
          Object.keys(val).forEach((key) => {
            if (this.formCustom.hasOwnProperty(key)) {
              this.formError[key] = val[key];
            }
          });
        } else {
          this.formError = {};
        }
      },
      immediate: true,
      deep: true,
    },
    allDicData: {
      handler(val) {
        for (let i in val) {
          if (this.hasOwnProperty(i)) {
            this[i] = val[i];
          }
        }
      },
      immediate: true,
      deep: true,
    },
  },
  computed: {
    isView() {
      return this.modalAction === 'view';
    },
  },
};
</script>
<style lang="less" scoped>
@leftMargin: 190px;
@inputWidth: 200px;
.safety-property {
  height: 586px;
  margin-top: 20px;
  @{_deep}.ivu-form-item {
    width: 100%;
    margin-right: 0;
  }
  .left-item {
    width: 50%;
    @{_deep} .ivu-form-item-error-tip {
      margin-left: @leftMargin;
    }
    @{_deep}.ivu-form-item-label {
      width: 190px;
    }
  }
  .error-tip {
    position: absolute;
    top: 100%;
    left: 0;
    line-height: 1;
    padding-top: 5px;
    color: #ed4014;
    margin-left: @leftMargin;
    width: @inputWidth;
  }
}
</style>
