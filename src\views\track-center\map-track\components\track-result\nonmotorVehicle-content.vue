<!--
    * @FileDescription: 非机动车
    * @Author: H
    * @Date: 2023/5/12
 * @LastEditors: liyuhang1 <EMAIL>
 * @LastEditTime: 2024-11-15 15:51:07
-->
<template>
  <div class="content-warpper">
    <div class="btn-list">
      <Button @click="handleSort" size="small">
        <Icon type="md-arrow-round-down" v-if="!timeUpDown" />
        <Icon type="md-arrow-round-up" v-else />
        时间排序
      </Button>
      <Button
        v-show="updateLayerId"
        size="small"
        @click="handleExport"
        :disabled="!updateLayerId"
        v-if="dataList.length"
      >
        <ui-icon
          type="daoru"
          :color="updateLayerId ? '#2C86F8' : 'rgba(0, 0, 0, 0.35)'"
        ></ui-icon>
        导出
      </Button>
    </div>
    <div class="warpper-box">
      <div v-for="(item, index) in dataList" :key="index" class="warpper-ul">
        <div
          class="box-item"
          :class="{ active: currentClickIndex == index }"
          @click="chooseMapItem($event, index)"
        >
          <div class="header">
            <div class="header-left">
              <span
                class="serialNumber"
                :class="{ activeNumber: currentClickIndex == index }"
              >
                <span>{{ index + 1 }}</span>
              </span>
            </div>
            <div class="header-name" v-show-tips>
              {{ item.recordId || "--" }}
            </div>
            <ui-icon
              v-if="canWrite"
              class="ml-5 mr-5"
              type="shanchu"
              @click.native.stop="deleteItem(item, index)"
            ></ui-icon>
          </div>
          <div class="content">
            <div class="content-left">
              <img v-lazy="item.traitImg" alt="" />
            </div>
            <div class="content-right">
              <div class="ellipsis">
                <ui-icon type="time" :size="14"></ui-icon>
                <span>{{ item.absTime || "--" }}</span>
              </div>
              <span class="ellipsis">
                <ui-icon type="location" :size="14"></ui-icon>
                <span>{{ item.deviceName || "--" }}</span>
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <ui-empty v-if="!dataList.length" />
  </div>
</template>

<script>
import { exportExcel } from "@/api/operationsOnTheMap";
export default {
  props: {
    // 当前点击顺序
    currentClickIndex: {
      type: Number,
      default: -1,
    },
    orderType: {
      type: String,
      default: "",
    },
    updateLayerId: {
      type: [String, Number],
      default: "",
    },
    dataList: {
      type: Array,
      default: () => [],
    },
    // 写权限，被分享的只能读
    canWrite: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      timeUpDown: false,
    };
  },
  watch: {
    // currentClickIndex: {
    //     handler (newVal){
    //         if(newVal > -1) {
    //             let list =  document.querySelectorAll('.box-item');
    //             list[newVal].scrollIntoView(false)
    //         }
    //     },
    // },
    orderType: {
      handler(newVal) {
        this.timeUpDown = newVal == "desc" ? false : true;
      },
    },
  },
  methods: {
    chooseMapItem($event, index) {
      $event.stopPropagation();
      this.$emit("chooseMapItem", index);
    },
    // 排序
    handleSort(val) {
      this.timeUpDown = !this.timeUpDown;
      this.$emit("changeOrder", this.timeUpDown ? "asc" : "desc");
    },
    handleExport() {
      exportExcel({
        id: this.updateLayerId,
        excelType: "nonmotorVehicle",
      }).then((res) => {
        if (res.data) {
          let aLink = document.createElement("a");
          aLink.href = res.data;
          aLink.click();
        }
      });
    },
    deleteItem(item, index) {
      this.$emit("deleteItem", item, index);
    },
  },
};
</script>

<style lang="less" scoped>
@import "style/index";
</style>
