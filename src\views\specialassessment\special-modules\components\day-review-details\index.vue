<template>
  <ui-modal v-model="visible" :title="modelTitle" :styles="styles" @onCancel="hide" footer-hide>
    <div class="day-detail-content auto-fill">
      <component
        v-if="visible"
        :is="componentName"
        :table-columns="tableColumns"
        :form-item-data="formItems"
        :table-data="tableData"
        :loading="loading"
        :active-index-item="activeIndexItem"
        :detail-model-width="detailModelWidth"
        :detail-model-height="detailModelHeight"
        :is-get-detail-byapi="isGetDetailByapi"
        :search-data="searchData"
        @startSearch="startSearch"
        @sortChange="sortChange"
      >
        <Button slot="btnslot" type="default" class="button-export" @click="onExport" :loading="exportLoading">
          <i class="icon-font icon-daochu font-white mr-xs f-14"></i>
          <span class="ml-xs">导出</span>
        </Button>
        <ui-page
          slot="page"
          class="page menu-content-background"
          :page-data="pageData"
          @changePage="handlePage"
          @changePageSize="handlePageSize"
        >
        </ui-page>
      </component>
    </div>
    <!-- 导出  -->
    <export-data
      ref="exportModule"
      :exportLoading="exportLoading"
      :tree-title="treeTitle"
      :all-tag-list="allTagList"
      :default-checked-keys="getDefaultCheckedKeys"
      @handleExport="handleExport"
    >
    </export-data>
  </ui-modal>
</template>
<script>
import downLoadTips from '@/mixins/download-tips';
import { menuConfig } from '@/views/specialassessment/utils/menuConfig.js';
import { formItemData, initFormData, getTableColumns } from './utils/dayReviewDetailsColumns.js';
import evaluationoverview from '@/config/api/evaluationoverview';
import { mapGetters } from 'vuex';

export default {
  name: 'day-review-details',
  mixins: [downLoadTips],
  props: {
    value: {},
    indexData: {
      type: Object,
      default: () => {},
    },
    dataDimensionEnum: {
      type: String,
      default: '',
    },
    rowData: {
      type: Object,
      default: () => {},
    },
    allTagList: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      visible: false,
      styles: {
        width: '9.45rem',
      },
      modelTitle: '',
      componentName: null,
      dayDetailConfig: {},
      tableColumns: [],
      tableData: [],
      formItems: [],
      searchData: {},
      loading: false,
      activeIndexItem: {},
      detailModelWidth: '',
      detailModelHeight: '',
      isGetDetailByapi: false,
      exportLoading: false,
      pageData: {
        pageNum: 1,
        pageSize: 20,
        totalCount: 0,
      },
      sortData: {
        sortField: '', // 排序字段
        sort: '', // 排序方式: ASC("升序") | DESC("降序")
      },
      treeTitle: '',
    };
  },
  computed: {
    ...mapGetters({
      propertySearchSxjgnlx: 'algorithm/sxjgnlx_receive',
    }),
    getDefaultCheckedKeys() {
      let { statisticsCode } = this.$route.query;
      return statisticsCode === '3' && this.rowData.code !== '-1' ? [Number(this.rowData.code)] : [this.rowData.code];
    },
  },
  methods: {
    async getTableList() {
      try {
        this.loading = true;
        let { dateType, statisticsCode } = this.$route.query;
        let { examineTime, batchId, indexId, code } = this.rowData;
        let params = {
          nodeDetails: this.indexData.details,
          examineTime: examineTime,
          type: dateType,
          dataDimensionEnum: this.dataDimensionEnum,
          pageNumber: this.pageData.pageNum,
          pageSize: this.pageData.pageSize,
          paramForm: {
            batchId: batchId,
            indexId: indexId,
            orgRegionCode: statisticsCode === '3' ? null : code,
            customParameters: {
              ...this.searchData,
              tagIds: statisticsCode === '3' && code !== '-1' ? [Number(code)] : null,
            },
          },
          statisticalModel: statisticsCode ? Number(statisticsCode) : null,
          displayType:
            statisticsCode === '1' ? 'ORG' : statisticsCode === '2' ? 'REGION' : statisticsCode === '3' ? 'TAG' : '',
        };
        if (this.sortData.sortField) {
          params.paramForm.sortField = this.sortData.sortField;
          params.paramForm.sort = this.sortData.sort;
        }
        const {
          data: { data },
        } = await this.$http.post(evaluationoverview.getSpecialDetailByDay, params);
        this.tableData = data.entities ? data.entities : [];
        this.pageData.totalCount = data.total;
      } catch (e) {
        console.log(e);
      } finally {
        this.loading = false;
      }
    },
    sortChange({ key, order }) {
      if (order === 'normal') {
        this.sortData = {};
      } else {
        this.sortData = {
          sortField: key,
          sort: order.toUpperCase(),
        };
      }
      this.getTableList();
    },
    handlePage(val) {
      this.pageData.pageNum = val;
      this.getTableList();
    },
    handlePageSize(val) {
      this.pageData.pageNum = 1;
      this.pageData.pageSize = val;
      this.getTableList();
    },
    startSearch(data) {
      this.pageData.pageNum = 1;
      this.searchData = data;
      this.getTableList();
    },
    onExport() {
      let { statisticsCode } = this.$route.query;
      this.treeTitle =
        statisticsCode === '1'
          ? '组织机构'
          : statisticsCode === '2'
            ? '行政区划'
            : statisticsCode === '3'
              ? '设备标签'
              : '';
      this.$refs.exportModule.init(this.rowData.batchId);
    },
    async handleExport(val) {
      this.exportLoading = true;
      let { dateType, statisticsCode } = this.$route.query;
      let { examineTime, batchId, indexId, code } = this.rowData;
      let params = {
        nodeDetails: this.indexData.details,
        examineTime: examineTime,
        type: dateType,
        // sbgnlxEnum: this.sbgnlxEnum,
        dataDimensionEnum: this.dataDimensionEnum,
        paramForm: {
          batchId: batchId,
          indexId: indexId,
          orgRegionCode: statisticsCode === '3' ? null : code,
          customParameters: {
            ...this.searchData,
            tagIds: statisticsCode === '3' && code !== '-1' ? [Number(code)] : null,
          },
          ...val,
        },
        statisticalModel: statisticsCode ? Number(statisticsCode) : null,
        displayType:
          statisticsCode === '1' ? 'ORG' : statisticsCode === '2' ? 'REGION' : statisticsCode === '3' ? 'TAG' : '',
      };
      if (this.sortData.sortField) {
        params.paramForm.sortField = this.sortData.sortField;
        params.paramForm.sort = this.sortData.sort;
      }
      try {
        this.$_openDownloadTip();
        const res = await this.$http.post(evaluationoverview.exportSecondStatInfoList, params);
        await this.$util.common.transformBlob(res.data.data);
      } catch (err) {
        console.log(err);
      } finally {
        this.exportLoading = false;
      }
    },
    async getUnqualifiedInfoList() {
      try {
        const queryParams = this.$route.query;
        const params = {
          nodeDetails: this.indexData.details,
          examineTime: this.rowData.examineTime,
          type: queryParams.dateType,
          // sbgnlxEnum: this.sbgnlxEnum,
          dataDimensionEnum: this.dataDimensionEnum,
          paramForm: {
            batchId: this.rowData.batchId,
            orgRegionCode: this.rowData.civilCode,
            indexId: this.rowData.indexId,
          },
        };
        const {
          data: { data },
        } = await this.$http.post(evaluationoverview.getUnqualifiedInfo, params);
        if (!data) return false;
        const errorCodeList = data.map((item) => {
          return {
            label: item.reason,
            value: item.code,
          };
        });
        this.formItems = this.formItems.map((item) => {
          if (item.key === 'errorCodes') {
            item.options = errorCodeList || [];
          }
          return item;
        });
      } catch (e) {
        console.log(e);
      }
    },
    // 设置设备重点类型默认值
    getIsImportant(val) {
      let isImportant = '';
      switch (val) {
        case 'DEVICE_NORMAL':
          isImportant = '0';
          break;
        case 'DEVICE_IMPORTANT':
          isImportant = '1';
          break;
        default:
          isImportant = '';
          break;
      }
      this.$set(this.searchData, 'isImportant', isImportant);
    },
    hide() {
      this.searchData = {};
    },
  },
  watch: {
    visible(val) {
      this.$emit('input', val);
    },
    value(val) {
      if (!val) return false;
      this.visible = val;
      this.pageData = {
        pageNum: 1,
        pageSize: 20,
        totalCount: 0,
      };
      const paramList = this.$route.query;
      this.formItems = formItemData[paramList.indexType];

      if (this.propertySearchSxjgnlx.length && this.formItems.some((item) => item.key === 'sbgnlx')) {
        // 处理 摄像机功能类型
        let options = [];
        this.propertySearchSxjgnlx.forEach((item) => {
          options.push({ value: item.dataKey, label: item.dataValue });
        });
        this.formItems = this.formItems.map((item) => {
          if (item.key === 'sbgnlx') {
            item.options = options;
          }
          return item;
        });
      }

      this.searchData = initFormData[paramList.indexType];
      const menuItem = menuConfig.find((item) => item.type === paramList.indexType);
      this.detailModelWidth = menuItem && menuItem.width;
      this.detailModelHeight = menuItem && menuItem.height;
      this.isGetDetailByapi = menuItem.isGetDetailByapi;
      const columns = getTableColumns();
      this.tableColumns = columns[menuItem.type];
      this.componentName = menuItem.dayDetailComponent;
      this.activeIndexItem = {
        indexType: this.rowData.jobType,
        ...this.rowData,
      };
      this.modelTitle = this.activeIndexItem.name + '-' + paramList.indexName + '检测详情';
      this.searchData.qualified = this.rowData.qualVal;
      this.sortData = {
        sortField: 'deviceId',
        sort: 'ASC', // 默认设备编码升序
      };
      this.getIsImportant(this.dataDimensionEnum);
      this.getUnqualifiedInfoList();
      this.getTableList();
    },
  },
  components: {
    OfflineStatistics: require('./components/offline-statistics.vue').default,
    AssetQuality: require('./components/asset-quality.vue').default,
    VideoOnline: require('./components/video-online.vue').default,
    VideoOsd: require('./components/video-osd.vue').default,
    VideoPass: require('./components/video-pass.vue').default,
    ImageOnline: require('./components/image-online.vue').default,
    ConnectInternet: require('./components/connect-internet.vue').default,
    ImageQuality: require('./components/image-quality.vue').default,
    ExportData: require('../export-data.vue').default,
    VideoCodeStandard: require('./components/video-code-standard.vue').default,
  },
};
</script>

<style lang="less" scoped>
.day-detail-content {
  width: 100%;
  height: 800px;
  .icon-daochu {
    color: var(--color-primary);
  }
}
</style>
