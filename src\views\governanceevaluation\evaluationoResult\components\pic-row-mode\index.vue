<template>
  <div class="pic-mode auto-fill">
    <div class="pic-mode-list">
      <pic-mode-card
        v-for="(item, index) in cardList"
        :key="`${item.id}-${index}`"
        :class="['pic-mode-item', 'mb-sm', 'mr-sm']"
        :img-index="index"
        :small-img-key="smallImgKey"
        :card-data="item"
        @handleLookScence="handleLookScence"
        @artificialReview="(cardData) => $emit('artificialReview', cardData)"
        @algorithmsReview="(cardData) => $emit('algorithmsReview', cardData)"
      >
        <template #picMessage>
          <slot name="picMessage" :card-data="item"></slot>
        </template>
        <template #cardInfo>
          <slot name="cardInfo" :card-data="item"> </slot>
        </template>
        <template #default>
          <slot :card-data="item"></slot>
        </template>
      </pic-mode-card>
    </div>
    <look-scene v-model="visibleScence" :img-list="imgList" :view-index="viewIndex"></look-scene>
  </div>
</template>

<script>
export default {
  name: 'pic-mode',
  props: {
    // 图片的字段
    imgKey: {
      type: String,
      default: 'imageUrl',
    },
    //小图
    smallImgKey: {
      type: String,
      default: 'imageUrl',
    },
    // 图片数据
    cardList: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      visibleScence: false,
      imgList: [],
      viewIndex: 0,
    };
  },
  methods: {
    handleLookScence(index) {
      this.viewIndex = index;
      this.visibleScence = true;
    },
  },
  watch: {
    cardList: {
      handler(val) {
        if (!val.length) return false;
        this.imgList = val.map((item) => {
          return item[this.imgKey];
        });
      },
      immediate: true,
    },
  },
  components: {
    PicModeCard: require('./pic-mode-card.vue').default,
  },
};
</script>

<style lang="less" scoped>
.pic-mode {
  &-list {
    flex: 1;
    display: flex;
    align-content: flex-start;
    flex-wrap: wrap;
    overflow-y: auto;
  }
  &-item {
    width: calc(calc(100% - 70px) / 4);
    height: fit-content;
  }
  .cardInfo {
    width: 100%;
  }
}
</style>
