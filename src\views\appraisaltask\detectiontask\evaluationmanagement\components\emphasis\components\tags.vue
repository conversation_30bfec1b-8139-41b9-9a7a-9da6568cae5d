<template>
  <ul class="tag">
    <li :class="{ active: curTag == index }" v-for="(item, index) in list" :key="index" @click="tagChange(index)">
      {{ item }}
    </li>
  </ul>
</template>
<script>
export default {
  name: 'tasktracking',
  props: {
    list: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      curTag: 0,
    };
  },
  created() {},
  methods: {
    tagChange(index) {
      this.curTag = index;
      this.$emit('tagChange', index, this.list[index]);
    },
  },
  watch: {},
  components: {},
};
</script>
<style lang="less" scoped>
.tag {
  // width: 500px;
  // overflow: hidden;
  li {
    float: left;
    text-align: center;
    color: #fff;
    // border: 1px solid #1b82d2;
    padding: 0px 6px;
    margin-right: -1px;
    border-radius: 2px;
    cursor: pointer;
  }
  .active {
    color: var(--color-primary);
    cursor: default;
  }
}
</style>
