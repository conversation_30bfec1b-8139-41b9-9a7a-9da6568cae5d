<template>
  <ui-modal title="检测不合格原因" v-model="visible" :styles="styles" footer-hide>
    <ui-table
      class="ui-table"
      :table-columns="unQuantifyTableColumns"
      :table-data="tableData"
      :minus-height="minusTable"
      :loading="loading"
    >
    </ui-table>
  </ui-modal>
</template>
<style lang="less" scoped></style>
<script>
export default {
  props: {
    value: {},
    unQuantifyTableColumns: {
      default: () => [],
    },
    tableData: {
      default: () => [],
    },
    loading: {
      default: false,
    },
  },
  data() {
    return {
      visible: false,
      styles: {
        width: '85%',
      },
      tableColumns: [
        { type: 'index', width: 70, title: '序号' },
        { title: '不合格原因', key: 'errorMessage' },
        { title: '不合格字段', key: 'propertyName' },
        { title: '检测规则', key: 'componentName' },
        { title: '实际结果', key: 'propertyValue' },
      ],
      minusTable: 480,
    };
  },
  created() {},
  mounted() {},
  methods: {
    changePage() {},
    changePageSize() {},
  },
  watch: {
    visible(val) {
      this.$emit('input', val);
    },
    value(val) {
      this.visible = val;
    },
  },
  components: {
    UiTable: require('@/components/ui-table.vue').default,
  },
};
</script>
