import home from '@/config/api/home';
import device from '@/assets/img/base-home/statistics/device.png';
import video from '@/assets/img/base-home/statistics/video.png';
import face from '@/assets/img/base-home/statistics/face.png';
import vehicle from '@/assets/img/base-home/statistics/vehicle.png';
import { mapGetters } from 'vuex';
import evaluationreport from '@/config/api/evaluationreport';
import governanceevaluation from '@/config/api/governanceevaluation';
import bottomJson from '@/views/home/<USER>/json/bottom-lottie';
import lottie from 'lottie-web';
import evaluationoResultMixin from '@/views/governanceevaluation/evaluationoResult/util/Mixins/evaluationoResultMixin.js';
export default {
  name: 'BaseHome',
  props: {},
  mixins: [evaluationoResultMixin],
  data() {
    return {
      queryAccessDataCount: {},
      isActivated: false,
      queryAccessData: [
        {
          label: '设备总量',
          key: 'deviceTotalAmount',
          img: device,
          icon: 'icon-shebeizongliang1',
          color: '#25F3F1',
          deviceTotalAmount: 0,
        },
        {
          label: '视频监控',
          key: 'videoSurveillanceAmount',
          img: video,
          icon: 'icon-shitujichushuju',
          vehicleViewAmount: 0,
          color: '#BF89F7',
          tooltipShow: true,
        },
        {
          label: '人脸卡口',
          key: 'faceSwanAmount',
          img: face,
          icon: 'icon-renliankakou',
          color: '#F5AF55',
          faceViewAmount: 0,
        },
        {
          label: '车辆卡口',
          key: 'vehicleBayonetAmount',
          img: vehicle,
          icon: 'icon-cheliangkakou',
          color: '#EB6B52',
          vehicleViewAmount: 0,
        },
      ],
      navigationPageShow: false,
      homePageConfig: {},
      navigationInfo: {},
      taskConfig: {},
      loading: false,
      evaluationIndexResultData: [], //评测总览 列表

      componentName: null,
      componentLevel: 0,
      activeComponent: 'taskToOverview',
      activeTabsName: '评测详情',
      activeTabsQuery: {
        displayType: 'REGION',
        indexId: null,
        code: null,
        batchId: null,
        access: 'EXAM_RESULT',
        uuid: null,
      },
      staticTooltipdata: {
        regionName: '视频监控',
        examineIndexModuleList: [
          {
            label: '一类点',
            value: 0,
          },
          {
            label: '二类点',
            value: 0,
          },
          {
            label: '三类点',
            value: 0,
          },
          {
            label: '内部监控',
            value: 0,
          },
          {
            label: '其他点位',
            value: 0,
          },
        ],
      },
    };
  },
  computed: {
    ...mapGetters({
      getHomeConfig: 'home/getHomeConfig',
      getFullscreen: 'home/getFullscreen',
      cacheRouterList: 'tabs/getCacheRouterList',
    }),
    fullScreen() {
      return this.getFullscreen ? 'full-screen-container' : '';
    },
  },
  filter: {},
  watch: {
    // fullScreen(val) {
    //   val && this.$refs.noticeRef.initFunc();
    // },
    navigationPageShow: {
      async handler(val) {
        if (!val) {
          await this.$nextTick();
          this.lottieInit();
        }
      },
      immediate: true,
    },
  },
  deactivated() {
    this.isActivated = true;
    this.unwatch && this.unwatch();
  },
  activated() {
    this.unwatch = this.$watch(
      '$route',
      () => {
        this.getParams();
      },
      { immediate: true },
    );
    this.isActivated = false;
    this.$nextTick(() => {
      this.lottieInit();
    });
  },
  beforeDestroy() {
    this.unwatch && this.unwatch();
  },
  async created() {
    this.loading = true;
    await this.viewByParamKey();
    await this.getOptionalBatchIdsByTaskSchemeId();
    await this.getOptionalResultsByTaskType();
    await this.getPageJiangsuEvaluationIndexResult();
    await this.queryIndexDeviceOverview();
  },
  async mounted() {
    await this.$nextTick();
    this.$refs.noticeRef.initFunc();
  },
  methods: {
    async onChangeFullScreen() {
      await this.$nextTick();
      this.$refs.noticeRef.initFunc();
    },
    getParams() {
      // 根据路由地址参数来判断加载组件或者路由本身
      this.selectModule(this.$route.query.componentName);
    },
    selectModule(name) {
      if (this.$route.query.componentName) {
        let components = name.split('-');
        this.componentName = components[this.componentLevel];
      } else {
        this.componentName = null;
      }
    },
    lottieInit() {
      // 初始化渲染数仓中间gift图
      const params = {
        container: document.getElementById('bottom-lottie'),
        renderer: 'svg',
        loop: true,
        autoplay: true,
        animationData: bottomJson,
      };
      lottie.loadAnimation(params);
    },
    // 统一跳转 方法
    jump(originData) {
      let { taskSchemeId } = this.homePageConfig;
      let { orgCode, regionCode, indexId, indexType, batchId } = originData;
      //isExistIndex 是否为新指标
      if (this.isExistIndex(indexType)) {
        this.$router.push({
          name: 'evaluationoResult',
          query: {
            orgCode: orgCode,
            regionCode: regionCode,
            statisticType: 'REGION',
            taskSchemeId: taskSchemeId,
            indexId: `${indexId}`,
            indexType: indexType,
            batchId: batchId,
          },
        });
      } else {
        this.activeComponent = 'taskToOverview';
        this.activeTabsName = '评测详情';
        this.activeTabsQuery = {
          displayType: 'REGION',
          indexId: indexId,
          code: regionCode,
          batchId: batchId,
          access: 'TASK_RESULT',
          uuid: batchId,
        };
        this.$nextTick(() => {
          this.$refs.createTabsRef.create();
        });
      }
    },
    /**
     *  地图跳转评测总览
     */
    onJumpMap(originData, params) {
      let { civilCode } = params;
      let { taskSchemeId } = this.homePageConfig;
      let query = {
        regionCode: `${civilCode}`,
        statisticType: 'REGION',
        taskSchemeId: taskSchemeId,
      };
      this.$router.push({
        name: 'evaluationoResult',
        query: query,
      });
    },
    async queryIndexDeviceOverview() {
      try {
        let {
          data: { data },
        } = await this.$http.get(home.queryIndexDeviceOverview);
        this.queryAccessDataCount = data || {};
        this.queryAccessData = this.queryAccessData.map((item) => {
          item[item.key] = data[item.key];
          if (item.key === 'deviceTotalAmount') {
            item['deviceTotalAmount'] = (data['focusAmount'] || 0) + (data['commonAmount'] || 0);
          }
          return item;
        });
        this.staticTooltipdata = {
          regionName: '视频监控',
          examineIndexModuleList: [
            {
              label: '一类点',
              value: data?.videoDataAmount?.oneDwAmount ?? 0,
            },
            {
              label: '二类点',
              value: data?.videoDataAmount?.twoDwAmount ?? 0,
            },
            {
              label: '三类点',
              value: data?.videoDataAmount?.threeDwAmount ?? 0,
            },
            {
              label: '内部监控',
              value: data?.videoDataAmount?.innerDwAmount ?? 0,
            },
            {
              label: '其他点位',
              value: data?.videoDataAmount?.otherDwAmount ?? 0,
            },
          ],
        };
      } catch (err) {
        console.log(err);
      }
    },
    noticeMore() {
      this.$router.push({
        name: 'noticeannouncements',
      });
    },
    changeHome() {
      this.navigationPageShow = true;
    },
    // 获取首页功能配置
    async viewByParamKey() {
      this.homePageConfig = await this.configFunc();
    },
    async configFunc(key = 'HOME_PAGE_CONFIG') {
      try {
        let params = { key: key };
        let {
          data: { data },
        } = await this.$http.get(home.viewByParamKey, { params });
        return JSON.parse(data.paramValue || '{}');
      } catch (e) {
        console.log(e);
      }
    },
    async getOptionalBatchIdsByTaskSchemeId() {
      try {
        let { taskSchemeId } = this.homePageConfig;
        if (!taskSchemeId) {
          this.loading = false;
          return;
        }
        let {
          data: { data },
        } = await this.$http.post(evaluationreport.getOptionalBatchIdsByTaskSchemeIdV2, {
          taskSchemeId: taskSchemeId,
          showResult: 1,
        });
        this.taskConfig = data || {};
      } catch (err) {
        console.log(err);
      }
    },
    async getOptionalResultsByTaskType() {
      // try {
      //   let { regionCode, taskSchemeId } = this.homePageConfig;
      //   const {
      //     data: { data },
      //   } = await this.$http.get(detectionResult.getNavigationInfo, {
      //     params: {
      //       taskSchemeId: taskSchemeId,
      //     },
      //   });
      //   this.navigationInfo = data;
      // } catch (err) {
      //   console.log(err);
      // } finally {
      // }
    },
    async getPageJiangsuEvaluationIndexResult() {
      this.evaluationIndexResultData = [];
      let { regionCode } = this.homePageConfig;
      let { batchIds } = this.taskConfig;
      if (!batchIds) {
        this.loading = false;
        return;
      }
      let params = {
        regionCode,
        batchIds,
        displayType: 'REGION',
        pageNumber: 1,
        pageSize: 200,
      };
      try {
        let {
          data: { data },
        } = await this.$http.post(governanceevaluation.getPageJiangsuEvaluationIndexResult, params);
        this.evaluationIndexResultData = data.entities || [];
      } catch (err) {
        console.log('err', err);
      } finally {
        this.loading = false;
      }
    },
  },
  components: {
    taskToOverview: require('@/views/governanceevaluation/evaluationoverview/overview-evaluation/index.vue').default,
    CreateTabs: require('@/components/create-tabs/create-tabs.vue').default,
    determinantAttribute: require('@/views/home/<USER>/determinant-attribute.vue').default, //左下echarts
    propertyDistributio: require('@/views/home/<USER>/property-distributio.vue').default, //左中（资产分布）
    MapEcharts: require('@/views/home/<USER>/map-echarts.vue').default,
    MapDom: require('@/views/home/<USER>/map-dom.vue').default,
    StaticsNum: require('@/views/home/<USER>/statics-num.vue').default,
    StatisticsTop: require('@/views/home/<USER>/statistics-top.vue').default,
    StatisticsTop2: require('@/views/home/<USER>/statistics-top.vue').default,
    FullScreen: require('@/views/home/<USER>/full-screen').default,
    TopTitle: require('@/views/home/<USER>/top-title.vue').default,
    TopTitle2: require('@/views/home/<USER>/top-title.vue').default,
    Notice: require('@/views/home/<USER>/notice').default,
    NoticeAnnouncements: require('@/views/notification/noticeannouncements/index.vue').default,
    NavigationPage: require('@/views/navigation-page/index.vue').default,
  },
};
