<template>
  <div class="knowledgemanage d_flex height-full">
    <section class="search-detail-content">
      <ui-search-tree
        ref="uiTree"
        class="customize-tree"
        placeholder="请输入知识库目录名称"
        node-key="id"
        :no-search="true"
        :show-checkbox="false"
        :current-node-key="activeCatalog.id"
        :tree-data="getCatalogTreeData"
        :default-props="defaultProps"
        :default-checked-keys="[]"
        expandAll
        checkStrictly
        @selectTree="selectTree"
      >
        <!-- <template #label="{ node, data }">
          <div class="custom-tree-node">
            <span>{{ node.label }}</span>
            <Poptip title=""
                    transfer
                    content="content"
                    placement="left-start"
                    v-model="node.poptipshow">
              <p @click="openAction(node, data)">Right Top</p>
              <template #content>
                <p><Button>Info</Button></p>
                <p><Button>Info</Button></p>
                <p><Button>Info</Button></p>
              </template>
            </Poptip>
          </div>
        </template> -->
      </ui-search-tree>
    </section>
    <table-module :active-catalog="activeCatalog"></table-module>
  </div>
</template>

<script>
import { mapActions, mapGetters } from 'vuex';
export default {
  name: 'searchDetail',
  data() {
    return {
      activeNode: null,
      defaultProps: {
        id: 'id',
        label: 'name',
        children: 'children',
      },
      activeCatalog: {
        id: null,
      },
    };
  },
  async created() {
    await this.setCatalogTreeData();
  },
  mounted() {},
  methods: {
    ...mapActions({
      setCatalogTreeData: 'knowledgebase/setCatalogTreeData',
    }),
    selectTree(data) {
      this.activeCatalog = data;
    },
    viewDetail() {
      this.detailShow = true;
    },
    openAction(node) {
      if (!!this.activeNode && node.id !== this.activeNode.id) {
        this.activeNode.poptipshow = false;
      }
      this.activeNode = node;
    },
  },
  watch: {},
  computed: {
    ...mapGetters({
      getCatalogTreeData: 'knowledgebase/getCatalogTreeData',
    }),
  },
  props: {},
  components: {
    UiSearchTree: require('@/components/ui-search-tree.vue').default,
    TableModule: require('./components/table-module.vue').default,
  },
};
</script>
<style lang="less" scoped>
[data-theme='light'],
[data-theme='deepBlue'] {
  .search-detail-content {
    border-right: 1px solid var(--border-color);
  }
}

.knowledgemanage {
  .search-detail-content {
    display: flex;
    .customize-tree {
      overflow-y: scroll;
      background: var(--bg-content);
      width: 250px;
      height: 100%;
      @{_deep}.custom-tree-node {
        display: flex;
        justify-content: space-between;
        width: 100%;

        .ivu-poptip-title {
          display: none;
        }
      }
      padding: 20px 10px;
    }
    .search-detail-content-list {
      padding: 0 10px 10px 10px;
      flex: 1;
      height: 77vh;
      overflow-y: scroll;
    }
  }
}
</style>
