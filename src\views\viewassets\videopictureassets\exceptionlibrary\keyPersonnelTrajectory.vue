<template>
  <div class="face auto-fill">
    <div class="header">
      <RadioGroup v-model="currentTabComponent" @on-change="handleChange" type="button" style="display: flex">
        <Radio :class="currentTabComponent == 'ImgPersonnelTrajectory' ? 'active' : ''" label="ImgPersonnelTrajectory"
          >图像模式</Radio
        >
        <Radio
          :class="currentTabComponent == 'GatherPersonnelTrajectory' ? 'active' : ''"
          label="GatherPersonnelTrajectory"
          >聚档模式</Radio
        >
      </RadioGroup>
    </div>
    <component
      class="auto-fill"
      :is="currentTabComponent"
      @lookScence="lookScence"
      @checkReason="checkReason"
    ></component>
    <look-scene v-model="visibleScence" :img-list="imgList" :view-index="viewIndex"></look-scene>
    <export-disqualify v-model="disqualifyShow" :disqualify-item="disqualifyItem"></export-disqualify>
  </div>
</template>
<script>
export default {
  props: {},
  data() {
    return {
      orgCode: null,
      statistic: {
        title: '重点人员轨迹数据',
        totalNum: 0,
        todayNum: 0,
      },
      visibleScence: false,
      imgList: [],
      viewIndex: 0,
      disqualifyShow: false,
      disqualifyItem: {},
      // activeBtn: "Iptdatasource",
      currentTabComponent: 'ImgPersonnelTrajectory',
      searchWidth: '0',
    };
  },
  created() {
    // this.initErrorList();
    // this.initStatistic();
    // this.search();
    // this.copySearchDataMx(this.searchData);
  },
  mounted() {},
  methods: {
    // pushCamera(list) {
    //   this.searchData.deviceIds = list.map((row) => {
    //     return row.deviceId;
    //   });
    // },
    // selectInfo(infoList) {
    //   this.searchData.reasons = infoList.map((row) => {
    //     return row.name;
    //   });
    //   this.search();
    // },
    // initSearchWidth() {
    //   this.$nextTick(() => {
    //     this.searchWidth = `width: calc(100% - ${this.$refs.statistic.clientWidth}px)`;
    //   });
    // },
    // async initStatistic() {
    //   try {
    //     let url = `${equipmentassets.queryDeviantImportantPersonLibCont}?orgCode=${this.orgCode}`;
    //     let res = await this.$http.get(url);
    //     this.statistic.totalNum = res.data.data.allCount;
    //     this.statistic.todayNum = res.data.data.curCount;
    //     this.initSearchWidth();
    //   } catch (err) {
    //     console.log(err);
    //   }
    // },
    // async initErrorList() {
    //   try {
    //     let res = await this.$http.get(
    //       equipmentassets.queryImportantPersonLibErrorMessage
    //     );
    //     this.errorList = res.data.data.map((row) => {
    //       return {
    //         name: row,
    //       };
    //     });
    //   } catch (err) {
    //     console.log(err);
    //   }
    // },
    // async init() {
    //   this.loading = true;
    //   this.tableData = [];
    //   try {
    //     let res = await this.$http.post(
    //       equipmentassets.queryDeviantImportantPersonLibListPage,
    //       this.searchData
    //     );
    //     this.pageData.totalCount = res.data.data.total;
    //     this.tableData = res.data.data.entities;
    //     this.imgList = this.tableData.map((row) => row.scenePath);
    //     this.loading = false;
    //   } catch (err) {
    //     console.log("err", err);
    //     this.loading = false;
    //   } finally {
    //     this.loading = false;
    //   }
    // },
    // search() {
    //   this.searchData.pageNumber = 1;
    //   this.init();
    // },
    // changePage(val) {
    //   this.searchData.pageNumber = val;
    //   this.init();
    // },
    // changePageSize(val) {
    //   this.searchData.pageSize = val;
    //   this.search();
    // },
    // clear() {
    //   this.$refs.uiSelectTabs.reset();
    //   this.resetSearchDataMx(this.searchData, this.search);
    // },
    lookScence(data) {
      this.imgList = data[0];
      this.viewIndex = data[1];
      this.visibleScence = true;
    },
    checkReason(item) {
      this.disqualifyShow = true;
      this.disqualifyItem = item;
    },
    handleChange() {},
  },
  watch: {},
  computed: {},
  components: {
    // UiTable: require("@/components/ui-table.vue").default,
    // UiSelectTabs: require("@/components/ui-select-tabs.vue").default,
    // SelectCamera: require("@/components/select-camera.vue").default,
    // uiImage: require("@/components/ui-image").default,
    ExportDisqualify: require('@/views/datagovernance/tasktracking/importantperson/popup/export-disqualify.vue')
      .default,
    UiGatherCard: require('@/components/ui-gather-card.vue').default,
    ImgPersonnelTrajectory: require('./img-personnel-trajectory.vue').default,
    GatherPersonnelTrajectory: require('./gather-personnel-trajectory.vue').default,
  },
};
</script>
<style lang="less" scoped>
// .search-module {
//   float: left;
//   padding: 20px 20px 10px 20px;
//   .keyword-input {
//     width: 300px;
//   }
// }
// .statistic {
//   float: right;
//   padding-right: 20px;
//   padding-top: 10px;
//   position: absolute;
//   top: 0;
//   right: 0;
//   .statistic-title {
//     color: #fff;
//   }
//   .statistic-total {
//     color: #05fef5;
//     font-size: 20px;
//     margin-bottom: 15px;
//   }
//   .statistic-today {
//     color: #f18a37;
//     font-size: 20px;
//   }
// }
// .table-module {
//   clear: both;
//   margin-left: 20px;
//   position: relative;
//   border-top: 1px solid #0b2348;
//   .group-unaggregated {
//     overflow: hidden;
//     position: absolute;
//     padding-top: 10px;
//     padding-right: 10px;
//     display: flex;
//     flex-wrap: wrap;
//     justify-content: space-between;
//     .group-item {
//       width: 177px;
//       height: 236px;
//       margin-bottom: 14px;
//       padding: 10px 0;
//       overflow: hidden;
//       position: relative;
//       background: #0f2f59;
//     }
//     .empty-item {
//       width: 177px;
//     }
//     .group-left {
//       .group-img {
//         position: relative;
//         width: 150px;
//         height: 150px;
//         cursor: pointer;
//         margin: 0 auto 10px;
//         img {
//           width: 100%;
//           height: 100%;
//         }
//         .shadow-box {
//           height: 28px;
//           width: 100%;
//           background: rgba(0, 0, 0, 0.3);
//           position: absolute;
//           bottom: 0;
//           display: none;
//           padding-left: 10px;
//           z-index: 10;
//           > i:hover {
//             color: var(--color-primary);
//           }
//         }
//         &:hover {
//           .shadow-box {
//             display: block;
//           }
//         }
//       }
//       .group-message {
//         margin: 0 auto;
//         padding: 0 15px;
//         color: #afbcd4;
//         .group-text {
//           width: 124px;
//         }
//         i {
//           font-size: 12px;
//         }
//       }
//     }
//   }
// }
.flex {
  display: flex;
  align-items: center;
  justify-content: center;
}
.header {
  padding: 10px 20px 0;
  // margin-bottom: 17px;
}
.active {
  color: #ffffff !important;
  background: var(--color-primary) !important;
}
@{_deep} .ivu-radio-group-button .ivu-radio-wrapper {
  .flex;
  width: 96px;
  height: 36px;
  background: transparent;
  color: #56789c;
  border: 1px solid #1b82d2;
}
@{_deep} .ivu-radio-group-button .ivu-radio-wrapper:before {
  background: var(--color-primary) !important;
}
</style>
