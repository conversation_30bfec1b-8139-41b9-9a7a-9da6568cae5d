.container {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  position: relative;

  .table-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;

    .data-above {
      .left-operater {
        display: flex;
      }

      .right-operater {
        font-size: 14px;
        color: rgba(0, 0, 0, 0.8);

        .flex-center {
          display: flex;
          align-items: center;
          cursor: pointer;
        }

        .rotate {
          transform: rotate(180deg);
        }
      }

      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 10px;

      button {
        margin-right: 10px;
      }
    }

    .table-content {
      flex: 1;
      overflow: scroll;

      .ui-table {
        height: 100%;
        overflow: unset;

        /deep/ .ivu-table-wrapper {
          overflow: unset;
        }
      }
    }
  }
}