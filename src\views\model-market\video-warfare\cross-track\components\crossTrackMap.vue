<template>
  <div class="map-box" ref="mapBox">
    <div
      id="cross-track-map"
      ref="cross-track-map"
      class="cross-track-map"
    ></div>
    <!-- 鼠标浮动 -->
    <mouse-title ref="mouseDom" :name="mouseName"></mouse-title>
  </div>
</template>

<script>
import axios from "axios";
import map from "@/api/map";
import { npMapMixins } from "@/mixins/npMap.js";
import mouseTitle from "@/components/map/mouse-title.vue";

let mapAnimationRadar = null;
let mouseInfoWindow = [];
let polylineMap = {};

export default {
  components: {
    mouseTitle,
  },
  data() {
    return {
      mouseName: "",
      centerPoint: "",
      scriptDomList:[]
    };
  },
  mixins: [npMapMixins],
  mounted() {
    this.loadNPMapScript();
  },
  methods: {
    async loadNPMapScript() {
      this.scriptDomList = []
      const scriptList = ["lib/threebox.js", "NPMapbox.js", "Npgis2.js"];
      for (const item of scriptList) {
        await this.loadExternalScript(
          BASE_URL + "/js/npgis/npgis_mapbox/" + item
        );
      }
      this.createMap().then(() => {
        this._mapGeometry = new MapPlatForm.Base.MapGeometry(this.Map);
        this.$emit("inited", this);
      });
    },
    loadExternalScript(url) {
      return new Promise((resolve, reject) => {
        const script = document.createElement("script");
        script.src = url; 
        script.type = "text/javascript"; 
        script.onload = function () {
          resolve();
        };
        document.body.appendChild(script); 
         this.scriptDomList.push(script)
      });
    },

    //初始化地图
    createMap() {
      return new Promise((resolve) => {
        //创建地图
        this.$nextTick(() => {
          axios.get(map.getMapBoxConfig).then((res) => {
            var mapJson = new MapPlatForm.Base.MapConfig().createMap(
              this.$refs["cross-track-map"],
              res.data
            );
            this.Map = mapJson.map;
            this.Map.load(() => {
              //此处这样处理是为了防止地图的一些样式未加载完成调用撒点时报错，故等所有都加载完再进行下一步操作
              resolve();
            });
          });
        });
      });
    },
    /**
     * 雷达效果
     */
    loadRadar(point, range) {
      if (JSON.stringify(point) === "{}") return;
      var threeModellayer = new NPMap.Layers.ThreeLayer("", {
        minZoom: 11,
        maxZoom: 18,
      });
      this.Map.addLayer(threeModellayer, false, "city_normal_poi");
      mapAnimationRadar && mapAnimationRadar.remove();
      mapAnimationRadar = new MapPlatForm.Base.AnimationRadar(
        this.Map,
        threeModellayer.getThreeBox(),
        { lineColor: "#2d87f9", angleColor: "#00ffff" }
      );
      let centerPoint = new NPMap.Geometry.Point(
        point.longitude,
        point.latitude
      );
      this.centerPoint = centerPoint;
      mapAnimationRadar.add(centerPoint, +range);

      this.Map.setCenter(
        new NPMap.Geometry.Point(point.longitude, point.latitude),
        this.Map.getMaxZoom() - 2
      );
    },
    // 判断 点和中心点的距离
    isDistance(lon, lat) {
      let point = new NPMap.Geometry.Point(lon, lat);
      console.log(point, this.centerPoint, "两个点位point， centerPoint");
      let distance = this.Map.getDistance(point, this.centerPoint);
      console.log("新点位距离", distance);
      return distance;
    },
    /**
     * 地图添加点位
     */
    addSortMarker(list, single, layer) {
      let mouseEvents = {
        click: (m) => {
          let data = m._data;
        },
        mouseover: (m) => {
          let data = m._data;
          this.showMouseDom({
            ext: { ...data, Lat: data.latitude, Lon: data.longitude },
          });
        },
        mouseout: (m) => {
          this.closeMouseInfoWindow();
        },
      };
      this.initMarkers(
        layer ? layer : "sortLapMarkerLayer",
        list,
        {
          ...mouseEvents,
        },
        (item) => {
          return {
            ...item,
            images: item.icon,
          };
        },
        () => {}
      );
    },
    // 鼠标浮动在资源图层图标上
    showMouseDom(marker) {
      let { Lat, Lon } = marker.ext;
      this.mouseName =
        marker.ext.placeName ||
        marker.ext.deviceName ||
        marker.ext.name ||
        marker.ext.address;
      let infoWindow = this.addInfoWindow(
        { lon: Lon, lat: Lat },
        this.$refs["mouseDom"].$el,
        {
          offset: [10, -24],
          iscommon: true, // 是否为普通的信息窗，普通的不带箭头,默认false
          enableCloseOnClick: false, // 默认false，是否开启改变地图（移动或缩放）关闭信息窗口（默认关闭）
          autoSize: true, // 默认true, 窗口大小是否自适应
          isAdaptation: false, // 默认true, 信息窗打开时，地图是否平滑移动，默认不平滑移动
        }
      );
      mouseInfoWindow.push(infoWindow);
    },
    closeMouseInfoWindow() {
      mouseInfoWindow.forEach((row) => {
        row.close();
      });
    },
    removeAllOverlays(layerName) {
      let layer = this.Map.getLayerByName(layerName);
      layer && layer.removeAllOverlays();
    },
    /**
     * 创建目标弹框
     */
    createFaceWindow(data, el) {
      if (!el) return;
      el.style.visibility = "visible";
      // this.isRtsp = false;
      this._infoWindow && this._infoWindow.close();
      this._infoWindow = this.addInfoWindow(
        { lon: data.longitude, lat: data.latitude },
        el,
        {
          offset: [0, -20],
          width: 200,
          height: 200,
          isAdaptation: false,
        }
      );
    },
    /**
     *轨迹
     */
    renderTrack(alarmList = []) {
      let length = alarmList.length,
        ps1 = [];
      if (length == 1) return;
      let points = alarmList.slice(0, 2).reverse();
      for (var i = 0; i < points.length; i++) {
        var p = new NPMap.Geometry.Point(
          points[i].longitude,
          points[i].latitude
        );
        ps1.push(p);
      }
      polylineMap[length] = new NPMap.Geometry.Polyline(ps1, {
        color: "transparent", //颜色
        weight: 20, //宽度，以像素为单位
        opacity: 1, //透明度，取值范围0 - 1
        lineStyle: NPMap.LINE_TYPE_SOLID, //样式
        arrowIcon: "track_jiantou",
      });
      // if (length >= 2 && polylineMap[length - 1]) {
      //     this.Map.removeOverlay(polylineMap[length - 1]);
      //     delete polylineMap[length - 1];
      // }
      this.Map.addOverlay(polylineMap[length]);
      let arrowline = new MapPlatForm.Base.ArrowLine(this.Map, true);
      arrowline.image.src = require("@/assets/img/map/track/track_jiantou.png");
      // arrowline.size=16;
      arrowline.load([polylineMap[length]]);
    },
    // 添加地图弹窗
    /**
     *  @param {*} content 窗体div元素
     *  @param {*} point {lon: node.longitude, lat: node.latitude}
     *  @param {*} opts
     *         width: 130,//信息窗宽度，单位像素
     *         height: 130,//信息窗高度，单位像素
     *         offset: [0,-30],//信息窗位置偏移值
     *         iscommon: false, //是否添加箭头
     *         isAdaptation: true //是否自适应
     *  @param {*} title 窗体标题
     */
    addInfoWindow(...args) {
      let [point, content, opts, title = ""] = args;
      if (point.lon && point.lat) {
        point = new NPMapLib.Geometry.Point(point.lon, point.lat);
      }
      let defaultPositionBlock = {
        imageSrc: require("@/assets/img/map/triangle.png"),
        imageSize: {
          width: 16,
          height: 16,
        },
        offset: new NPMapLib.Geometry.Size(0, 80),
      };

      // 适配2d地图弹框图标
      opts.positionBlock = Object.assign(
        {},
        defaultPositionBlock,
        opts.positionBlock || {}
      );
      if (opts.offset)
        opts.offset = new NPMapLib.Geometry.Size(
          opts.offset[0],
          opts.offset[1]
        );
      const infoWindow = new NPMapLib.Symbols.InfoWindow(
        point,
        null,
        content,
        opts
      );
      this.Map.addOverlay(infoWindow);
      infoWindow.open(null, false);
      return infoWindow;
    },
    destroyScriptList(){
      this.scriptDomList.forEach((item)=>{
        document.body.removeChild(item)
      })
      this.scriptDomList = []
    }
  },
  beforeDestroy() {
    this.destroyScriptList()
    // if (this.Map) {
    //   this.closeMouseInfoWindow()
    //   this._infoWindow && this._infoWindow.close();
    //   this.removeAllOverlays("roundLapMarkerLayer")
    //   this.removeAllOverlays("sortLapMarkerLayer")
    //   for (let key in polylineMap) {
    //       this.Map.removeOverlay(polylineMap[key]);
    //   }
    //   polylineMap = {}
    //   this.Map.destroyMap()
    //   this.Map = null
    // }
  },
};
</script>
<style lang="less" scoped>
.map-box,
.cross-track-map {
  height: 100%;
  width: 100%;
}
</style>
