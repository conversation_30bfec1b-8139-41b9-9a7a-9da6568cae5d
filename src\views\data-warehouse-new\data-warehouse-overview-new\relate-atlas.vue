<template>
  <ui-card title="关系图谱" class="relate-top" :show="true">
    <div slot="extra" class="share-service-tabs">
      <div 
        :class="type == 1 ? 'tab-item-active primary':'tab-item'" 
        class="label-color" 
        @click="typeChange(1)">
        实体
      </div>
      <div 
        :class="type == 2 ? 'tab-item-active primary':'tab-item'" 
        class="label-color" 
        @click="typeChange(2)">
        关系
      </div>
    </div>
    <div class="allCount" slot="content">
      {{type == 1 ? '实体' : '关系'}}总量：
      <count-to :start-val="0" :end-val="allCount" :duration="1000" class="number primary"></count-to>
    </div>
    <div class="relative-item" v-for="(item, $index) in dataList" :key="$index" :title="item.nameCn">
      <span class="label-Index">{{ $index > 2 ? $index + 1 : '' }}</span>
      <span class="label-name ellipsis">{{ item.nameCn }}</span>
      <Progress :percent="percentCuc(item.count || 0)" :stroke-color="['#2C86F8', '#5BC7FF']" class="progress">
        <span class="progress-text">{{ item.count }}</span>
      </Progress>
    </div>
  </ui-card>
</template>
<script>
  import CountTo from 'vue-count-to'
export default {
  components: {
    CountTo
  },
  props: {
    statisticsList: {
      type: Object,
      default: () => {}
    }
  },
  data () {
    return {
      type: 1,
      dataList: [],
      allCount: 0,
      total: 0,
      isHover: false
    }
  },
  watch: {
    'statisticsList': {
      handler (val) {
        this.init()
      },
      immediate: true
    }
  },
  methods: {
    init() {
      this.typeChange(this.type)
    },
    percentCuc (count) {
      return count * 100 / this.total
    },
    /**
     * 标签对象总量TOP10
    */
    updataLabelObject (list) {
      return {
        names: list.map(item => {
          return item.nameCn
        }),
        values: list.map(item => {
          return item.count
        })
      }
    },
    typeChange(val) {
      if( !this.statisticsList.entitys ) return
      this.type = val
      this.allCount = 0
      if (val == 1) {
        this.dataList = this.statisticsList.entitys.slice(0,5)
        this.statisticsList.entitys.forEach(item => {
          this.allCount += item.count
        })
      }else {
        this.dataList = this.statisticsList.relations.slice(0,5)
        this.statisticsList.relations.forEach(item => {
          this.allCount += item.count
        })
      }
    }
  }
}
</script>
<style lang="less" scoped>
.relate-top {
  /deep/.card-content {
    height: 280px;
    padding: 0 20px 0 0!important;
    overflow-y: scroll;
    margin: 10px 20px 10px 20px;
  }
}

.progress {
  width: 95%;

  /deep/.progress-text {
    color: #2C86F8;
    font-size: 14px;
    font-weight: 800;
    display: inline-block;
    text-align: center;
    width: 40px;
  }
}

.relative-item {
  display: flex;
  margin-bottom: 10px;

  .label-Index {
    display: inline-block;
    margin-right: 10px;
    text-align: center;
    width: 25px;
    height: 20px;
    font-size: 12px;
    color: #93ABCE;
  }

  &:nth-of-type(1) {
    .label-Index {
      background: url("~@/assets/img/card/num1.png") no-repeat;
    }

  }

  &:nth-of-type(2) {
    .label-Index {
      background: url("~@/assets/img/card/num2.png") no-repeat;
    }
  }

  &:nth-of-type(3) {
    .label-Index {
      background: url("~@/assets/img/card/num3.png") no-repeat;
    }
  }

  .label-name {
    display: inline-block;
    width: 80px;
  }
}
.allCount {
  // padding: 10px 20px 1px 20px;
  // color: #333;
  color: rgba(0, 0, 0, 0.45);
  flex: 1;
  // font-weight: 600;
  padding-left: 10px;
  height: 30px;
  line-height: 36px;
}
.share-service-tabs {
  display: flex;
  z-index: 99;
  .tab-item, .tab-item-active {
    font-size: 14px;
    line-height: 20px;
    margin-right: 20px;
    cursor: pointer;
  }
  .tab-item-active {
    font-family: 'MicrosoftYaHei-Bold';
    font-weight: bold;
    position: relative;
  }
  .tab-item-active::after {
    content: '';
    width: 100%;
    height: 2px;
    background: #2C86F8;
    position: absolute;
    left: 0;
    bottom: -1px;
  }
}
</style>
