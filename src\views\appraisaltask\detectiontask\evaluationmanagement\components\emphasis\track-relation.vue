<template>
  <!-- 轨迹关联率弹框 -->
  <ui-modal class="track-relation" v-model="visible" title="查看详情" :styles="styles" :footer-hide="true">
    <div class="content auto-fill">
      <div class="container auto-fill">
        <div class="title_text">
          <span class="fl text_left mr-lg">指标名称：{{ this.$parent.row.indexName }}</span>
          <span class="fl text_left">评测时间：{{ this.$parent.row.createTime }}</span>
          <div class="export fr">
            <!-- <Button type="primary" class="btn_search">
              <i class="icon-font icon-daochu font-white f-14 mr-xs"></i>
              <span class="inline ml-xs">导出</span>
            </Button> -->
          </div>
        </div>
        <line-title title-name="检测结果统计"></line-title>
        <div class="statistics_list">
          <div class="statistics">
            <div class="sta_item_left">
              <draw-echarts
                :echart-option="echartRing"
                :echart-style="ringStyle"
                ref="zdryChart"
                class="charts"
              ></draw-echarts>
            </div>
            <div class="line"></div>
            <no-standard :module-data="moduleData"></no-standard>
          </div>
        </div>
        <line-title title-name="检测结果详情"></line-title>
        <track-device-search ref="faceSearchRef" @startSearch="startSearch" :selectList="selectList">
        </track-device-search>
        <div class="list auto-fill">
          <!-- <table-data class="table-data" :tableData="tableData"></table-data> -->
          <ui-table class="ui-table auto-fill" :table-columns="tableColumns" :table-data="tableData" :loadin="loading">
            <template #trackImage="{ row }">
              <img @click="viewBigPic(row)" :src="!row.trackImage ? noImg : row.trackImage" alt="" />
            </template>
            <template #identityPhoto="{ row }">
              <img @click="viewBig(row)" :src="!row.identityPhoto ? noImg : row.identityPhoto" alt="" />
            </template>
            <template #option="{ row }">
              <span @click="show">{{ row.option }}</span>
            </template>
          </ui-table>

          <loading v-if="loading"></loading>
        </div>
        <ui-page class="page" :page-data="searchData" @changePage="changePage" @changePageSize="changePageSize">
        </ui-page>
      </div>

      <look-scene v-model="bigPictureShow" :img-list="imgList"></look-scene>
    </div>
    <!-- <div class="no-box" v-else>
      <div class="no-data">
        <img src="@/assets/img/common/nodata.png" alt />
      </div>
    </div> -->
  </ui-modal>
</template>

<style lang="less" scoped>
.track-relation {
  @{_deep} .ivu-modal {
    .ivu-modal-header {
      margin-bottom: 0;
    }
    .ivu-modal-body {
      padding: 10px 20px;
    }
  }
  .no-box {
    width: 1686px;
    min-height: 820px;
    max-height: 820px;
  }
  .content {
    color: #fff;
    background: var(--bg-content);
    min-height: 820px;
    max-height: 820px;
    border-radius: 4px;
    position: relative;
    .container {
      .list_item {
        margin-top: 10px;
      }
      .title_text {
        width: 100%;
        height: 50px;
        display: inline-block;
        line-height: 50px;
        // background-color: #239df9;
        .text_left {
          margin-top: 10px;
        }
        .export {
          margin-top: 10px;
          //   position: absolute;
          //   top: 20px;
          //   right: 20px;
        }
      }
      .scheme_header {
        height: 30px;
        line-height: 30px;
        width: 100%;
        display: inline-block;
        margin-top: 10px;
        .scheme_line {
          width: 8px;
          height: 30px;
          vertical-align: middle;
          display: inline-block;
          background: #239df9;
          opacity: 1;
        }
        .scheme_title {
          width: calc(100% - 18px);
          height: 30px;
          vertical-align: middle;
          display: inline-block;
          margin-left: 10px;
          padding-left: 10px;
          background: linear-gradient(90deg, #0a4f8d 0%, rgba(9, 40, 77, 0) 100%);
        }
      }
      .statistics_list {
        width: 100%;
        height: 180px;
        line-height: 180px;
        background-color: var(--bg-sub-content);
        margin-top: 10px;
      }
      .statistics {
        width: 100%;
        height: 180px;
        line-height: 180px;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        .sta_item_left {
          width: 48%;
          display: flex;
          justify-content: flex-start;
          align-items: flex-start;
        }
        .line {
          width: 1px;
          height: 70%;
          background-color: #0d477d;
        }
      }
    }
    .list {
      position: relative;
      margin-top: 10px;
      //   height: 450px;
      //   overflow-y: auto;

      .no-data {
        position: absolute;
        top: 50%;
        left: 50%;
      }
      img {
        width: 56px;
        height: 56px;
      }
      @{_deep}.ivu-table-cell-slot {
        margin-top: 10px;
      }
    }
  }
  /deep/.base-search {
    margin: 15px 0 0;
  }
}
</style>
<script>
import governanceevaluation from '@/config/api/governanceevaluation';
import imgSrc from '@/assets/img/load-error-img.png';
export default {
  props: {
    value: {},
    statisticsList: {},
  },
  data() {
    return {
      noImg: imgSrc,
      echartRing: {},
      ringStyle: {
        width: '650px',
        height: '180px',
      },
      zdryChartObj: {
        xAxisData: ['检测轨迹未关联图像数量', '轨迹图像关联设备数量'],
        showData: [
          { name: '检测轨迹未关联图像数量', value: 0 },
          { name: '轨迹图像关联设备数量', value: 0 },
        ],
        zdryTimer: null,
        count: 0,
        formData: {},
        color: [
          [
            { offset: 0, color: '#32A19E' },
            { offset: 1, color: '#05CE98' },
          ],
          [
            { offset: 0, color: '#983C0D' },
            { offset: 1, color: '#E9410F' },
          ],
        ],
      },
      selectList: [
        { id: 0, label: '轨迹关联设备' },
        { id: 1, label: '轨迹设备未关联设备资产库' },
        { id: 2, label: '轨迹编码不存在' },
      ],
      moduleData: {
        rate: '轨迹设备关联率',
        rateValue: 0,
        price: '达标值',
        priceValue: 0,
        result: '考核结果',
        resultValue: '',
        remarkValue: '',
      },
      tableColumns: [
        { type: 'index', width: 70, title: '序号', align: 'center' },
        {
          title: '人脸抓拍',
          key: 'trackImage',
          slot: 'trackImage',
          tooltip: true,
          minWidth: 150,
        },
        { title: '抓拍时间', key: 'shotTime', tooltip: true, minWidth: 150 },
        { title: '抓拍点位', key: 'catchPlace', tooltip: true, minWidth: 150 },
        {
          title: '档案照',
          key: 'identityPhoto',
          slot: 'identityPhoto',
          tooltip: true,
          minWidth: 150,
        },
        { title: '姓名', key: 'name', tooltip: true, minWidth: 150 },
        { title: '证件号', key: 'idCard', tooltip: true, minWidth: 150 },
        {
          title: '设备编码',
          key: 'deviceId',
          tooltip: true,
          minWidth: 150,
        },
        {
          title: '是否关联设备资产库1',
          key: 'isguan',
          minWidth: 150,
        },
        {
          title: '检测结果',
          key: 'synthesisResult',
          tooltip: true,
          width: 300,
        },
      ],
      tableData: [],
      minusTable: 600,
      searchData: {
        totalCount: 0,
        pageNum: 1,
        pageSize: 20,
        params: {
          pageNumber: 1,
          pageSize: 20,
        },
      },
      styles: {
        width: '9rem',
      },
      visible: true,
      loading: false,
      bigPictureShow: false,
      imgList: [require('@/assets/img/navigation-page/systemmanagement.png')],
      trackList: {},
    };
  },
  async mounted() {
    await this.init();
    await this.getStatistics();
    await this.initRing();
  },

  methods: {
    viewBigPic(item) {
      if (!item.trackLargeImage) {
        this.$Message.warning('大图URL缺失');
        return;
      }
      this.imgList = [item.trackLargeImage];
      this.bigPictureShow = true;
    },
    viewBig(item) {
      if (!item.identityPhoto) {
        this.$Message.warning('大图URL缺失');
        return;
      }
      this.imgList = [item.identityPhoto];
      this.bigPictureShow = true;
    },

    // 列表
    async init() {
      this.searchData.resultId = this.$parent.row.resultId;
      this.searchData.taskId = this.$route.query.id;
      try {
        this.loading = true;
        this.tableData = [];
        let res = await this.$http.post(governanceevaluation.focusRelateList, this.searchData);
        this.tableData = res.data.data.entities;
        for (let i of this.tableData) {
          if (i.synthesisResult === '0') {
            i.synthesisResult = '轨迹关联设备';
            i.isguan = '是';
          } else if (i.synthesisResult === '1') {
            i.synthesisResult = '轨迹设备未关联设备资产库';
            i.isguan = '否';
          } else if (i.synthesisResult === '2') {
            i.synthesisResult = '轨迹设备编码不存在';
            i.isguan = '-';
          }
        }
        this.searchData.totalCount = res.data.data.total;
        this.loading = false;
      } catch (err) {
        console.log(err);
        this.loading = false;
      } finally {
        this.loading = false;
      }
    },
    startSearch(searchData) {
      this.searchData.deviceIds = searchData.deviceIds;
      this.searchData.endTime = searchData.endTime;
      this.searchData.startTime = searchData.startTime;
      this.searchData.synthesisResult = searchData.deviceResult;
      this.searchData.pageNum = 1;
      this.searchData.params.pageNumber = 1;
      if (this.modelTag === true) {
        this.init(true);
      } else {
        this.init();
      }
    },
    // 统计
    async getStatistics() {
      try {
        let res = await this.$http.post(governanceevaluation.getResultStatistics, {
          resultId: this.$parent.row.resultId,
          indexId: this.$parent.row.indexId,
          taskId: this.$route.query.id,
        });
        this.trackList = res.data.data;
        this.moduleData.rateValue = this.trackList.relatedRate || 0;
        this.moduleData.priceValue = this.$parent.row.standardsValue || 0;
        this.moduleData.resultValue = this.$parent.row.qualifiedDesc || 0;
      } catch (error) {
        console.log(error);
      }
    },
    // echarts图表
    initRing() {
      let xAxisData = this.zdryChartObj.xAxisData;
      this.zdryChartObj.showData.map((item) => {
        if (item.name === '检测轨迹未关联图像数量') {
          item.value = this.trackList.detectionAmount - this.trackList.relatedAmount;
        } else {
          item.value = this.trackList.relatedAmount;
        }
      });
      this.zdryChartObj.count = this.trackList.detectionAmount;
      let formatData = {
        seriesName: '检测轨迹图像',
        xAxisData: xAxisData,
        showData: this.zdryChartObj.showData,
        count: this.zdryChartObj.count,
        color: this.zdryChartObj.color,
      };
      this.zdryChartObj.formatData = formatData;
      this.echartRing = this.$util.doEcharts.evaluationPageRin(this.zdryChartObj.formatData);
    },

    changePage(val) {
      this.searchData.pageNum = val;
      this.searchData.params.pageNumber = val;
      this.init();
    },
    changePageSize(val) {
      this.searchData.pageSize = val;
      this.searchData.params.pageSize = val;
      this.searchData.pageNum = 1;
      this.searchData.params.pageNumber = 1;
      this.init();
    },
    // search() {
    //   this.searchData.pageNum = 1
    //   this.init()
    // },
  },
  watch: {
    visible(val) {
      this.$emit('input', val);
    },
    value(val) {
      this.visible = val;
    },
  },
  components: {
    UiTable: require('@/components/ui-table.vue').default,
    DrawEcharts: require('@/components/draw-echarts').default,
    lineTitle: require('@/components/line-title').default,
    noStandard: require('@/views/appraisaltask/detectiontask/evaluationmanagement/components/no-standard.vue').default,
    TrackDeviceSearch: require('@/components/track-detail-search.vue').default,
  },
};
</script>
