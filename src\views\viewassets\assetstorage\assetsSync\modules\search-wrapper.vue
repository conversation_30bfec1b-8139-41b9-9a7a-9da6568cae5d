<template>
  <div class="search-module">
    <div class="d_flex mt-sm mb-sm">
      <ui-switch-tab class="ui-switch-tab mb-sm" v-model="state" :tab-list="stateOptions" @changeTab="changeTab">
      </ui-switch-tab>
      <Tooltip placement="right" class="tips">
        <i class="icon-font icon-wenhao ml-sm"></i>
        <ul slot="content">
          <li v-for="(item, index) of tipsContent" :key="index" class="li">
            <span class="font-active-color"> {{ item.name }}</span>
            {{ item.content }}
          </li>
        </ul>
      </Tooltip>
    </div>
    <ui-label class="inline mb-sm mr-lg" :label="`${global.filedEnum.deviceId}`">
      <Input v-model="searchData.deviceId" class="width-input" placeholder="请输入设备编码"></Input>
    </ui-label>
    <ui-label class="inline mb-sm mr-lg" :label="`${global.filedEnum.deviceName}`">
      <Input v-model="searchData.deviceName" class="width-input" placeholder="请输入设备名称"></Input>
    </ui-label>
    <ui-label class="inline mb-sm mr-lg" label="IP地址">
      <Input v-model="searchData.ipAddr" class="width-input" placeholder="请输入IP地址"></Input>
    </ui-label>
    <ui-label class="inline mb-sm mr-lg" label="检测状态">
      <Select
        class="width-input"
        v-model="searchData.checkStatus"
        :placeholder="`请选择检测状态`"
        clearable
        :disabled="isDisabled"
        :max-tag-count="1"
      >
        <Option v-for="(item, index) in checkStatusList" :key="index" :value="item.dataKey"
          >{{ item.dataValue }}
        </Option>
      </Select>
    </ui-label>
    <ui-label class="inline mr-lg mb-sm" label="比对状态">
      <Select
        class="width-input"
        v-model="searchData.compareStatus"
        :placeholder="`请选择比对状态`"
        clearable
        :disabled="isDisabled"
        :max-tag-count="1"
      >
        <Option v-for="(item, index) in compareStatusList" :key="index" :value="item.dataKey"
          >{{ item.dataValue }}
        </Option>
      </Select>
    </ui-label>
    <ui-label class="inline mr-lg" label="入库状态">
      <Select
        class="width-input"
        v-model="searchData.storageStatus"
        :placeholder="`请选择入库状态`"
        clearable
        :disabled="isDisabled"
        :max-tag-count="1"
      >
        <Option v-for="(item, index) in storageStatusList" :key="index" :value="item.dataKey"
          >{{ item.dataValue }}
        </Option>
      </Select>
    </ui-label>
    <!-- 监控点位类型 -->
    <ui-label class="inline mb-sm mr-lg" :label="global.filedEnum.sbdwlx">
      <Select class="width-md" v-model="searchData.sbdwlx" :placeholder="`请选择${global.filedEnum.sbdwlx}`" clearable>
        <Option v-for="(item, index) in propertySearchLbdwlx" :key="index" :value="item.dataKey"
          >{{ item.dataValue }}
        </Option>
      </Select>
    </ui-label>

    <!-- 监控点位类型 -->
    <ui-label class="inline mb-sm mr-lg" :label="global.filedEnum.sbgnlx">
      <Select class="width-md" v-model="searchData.sbgnlx" :placeholder="`请选择${global.filedEnum.sbgnlx}`" clearable>
        <Option v-for="(item, index) in propertySearchSxjgnlx" :key="index" :value="item.dataKey"
          >{{ item.dataValue }}
        </Option>
      </Select>
    </ui-label>
    <div class="right-btn inline">
      <Button type="primary" @click="searchList">查询</Button>
      <Button class="ml-sm" @click="reset">重置</Button>
      <Tooltip class="superior-search-toolTips" placement="bottom" :delay="0" :always="superiorSearch">
        <!-- <span class="font-active-color ml-md f-14 pointer"
              @click="superiorSearch = !superiorSearch">高级搜索</span> -->
        <div slot="content">
          <Input class="textarea-class" type="textarea" v-model="superiorInput" :maxlength="600"></Input>
        </div>
      </Tooltip>
      <div class="mutiple-button">
        <slot></slot>
      </div>
    </div>
    <div class="detail-search d_flex">
      <div class="d_flex">
        <Checkbox class="mb-sm ml-xs" v-model="isAllSelect" @on-change="$emit('chooseAll', isAllSelect)">
          <span class="base-text-color ml-sm"> 全选 </span>
        </Checkbox>
        <p class="hasline base-text-color ml-lg mr-md f-14">已选中 <slot name="totalSlot"></slot> 条</p>
        <Checkbox
          class="mb-sm ml-lg hasline"
          v-if="state === stateOptionsObject['差异设备'].value"
          v-model="mainChecked"
          @on-change="$emit('compareModeChange', mainChecked)"
        >
          <span class="base-text-color"> 比对模式 </span>
        </Checkbox>
        <RadioGroup class="ml-lg" v-model="excludeType" @on-change="excludeTypeChange">
          <Radio label="isDiffer" :disabled="isDisabled"> 标识差异字段</Radio>
          <Radio label="isCheck" :disabled="isDisabled" class="ml-lg"> 标识不合格字段</Radio>
        </RadioGroup>
      </div>
    </div>
  </div>
</template>
<script>
import { mapGetters } from 'vuex';
import { stateOptionsObject, stateOptions } from '../util/enum.js';
export default {
  props: {
    isDisabled: {
      default: false,
    },
  },
  data() {
    return {
      mainChecked: false,
      state: '',
      stateOptions: Object.freeze(stateOptions),
      superiorInput: '',
      stateOptionsObject: Object.freeze(stateOptionsObject),
      superiorSearch: false,
      searchData: {
        orgCode: '',
        ipAddr: '',
        deviceId: '',
        deviceName: '',
        checkStatus: '',
        storageStatus: '',
        compareStatus: '',
        deviceType: '',
        sbdwlx: '',
        sbgnlx: '',
        isCheck: null,
        isDiffer: null,
      },
      isAllSelect: false,
      excludeType: null,
      tipsContent: Object.freeze([
        { name: '【新增设备】', content: '：新增或者导入资产库不存在的设备。' },
        {
          name: '【相同设备】',
          content: '：本次同步的设备和资产库已有设备相比，如果所有配置的字段内容均相同，即为相同设备；',
        },
        {
          name: '【差异设备】',
          content: '：本次同步的设备和资产库已有设备相比，如果任何一个配置的字段内容上有差异，即为差异设备；',
        },
        {
          name: '【删除设备】',
          content:
            '：本次同步的全量设备和资产库设备相比（比对条件可配置），资产库存在的设备本次采集列表没有，即为删除设备。',
        },
      ]),
      dictData: {},
    };
  },
  async created() {
    this.copySearchDataMx(this.searchData);
  },
  mounted() {},
  methods: {
    changeTab(val) {
      this.searchData.deviceType = val;
      this.$emit('changeTab', this.searchData);
    },
    excludeTypeChange(val) {
      if (val === 'isDiffer') {
        this.searchData.isDiffer = 1;
        this.searchData.isCheck = 0;
      } else {
        this.searchData.isDiffer = 0;
        this.searchData.isCheck = 1;
      }
      this.$emit('startSearch', this.searchData);
    },
    searchList() {
      this.$emit('startSearch', this.searchData);
    },
    reset(reserveParams = {}, isResetState = true) {
      this.mainChecked = false;
      this.isAllSelect = false;
      this.excludeType = null;
      // 重置选择全部设备
      isResetState ? (this.state = '') : null;
      this.$emit('chooseAll', this.isAllSelect);
      this.searchData = {
        orgCode: '',
        ipAddr: '',
        deviceId: '',
        deviceName: '',
        checkStatus: '',
        storageStatus: '',
        compareStatus: '',
        deviceType: '',
        sbdwlx: '',
        sbgnlx: '',
        isCheck: null,
        isDiffer: null,
      };
      Object.assign(this.searchData, reserveParams);
      this.$emit('startSearch', this.searchData);
    },
    resetLess() {
      this.mainChecked = false;
      this.isAllSelect = false;
      this.excludeType = null;
      this.state = '';
      this.$emit('chooseAll', this.isAllSelect);
    },
  },
  watch: {},
  computed: {
    ...mapGetters({
      aysc_check_status: 'assets/aysc_check_status',
      aysc_storage_status: 'assets/aysc_storage_status',
      aysc_compare_status: 'assets/aysc_compare_status',
      // aysc_storage_type: 'assets/aysc_storage_type',
      propertySearchLbdwlx: 'algorithm/propertySearch_sbdwlx', // 点位类型
      propertySearchSxjgnlx: 'algorithm/sxjgnlx_receive', // 功能类型
    }),
    // 0 未检测  ,1 合格，2 不合格，3 检测中
    checkStatusList() {
      return this.aysc_check_status;
    },
    // 入库状态 （0 未入库 ，1 已入库）
    storageStatusList() {
      return this.aysc_storage_status;
    },
    // 0 未比对  ,1 已比对，2 比对中
    compareStatusList() {
      return this.aysc_compare_status;
    },
  },
  components: {
    UiSwitchTab: require('@/components/ui-switch-tab/ui-switch-tab.vue').default,
  },
};
</script>
<style lang="less" scoped>
.search-module {
  padding: 0 20px;
  position: relative;
  .detail-search {
    height: 60px;
    line-height: 60px;
    justify-content: space-between;
  }
  .hasline {
    position: relative;
    &::before {
      top: 20px;
      position: absolute;
      content: '';
      height: 20px;
      width: 1px;
      background: var(--devider-line);
      right: -20px;
    }
  }
  .right-btn {
    .mutiple-button {
      position: absolute;
      right: 20px;
      bottom: 15px;
      display: flex;
    }
  }
  .tips {
    @{_deep}.ivu-tooltip-inner {
      max-width: 850px;
      .li {
        width: 850px;
      }
    }
  }
  @{_deep}.ui-switch-tab {
    .list {
      border-radius: 5px;
      li {
        height: 30px;
        line-height: 30px;
        &:not(:first-of-type){
          border-left: none;
        }
      }
    }
  }
  .width-input {
    width: 230px;
  }
  .superior-search-toolTips {
    @{_deep}.ivu-tooltip-inner {
      max-width: 1400px !important;
      width: 1400px;
    }
  }
  .icon-wenhao {
    color: #b8580d;
    font-size: 16px;
  }
}
</style>
