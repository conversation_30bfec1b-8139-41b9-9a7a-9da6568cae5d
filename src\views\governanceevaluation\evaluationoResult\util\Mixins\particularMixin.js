import evaluationoverview from '@/config/api/evaluationoverview';
import detectionResult from '@/config/api/detectionResult';
import downLoadTips from '@/mixins/download-tips';

// 检测明细公共方法混入
const mixin = {
  mixins: [downLoadTips],
  data() {
    return {
      exportLoading: false,
    };
  },
  props: {
    isUseRouterQuery: {
      type: Boolean,
      default: true,
    },
    //非路由传参时使用此对象代替Route.query,需搭配isUseRouterQuery生效
    routerQuerySubstitution: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  methods: {
    //车辆指标
    /**
     *
     * @param data
     * @param errorFields '[{"value":"车辆类型为空","key":"vehicleClassType"},{"value":"车辆品牌为空","key":"vehicleBrandType"},{"value":"车辆型号为空","key":"vehicleModelType"}]'
     * @param key
     * @return
     * @description 只要跟算法返回的错误原因errorFields匹配，都标红（包括 data存在值）
     */
    getErrorField(errorFields, key) {
      if (!Array.isArray(errorFields)) {
        return {
          style: '',
          desc: '',
        };
      }
      let field = errorFields.find((item) => item.key === key);
      if (!field) {
        return {
          style: '',
          desc: '',
        };
      }
      return {
        style: { color: 'var(--color-failed)' },
        desc: field.value,
      };
    },
    // 获取统计
    async MixinGetStatInfo(
      { paramList, code } = {
        paramList: {},
        code: null,
      },
    ) {
      this.statisticalData = {};
      const importantIdList = [4001, 4002, 4003, 4004];
      const paramsData = Object.keys(this.useRouterQueryObj).length ? this.useRouterQueryObj : paramList;
      let data = {
        indexId: paramsData.indexId,
        batchId: paramsData.batchId,
        access: 'TASK_RESULT',
        displayType: paramsData.statisticType || paramsData.displayType,
        isImportant: importantIdList.includes(this.indexId) ? '1' : undefined,
      };
      data.orgRegionCode = code ? code : data.displayType === 'REGION' ? paramsData.regionCode : paramsData.orgCode;
      if (paramsData.canPlay && paramsData.canPlay !== '2') {
        Object.assign(data, {
          customParameters: {
            canPlay: paramsData.canPlay,
          },
        });
      }
      try {
        let res = await this.$http.post(evaluationoverview.getStatInfo, data);
        return res.data.data || {};
      } catch (err) {
        console.log(err);
      }
    },
    // 获取设备模式列表的参数
    MixinGetTableDataParams({ code }) {
      try {
        let params = {
          indexId: this.useRouterQueryObj.indexId,
          batchId: this.useRouterQueryObj.batchId,
          access: 'TASK_RESULT',
          displayType: this.useRouterQueryObj.statisticType,
          customParameters: this.formData,
          pageNumber: this.pageData.pageNum,
          pageSize: this.pageData.pageSize,
        };
        params.orgRegionCode = code
          ? code
          : params.displayType === 'REGION'
          ? this.useRouterQueryObj.regionCode
          : this.useRouterQueryObj.orgCode;
        //人车、重点人两个指标 添加排除总数的参数
        let unSimpleIndexTypes = [
          'VEHICLE_PLATFORM_ONLINE_RATE',
          'VEHICLE_MONITOR_API_STABILITY',
          'VEHICLE_TRACK_API_STABILITY',
          'VEHICLE_CAPTURE_STABILITY',

          'FACE_PLATFORM_ONLINE_RATE',
          'FACE_MONITOR_API_STABILITY',
          'FACE_CAPTURE_STABILITY',
        ];
        //重点人的两个指标总数排除
        let zdrSupportIndexType = ['FOCUS_TRACK_REAL', 'FOCUS_TRACK_URL_AVAILABLE'];
        let indexType = this.useRouterQueryObj.indexType;
        if (
          indexType &&
          (indexType.includes('VEHICLE_') || indexType.includes('FACE_') || zdrSupportIndexType.includes(indexType)) &&
          !unSimpleIndexTypes.includes(indexType)
        ) {
          params.excludeTotal = true;
        }
        if (this.useRouterQueryObj.canPlay && this.useRouterQueryObj.canPlay !== '2') {
          Object.assign(params, {
            customParameters: {
              ...params.customParameters,
              canPlay: this.useRouterQueryObj.canPlay,
            },
          });
        }
        return params;
      } catch (err) {
        console.log('获取设备模式列表参数异常', err);
        return {};
      }
    },
    // 设备模式列表数据
    async MixinGetTableData(code) {
      try {
        this.tableLoading = true;
        let params = this.MixinGetTableDataParams({ code });
        return await this.MixinGetFirstModeData(params);
      } catch (error) {
        console.log(error);
      } finally {
        this.tableLoading = false;
      }
    },
    async MixinGetFirstModeData(params) {
      try {
        let { data } = await this.$http.post(evaluationoverview.getDetailData, params);
        return (
          data.data || {
            total: 0,
            entities: [],
          }
        );
      } catch (error) {
        console.log(error);
      } finally {
        this.tableLoading = false;
      }
    },
    // 获取设备模式列表总数，异步请求，用于分页
    async MixinGetTableDataTotal(code) {
      try {
        let params = this.MixinGetTableDataParams({ code });
        let {
          data: { data },
        } = await this.$http.post(evaluationoverview.getFirstModelDataTotal, params);
        return data || 0;
      } catch (error) {
        console.log(error);
      }
    },
    // 图片模式列表获取
    async MixinGetImageList(
      { code, ...rest } = {
        code: null,
      },
    ) {
      this.tableLoading = true;
      const params = {
        indexId: this.useRouterQueryObj.indexId,
        batchId: this.useRouterQueryObj.batchId,
        displayType: this.useRouterQueryObj.statisticType,

        // indexId: 2006,
        // batchId: "A#3788#0000000009",
        // access: 'TASK_RESULT',
        // displayType: "REGION",
        customParameters: this.formData,
        pageNumber: this.pageData.pageNum,
        pageSize: this.pageData.pageSize,
        ...rest,
      };
      params.orgRegionCode = code
        ? code
        : params.displayType === 'REGION'
        ? this.useRouterQueryObj.regionCode
        : this.useRouterQueryObj.orgCode;
      try {
        let { data } = await this.$http.post(evaluationoverview.getPolyData, params);
        return (
          data.data || {
            total: 0,
            entities: [],
          }
        );
      } catch (err) {
        console.log(err);
      } finally {
        this.tableLoading = false;
      }
    },
    async MixinGetQualificationList() {
      let { indexId, batchId, statisticType, regionCode, orgCode } = this.useRouterQueryObj;
      let params = {
        indexId: indexId,
        batchId: batchId,
        access: 'TASK_RESULT',
        displayType: statisticType,
        orgRegionCode: statisticType === 'REGION' ? regionCode : orgCode,
      };
      try {
        let {
          data: { data = [] },
        } = await this.$http.post(detectionResult.getVideoUnqualifiedInfo, params);
        return data;
      } catch (err) {
        console.log(err);
      }
    },

    // 获取不合格原因下拉列表
    // async MixinDisQualificationList() {
    //   let params = {
    //     indexId: this.$route.query.indexId,
    //     batchId: this.$route.query.batchId,
    //     access: 'TASK_RESULT',
    //     displayType: this.$route.query.statisticType,
    //     customParameters: this.formData,
    //   }
    //   params.orgRegionCode = params.displayType === 'REGION' ? this.$route.query.regionCode :
    //     this.$route.query.orgCode
    //   try {
    //     let res = await this.$http.post(detectionResult.getUnqualifiedInfo, params)
    //     return res.data.data || {}
    //   } catch (err) {
    //     console.log(err)
    //   }
    // },
    // 获取不合格原因下拉列表
    // mode 1:设备模式，2:图片模式
    async MixinDisQualificationList(mode = 1) {
      let params = {
        indexType: this.useRouterQueryObj.indexType,
        model: mode,
      };
      try {
        let res = await this.$http.get(detectionResult.getUnqualifiedInfo, {
          params: params,
        });
        return res.data.data || {};
      } catch (err) {
        console.log(err);
      }
    },
    // 导出设备列表
    async MixinGetExport(val = {}) {
      this.exportLoading = true;
      let params = {
        indexId: this.useRouterQueryObj.indexId,
        batchId: this.useRouterQueryObj.batchId,
        customParameters: this.formData,
        ...val,
      };
      if (this.useRouterQueryObj.canPlay && this.useRouterQueryObj.canPlay !== '2') {
        Object.assign(params, {
          customParameters: {
            ...params.customParameters,
            canPlay: this.useRouterQueryObj.canPlay,
          },
        });
      }
      try {
        this.$_openDownloadTip();
        const res = await this.$http.post(evaluationoverview.exportDeviceDetailData, params);
        await this.$util.common.transformBlob(res.data.data);
        this.exportLoading = false;
      } catch (err) {
        this.exportLoading = false;
      }
    },
    // 导出图片列表
    async MixinGetSecondExport() {
      try {
        this.exportLoading = true;
        let params = {
          indexId: this.useRouterQueryObj.indexId,
          batchId: this.useRouterQueryObj.batchId,
          displayType: this.useRouterQueryObj.statisticType,
          customParameters: this.formData,
        };
        params.orgRegionCode =
          params.displayType === 'REGION' ? this.useRouterQueryObj.regionCode : this.useRouterQueryObj.orgCode;
        this.$_openDownloadTip();
        const res = await this.$http.post(evaluationoverview.exportSecondModelData, params);
        await this.$util.common.transformBlob(res.data.data);
        this.exportLoading = false;
      } catch (err) {
        console.log(err);
        this.exportLoading = false;
      }
    },
    MixinSpecialIcon(item, qualified) {
      // 配置合格不合格图标
      qualified === '1' ? (item.tailIcon = 'icon-dabiao') : (item.tailIcon = ' icon-budabiao');
    },
  },
  computed: {
    useRouterQueryObj() {
      return this.isUseRouterQuery ? this.$route.query : this.routerQuerySubstitution;
    },
  },
};
export default mixin;
