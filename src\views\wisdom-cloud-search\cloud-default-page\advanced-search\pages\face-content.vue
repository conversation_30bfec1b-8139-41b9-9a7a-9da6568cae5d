<template>
  <section class="main-container">
    <div class="search-bar">
      <search-face ref="searchBar"
                   @search="searchHandle"
                   @reset="resetHandle" />
    </div>
    <div class="table-container">
      <div class="data-above"
           v-if="mapOnData && this.queryParam.dataSource == '2'">
        <Checkbox @on-change="checkAllHandler"
                  v-model="checkAll">全选</Checkbox>
        <Button @click="dataAboveMapHandler">
          <ui-icon type="dongtai-shangtudaohang"
                   color="#2C86F8"></ui-icon>
          数据上图
        </Button>
      </div>
      <div class="table-content">
            <div class="list-card box-1" :class="{ isChecked: item.isChecked }"
                v-for="(item, index) in dataList"
                :key="index">
            <div class="collection paddingIcon">
                <div class="bg"></div>
                <ui-btn-tip class="collection-icon" v-if="item.myFavorite == '1'" content="取消收藏" icon="icon-yishoucang" transfer @click.native="collection(item, 2)" />
                <ui-btn-tip class="collection-icon" v-else content="收藏" icon="icon-shoucang" transfer @click.native="collection(item, 1)" />
            </div>
            <Checkbox class="check-box"
                v-if="mapOnData"
                v-model="item.isChecked"
                @on-change="e => checkHandler(e, index)"></Checkbox>
            <p class="img-content">
                <span class="num" v-if="item.score">{{ item.score }}%</span>
                <template v-if="childDataSourceVal == 1">
                    <ui-image :src="item.photoUrl"
                        alt="静态库"
                        viewer />
                    <div class="idCardNo">{{ item.idCardNo }}</div>
                </template>
                <template v-else-if="childDataSourceVal == 2">
                    <ui-image :src="item.traitImg"
                    alt="动态库"
                    @click.native="faceDetailFn(item, index)" />
                </template>
                <!-- <b class="shade car" >
                <span class="color-warning">{{item.spIdCard}}</span>
                <span class="color-primary">{{item.spIdCard}}</span>
                </b> -->
            </p>
          <!-- 静态库 -->
          <div class="bottom-info"
               v-if="childDataSourceVal == 1">
            <time>
              <Tooltip content="姓名"
                       placement="right"
                       transfer
                       theme="light">
                <i class="iconfont icon-xingming"></i>
              </Tooltip>
              {{ item.name }}
            </time>
            <p>
              <Tooltip content="库名称"
                       placement="right"
                       transfer
                       theme="light">
                <i class="iconfont icon-shujulaiyuan"></i>
              </Tooltip>
              {{ item.libName }}
            </p>
            <!-- <p>
              <Tooltip content="来源" placement="right" transfer theme="light">
                <i class="iconfont icon-shujulaiyuan"></i>
              </Tooltip>
              {{ item.libName }}
            </p> -->
            <!-- <time><i class="iconfont icon-time"></i>{{item.absTime}}</time>
            <p><i class="iconfont icon-location"></i>{{item.shotAddress}}</p>
            <p><i class="iconfont icon-location"></i>{{item.vid}}</p> -->
          </div>
          <!-- 动态库 -->
          <div class="bottom-info"
               v-else>
            <time>
              <Tooltip content="抓拍时间"
                       placement="right"
                       transfer
                       theme="light">
                <i class="iconfont icon-time"></i>
              </Tooltip>
              {{ item.absTime }}
            </time>
            <p>
              <Tooltip content="抓拍地点"
                       placement="right"
                       transfer
                       theme="light">
                <i class="iconfont icon-location"></i>
              </Tooltip>
              <span class="ellipsis" v-show-tips>{{item.deviceName}}</span>
            </p>
            <!-- <time><i class="iconfont icon-time"></i>{{item.absTime}}</time>
            <p><i class="iconfont icon-location"></i>{{item.shotAddress}}</p>
            <p><i class="iconfont icon-location"></i>{{item.vid}}</p> -->
          </div>
          <!-- <div class="operate-bar">
            <p class="operate-content">
              <ui-btn-tip content="档案"
                          icon="icon-dangan2"
                          @click.native="archivesPage(item)" /> -->
              <!-- <ui-btn-tip content="收藏"
                          icon="icon-shoucang" /> -->
              <!-- <ui-btn-tip v-if="item.myFavorite == '1'" content="收藏" icon="icon-yishoucang" transfer @click.native="collection(item, 2)" />
              <ui-btn-tip v-else content="收藏" icon="icon-shoucang" transfer @click.native="collection(item, 1)" /> -->
              <!-- <ui-btn-tip content="分析"
                          icon="icon-fenxi" />
              <ui-btn-tip content="布控"
                          icon="icon-dunpai"
                          transfer />
            </p>
          </div> -->
        </div>
        <div class="empty-card-1"
             v-for="(item, index) of 9 - (dataList.length % 9)"
             :key="index + 'demo'"></div>
      </div>
      <ui-empty v-if="dataList.length === 0 && !listLoading"></ui-empty>
      <ui-loading v-if="listLoading"></ui-loading>
      <!-- 分页 -->
      <ui-page :current="pageInfo.pageNumber"
            :total="total"
            countTotal
            :page-size="pageInfo.pageSize"
            @pageChange="pageChange"
            @pageSizeChange="pageSizeChange"> </ui-page>
    </div>
    <!-- 动态库人脸详情 -->
    <face-detail v-if="faceShow"
                 class="faceDetail"
                 ref="faceDetail"
                 @close="faceShow = false" />
    <video-detail v-show="videoShow" class="faceDetail" ref="videoDetail" @close="videoShow = false"
      @preDetial="preDetial" @nextDetail="nextDetail" />
  </section>
</template>
<script>
import searchFace from '../components/search-face'
import videoDetail from '@/components/detail/video'
import faceDetail from '@/components/detail/face'
import { queryFaceRecordSearch } from '@/api/wisdom-cloud-search'
import { addCollection, deleteMyFavorite } from '@/api/user'
import { mapMutations, mapGetters } from 'vuex'
export default {
  name: 'faceContent',
  props: {
    // 首页参数
    indexSearchData: {
      type: Object,
      default: () => { }
    },
    mapOnData: {
      type: Boolean,
      default: false
    }
  },
  components: {
    searchFace, faceDetail, videoDetail
  },
  data () {
    return {
      faceShow: false,
      videoShow: false,
      listLoading: false,
      dataList: [],
      queryParam: {},
      currentIndex: 0,
      pageInfo: {
        pageNumber: 1,
        pageSize: 20,
      },
      total: 0,
      childDataSourceVal: 2, // 子组件数据资源值
      checkAll: false,
    }
  },
  activated() {
    if(this.$route.query.deviceInfo){
        this.$nextTick(() =>{
            let deviceInfo = JSON.parse(this.$route.query.deviceInfo)
            this.$refs.searchBar.selectData([{...deviceInfo, select: true}])
        })
    }
    this.$nextTick(() =>{
        if(this.queryParam.keyWords || (this.queryParam.features && this.queryParam.features.length > 0)){
            this.searchHandle()
        }
    })
  },
  created () {
    this.$nextTick(() => {
        this.searchHandle()
    })
  },
  mounted () {
    let { videoIdentity, deviceId } = this.$route.query
    // 一人一档，实名档案跳转
    if (videoIdentity) {
      this.$refs.searchBar.queryParam.videoIdentity = videoIdentity
    }

    // 一机一档跳转
    if (deviceId) {
      this.$refs.searchBar.queryParam.devices = [deviceId]
    }
  },
  computed:{
    ...mapGetters({
        getMaxLayer: 'countCoverage/getMaxLayer',
        layerNum: 'countCoverage/getLayer',
        pointsNum: 'countCoverage/getPointsInLayer',
        getListNum: 'countCoverage/getListNum',
    }),
  },
  methods: {
    async getDataList () {
      // if (this.$store.getters['common/getPageType'] == 0) {
      //   if (!this.queryParam.keyWords && this.queryParam.features.length == 0) {
      //     this.$Message.warning('请输入关键词或上传图片已搜索')
      //     return
      //   }
      // }
      this.listLoading = true
      await queryFaceRecordSearch(this.queryParam)
        .then(res => {
          const { total, entities } = res.data
          this.total = total
          this.dataList = entities || []
        })
        .catch(err => {
          console.error(err)
        })
        .finally(() => {
          this.listLoading = false
          // this.isActivated = true
        })
    },
    searchHandle () {
        this.checkAll = false
        let queryParam = this.$refs.searchBar.queryParam
        var labelIds = [];
        let formData = {...queryParam};
        if (queryParam.labelIds && queryParam.labelIds.length > 0) {
            queryParam.labelIds.forEach(item => {

            if (item && item != undefined) {
                labelIds.push(item.id)
            }
            })
            formData.labelIds = labelIds
        }
      queryParam = { ...formData, ...this.indexSearchData, ...this.pageInfo }

      this.childDataSourceVal = queryParam.dataSource
      // 精准搜索处理
      if (this.$store.getters['common/getPageType'] == 1) {
        queryParam.similarity = queryParam.similarity / 100
      }
      this.queryParam = queryParam

      // 抓拍时段时间处理
      switch (this.queryParam.timeSlot) {
        case '近一天':
          var arr = this.getConfigDate(-1)
          this.queryParam.startDate = arr[1] + ' 00:00:00'
          this.queryParam.endDate = arr[1] + ' 23:59:59'
          break
        case '近三天':
          var arr = this.getConfigDate(-2)
          this.queryParam.startDate = arr[0] + ' 00:00:00'
          this.queryParam.endDate = arr[1] + ' 23:59:59'
          break
        case '近一周':
          var arr = this.getConfigDate(-6)
          this.queryParam.startDate = arr[0] + ' 00:00:00'
          this.queryParam.endDate = arr[1] + ' 23:59:59'
          break
        case '自定义':
          this.queryParam.startDate = this.$dayjs(this.queryParam.timeSlotArr[0]).format('YYYY-MM-DD HH:mm:ss')
          this.queryParam.endDate = this.$dayjs(this.queryParam.timeSlotArr[1]).format('YYYY-MM-DD HH:mm:ss')
          break
        default:
          break
      }

      // 处理已选择设备
      var ids = []
      this.queryParam.selectDeviceList.forEach(item => {
        ids.push(item.deviceId)
      })
      this.queryParam.deviceIds = ids
      delete this.queryParam.selectDeviceList
      delete this.queryParam.urlList
      this.childDataSourceVal = this.queryParam.dataSource

      this.getDataList()
    },
    /**
     * 重置
     */
    resetHandle () {
      this.$nextTick(() => {
        this.queryParam = {}
        this.pageInfo.pageNumber = 1;
        this.queryParam = { ...this.pageInfo, ...this.indexSearchData }
        this.queryParam.dataSource = 2 // 资源类型
        this.queryParam.similarity = 0.6 // 资源类型
        this.childDataSourceVal = 2
        this.getDataList()
      })
    },
    // 页数改变
    pageChange (size) {
      this.pageInfo.pageNumber = size
      this.searchHandle()
    },
    // 页数量改变
    pageSizeChange (size) {
      this.pageInfo.pageNumber = 1
      this.pageInfo.pageSize = size
      this.searchHandle()
    },
    /**
     * 首页调用方法
     */
    indexSearchHandle () {
      this.pageInfo.pageNumber = 1
      this.queryParam = {
        dataSource: 2, // 资源类型
        keyWords: this.indexSearchData.keyWords
      }
      this.indexSearchData.features = []
      this.$refs.searchBar.resetHandle()
    },
    /**
     * 获取当前时间的前后N天日期
     * @param {*} num 获取前N天的数据，2：两天后， -2 两天前
     */
    getConfigDate (num) {
      var today = new Date()
      var targetday_milliseconds = today.getTime() + 1000 * 60 * 60 * 24 * num
      var targetday = new Date()
      targetday.setTime(targetday_milliseconds) //注意，这行是关键代码

      var cur_year = today.getFullYear()
      var cur_month = today.getMonth() + 1
      var cur_day = today.getDate()
      cur_month = (cur_month < 10 ? '0' : '') + cur_month
      cur_day = (cur_day < 10 ? '0' : '') + cur_day

      var year = targetday.getFullYear()
      var month = targetday.getMonth() + 1
      var day = targetday.getDate()
      month = (month < 10 ? '0' : '') + month
      day = (day < 10 ? '0' : '') + day
      return [year + '-' + month + '-' + day, cur_year + '-' + cur_month + '-' + cur_day]
    },

    faceDetailFn (row, index) {
      this.currentIndex = index
      this.videoShow = true
      this.$nextTick(() => {
        this.$refs.videoDetail.init(row)
        // this.$refs.faceDetail.init(row)
      })
    },
    /**
     * 跳转到一人一档页面
     */
    archivesPage (item) {
        if(this.$refs.searchBar.queryParam.dataSource == 1){  // 静态库
            const { href } = this.$router.resolve({
                name: 'people-archive',
                query: {
                    archiveNo: item.idCardNo,
                    source: 'people',
                    initialArchiveNo: item.idCardNo
                }
            })
            window.open(href, '_blank')
        }else{   // 动态库
            if(!item.vid){
                this.$Message.warning('档案不存在！');
                return;
            }
            const { href } = this.$router.resolve({
                name: 'video-archive',
                query: {
                    archiveNo: item.vid,
                    source: 'video',
                    initialArchiveNo: item.vid
                }
            })
            window.open(href, '_blank')
        }
    },
    checkAllHandler (val) {
      this.dataList = this.dataList.map(e => {
        return {
          ...e,
          isChecked: val
        }
      })
    },
    checkHandler (e, i) {
      this.dataList[i].isChecked = e
      this.checkAll = this.dataList.filter(e => e.isChecked).length === this.dataList.length ? true : false
    },
    ...mapMutations({
        setNum:'countCoverage/setNum',
        pointsInLayer:'countCoverage/pointsInLayer', //用于删除做标识
        setList: 'countCoverage/setList',
    }),
    dataAboveMapHandler () {
        let seleNum = this.dataList.filter(e => e.isChecked);
        if (!seleNum.length) {
            this.$Message.warning('请选择上图数据')
            return;
        }
        let newNumLayer = this.layerNum + 1;
        let newNumPoints = this.pointsNum + seleNum.length;
        if(this.getMaxLayer.maxNumberOfLayer < this.newNumLayer) {
            this.$Message.warning('已达到图层最大创建数量')
            return
        }
        if(this.getMaxLayer.maxNumberOfPointsInLayer < this.newNumPoints) {
            this.$Message.warning('已达到上图最大点位总量')
            return
        }
        let num = JSON.stringify(this.getListNum);
        this.setList(num++)
        this.setNum({'layerNum': newNumLayer, 'pointsNum': newNumPoints})
        seleNum.map(item => {
            item.delePoints = true;
            item.deleType = 'face'
        })
        let list = this.dataList.map((item, index) => {
            return {
                age:item.age,
                ageName: item.ageName,
                eyeglass: item.eyeglass,
                isCap: item.isCap,
                mask: item.mask,
                absTime: item.absTime,
                deleType: item.deleType,
                detailAddress: item.detailAddress,
                deviceId: item.deviceId,
                deviceName: item.deviceName,
                driverFlag: item.driverFlag,
                featureId: item.featureId,
                geoPoint: item.geoPoint,
                id: item.id,
                lat: item.lat,
                location: item.location,
                lon: item.lon,
                recordId: item.recordId,
                sceneImg: item.sceneImg,
                sourceId: item.sourceId,
                traitImg: item.traitImg,
                vid: item.vid,
                isChecked: item.isChecked,
                structureTime: item.structureTime,
                infoKind: item.infoKind
            }
        })
      this.$emit('dataAboveMapHandler', {
        type: 'face',
        deleIdent: 'face-' + this.getListNum,
        list: list.filter(e => e.isChecked)
      })
    },
    /**
     * 收藏
     */
    collection( data, flag ) {
      var val = this.$refs.searchBar.queryParam.dataSource
      var param = {
        favoriteObjectId: data.id,
        favoriteObjectType: val == 2 ? 5 : 13,
      }
      if(flag == 1){
        addCollection(param).then(res => {
          this.$Message.success("收藏成功");
          this.getDataList()
        })
      }else{
        deleteMyFavorite(param).then(res => {
          this.$Message.success("取消收藏成功");
          this.getDataList()
        })
      }
    },
    /**
     * 上一个
     */
    preDetial() {
      if (this.currentIndex == 0 ) {
        if (this.pageInfo.pageNumber == 1) {
          this.$Message.warning("已经是第一个了")
          return
        }else{
          this.pageChange(this.pageInfo.pageNumber-1)
          setTimeout(()=> {
            this.faceDetailFn(this.dataList[(this.dataList.length-1)], (this.dataList.length-1))
          }, 200)
        }
      }else {
        this.faceDetailFn(this.dataList[(this.currentIndex-1)], (this.currentIndex-1))
      } 

    },
    /**
     * 下一个
     */
    nextDetail() {
      if(this.currentIndex == (this.dataList.length - 1)){
        var num = this.pageInfo.pageNumber
        var size = this.pageInfo.pageSize
        var total = this.total
        if (total <= num*size) {
          this.$Message.warning("已经是最后一个了")
          return
        }else{
          this.pageChange(this.pageInfo.pageNumber+1)
          setTimeout(()=> {
            this.faceDetailFn(this.dataList[0], 0)
          }, 200)
        }
      }else{
        this.faceDetailFn(this.dataList[(this.currentIndex+1)], (this.currentIndex+1))
      }
    },
  }
}
</script>
<style lang="less" scoped>
@import 'style/index';

.faceDetail {
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 999;
}
.data-above {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 10px;
}
.check-box {
  position: absolute;
  left: 4px;
  top: 4px;
  display: none;
  z-index: 10;
}
.box-1 {
  &:hover {
    border-color: #2c86f8;
    .img-content {
      .check-box {
        display: inline-block;
      }
    }
  }
}
.isChecked{
    // border-color: #2C86F8!important;
    .img-content{
      .check-box{
        display: inline-block;
      }
    }
}

.idCardNo {
  background: rgba(0,0,0,0.7);
  height: 26px;
  line-height: 26px;
  text-align: center;
  margin-top: -26px;
  z-index: 999;
  position: absolute;
  width: 100%;
  color: #2c86f8;
  // font-size: 13px;
  font-weight: 600;
}
</style>
