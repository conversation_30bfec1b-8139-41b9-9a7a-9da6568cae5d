import request from "@/libs/request";
import { manager, service } from "./Microservice";
// 资源目录
// 资源查询列表
export function getResourcePageList(data) {
  return request({
    url: manager + "/resource/pageList",
    method: "POST",
    data: data,
  });
}
// 获取所有资源
export function getResourceAll(data) {
  return request({
    url: manager + "/resource/resourceAll",
    method: "POST",
  });
}
// 资源配置
export function resourceConfigur(data) {
  return request({
    url: manager + "/resource/resourceConfigur",
    method: "POST",
    data: data,
  });
}
// 资源编辑
export function updateResource(data) {
  return request({
    url: manager + "/resource/updateResource",
    method: "POST",
    data: data,
  });
}
// 资源新增
export function addResource(data) {
  return request({
    url: manager + "/resource/addResource",
    method: "POST",
    data: data,
  });
}
// 资源删除
export function removeResource(data) {
  return request({
    url: manager + "/resource/removeResource",
    method: "POST",
    data: data,
  });
}
// 解析上传的excel文件
export function parseExcel(file) {
  return request({
    url: manager + "/resource/uploadExcel",
    method: "POST",
    headers: {
      "Content-Type": "multipart/form-data",
    },
    data: file,
  });
}
// 根据上传的excel文件创建资源
export function saveResource(data) {
  return request({
    url: manager + "/resource/saveResource",
    method: "POST",
    data: data,
  });
}
// 导出资源
export function exportResource(data) {
  return request({
    url: manager + "/resource/exportResource",
    method: "POST",
    data: data,
  });
}
// 查询指定资源所有数据项
export function queryResItems(resourceId) {
  return request({
    url: manager + "/resource/queryResItems/" + resourceId,
    method: "GET",
  });
}
// 新增数据项
export function addResItems(data) {
  return request({
    url: manager + "/resource/addResItems",
    method: "POST",
    data: data,
  });
}
// 资源配置查询字段
export function queryResourceConfigur(data) {
  return request({
    url: manager + "/resource/queryResourceConfigur",
    method: "GET",
    params: data,
  });
}
// 资源数据预览
export function dataPreview(resourceId) {
  return request({
    url: `${manager}/resource/dataPreview?resourceId=${resourceId}`,
    method: "GET",
  });
}
// 导入资源数据
export function importResourceData(data) {
  return request({
    url: `${manager}/resource/importResourceData?resourceId=${data.resourceId}`,
    method: "POST",
    headers: {
      "Content-Type": "multipart/form-data",
    },
    data: data.file,
  });
}
// 资源生成数据查询字段
export function queryResourceField(resourceId) {
  return request({
    url: manager + "/resource/queryData/" + resourceId,
    method: "GET",
  });
}
// 资源生成数据
export function resourceField(data) {
  return request({
    url: manager + "/resource/generateData",
    method: "POST",
    data: data,
  });
}
// 目录列表
export function getConfigurPageList(data) {
  return request({
    url: manager + "/catalog/resourceConfigurPageList",
    method: "GET",
    params: data,
  });
}
// 目录树
export function getSelectCatalogList(data) {
  return request({
    url: manager + "/catalog/selectCatalog",
    method: "POST",
    data: data,
  });
}
// 新增目录
export function addCatalog(data) {
  return request({
    url: manager + "/catalog/addCatalog",
    method: "POST",
    data: data,
  });
}
// 删除目录
export function removeCatalog(id) {
  return request({
    url: manager + "/catalog/removeCatalog?id=" + id,
    method: "DELETE",
  });
}
// 编辑目录
export function updateCatalog(data) {
  return request({
    url: manager + "/catalog/updateCatalog",
    method: "POST",
    data: data,
  });
}
// 对应字典
export function getDictTypeList() {
  return request({
    url: service + "/dictType/list",
    method: "get",
  });
}

// 获取已添加管理资源
export function getManageResourceList(data) {
  return request({
    url: manager + "/resource/getManageResourceList",
    method: "POST",
    data: data,
  });
}

// 查看资源数据
export function viewDataApi(params) {
  return request({
    url: manager + "/resource/dataPreview",
    method: "get",
    params,
  });
}

/**
 * 人员表管理
 */
// 人员静态库列表
export function queryDeviceInfoPageList(data) {
  return request({
    url: manager + "/lib/facePerson/queryFaceLibPersonPageList",
    method: "POST",
    data: data,
  });
}
// 人员静态库新增
export function addFaceLibPersonInfo(data) {
  return request({
    url: manager + "/lib/facePerson/addFaceLibPersonInfo",
    method: "POST",
    data: data,
  });
}
// 人员静态库编辑
export function motifyFaceLibPersonInfo(data) {
  return request({
    url: manager + "/lib/facePerson/motifyFaceLibPersonInfo",
    method: "POST",
    data: data,
  });
}
// 人员静态库删除
export function deleteFaceLibPersonInfo(id) {
  return request({
    url: manager + "/lib/facePerson/deleteFaceLibPersonInfo/" + id,
    method: "delete",
  });
}
// 多选删除
export function batchDeleteFaceLibPerson(data) {
  return request({
    url: manager + "/lib/facePerson/batchDeleteFaceLibPerson",
    method: "delete",
    data: data,
  });
}
// 人员静态库-导入
export function getUploadZipPersonUrl() {
  return manager + "/lib/facePerson/uploadZipPersonFiles";
}
export function uploadZipPersonFiles(data) {
  return request({
    url: getUploadZipPersonUrl(),
    method: "POST",
    data: data,
  });
}
// 人员静态库-导出
export function doExportZipPersonFiles(data) {
  return request({
    url: manager + "/lib/facePerson/doExportZipPersonFiles",
    method: "POST",
    data: data,
  });
}
