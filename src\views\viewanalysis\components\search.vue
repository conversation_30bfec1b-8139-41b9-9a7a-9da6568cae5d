<template>
  <div class="search">
    <Form ref="form" :model="formData" class="form" inline>
      <FormItem label="任务名称:" prop="jobName" class="search-input">
        <Input placeholder="请输入" clearable v-model="formData.jobName" maxlength="50" />
      </FormItem>
      <FormItem v-if="subTaskType == 'real' || subTaskType == 'his'" label="设备名称:" prop="deviceName" class="search-input">
        <Input placeholder="请输入" clearable v-model="formData.deviceName" maxlength="50" />
      </FormItem>
      <FormItem v-else label="文件名称:" prop="fileName" class="search-input">
        <Input placeholder="请输入" clearable v-model="formData.fileName" maxlength="50" />
      </FormItem>
      <FormItem label="解析类型:" prop="structureType" class="search-input">
        <Select v-model="formData.structureType" clearable>
          <Option v-for="item in structDataTypeList" :value="item.dataKey" :key="item.dataKey" placeholder="请选择">{{ item.dataValue }}</Option>
        </Select>
      </FormItem>
      <div class="flex-b">
        <div>
          <FormItem label="任务状态:" prop="taskStatus" class="search-input">
            <Select v-model="formData.taskStatus" clearable>
              <Option v-for="item in statusList" :value="item.dataKey" :key="item.dataKey" placeholder="请选择">{{ item.dataValue }}</Option>
            </Select>
          </FormItem>
          <FormItem label="创建时间:" prop="daterange" class="search-input">
            <div class="datepicker-wrap">
              <hl-daterange v-model="daterange[0]" key="1"></hl-daterange>
              <div class="line"></div>
              <hl-daterange v-model="daterange[1]" key="2"></hl-daterange>
            </div>
          </FormItem>
          <FormItem label="创建人员:" prop="userName" class="search-input">
            <Input placeholder="请输入" clearable v-model="formData.userName" maxlength="50" />
          </FormItem>
        </div>
        <FormItem class="btn-group">
          <Button type="primary" @click="startSearch">查询</Button>
          <Button @click="resetHandle">重置</Button>
        </FormItem>
      </div>
    </Form>
  </div>
</template>
<script>
import { mapGetters } from 'vuex'
import hlDaterange from '@/components/hl-daterange/index.vue';
export default {
  components: {
    hlDaterange
  },
  props: {
    subTaskType: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      formData: {
        jobName: '',
        deviceName: '',
        fileName: '',
        structureType: '',
        taskStatus: '',
        userName: '',
        startTime: '',
        endTime: ''
      },
      daterange: []
    }
  },
  computed: {
    ...mapGetters({
      structDataTypeList: 'dictionary/getStructDataType', //解析类型
      structTaskStatusList: 'dictionary/getStructTaskStatus', //实时视频解析任务状态
      structHistroytaskStatusList: 'dictionary/getStructHistroytaskStatus', //历史视频解析任务状态
      filestrucureTaskStatusList: 'dictionary/getFilestrucureTaskStatus', //文件解析任务状态
    }),
    statusList() {
      return this.subTaskType == 'real' ? this.structTaskStatusList : this.subTaskType == 'his' ? this.structHistroytaskStatusList : this.filestrucureTaskStatusList
    }
  },
  created() {},
  methods: {
    // 查询
    startSearch() {
      let {jobName, deviceName, fileName, structureType, taskStatus, userName} = {...this.formData}
      this.$emit('searchForm', {
        jobName: jobName ? jobName : undefined,
        deviceName: deviceName ? deviceName : undefined,
        fileName: fileName ? fileName : undefined,
        structureType: structureType ? structureType :undefined,
        taskStatus: taskStatus ? taskStatus : undefined,
        userName: userName ? userName : undefined,
        startTime:this.daterange[0] ? this.$dayjs(this.daterange[0]).valueOf() : this.daterange[0],
        endTime:this.daterange[1] ? this.$dayjs(this.daterange[1]).valueOf() : this.daterange[1]
      })
    },
    // 重置
    resetHandle() {
      this.$refs.form.resetFields()
      this.daterange = []
      this.startSearch()
    }
  }
}
</script>
<style lang="less" scoped>
.search {
  border-bottom: 1px solid #d3d7de;
  width: 100%;
  .form {
    width: 100%;
    .search-input {
      display: inline-flex;
      margin-right: 30px;
      /deep/ .ivu-input {
        width: 200px;
      }
      /deep/.ivu-select {
        width: 200px;
      }
    }
    /deep/.ivu-form-item {
      margin-bottom: 16px;
    }
    /deep/ .ivu-form-item-label {
      white-space: nowrap;
      width: 72px;
      text-align-last: justify;
      text-align: justify;
      text-justify: distribute-all-lines; // 这行必加，兼容ie浏览器
    }
    .label-text {
      /deep/.ivu-form-item-label {
        text-align-last: justify;
        text-align: justify;
        text-justify: distribute-all-lines !important; // 这行必加，兼容ie浏览器
        width: 72px;
        white-space: nowrap;
      }
    }
    .btn-group {
      margin-right: 0;
    }
    .datepicker-wrap{
      display: flex;
      align-items: center;
      .line{
          height: 3px;
          width: 20px;
          background: #d2d8db;
          margin: 0 5px;
      }
      .hl-btn{
          color: #2C86F8;
          margin-left: 10px;
          cursor: pointer;
      }
      margin-right: 10px;
    }
  }
}
</style>
