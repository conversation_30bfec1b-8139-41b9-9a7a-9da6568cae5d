import global from '@/util/global';
import { qualifiedColorConfig } from '@/views/governanceevaluation/evaluationoResult/util/CommonEnum/ReviewParticular.js';
export const iconStaticsList = [
  {
    name: '检测地市数量:',
    count: '0',
    countStyle: {
      color: 'var(--color-bluish-green-text)',
    },
    iconName: 'icon-shipinjiankongjianshezongliang',
    fileName: 'detectionCityOrCountyCount',
  },
  {
    name: '达标地市数量:',
    count: '0',
    countStyle: {
      color: '#DE990F',
    },
    iconName: 'icon-shipinjiankongjianshezongliang',
    fileName: 'detectionCityOrCountyQualifiedCount',
  },
  {
    name: '不达标地市数量:',
    count: '0',
    countStyle: {
      color: '#269F26',
    },
    iconName: 'icon-shipinjiankongjianshezongliang',
    fileName: 'detectionCityOrCountyUnQualifiedCount',
  },
  {
    name: '治理前可调阅率:',
    count: '0',
    countStyle: {
      color: '#269F26',
    },
    iconName: 'icon-shipinjiankongjianshezongliang',
    fileName: 'beforeRate',
    type: 'percent', // 百分比
  },
  {
    name: '治理后可调阅率:',
    count: '0',
    countStyle: {
      color: '#269F26',
    },
    iconName: 'icon-shipinjiankongjianshezongliang',
    fileName: 'afterRate',
    type: 'percent', // 百分比
  },
  {
    name: '字幕标注合规率提升率',
    count: '0',
    countStyle: {
      color: '#269F26',
    },
    iconName: 'icon-shipinjiankongjianshezongliang',
    fileName: 'resultValueFormat',
    type: 'percent', // 百分比
  },
];
export const normalFormData = [
  {
    type: 'input',
    key: 'deviceId',
    label: '设备编码',
  },
  {
    type: 'input',
    key: 'deviceName',
    label: '设备名称',
  },
  {
    type: 'select',
    key: 'outcome',
    label: '检测结果',
    placeholder: '请选择检测结果',
    options: Object.keys(qualifiedColorConfig).map((key) => {
      return {
        value: key,
        label: qualifiedColorConfig[key].dataValue,
      };
    }),
  },
  {
    type: 'select',
    key: 'errorCodes',
    // causeErrors
    label: '不合格原因',
    selectMutiple: true,
    placeholder: '请选择异常原因',
    options: [],
  },
];

// 字幕标注合规率
export const tableColumns = [
  {
    type: 'index',
    width: 70,
    title: '序号',
    align: 'center',
  },
  {
    title: `${global.filedEnum.deviceId}`,
    key: 'deviceId',
    minWidth: 200,
    tooltip: true,
  },
  {
    title: `${global.filedEnum.deviceName}`,
    key: 'deviceName',
    minWidth: 200,
    tooltip: true,
  },
  {
    title: '组织机构',
    key: 'orgName',
    minWidth: 120,
    tooltip: true,
  },

  {
    title: '监控点位类型',
    key: 'sbdwlxText',
    align: 'left',
    tooltip: true,
    minWidth: 130,
  },
  {
    title: '设备状态',
    slot: 'phyStatus',
    align: 'left',
    tooltip: true,
    minWidth: 120,
  },
  {
    title: '检测方式',
    slot: 'detectionMode',
    minWidth: 150,
    tooltip: true,
  },
  {
    title: '检测结果',
    slot: 'outcome',
    minWidth: 150,
    tooltip: true,
  },
  {
    title: '原因',
    key: 'errorCodeName',
    minWidth: 150,
    tooltip: true,
  },
  {
    title: '备注',
    key: 'reason',
    minWidth: 150,
    tooltip: true,
  },
  {
    title: '检测时间',
    key: 'videoStartTime',
    minWidth: 150,
    tooltip: true,
  },
  {
    minWidth: 150,
    title: '设备标签',
    slot: 'tagNames',
  },
  {
    title: '操作',
    slot: 'option',
    align: 'center',
    tooltip: true,
    minWidth: 160,
    fixed: 'right',
    className: 'table-action-padding', // 操作栏列-单元格padding设置
  },
];
