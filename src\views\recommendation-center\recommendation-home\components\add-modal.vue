<template>
  <ui-modal
    v-model="visible"
    :title="type === 1 ? '新增' : '编辑'"
    :r-width="600"
    @onOk="comfirmHandle"
  >
    <div class="formBox">
      <Form ref="form" :label-width="120">
        <FormItem label="推荐类型">
          <span>{{ model.name }}</span>
        </FormItem>
      </Form>
      <div class="add-modal-box">
        <Form
          ref="paramsForm"
          :model="taskParam"
          :rules="paramRules"
          :label-width="120"
        >
          <FormItem
            :prop="item.paramKey"
            :label="item.label"
            v-for="item in paramSetting"
            :key="item.paramKey"
          >
            <!-- 日期 -->
            <hl-daterange
              v-if="item.type == 'datetime'"
              v-model="taskParam[item.paramKey]"
              key="1"
            ></hl-daterange>
            <!-- 时间段 -->
            <div class="flex" v-else-if="item.type == 'timeRange'">
              <TimePicker
                v-model="taskParam[item.paramKey]"
                format="HH:mm:ss"
                placeholder="出现时段"
                transfer
                :clearable="false"
              ></TimePicker>
              <span class="ml-5 mr-5">-</span>
              <TimePicker
                v-model="taskParam[item.paramKey2]"
                format="HH:mm:ss"
                placeholder="出现时段"
                transfer
                :clearable="false"
              ></TimePicker>
            </div>
            <!-- 年龄段 -->
            <div
              class="flex"
              style="align-items: center"
              v-else-if="item.type == 'ageRange'"
            >
              <span class="mr-5">{{ taskParam.ageupdown[0] }}</span>
              <Slider v-model="taskParam.ageupdown" range :max="60"></Slider>
              <span>{{ taskParam.ageupdown[1] }}</span>
            </div>
            <!-- 设备 -->
            <div
              v-else-if="item.type == 'selectDevice'"
              class="select-tag-button"
              @click="selectDevice()"
            >
              选择设备/已选（{{ taskParam.selectDeviceList.length }}）
            </div>
            <!-- 非机动车违法设备 -->
            <div
              v-else-if="item.type == 'selectNonMotorIllegalDevice'"
              class="select-tag-button"
              @click="selectNonMotorIllegalDevice()"
            >
              选择设备/已选（{{ taskParam.selectDeviceList.length }}）
            </div>
            <!-- 数字 -->
            <InputNumber
              v-else-if="item.type == 'inputNumber'"
              :min="1"
              v-model="taskParam[item.paramKey]"
            ></InputNumber>
            <!-- 车牌颜色 -->
            <ui-tag-select
              v-else-if="item.type == 'plateColor'"
              ref="bodyColor"
              :value="taskParam[item.paramKey]"
              @input="
                (e) => {
                  taskParam[item.paramKey] = e;
                }
              "
            >
              <ui-tag-select-option
                v-for="(item, $index) in licensePlateColorList"
                :key="$index"
                effect="dark"
                :name="item.dataKey"
              >
                <div
                  v-if="licensePlateColorArray[item.dataKey]"
                  :style="{
                    borderColor:
                      licensePlateColorArray[item.dataKey].borderColor,
                  }"
                  class="plain-tag-color"
                >
                  <div
                    :style="licensePlateColorArray[item.dataKey].style"
                  ></div>
                </div>
              </ui-tag-select-option>
            </ui-tag-select>
            <!-- 车辆颜色 -->
            <Select
              v-else-if="item.type == 'vehicleColor'"
              v-model="taskParam[item.paramKey]"
              placeholder="请选择"
            >
              <Option
                v-for="(item, $index) in bodyColorList"
                :key="$index"
                :value="item.dataKey"
                >{{ item.dataValue }}</Option
              >
            </Select>
            <!-- 车辆类型 -->
            <Select
              v-else-if="item.type == 'vehicleType'"
              v-model="taskParam[item.paramKey]"
              placeholder="请选择"
            >
              <Option
                v-for="(item, $index) in vehicleClassTypeList"
                :key="$index"
                :value="item.dataKey"
                >{{ item.dataValue }}</Option
              >
            </Select>
            <!-- 车辆品牌 -->
            <Select
              v-else-if="item.type == 'vehicleBrand'"
              v-model="taskParam[item.paramKey]"
              placeholder="请选择"
            >
              <Option
                v-for="(item, $index) in vehicleBrandList"
                :key="$index"
                :value="item.dataKey"
                >{{ item.dataValue }}</Option
              >
            </Select>
            <!-- 车牌类型 -->
            <Select
              v-else-if="item.type == 'plateClass'"
              v-model="taskParam[item.paramKey]"
              placeholder="请选择"
            >
              <Option
                v-for="(item, $index) in plateClassList"
                :key="$index"
                :value="item.dataKey"
                >{{ item.dataValue }}</Option
              >
            </Select>
            <!-- 数据总量状态 -->
            <Select
              v-else-if="item.type == 'selectStatus'"
              v-model="taskParam[item.paramKey]"
              placeholder="请选择"
              multiple
            >
              <Option
                v-for="(item, $index) in dataStatusArrayList"
                :key="$index"
                :value="item.dataKey"
                >{{ item.dataValue }}</Option
              >
            </Select>
            <!-- 数据有无状态 -->
            <Select
              v-else-if="item.type == 'selectHaveDataStatus'"
              v-model="taskParam[item.paramKey]"
              placeholder="请选择"
              multiple
            >
              <Option
                v-for="(item, $index) in haveDataStatusList"
                :key="$index"
                :value="item.dataKey"
                >{{ item.dataValue }}</Option
              >
            </Select>
            <!-- 数据延迟状态 -->
            <Select
              v-else-if="item.type == 'selectDelayStatus'"
              v-model="taskParam[item.paramKey]"
              placeholder="请选择"
              multiple
            >
              <Option
                v-for="(item, $index) in delayStatusList"
                :key="$index"
                :value="item.dataKey"
                >{{ item.dataValue }}</Option
              >
            </Select>
            <!-- 昼伏夜出-时段 -->
            <div
              class="search_content flex"
              v-else-if="item.type == 'selectDayNightTime'"
            >
              <Select v-model="taskParam[item.paramKey]" placeholder="请选择">
                <Option
                  v-for="(item, $index) in startTimeList"
                  :key="$index"
                  :value="item"
                  >{{ item }}</Option
                >
              </Select>
              <span class="ml-5 mr-5">-</span>
              <Select v-model="taskParam[item.paramKey2]" placeholder="请选择">
                <Option
                  v-for="(item, $index) in endTimeList"
                  :key="$index"
                  :value="item"
                  >{{ item }}</Option
                >
              </Select>
            </div>
            <!-- 时间范围 -->
            <template v-else-if="item.type == 'daySum'">
              近&nbsp;
              <InputNumber
                :min="1"
                v-model="taskParam[item.paramKey]"
              ></InputNumber>
              &nbsp;天
            </template>
            <!-- 布控库 -->
            <Select
              v-else-if="item.type == 'alarmLib'"
              v-model="taskParam[item.paramKey]"
              placeholder="请选择"
              filterable
              transfer
              :max-tag-count="10"
            >
              <Option
                :value="item.taskId"
                v-for="item in libList"
                :key="item.taskId"
                >{{ item.taskName }}</Option
              >
            </Select>
            <!-- 输入 -->
            <Input
              v-else
              v-model="taskParam[item.paramKey]"
              :placeholder="'请输入' + item.label"
            />
          </FormItem>
        </Form>
      </div>
    </div>

    <select-device
      ref="selectDevice"
      :showOrganization="true"
      @selectData="selectData"
    />
    <!-- 选择非机动车违法设备 -->
    <select-nonMotorIllegal-device
      ref="selectNonMotorIllegalDevice"
      @selectData="selectNonMotorIllegalData"
    />
  </ui-modal>
</template>
<script>
import { mapActions, mapGetters } from "vuex";
import { licensePlateColorArray } from "@/libs/system";
import { tabList } from "../tabList.js";
import { recommendAdd, recommendUpdate } from "@/api/recommend.js";
import hlDaterange from "@/components/hl-daterange/index.vue";
import { taskPageList } from "@/api/target-control";
import selectNonMotorIllegalDevice from "@/components/select-modal/select-nonMotorIllegal-device.vue";
export default {
  components: {
    hlDaterange,
    selectNonMotorIllegalDevice,
  },
  data() {
    return {
      type: 1,
      visible: false,
      tabList: tabList,
      taskParam: {
        selectDeviceList: [],
        ageupdown: [18, 40],
        timePeriodSt: "18:00:00", //
        timePeriodEt: "23:00:00",
      },
      formRules: {},
      paramRules: {},
      paramSetting: [],
      model: {},
      licensePlateColorArray, // 车牌颜色
      startTimeList: ["21:00", "22:00", "23:00", "24:00"],
      endTimeList: ["03:00", "04:00", "05:00", "06:00"],
      libList: [],
    };
  },
  computed: {
    ...mapGetters({
      bodyColorList: "dictionary/getBodyColorList", //车辆颜色
      licensePlateColorList: "dictionary/getLicensePlateColorList", // 车牌颜色
      vehicleClassTypeList: "dictionary/getVehicleTypeList", //车辆类型
      vehicleBrandList: "dictionary/getVehicleBrandList", // 车辆品牌
      plateClassList: "dictionary/getPlateClassList", // 车牌类型(枚举精准检索)
      haveDataStatusList: "dictionary/getHaveDataStatusList", // 数据有无状态
      delayStatusList: "dictionary/getDelayStatusList", //数据延迟状态
      dataStatusArrayList: "dictionary/getDataStatusList", // 数据状态
    }),
  },
  async created() {
    await this.getDictData();
    this.getLibList();
  },
  methods: {
    ...mapActions({
      getDictData: "dictionary/getDictAllData",
    }),
    getLibList() {
      let params = {
        compareType: 1,
        taskType: "2",
        pageNumber: 1,
        pageSize: 999,
      };
      taskPageList(params).then((res) => {
        this.libList = res.data.entities;
      });
    },
    show(val, item) {
      this.type = val;
      this.model = item;
      if (val == 1) {
        this.paramSetting = item.paramSetting;
        this.taskParam = {
          selectDeviceList: [],
          ageupdown: [18, 40],
          timePeriodSt: "18:00:00", //
          timePeriodEt: "23:00:00",
        };
        if (!this.paramSetting.length) {
          // 无需参数直接保存
          this.comfirmHandle();
          return;
        }
      } else {
        this.paramSetting = item.paramSetting;
        try {
          this.taskParam = JSON.parse(item.task.taskParam);
        } catch {
          this.taskParam = {};
        }
        if (!this.paramSetting.length) {
          // 没有参数
          this.$Message.info("该推荐无需参数");
          return;
        }
        if (!this.taskParam.selectDeviceList)
          this.taskParam.selectDeviceList = [];
        this.taskParam.ageupdown = [18, 40];
        this.taskParam.timePeriodSt = "18:00:00"; //
        this.taskParam.timePeriodEt = "23:00:00";
      }
      this.visible = true;
    },
    /**
     * 选择设备
     */
    selectDevice() {
      this.$refs.selectDevice.show(this.taskParam.selectDeviceList, "");
    },
    selectData(arr) {
      this.taskParam.selectDeviceList = arr.map(
        ({ deviceId, deviceGbId, deviceName }) => {
          return { deviceId, deviceGbId, deviceName, select: true };
        }
      );
      this.$forceUpdate();
    },
    /**
     * 选择非机动车违法设备
     */
    selectNonMotorIllegalDevice() {
      this.$refs.selectNonMotorIllegalDevice.show(
        this.taskParam.selectDeviceList,
        ""
      );
    },
    selectNonMotorIllegalData(arr) {
      this.taskParam.selectDeviceList = arr.map(
        ({ deviceId, deviceGbId, name }) => {
          return { deviceId, deviceGbId, name, select: true };
        }
      );
      this.$forceUpdate();
    },
    comfirmHandle() {
      let taskParam = { ...this.taskParam };
      taskParam.deviceGbIdList = taskParam.selectDeviceList.map(
        (v) => v.deviceGbId
      );
      taskParam.ageMin = taskParam.ageupdown[0];
      taskParam.ageMax = taskParam.ageupdown[1];
      // 新增
      if (this.type === 1) {
        let param = this.model.defaultParam
          ? { ...taskParam, ...this.model.defaultParam }
          : taskParam;
        recommendAdd({
          taskName: this.model.name,
          taskType: this.model.type,
          taskParam: JSON.stringify(param),
        }).then((res) => {
          if (res.code == 200) {
            this.$Message.success("新增成功");
            this.visible = false;
            this.$emit("updated");
          }
        });
      } else {
        let param = this.model.defaultParam
          ? { ...taskParam, ...this.model.defaultParam }
          : taskParam;
        recommendUpdate({
          id: this.model.task.id,
          taskName: this.model.name,
          taskType: this.model.type,
          taskParam: JSON.stringify(param),
        }).then((res) => {
          if (res.code == 200) {
            this.$Message.success("修改成功");
            this.visible = false;
            this.$emit("updated");
          }
        });
      }
    },
  },
};
</script>
<style lang="less" scoped>
.formBox {
  padding: 20px 30px 0 0;
  min-height: 200px;

  .add-modal-box {
    /deep/ .ivu-icon-ios-close {
      font-size: 12px !important;
      top: 5px !important;
    }
  }
}
</style>
