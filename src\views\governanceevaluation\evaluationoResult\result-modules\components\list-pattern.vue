<template>
  <div class="auto-fill">
    <dynamic-condition
      form-item-size="width-sm"
      :form-item-data="formItemData"
      :form-data="formData"
      @search="startSearch"
      @reset="startSearch"
    >
      <template #otherButton>
        <slot name="otherButton"></slot>
      </template>
    </dynamic-condition>
    <ui-table class="ui-table auto-fill" :loading="loading" :table-columns="tableColumns" :table-data="tableData">
      <template v-for="item in tableColumns" :slot="item.slot" slot-scope="{ row, index }">
        <slot :name="item.slot" :row="row" :item="item" :index="index"></slot>
      </template>
    </ui-table>
    <ui-page
      class="page menu-content-background"
      :page-data="pageData"
      @changePage="handlePage"
      @changePageSize="handlePageSize"
    >
    </ui-page>
  </div>
</template>

<script>
export default {
  name: 'ListPattern',
  props: {
    formItemData: {
      type: Array,
      default: () => [],
    },
    formData: {
      type: Object,
      default: () => {},
    },
    tableColumns: {
      type: Array,
      default: () => [],
    },
    resultData: {
      type: Object,
      default: () => {},
    },
    loading: {},
    //需要单独的total
    needSeparateTotal: {
      type: Boolean,
      default: false,
    },
    dataTotalCount: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      searchData: {},
      tableData: [],
      pageData: {
        pageNum: 1,
        pageSize: 20,
        totalCount: 0,
      },
    };
  },
  mounted() {
    // this.getTableList()
  },
  methods: {
    startSearch(searchData) {
      this.searchData = searchData;
      this.pageData.pageNum = 1;
      this.getTableList();
    },
    /**
     * 是否需要单独调接口查询总数
     */
    getTableList(isSearchTotal = true) {
      let params = {
        searchData: this.searchData,
        pageData: this.pageData,
        isSearchTotal,
      };
      this.$emit('startSearch', params);
    },
    handlePage(val) {
      this.pageData.pageNum = val;
      this.getTableList(false);
    },
    handlePageSize(val) {
      this.pageData.pageNum = 1;
      this.pageData.pageSize = val;
      this.getTableList(false);
    },
  },
  watch: {
    resultData: {
      handler(val) {
        if (val) {
          this.tableData = val.entities || [];
          if (!this.needSeparateTotal) {
            this.pageData.totalCount = val.total;
          }
        }
      },
      immediate: true,
    },
    dataTotalCount(val) {
      if (!this.needSeparateTotal) {
        return;
      }
      this.pageData.totalCount = val;
    },
  },
  components: {
    DynamicCondition: require('@/views/governanceevaluation/evaluationoResult/components/dynamic-condition.vue')
      .default,
    UiTable: require('@/components/ui-table.vue').default,
  },
};
</script>

<style scoped></style>
