<template>
  <ui-modal
    v-model="visible"
    :title="!isEdit ? '新增' : '编辑'"
    :r-width="720"
    @onOk="comfirmHandle"
  >
    <Form
      ref="staticLibraryForm"
      :model="staticLibraryForm"
      :rules="ruleInline"
      :label-width="120"
    >
      <FormItem prop="libName" label="名称">
        <Input
          v-model="staticLibraryForm.libName"
          :maxlength="20"
          placeholder="请输入名称"
        />
      </FormItem>
      <FormItem label="是否用于置信" v-show="this.libraryType == '1'">
        <RadioGroup v-model="staticLibraryForm.archiveStatus">
          <Radio label="1">是</Radio>
          <Radio label="0">否</Radio>
        </RadioGroup>
      </FormItem>
      <FormItem label="是否用于布控">
        <RadioGroup v-model="staticLibraryForm.controlStatus">
          <Radio label="1" :disabled="isEdit != 1">是</Radio>
          <Radio label="0" :disabled="isEdit != 1">否</Radio>
        </RadioGroup>
      </FormItem>
      <FormItem label="业务库类型" v-show="this.libraryType == '1'">
        <Select
          v-model="staticLibraryForm.bizType"
          placeholder="选择业务库类型"
          transfer
          multiple
          :disabled="isEdit != 1"
          class="select-box"
        >
          <Option
            v-for="item in faceLibBizList"
            :key="item.id"
            :value="item.id"
            >{{ item.bizTypeName }}</Option
          >
        </Select>
      </FormItem>
      <FormItem label="有效期:" class="form-item-time" prop="timeType">
        <div>
          <RadioGroup
            v-model="staticLibraryForm.timeType"
            @on-change="handleRadioChange"
          >
            <Radio label="0">永久</Radio>
            <Radio label="1">自定义</Radio>
          </RadioGroup>
          <DatePicker
            style="width: 330px"
            v-if="staticLibraryForm.timeType == 1"
            v-model="staticLibraryForm.timeSlotArr"
            type="datetimerange"
            @on-change="dateChange"
            format="yyyy-MM-dd HH:mm:ss"
            placeholder="请选择自定义时间段"
            transfer
          >
          </DatePicker>
        </div>
      </FormItem>
      <FormItem prop="remark" label="备注">
        <Input
          v-model="staticLibraryForm.remark"
          :maxlength="200"
          :rows="4"
          type="textarea"
          placeholder="请输入备注"
        />
      </FormItem>
    </Form>
  </ui-modal>
</template>
<script>
import { addFaceLib, updateFaceLib } from "@/api/monographic/juvenile.js";
import { getBizTypes } from "@/api/data-warehouse.js";
export default {
  props: {
    libraryType: {
      type: String,
      default: "1",
    },
  },
  data() {
    return {
      isEdit: 1,
      visible: false,
      staticLibraryForm: {
        archiveStatus: "1",
        controlStatus: "1",
        libSource: "2",
        libName: "",
        remark: "",
        timeType: "0",
        timeSlotArr: [],
        bizType: [],
      },
      ruleInline: {
        libName: [{ required: true, message: "请输入名称", trigger: "blur" }],
        timeType: [
          { required: true, message: "请选择有效期", trigger: "change" },
        ],
      },
      faceLibBizList: [],
    };
  },
  computed: {
    // ...mapGetters({
    //   faceLibBizList: "dictionary/getFaceLibBizList", //人脸库业务库类型
    // }),
  },
  mounted() {
    this.getFaceLibBizList();
  },
  methods: {
    async getFaceLibBizList() {
      const res = await getBizTypes({ bizTypeIds: [] });
      this.faceLibBizList =
        res?.data?.map((el) => ({ ...el, id: el.id?.toString() })) || [];
    },
    show(val, item) {
      this.isEdit = val;
      this.visible = true;
      this.$nextTick(() => {
        this.$refs.staticLibraryForm.resetFields();
        this.staticLibraryForm.bizType = [];
        if (val === 2) {
          this.staticLibraryForm = JSON.parse(JSON.stringify(item));
          // 人像库能力
          if (this.libraryType == 1 && val === 2) {
            this.staticLibraryForm.bizType =
              this.staticLibraryForm.bizType?.split(",") || [];
          }
          // 有效期
          this.staticLibraryForm.timeType =
            this.staticLibraryForm.timeType + "";
          this.staticLibraryForm.timeSlotArr = [
            this.staticLibraryForm.beginTime,
            this.staticLibraryForm.endTime,
          ];
        }
      });
    },
    comfirmHandle() {
      this.$refs.staticLibraryForm.validate((valid) => {
        if (valid) {
          // 有效期 自定义
          let beginTime = "",
            endTime = "";
          if (this.staticLibraryForm.timeType == 1) {
            let time = this.staticLibraryForm.timeSlotArr.filter(
              (item) => item
            );
            if (time.length > 0) {
              beginTime = this.$dayjs(
                this.staticLibraryForm.timeSlotArr[0]
              ).format("YYYY-MM-DD HH:mm:ss");
              endTime = this.$dayjs(
                this.staticLibraryForm.timeSlotArr[1]
              ).format("YYYY-MM-DD HH:mm:ss");
            } else {
              beginTime = "";
              endTime = "";
              this.$Message.warning("有效期中自定义时间段未选择");
              return;
            }
          }
          // 新增
          if (this.isEdit === 1) {
            if (this.libraryType == "1") {
              var param = {
                ...this.staticLibraryForm,
                beginTime,
                endTime,
              };
              param.bizType = param.bizType?.join(",");
              delete param.timeSlotArr;
              addFaceLib(param).then((res) => {
                this.visible = false;
                this.$Message.success("新增成功");
                // this.$emit('on-change', this.staticLibraryForm)
                this.$parent.init();
              });
            }
          } else {
            //编辑
            if (this.libraryType == "1") {
              var param = {
                ...this.staticLibraryForm,
                beginTime,
                endTime,
              };
              param.bizType = param.bizType?.join(",");
              delete param.timeSlotArr;
              updateFaceLib(param).then((res) => {
                this.visible = false;
                this.$Message.success("编辑成功");
                this.$parent.init();
              });
            }
          }
        }
      });
    },
    // 改变时间类型
    handleRadioChange() {
      this.staticLibraryForm.timeSlotArr = [];
    },
    dateChange(val1, val2) {
      if (val1[1].slice(-8) === "00:00:00") {
        val1[1] = val1[1].slice(0, -8) + "23:59:59";
        // 由于this.time是标准日期，因此必须使用这样的语句
        // this.$set(this.queryParam, 'timeSlotArr', val1)
      }
      this.staticLibraryForm.timeSlotArr = val1;
      this.$forceUpdate();
    },
  },
};
</script>
<style lang="less" scoped>
.select-box {
  /deep/.ivu-select-selection {
    .ivu-icon-ios-close {
      top: 3px !important;
      font-size: 14px !important;
    }
  }
}
</style>
