<!--
    * @FileDescription: 解析专题大屏
    * @Author: H
    * @Date: 2024/04/25
 * @LastEditors: du<PERSON>en
 * @LastEditTime: 2024-09-14 14:43:06
 -->
<template>
  <div class="screen">
    <div class="screen-left">
      <card title="解析任务类型统计" class="screen-box">
        <statis-chart></statis-chart>
      </card>
      <card title="解析任务趋势统计" class="screen-box">
        <trend-chart></trend-chart>
      </card>
      <card title="解析任务状态统计" class="screen-box">
        <div class="form-box">
          <p class="warpper-title">任务类型</p>
          <div class="warpper-content">
            <Select
              v-model="formData.taskType"
              size="small"
              @on-change="handleChange"
            >
              <Option value="0">视频实时解析</Option>
              <Option value="1">历史录像解析</Option>
              <Option value="2">视频文件解析</Option>
              <Option value="3">图片序列解析</Option>
            </Select>
          </div>
        </div>
        <pie-chart
          ref="taskpieChart"
          :picData="taskList"
          :urlType="0"
        ></pie-chart>
      </card>
    </div>
    <div class="screen-main">
      <div class="content-top">
        <div class="content-box-right view-box">
          <div class="title-img bigtitle title-img-big">解析任务排名top3</div>
          <div class="ranking" v-if="taskList.length">
            <div class="ranking-no2">
              <img :src="getImgUrl('icon-no2.png')" alt="" />
              <p class="ranking-name">{{ tasklist[1].projectName }}</p>
              <count-to
                :start-val="0"
                :end-val="200234"
                :duration="1000"
                class="ranking-num"
              ></count-to>
            </div>
            <div class="ranking-no1">
              <img :src="getImgUrl('icon-no1.png')" alt="" />
              <p class="ranking-name">{{ tasklist[0].projectName }}</p>
              <count-to
                :start-val="0"
                :end-val="323234"
                :duration="1000"
                class="ranking-num"
              ></count-to>
            </div>
            <div class="ranking-no3">
              <img :src="getImgUrl('icon-no3.png')" alt="" />
              <p class="ranking-name">{{ tasklist[2].projectName }}</p>
              <count-to
                :start-val="0"
                :end-val="123234"
                :duration="1000"
                class="ranking-num"
              ></count-to>
            </div>
          </div>
        </div>
        <div class="view-data view-box">
          <div class="title-img bigtitle">价值数据转化率</div>
          <div class="view-box-contnet">
            <div class="box-content-left">
              <div class="content-left-top">
                <p class="box-title">价值数据总量</p>
                <count-to
                  :start-val="0"
                  :end-val="parseDataConver.total"
                  :duration="1000"
                  class="bank-li-total dinpro"
                ></count-to>
              </div>
              <div class="content-left-bottom">
                <p class="box-title">抓拍数据总量</p>
                <count-to
                  :start-val="0"
                  :end-val="parseDataConver.captureTotal"
                  :duration="1000"
                  class="bank-li-total dinpro"
                ></count-to>
              </div>
            </div>
            <div class="box-content-right">
              <div class="data-percent">
                {{ parseDataConver.conversionRate }}%
              </div>
              <p>战果转化率</p>
            </div>
          </div>
        </div>
      </div>
      <map-chart ref="mapChart" class="map-chart"></map-chart>
    </div>
    <div class="screen-right">
      <card title="价值数据有效率" class="screen-box">
        <pie-chart
          ref="pieChart"
          :picData="costList"
          title="解析数据总量"
          :totalCount="efficiencyTotal"
          :urlType="0"
        ></pie-chart>
      </card>
      <card title="实时解析结果展示" class="screen-bigbox">
        <div class="realtime" v-for="(ite, ind) in realList" :key="ind">
          <div class="realtime-title">
            <div class="realtime-circle"></div>
            <p>{{ ite.typename }}</p>
          </div>
          <ul class="realtime-photo">
            <li
              class="realtime-list"
              :class="{ 'realtime-list-active': realIndex == index }"
              @click="handleDetails(item, index, ind)"
              v-for="(item, index) in ite.list"
              :key="index"
            >
              <img :src="item.traitImg" alt="" />
            </li>
          </ul>
        </div>
      </card>
    </div>
  </div>
</template>
<script>
import {
  parseRealTimeResult,
  parseTaskRanking,
  taskType,
  parseTaskTrendStat,
  parseValueDataConversionRate,
  parseValueDataEfficiency,
  anRegionStat,
} from "@/api/largeScreen";
import {
  queryFaceRecordSearch,
  vehicleRecordSearchEx,
  queryHumanRecordSearchEx,
  queryNonmotorRecordSearch,
} from "@/api/wisdom-cloud-search";
import card from "@/components/screen/srceen-card.vue";
import statisChart from "./statis-chart.vue";
import trendChart from "./trend-chart.vue";
import mapChart from "./map-chart.vue";
import pieChart from "./pie-chart.vue";
import CountTo from "vue-count-to";
export default {
  components: {
    card,
    statisChart,
    trendChart,
    mapChart,
    pieChart,
    CountTo,
  },
  data() {
    return {
      realIndex: -1,
      formData: {
        taskType: "0",
      },
      costList: [
        { value: "0", name: "有效", type: "validRate" },
        { value: "0", name: "无效", type: "invalidRate" },
      ],
      taskList: [
        { value: "42", name: "运行中", type: "runningPercent" },
        { value: "25", name: "已完成", type: "finishedPercent" },
        { value: "33", name: "已停止", type: "stoppedPercent" },
      ],
      realList: [
        {
          typename: "人脸",
          list: [
            { traitImg: "", time: "", deviceName: "" },
            { traitImg: "", time: "", deviceName: "" },
            { traitImg: "", time: "", deviceName: "" },
            { traitImg: "", time: "", deviceName: "" },
            { traitImg: "", time: "", deviceName: "" },
          ],
        },
        {
          typename: "车辆",
          list: [
            { traitImg: "", time: "", deviceName: "" },
            { traitImg: "", time: "", deviceName: "" },
            { traitImg: "", time: "", deviceName: "" },
            { traitImg: "", time: "", deviceName: "" },
            { traitImg: "", time: "", deviceName: "" },
          ],
        },
        {
          typename: "人体",
          list: [
            { traitImg: "", time: "", deviceName: "" },
            { traitImg: "", time: "", deviceName: "" },
            { traitImg: "", time: "", deviceName: "" },
            { traitImg: "", time: "", deviceName: "" },
            { traitImg: "", time: "", deviceName: "" },
          ],
        },
        {
          typename: "非机动车",
          list: [
            { traitImg: "", time: "", deviceName: "" },
            { traitImg: "", time: "", deviceName: "" },
            { traitImg: "", time: "", deviceName: "" },
            { traitImg: "", time: "", deviceName: "" },
            { traitImg: "", time: "", deviceName: "" },
          ],
        },
      ],
      parseDataConver: {},
      efficiencyTotal: 0,
      tasklist: [
        { projectName: "", projectValue: 0 },
        { projectName: "", projectValue: 0 },
        { projectName: "", projectValue: 0 },
      ],
      searchfourList: null,
    };
  },
  created() {
    this.init();
    this.queryMap();
    this.queryTaskType();
    this.searchList();
    this.searchfourList = setInterval(() => {
      this.searchList();
    }, 600000);
  },
  beforeDestroy() {
    clearInterval(this.searchfourList);
    this.searchfourList = null;
  },
  methods: {
    init() {
      parseRealTimeResult().then((res) => {
        console.log(res, "parseRealTimeResult");
      });
      //   TODO 这里先注释，后期接口调通需要放开
      //   this.tasklist = [];
      parseTaskRanking().then((res) => {
        res.data.forEach((item) => {
          this.tasklist.push({
            projectName: item.projectName,
            projectValue: Number(item.projectValue),
          });
        });
      });
      parseTaskTrendStat().then((res) => {
        console.log(res, "parseTaskTrendStat");
      });
      parseValueDataConversionRate().then((res) => {
        this.parseDataConver = res.data;
      });
      parseValueDataEfficiency().then((res) => {
        this.efficiencyTotal = res.data.total;
        this.costList.forEach((item) => {
          if (item.type == "validRate") {
            item.value = res.data.validRate;
          } else {
            item.value = res.data.invalidRate;
          }
        });
        this.$nextTick(() => {
          this.$refs.pieChart.init(this.costList);
        });
      });
    },
    queryMap() {
      anRegionStat().then((res) => {
        this.$nextTick(() => {
          this.$refs.mapChart.init(res.data);
        });
      });
    },
    queryTaskType(type = 0) {
      taskType(type).then((res) => {
        this.taskList.forEach((item) => {
          if (item.type == "runningPercent") {
            item.value = res.data.runningPercent;
          } else if (item.type == "finishedPercent") {
            item.value = res.data.finishedPercent;
          } else {
            item.value = res.data.stoppedPercent;
          }
        });
        this.$nextTick(() => {
          this.$refs.taskpieChart.init(this.taskList);
        });
      });
    },
    searchList() {
      let startDate = this.$dayjs(new Date())
        .subtract(24, "hour")
        .format("YYYY-MM-DD HH:mm:ss");
      let endDate = this.$dayjs(new Date()).format("YYYY-MM-DD HH:mm:ss");
      let params = {
        startDate: startDate,
        endDate: endDate,
        pageNumber: 1,
        pageSize: 5,
        similarity: 0.86,
        // algorithmVendorType: "GLST",
        order: "desc",
        sortField: "absTime",
        deviceIds: [],
      };
      queryFaceRecordSearch({ ...params, dataSource: 2 }).then((res) => {
        this.realList[0].list = res.data.entities;
      });
      vehicleRecordSearchEx(params).then((res) => {
        this.realList[1].list = res.data.entities;
      });
      queryHumanRecordSearchEx(params).then((res) => {
        this.realList[2].list = res.data.entities;
      });
      queryNonmotorRecordSearch(params).then((res) => {
        this.realList[3].list = res.data.entities;
      });
    },
    handleDetails(item, index, rowInd) {
      this.realIndex = index;
    },
    handleChange() {
      this.queryTaskType(this.formData.taskType);
    },
    getImgUrl(val) {
      return require(`@/assets/img/screen/${val}`);
    },
  },
};
</script>
<style lang="less" scoped>
@import "../style/resetui.less";
.screen {
  .form-box {
    top: 25px !important;
    right: 5px !important;
    .warpper-content {
      width: 120px !important;
    }
  }
  .screen-bigbox {
    height: calc(~"66% + 5px");
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    .realtime {
      height: 25%;
      display: flex;
      flex-direction: column;
      .realtime-title {
        display: flex;
        align-items: center;
        .realtime-circle {
          width: 5px;
          height: 5px;
          background: #ffc963;
        }
        p {
          font-weight: 700;
          font-size: 14px;
          color: #ffffff;
        }
      }
      .realtime-photo {
        display: flex;
        justify-content: space-between;
        flex: 1;
        height: calc(~"100% - 22px");
        .realtime-list {
          // width: 75px;
          // height: 110px;
          width: calc(~"20% - 5px");
          height: 100%;
          border: 1px solid #045e9f;
          cursor: pointer;
          img {
            width: 100%;
            height: 100%;
            border: none;
          }
        }
        .realtime-list-active {
          border: 2px solid #0096ff;
        }
      }
    }
  }
  .title-img-big {
    width: 200px !important;
  }
}
</style>
