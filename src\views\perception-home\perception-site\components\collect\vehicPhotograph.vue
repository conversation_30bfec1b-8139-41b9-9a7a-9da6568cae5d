/**
* 车辆抓拍
 */
<template>
    <div class="list-card box-1">
        <!-- <div class="list-card box-1" v-for="(item, index) in dataList" :key="index"> -->
        <div class="collection paddingIcon">
            <!-- <div class="bg"></div> -->
            <ui-btn-tip class="collection-icon" v-if="data.myFavorite == '1'" content="取消收藏" icon="icon-yishoucang" transfer @click.native="collection(data, 2)" />
            <ui-btn-tip class="collection-icon" v-else content="收藏" icon="icon-shoucang" transfer @click.native="collection(data, 1)" />
        </div>
        <p class="img-content">
            <span class="num" v-if="data.similarity">{{ data.similarity || '0' }}%</span>
            <ui-image :src="data.traitImg" alt="动态库" @click.native="vehicDetailFn(data)" />
            <b class="shade vehicle">
                <!-- <div class="plate-number-content">
                    <span>{{data.plateNo}}</span>
                </div> -->
                <ui-plate-number :plateNo="data.plateNo" :color="data.plateColor" size="mini"></ui-plate-number>
                <!-- <plate-number :plateNo="data.plateNo"></plate-number> -->
            </b>
        </p>
        <div class="bottom-info">
            <time>
                <Tooltip content="时间" placement="right" transfer theme="light">
                    <i class="iconfont icon-time"></i>
                </Tooltip>
                {{ data.absTime }}
            </time>
            <p>
                <Tooltip content="位置" placement="right" transfer theme="light">
                    <i class="iconfont icon-location" title="123"></i>
                </Tooltip>
                {{ data.detailAddress }}
            </p>
        </div>
        <!-- <div class="operate-bar">
            <p class="operate-content">
                <ui-btn-tip content="档案" icon="icon-dangan2" @click.native="archivesPage(data)" />
                <ui-btn-tip content="分析" icon="icon-fenxi" />
                <ui-btn-tip content="布控" icon="icon-dunpai" transfer />
            </p>
        </div> -->
        <!-- </div> -->
    </div>
</template>

<script>
import { getVehicleBaseInfoByplateNo } from '@/api/vehicleArchives'
export default {
    name: '',
    components:{
    },
    props:{
        data: {
            type: Object,
            default: () => {
                return {}
            }
        },
    },
    data () {
        return {
            vehicleShow:false,
            swiperWidth: '50%',
        }
    },
    watch:{
            
    },
    computed:{
            
    },
    created() {
            
    },
    mounted(){
            
    },
    methods: {
        collection(data, flag){
            var param = {
                favoriteObjectId: data.id,
                favoriteObjectType: 6,
            }
            this.$emit('collection', param, flag)
        },
        vehicDetailFn() {
            this.$emit('vehicDetailFn')
        },
        archivesPage(row) {
            getVehicleBaseInfoByplateNo(row.plateNo).then(res => {
                if(res.data.archiveNo){
                    const { href } = this.$router.resolve({
                        name: 'vehicle-archive',
                        query: { 
                        archiveNo: JSON.stringify(res.data.archiveNo),
                        plateNo: JSON.stringify(row.plateNo),
                        source: 'car' 
                        }
                    })
                    window.open(href, '_blank')
                }else{
                    this.$Message.error('尚未查询到该辆车的档案信息')
                }
            })
        }
    }
}
</script>

<style lang='less' scoped>
// @swiper-width: e(`window.swiperWidth`);
@import 'style/index';
</style>
