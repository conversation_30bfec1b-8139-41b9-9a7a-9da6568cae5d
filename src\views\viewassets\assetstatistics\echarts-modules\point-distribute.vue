<template>
  <div class="function-distribute">
    <tab-title v-model="activeValue" :data="tabData" @on-change="onChangeTitle">
      <template #filter>
        <drop-select v-model="searchData.sbgnlxList" :data="dropData" @on-change="handleChangeSelect"></drop-select>
      </template>
    </tab-title>
    <div class="body-container" v-ui-loading="{ loading: echartsLoading, tableData: echartList }">
      <draw-echarts
        :echart-option="propertyEchart"
        :echart-style="echartStyle"
        ref="chartsRef"
        :echarts-loading="echartsLoading"
      ></draw-echarts>
      <next-chart-icon
        v-if="echartList.length > comprehensiveConfig.homeNum"
        @scrollRight="scrollRight('chartsRef', echartList, [], comprehensiveConfig.homeNum)"
      ></next-chart-icon>
    </div>
  </div>
</template>

<script>
import { oneType, twoType } from '../utils/enum';
import equipmentassets from '@/config/api/equipmentassets';
import { mapGetters } from 'vuex';
import dataZoom from '@/mixins/data-zoom';
import assetstatisticsMultipleBar from '@/views/viewassets/assetstatistics/utils/echarts-config-bar.js';
export default {
  mixins: [dataZoom],
  name: 'pie-chart',
  components: {
    NextChartIcon: require('../components/next-chart-icon.vue').default,
    DropSelect: require('../components/drop-select.vue').default,
    DrawEcharts: require('@/components/draw-echarts').default,
    TabTitle: require('../components/tab-title.vue').default,
  },
  props: {
    homeConfig: {
      default: () => ({}),
    },
    activeTab: {
      default: '1',
    },
  },
  data() {
    let restData = twoType.map((item) => {
      return {
        label: item.name,
        id: item.id,
      };
    });
    let dropData = [{ label: '全部', id: '' }, ...restData];
    return {
      dropData: dropData,
      searchData: {
        dataKey: 'dwlx_chart_overview',
        sbgnlxList: '',
      },
      activeValue: 'deviceStatus',
      tabData: [{ label: '点位类型区域分布', id: 'deviceStatus' }],
      echartStyle: {
        width: '100%',
        height: '100%',
      },
      propertyEchart: {},
      echartsLoading: false,
      echartList: [],
    };
  },
  computed: {
    ...mapGetters({
      comprehensiveConfig: 'common/getComprehensiveConfig',
    }),
  },
  watch: {
    activeTab: {
      async handler() {
        this.init();
      },
    },
    homeConfig: {
      async handler(val) {
        if (Object.keys(val).length > 0) {
          this.init();
        }
      },
      deep: true,
    },
  },
  filter: {},
  mounted() {},
  methods: {
    async init() {
      await this.queryChartPropertyStatisticsList();
    },
    async handleChangeSelect(val) {
      console.log('val', val);
      this.init();
    },
    async onChangeTitle(val) {
      console.log('val', val);
      this.init();
    },
    initCharts() {
      setTimeout(() => {
        this.setDataZoom('chartsRef', [], this.comprehensiveConfig.homeNum);
      });
    },

    // GNLX_CHART_OVERVIEW("gnlx_chart_overview","功能类型区域分布")
    // DWLX_CHART_OVERVIEW("dwlx_chart_overview","点位类型区域分布")

    async queryChartPropertyStatisticsList() {
      try {
        this.echartsLoading = true;
        let { regionCode } = this.homeConfig;
        let params = {
          civilCode: regionCode,
          type: this.activeTab,
          ...this.searchData,
          sbgnlxList: this.searchData.sbgnlxList ? [this.searchData.sbgnlxList] : [],
        };
        let {
          data: { data },
        } = await this.$http.post(equipmentassets.queryChartPropertyStatisticsList, params);
        this.echartList = data ?? [];
        this.handleCharts();
      } catch (e) {
        console.log(e);
      } finally {
        this.echartsLoading = false;
      }
    },
    handleCharts() {
      let xAxisData = [];
      let legendData = [];
      let baseSeriesStragety = oneType;
      let seriesData = baseSeriesStragety.map((item) => {
        legendData.push(item.name); // lengend
        return {
          name: item.name,
          filedName: item.filedName,
          itemStyle: {
            normal: { color: item.color },
          },
          barWidth: 8,
          data: [],
        };
      });
      this.echartList.forEach((item) => {
        xAxisData.push({
          value: item.civilName,
        });
        seriesData.forEach((one) => {
          one.data.push(item[one.filedName] ?? 0);
        });
      });
      let data = {
        titleText: '设备数量',
        legendData: legendData,
        xAxisData: xAxisData,
        seriesData: seriesData,
      };
      this.propertyEchart = assetstatisticsMultipleBar(data);
      this.initCharts();
    },
  },
};
</script>

<style lang="less" scoped>
.function-distribute {
  flex: 1;
  height: 318px;
  background: var(--bg-sub-echarts-content);
  // border: 1px solid var(--border-color);
  box-shadow: var(--shadow-sub-echarts-content);
  .body-container {
    width: 100%;
    position: relative;
    height: calc(100% - 30px) !important;
    .charts {
      width: 100%;
      height: 100% !important;
    }
  }
}
</style>
