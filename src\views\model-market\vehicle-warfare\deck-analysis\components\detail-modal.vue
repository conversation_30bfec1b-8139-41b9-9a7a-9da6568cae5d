<template>
  <Modal
    ref="modal"
    :title="vehicleData.vehicle.plateNo"
    v-model="modalOptions.open"
    width="800"
    class="modalExtendClass"
  >
    <div v-if="modalOptions.open">
      <div class="dialog-msg">
        <div class="msg-block block-a">
          <div class="carCode">车辆A</div>
          <div class="imgDiv">
            <img :src="vehicleData.vehicle.sceneImg" alt="" v-viewer />
          </div>
          <div class="lable-item">
            <span class="label">车辆品牌：</span>
            <span class="msg">{{
              vehicleData.vehicle.vehicleBrand
                | commonFiltering(vehicleBrandList)
            }}</span>
            <span class="label" style="margin-left: 40px">车辆颜色：</span>
            <span class="msg">{{
              vehicleData.vehicle.vehicleColor
                | commonFiltering(plateColorIpbdList)
            }}</span>
          </div>
          <div class="lable-item">
            <span class="label">车辆子品牌：</span>
            <span class="msg">{{ vehicleData.vehicle.vehicleSubBrand }}</span>
          </div>
          <div class="lable-item">
            <span class="label">通行时间：</span>
            <span class="msg">{{ vehicleData.vehicle.absTime }}</span>
          </div>
          <div class="lable-item">
            <span class="label">通行设备：</span>
            <span class="msg" :title="vehicleData.vehicle.deviceName">{{
              vehicleData.vehicle.deviceName
            }}</span>
          </div>
          <div class="pk-icon" :class="['handle-msg' + form.result]"></div>
        </div>
        <div class="msg-block block-b">
          <div class="carCode">车辆B</div>
          <div class="imgDiv">
            <img :src="vehicleData.vehicleFake.sceneImg" alt="" v-viewer />
          </div>
          <div class="lable-item">
            <span class="label">车辆品牌：</span>
            <span class="msg">{{
              vehicleData.vehicleFake.vehicleBrand
                | commonFiltering(vehicleBrandList)
            }}</span>
            <span class="label" style="margin-left: 40px">车辆颜色：</span>
            <span class="msg">{{
              vehicleData.vehicleFake.vehicleColor
                | commonFiltering(plateColorIpbdList)
            }}</span>
          </div>
          <div class="lable-item">
            <span class="label">车辆子品牌：</span>
            <span class="msg">{{
              vehicleData.vehicleFake.vehicleSubBrand
            }}</span>
          </div>
          <div class="lable-item">
            <span class="label">通行时间：</span>
            <span class="msg">{{ vehicleData.vehicleFake.absTime }}</span>
          </div>
          <div class="lable-item">
            <span class="label">通行设备：</span>
            <span class="msg" :title="vehicleData.vehicleFake.deviceName">{{
              vehicleData.vehicleFake.deviceName
            }}</span>
          </div>
          <div class="pk-icon" :class="['handle-msg' + form.result]"></div>
        </div>
      </div>
      <div class="dialog-form">
        <Form :label-width="100">
          <FormItem label="套牌车辆:" prop="result">
            <RadioGroup v-model="form.result">
              <Radio
                v-for="item in fakeResultList"
                :key="item.dataKey"
                :label="item.dataKey"
                >{{ item.dataValue }}</Radio
              >
            </RadioGroup>
          </FormItem>
          <FormItem label="判定原因:">
            <span>两次识别特征信息比对不一致</span>
          </FormItem>
          <FormItem label="审定批注:">
            <div class="text">
              <Input
                type="textarea"
                :rows="3"
                placeholder="最多输入50个字符"
                v-model="form.comment"
                maxlength="50"
                @input="checkSize"
              >
              </Input>
              <span>
                可输入：<br />
                {{ inputLength }}个字符
              </span>
            </div>
          </FormItem>
        </Form>
      </div>
    </div>
    <div
      slot="footer"
      style="padding: 10px; text-align: center"
      class="nui-border"
    >
      <Button @click="cancel">取消</Button>
      <Button type="primary" @click="saveValue">保存</Button>
    </div>
  </Modal>
</template>

<script>
import { mapGetters } from "vuex";
import { fakeVehicleHandle } from "@/api/modelMarket";
export default {
  name: "editTreePop",
  data() {
    return {
      modalOptions: {
        open: false,
      },
      vehicleData: {
        vehicle: {},
        vehicleFake: {},
      },
      form: {
        result: "",
        comment: "",
      },
      inputLength: 50, //可输入长度
      handleInfo: {},
    };
  },
  computed: {
    ...mapGetters({
      vehicleBrandList: "dictionary/getVehicleBrandList", // 车辆品牌
      plateColorIpbdList: "dictionary/getVehicleColorList", // 车身颜色
      fakeResultList: "dictionary/getFakeResultList", // 套牌处理状态
    }),
  },
  mounted() {},
  methods: {
    show(data) {
      this.vehicleData.vehicle = data.vehicle;
      this.vehicleData.vehicleFake = data.vehicleFake;
      this.handleInfo = data.handleStatus;
      this.form.result = data.handleStatus?.handleResult.toString();
      this.form.comment = data.handleStatus?.comment;
      this.modalOptions.open = true;
    },
    saveValue() {
      if (!this.form.result) {
        this.$Message.warning("请选择处理状态");
        return;
      }
      fakeVehicleHandle({
        id: this.handleInfo.id,
        ...this.form,
      }).then((res) => {
        if (res.code === 200) {
          this.$Message.success("保存成功");
          this.$emit("handleComplete");
          this.cancel();
        }
      });
    },
    //textrea限制字数
    checkSize() {
      const textLength = this.form.comment.length;
      this.inputLength = 50 - textLength;
    },
    cancel() {
      this.modalOptions.open = false;
    },
  },
};
</script>

<style lang="less">
.dialog-msg {
  display: flex;
  justify-content: space-between;
  //border: solid 1px #e8ebee;
  border: solid 1px rgb(222, 222, 222);
  .msg-block {
    position: relative;
    margin: 0 20px;
    width: 300px;
    .carCode {
      margin: 5px 0px;
      font-weight: bold;
      color: #2c86f8;
    }
    .imgDiv {
      width: 300px;
      height: 200px;
      border: solid 1px #e8ebee;
      background: #f9f9f9;
      display: flex;
      align-items: center;
      justify-content: center;
      img {
        width: 200px;
        height: 200px;
        object-fit: cover;
        cursor: pointer;
      }
    }
    .lable-item {
      margin: 10px 0px !important;
      &:last-child {
        margin-bottom: 10px;
      }
      .label {
        font-size: 12px;
        color: rgba(0, 0, 0, 0.6);
      }
      .msg {
        font-size: 12px;
        margin-left: 3px;
        color: rgba(0, 0, 0, 0.9);
      }
    }
  }
  // 无效: 0; A: 1; B: 2;  A、B都是： 3
  .block-a {
    .pk-icon {
      position: absolute;
      bottom: 25px;
      right: 0px;
      width: 50px;
      height: 50px;
      &.handle-msg0 {
        background: url("~@/assets/img/tpfx/invalid.png") no-repeat center
          center;
        background-size: contain;
      }
      &.handle-msg1 {
        background: url("~@/assets/img/tpfx/fake-plate.png") no-repeat center
          center;
        background-size: contain;
      }
      &.handle-msg2 {
        background: url("") no-repeat center center;
        background-size: contain;
      }
      &.handle-msg3 {
        background: url("~@/assets/img/tpfx/fake-plate.png") no-repeat center
          center;
        background-size: contain;
      }
      &.handle-msgundefined,
      &.handle-msgnull {
        background: url("") no-repeat center center;
        background-size: contain;
      }
    }
  }
  .block-b {
    .pk-icon {
      position: absolute;
      bottom: 25px;
      right: 0px;
      width: 50px;
      height: 50px;
      &.handle-msg0 {
        background: url("~@/assets/img/tpfx/invalid.png") no-repeat center
          center;
        background-size: contain;
      }
      &.handle-msg1 {
        background: url("") no-repeat center center;
        background-size: contain;
      }
      &.handle-msg2 {
        background: url("~@/assets/img/tpfx/fake-plate.png") no-repeat center
          center;
        background-size: contain;
      }
      &.handle-msg3 {
        background: url("~@/assets/img/tpfx/fake-plate.png") no-repeat center
          center;
        background-size: contain;
      }
      &.handle-msgundefined,
      &.handle-msgnull {
        background: url("") no-repeat center center;
        background-size: contain;
      }
    }
  }
}
.dialog-form {
  border: solid 1px rgb(222, 222, 222);
  margin-top: 10px;
  padding-bottom: 10px;
  .ivu-form-item {
    margin-bottom: 0px;
    .ivu-input-type-textarea {
      width: 450px;
    }
    .text {
      display: flex;
      span {
        margin-left: 10px;
      }
    }
  }
}
</style>
