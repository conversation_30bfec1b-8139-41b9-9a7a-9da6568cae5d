<!--
    * @FileDescription: 人脸
    * @Author: H
    * @Date: 2023/5/17
 * @LastEditors: duansen
 * @LastEditTime: 2024-09-18 16:08:57
-->
<template>
  <div class="dom-wrapper">
    <div class="dom" @click="($event) => $event.stopPropagation()">
      <header>
        <span>抓拍详情</span>
        <Icon
          type="md-close"
          size="14"
          @click.native="() => $emit('close', $event)"
        />
      </header>
      <section class="dom-content">
        <div class="info-box">
          <div class="info-box-left">
            <div class="thumbnail">
              <img v-lazy="datailsInfo.traitImg" alt="" />
              <span class="similarity" v-if="datailsInfo.score"
                >{{ datailsInfo.score }}%</span
              >
            </div>
            <div
              class="record-title"
              :style="{
                'justify-content': datailsInfo.vid
                  ? 'space-between'
                  : 'flex-start',
              }"
            >
              <span :class="{ active: checkIndex == 0 }" @click="tabsChange(0)"
                >抓拍记录</span
              >
              <!-- 没有vid时不展示档案 -->
              <span
                v-show="datailsInfo.vid"
                class="record-right"
                :class="{ active: checkIndex == 1 }"
                @click="tabsChange(1)"
                >人员档案</span
              >
              <span
                class="record-right"
                :class="{ active: checkIndex == 2 }"
                @click="tabsChange(2)"
                >人车关联</span
              >
            </div>
            <div class="through-record" v-if="checkIndex == 0">
              <div class="wrapper-content">
                <span class="label">抓拍地点</span>：
                <!-- <ui-textOver-tips class="message" refName="deviceName" :content="datailsInfo.deviceName"></ui-textOver-tips> -->
                <span
                  class="message ellipsis"
                  :class="datailsInfo.taskType != '3' ? 'device-click' : ''"
                  v-html="datailsInfo.deviceName"
                  @click="handlePlace(datailsInfo)"
                  :title="
                    datailsInfo.deviceName
                      ? datailsInfo.deviceName.replace(/(<\/?span.*?>)/gi, '')
                      : ''
                  "
                ></span>
              </div>
              <div class="wrapper-content">
                <span class="label">抓拍时间</span>：
                <span class="message">{{ datailsInfo.absTime || "--" }}</span>
              </div>
              <div
                class="wrapper-content"
                v-for="(item, index) in faceList"
                :key="index"
              >
                <span class="label">{{ item.title }}</span
                ><span>：</span>
                <span class="message" v-if="item.dictionary == 'sbgnlxList'">
                  <span
                    v-if="!Array.isArray(facilitySplit(datailsInfo[item.key]))"
                  >
                    {{
                      datailsInfo[item.key]
                        | commonFiltering(translate(item.dictionary))
                    }}
                  </span>
                  <span
                    v-else
                    v-for="(ite, inde) in facilitySplit(datailsInfo[item.key])"
                    :key="inde"
                  >
                    {{ ite | commonFiltering(translate(item.dictionary))
                    }}{{
                      inde + 1 < facilitySplit(datailsInfo[item.key]).length
                        ? "/"
                        : ""
                    }}
                  </span>
                </span>
                <span class="message" v-else-if="item.type == 'filter'">{{
                  datailsInfo[item.key]
                    | commonFiltering(translate(item.dictionary))
                }}</span>
                <span class="message" v-else-if="item.type == 'wear'">{{
                  handleWear(datailsInfo[item.key])
                }}</span>
                <span class="message" v-else>{{
                  datailsInfo[item.key] || "--"
                }}</span>
              </div>
            </div>
            <div class="through-record" v-else-if="checkIndex == 1">
              <div class="wrapper-content">
                <span class="label">视频身份</span>：
                <span
                  class="message identity"
                  v-show-tips
                  v-if="datailsInfo.vid"
                  @click="toDetail"
                  >{{ datailsInfo.vid }}</span
                >
                <span class="message" v-else>{{ "--" }}</span>
              </div>
              <div class="wrapper-content">
                <span class="label">姓名</span>：
                <span class="message">{{ recordList.xm || "--" }}</span>
              </div>
              <div class="wrapper-content">
                <span class="label">联系方式</span>：
                <span class="message">{{ recordList.lxdh || "--" }}</span>
              </div>
              <div class="wrapper-content">
                <span class="label">身份证号</span>：
                <span class="message">{{ recordList.gmsfhm || "--" }}</span>
              </div>
              <div class="wrapper-content">
                <span class="label">家庭住址</span>：
                <span class="message" v-show-tips>{{
                  recordList.xzz_mlxxdz || "--"
                }}</span>
              </div>
            </div>
            <div class="through-record" v-else>
              <div class="rcgl">
                <span class="title">关联车辆:</span>
                <span
                  class="imgBox"
                  v-if="linkVehicle.traitImg"
                  @click="toVehicle(linkVehicle)"
                >
                  <img v-lazy="linkVehicle.traitImg" alt="" />
                  <div
                    class="driverFlag"
                    v-if="
                      datailsInfo.driverFlag == 1 || datailsInfo.driverFlag == 2
                    "
                  >
                    {{ datailsInfo.driverFlag == 1 ? "主" : "副" }}
                  </div>
                </span>
              </div>
            </div>
            <!-- <div class="structuring">
							<p class="structuring-title">结构化信息:</p>
							<ul class="struct-box-ul">
								<li v-for="( item, index) in faceList" :key="index" class="struct-box-list">
									<p class="struct-title">{{ item.title }}</p><span>:</span>
									<p class="struct-content" v-if="item.type == 'filter'">{{ datailsInfo[item.key] | commonFiltering(translate(item.dictionary)) }}</p>
									<p class="struct-content" v-else-if="item.type == 'wear'">{{ handleWear(datailsInfo[item.key]) }}</p>
									<p class="struct-content" v-else>{{ datailsInfo[item.key] || '--' }}</p>
								</li>
							</ul>
						</div> -->
          </div>
          <div class="info-box-right">
            <i
              class="iconfont icon-doubleleft arrows cursor-p prev"
              @click="handleLeft"
            ></i>
            <details-largeimg
              boxSeleType="rect"
              :info="datailsInfo"
              :btnJur="[
                'tp',
                'rl',
                'ytst',
                'ss',
                'lx',
                'fd',
                'sx',
                'xz',
                'sc',
                'hy',
                'dmt',
              ]"
              @collection="collection"
              :collectionType="collectionType"
              :algorithmType="1"
              @onAction="multilAnalysisHandler"
            ></details-largeimg>
            <i
              class="iconfont icon-doubleright arrows cursor-p next"
              @click="handleRight"
            ></i>
          </div>
        </div>
      </section>
      <footer>
        <i
          class="iconfont icon-doubleleft arrows mr-10 cursor-p"
          @click="handleLeft"
        ></i>
        <div class="box-wrapper">
          <div class="present" id="present"></div>
          <ul
            class="list-wrapper"
            id="scroll-ul"
            :style="{ width: 120 * cutList.length + 'px' }"
          >
            <li
              class="list-box"
              @click="handleTab(item, index)"
              v-for="(item, index) in cutList"
              :key="index"
            >
              <div class="img-box">
                <img v-lazy="item.traitImg" alt="" />
              </div>
            </li>
          </ul>
        </div>
        <i
          class="iconfont icon-doubleright arrows ml-10 cursor-p"
          @click="handleRight"
        ></i>
      </footer>
    </div>
    <detailMutiAnalysis
      v-if="isShowMutilAnalysis"
      ref="detailMutiAnalysisRef"
      :tableList="multiDataList"
      :isNoPage="true"
      @close="isShowMutilAnalysis = false"
    ></detailMutiAnalysis>
  </div>
</template>

<script>
import { getPersonInfoByPersonId } from "@/api/operationsOnTheMap";
import { mapActions, mapGetters, mapMutations } from "vuex";
import { swiper, swiperSlide } from "vue-awesome-swiper";
import detailsLargeimg from "./details-largeimg.vue";
import { faceList } from "./structuring.js";
import { cutMixins } from "./mixins.js";
import { commonMixins } from "@/mixins/app.js";
import { getLinkVehicleCapture } from "@/api/wisdom-cloud-search";
import { mutilMixins } from "./mutil-mixins.js";
import detailMutiAnalysis from "@/components/detail/detail-muti-analysis.vue";
export default {
  name: "",
  mixins: [commonMixins, cutMixins, mutilMixins], //全局的mixin
  components: {
    swiper,
    swiperSlide,
    detailsLargeimg,
    detailMutiAnalysis,
  },
  data() {
    return {
      swiperOption: {
        spaceBetween: 10,
        slidesPerView: 12,
        freeMode: true,
        watchSlidesProgress: true,
        navigation: {
          nextEl: ".snap-next",
          prevEl: ".snap-prev",
        },
      },
      cutList: [],
      activeIndex: 0,
      faceList,
      transformWidth: 0,
      pageNum: 0,
      exampleImg: require("../../assets/img/login/bg.png"),
      rate: 1,
      datailsInfo: {},
      recordList: {},
      overlay: false,
      collectionType: 0,
      checkIndex: 0,
      imgBoxWidth: "",
      linkVehicle: {},
    };
  },
  watch: {},
  computed: {
    totalSize() {
      return Math.ceil(this.cutList.length / 11);
    },
  },
  async created() {
    await this.getDictData();
  },
  mounted() {},
  methods: {
    // 开始数据
    init(row, list, index, type, page) {
      this.checkIndex = 0;
      this.cutList = list;
      this.activeIndex = index;
      this.datailsInfo = row;
      this.collectionType = type;
      this.$forceUpdate();
      let small_pic = document.getElementById("scroll-ul");
      small_pic.style.left = 0;
      this.setPageInfo({
        startPage: page,
        endPage: page,
      });
      this.num = index;
      this.fetchVehicleCaptureLink();
      this.$nextTick(() => {
        const divElement = document.querySelector(".list-wrapper");
        const firstElement = divElement.firstChild;
        this.imgBoxWidth = firstElement.clientWidth;
        this.imageNumWidth = this.imgBoxWidth;
        this.locationPlay(index);
      });
    },
    // 切换
    handleTab(item, index) {
      this.checkIndex = 0;
      this.resetData();
      this.datailsInfo = item;
      this.fetchVehicleCaptureLink();
      this.activeIndex = index;
      this.play(index);
    },
    // 人车关联
    fetchVehicleCaptureLink() {
      this.linkVehicle = {};
      if (this.datailsInfo.linkVehicleRid) {
        getLinkVehicleCapture({
          absTime: this.datailsInfo.absTime,
          recordId: this.datailsInfo.linkVehicleRid,
        }).then((res) => {
          this.linkVehicle = res.data || {};
        });
      }
    },
    scrollIntoViews() {
      this.$nextTick(() => {
        let ul = document.querySelector(".list-wrapper");
        let li = [...ul.childNodes][this.activeIndex];
        li.scrollIntoView({
          behavior: "smooth",
        });
      });
    },
    collection(flag) {
      this.$set(this.cutList[this.activeIndex], "myFavorite", flag);
    },
    async tabsChange(index) {
      if (this.checkIndex == index) {
        return;
      }
      this.checkIndex = index;
      if (this.checkIndex === 1) {
        if (!this.datailsInfo.vid) {
          // this.$Message.warning('暂无该人员档案信息');
          return;
        }
        try {
          let res = await getPersonInfoByPersonId({
            vid: this.datailsInfo.vid,
          });
          if (res.code === 200 && !!res.data) {
            this.recordList = res.data || {};
          } else {
            throw error;
          }
        } catch (error) {
          // this.$Message.warning('档案暂无数据')
        }
      }
    },
    toDetail() {
      if (!this.datailsInfo.vid) {
        return;
      }
      const { href } = this.$router.resolve({
        name: "video-archive",
        query: {
          archiveNo: this.datailsInfo.vid,
          source: "video",
          initialArchiveNo: this.datailsInfo.vid,
        },
      });
      // if (!this.datailsInfo.archiveNo) {
      //     this.$Message.warning('暂无该人员档案信息')
      //     return
      // }
      // const { href } = this.$router.resolve({
      //     name: 'people-archive',
      //     query: {
      //         archiveNo: this.recordList.archiveNo,
      //         source: 'people',
      //         initialArchiveNo: this.recordList.archiveNo
      //     }
      // })
      window.open(href, "_blank");
    },
    toVehicle(item) {
      const { href } = this.$router.resolve({
        path: "/wisdom-cloud-search/search-center?sectionName=vehicleContent&noMenu=1",
        query: {
          imgUrl: item.traitImg,
        },
      });
      this.$util.openNewPage(href, "_blank");
    },
    handlePlace(row) {
      if (row.taskType === "3") return;
      const { href } = this.$router.resolve({
        path: "/wisdom-cloud-search/search-center?sectionName=faceContent&noMenu=1",
        query: {
          deviceInfo: JSON.stringify({ ...row, deviceGbId: row.deviceId }),
        },
      });
      this.$util.openNewPage(href, "_blank");
    },
  },
};
</script>

<style lang="less" scoped>
@import "./style/index";
.info-box-left {
  .record-title {
    display: flex;
    > span {
      cursor: pointer;
    }
    .record-right {
      margin-left: 20px !important;
    }
  }
}
.device-click {
  cursor: pointer;
  text-decoration: underline;
}
.rcgl {
  .title {
    color: #2c86f8;
  }
  .imgBox {
    cursor: pointer;
    width: 80%;
    margin: 5px 0;
    display: block;
    position: relative;
    img {
      width: 100%;
    }
    .driverFlag {
      position: absolute;
      right: 2px;
      top: 2px;
      color: #fff;
      background: red;
      border-radius: 20px;
      width: 20px;
      height: 20px;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
}
</style>
