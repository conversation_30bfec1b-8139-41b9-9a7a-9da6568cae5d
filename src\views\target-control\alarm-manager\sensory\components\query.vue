<template>
  <Form ref="formData" :inline="true" :label-width="80">
    <FormItem label="Mac地址:" prop="mac" v-if="compareType == 3">
      <Input v-model="queryParam.mac" placeholder="请输入"></Input>
    </FormItem>
    <FormItem label="IMSI编码:" prop="imsi" v-if="compareType == 4">
      <Input v-model="queryParam.imsi" placeholder="请输入"></Input>
    </FormItem>
    <FormItem label="Rfid地址:" prop="rfidCode" v-if="compareType == 5">
      <Input v-model="queryParam.rfidCode" placeholder="请输入"></Input>
    </FormItem>
    <FormItem label="Etc编号:" prop="obuId" v-if="compareType == 6">
      <Input v-model="queryParam.obuId" placeholder="请输入"></Input>
    </FormItem>
    <FormItem label="姓名:" prop="name">
      <Input v-model="queryParam.name" placeholder="请输入"></Input>
    </FormItem>
    <FormItem label="布控类型:">
      <selectTag
        class="selectTag"
        :list="typeList"
        ref="taskType"
        vModel="taskType"
        @selectItem="selectItem"
      />
    </FormItem>
    <FormItem
      label="库选择:"
      prop="featureLibIds"
      v-if="queryParam.taskType == '2'"
    >
      <Select
        v-model="queryParam.featureLibIds"
        placeholder="请选择"
        multiple
        transfer
        :max-tag-count="1"
      >
        <Option :value="item.id" v-for="item in libList" :key="item.libName">{{
          item.libName
        }}</Option>
      </Select>
    </FormItem>
    <FormItem label="报警设备:">
      <div class="select-tag-button" @click="selectDevice()">
        选择设备/已选（{{ queryParam.deviceIds.length }}）
      </div>
    </FormItem>
    <FormItem label="布控级别:">
      <selectTag
        class="selectTag"
        :list="levelList"
        ref="taskLevel"
        vModel="taskLevel"
        @selectItem="selectItem"
      />
    </FormItem>
    <select-device
      ref="selectDevice"
      :showOrganization="true"
      @selectData="selectData"
    />
  </Form>
</template>
<script>
import selectTag from "../../../components/select-tag.vue";
import { queryVehicleLibList } from "@/api/target-control";
import { queryDeviceList } from "@/api/wisdom-cloud-search";
export default {
  components: { selectTag },
  props: {
    compareType: {
      type: [String, Number],
      default: () => "",
    },
  },
  data() {
    return {
      selectDeviceList: [], // 选中的设备
      queryParam: {
        mac: "", // Mac地址
        imsi: "", // IMSI编码
        rfidCode: "", // Rfid地址
        obuId: "", // Etc编号
        name: "",
        taskType: null, // 布控类型：null - 全部
        taskLevel: null, // 布控级别：null - 全部
        deviceIds: [], // 选中设备的Id
        faceLibIds: [], // 选择的布控库id
      },
      typeList: [
        { name: "单体布控", value: "1" },
        { name: "库布控", value: "2" },
      ],
      levelList: [
        { name: "一级", value: "1" },
        { name: "二级", value: "2" },
        { name: "三级", value: "3" },
      ],
      libList: [],
    };
  },
  computed: {
    keyWords() {
      return this.$route.query.keyWords || "";
    },
  },
  async created() {
    if (this.keyWords) {
      let params = {
        deviceName: this.keyWords,
        deviceType: 1,
        pageNumber: 1,
        pageSize: 100,
        filter: this.judgeUser,
        orgCodes: [],
      };
      let { data } = await queryDeviceList(params);
      data.entities.map((item) => {
        item.select = true;
      });
      this.selectData(data.entities);
    }
    queryVehicleLibList({
      controlStatus: 1,
      libSource: 2,
    }).then((res) => {
      this.libList = res.data;
    });
  },
  methods: {
    /**
     * @description: 选择设备，打开弹框
     */
    selectDevice() {
      this.$refs.selectDevice.show(this.selectDeviceList, this.keyWords);
    },

    /**
     * @description: 初始化已选择的设备
     * @param {array} arr 已选择的设备
     */
    selectData(arr) {
      this.selectDeviceList = arr;
      this.queryParam.deviceIds = arr.map((e) => e.deviceGbId);
    },

    reset() {
      this.selectDeviceList = [];
      this.queryParam.mac = "";
      this.queryParam.imsi = "";
      this.queryParam.rfidCode = "";
      this.queryParam.obuId = "";
      this.queryParam.name = "";
      this.queryParam.taskType = null;
      this.queryParam.taskLevel = null;
      this.queryParam.deviceIds = [];
      this.queryParam.featureLibIds = [];
      this.$refs.taskType.currentIndex = -1;
      this.$refs.taskLevel.currentIndex = -1;
    },

    /**
     * @description: 选中tag值
     * @param {string} key 当前的类别
     * @param {object} item 选中的值
     */
    selectItem(key, item) {
      // 具体业务处理逻辑
      if (item) {
        this.queryParam[key] = item.value;
      } else {
        // 全部选项，不返回数据到后端
        this.queryParam[key] = null;
      }
    },

    /**
     * @description: 获取查询参数，暴露给父组件
     * @return {object}
     */
    getQueryParams() {
      return this.queryParam;
    },
  },
};
</script>
<style lang="less" scoped>
.selectTag {
  margin-top: 4px;
}
</style>
