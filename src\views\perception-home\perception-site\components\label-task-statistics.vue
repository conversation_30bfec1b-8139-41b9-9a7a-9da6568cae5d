<template>
  <ui-card>
    <div slot="title" class="head-title">
      <img src="@/assets/img/home/<USER>" alt=''/><span class="title-text">标签任务统计</span>
    </div>
    <div class="echart-wrap">
      <div class="echart-legend">
        <span v-for="(item, $index) in labelTaskObj.list" :key="$index">{{ item.name }}</span>
      </div>
      <two-way-echart v-if="labelTaskObj.names.length" :list="labelTaskObj.list" :names="labelTaskObj.names"/>
    </div>
  </ui-card>
</template>
<script>
  import TwoWayEchart from './echarts/two-way-echart.vue'
  import { mapGetters } from 'vuex'
  export default {
    components: {
      TwoWayEchart
    },
    props: {
      statisticsList: {
        type: Array,
        default: () => []
      }
    },
    data () {
      return {
        labelTaskObj: {
          list: [
            { name: '规则任务', data: [] },
            { name: '模型任务', data: [] }
          ],
          names: []
        }
      }
    },
    computed: {
      ...mapGetters({
        getTaskStatusList: 'dictionary/getTaskStatusList' // 任务状态列表
      })
    },
    watch: {
      'statisticsList': {
        handler (val) {
          this.dataAccessObject = this.updataLabelTaskType(this.statisticsList)
        },
        immediate: true
      }
    },
    created () {
      this.labelTaskObj = {
        list: [
          { name: '规则任务', data: [] },
          { name: '模型任务', data: [] }
        ],
        names: []
      }
    },
    methods: {
      /**
       * 标签任务统计
      */
      updataLabelTaskType (list) {
        this.labelTaskObj.list[0].data = []
        this.labelTaskObj.list[1].data = []
        this.labelTaskObj.names = []
        this.getTaskStatusList.forEach(v => {
          const item = list.find(n => {
            return n.status === v.dataKey
          })
          this.labelTaskObj.names.push(v.dataValue)
          this.labelTaskObj.list[0].data.push({
            type: '1',
            status: v.dataKey,
            value: item ? item.ruleCount : 0,
            color: this.switchColor(v.dataKey)
          })
          this.labelTaskObj.list[1].data.push(
            {
            type: '2',
            status: v.dataKey,
            value: item ? item.modelCount : 0,
            color: this.switchColor(v.dataKey)
          })
        })
      },
      switchColor (dataKey) {
        if (dataKey === '1') {
          return '#797F82'
        } else if (dataKey === '2') {
          return '#BF5E1D'
        } else if (dataKey === '3') {
          return '#D45321'
        } else if (dataKey === '4') {
          return '#5D9FF4'
        } else if (dataKey === '5') {
          return '#60C80B'
        }
      }
    }
  }
</script>
<style lang="less" scoped>
  .echart-wrap {
    display: flex;
    flex: 1;
    flex-direction: column;
    .echart-legend {
      display: flex;
      color: rgba(255, 255, 255, 0.89);
      &>span {
        display: inline-block;
        width: 50%;
        text-align: center;
      }
    }
  }
</style>