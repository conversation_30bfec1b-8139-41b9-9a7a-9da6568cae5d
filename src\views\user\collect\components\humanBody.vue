<!--
    * @FileDescription: 人体，非机动车
    * @Author: H
    * @Date: 2023/05/22
    * @LastEditors: 
    * @LastEditTime: 
 -->
<template>
    <div class="list-card box-1">
        <div class="collection paddingIcon">
            <ui-btn-tip class="collection-icon" v-if="data.myFavorite == '1'" content="取消收藏" icon="icon-yishoucang" transfer @click.native="collection(data, 2)" />
            <ui-btn-tip class="collection-icon" v-else content="收藏" icon="icon-shoucang" transfer @click.native="collection(data, 1)" />
        </div>
        <p class="img-content">
            <span class="num" v-if="data.score">{{ data.score }}%</span>
            <ui-image :src="data.traitImg" alt="" @click.native="detailFn(data)" />
        </p>
        <div class="bottom-info">
            <time>
                <Tooltip content="抓拍时间" placement="right" transfer theme="light">
                    <i class="iconfont icon-time"></i>
                </Tooltip>
                {{ data.absTime }}
            </time>
            <p>
                <Tooltip content="抓拍地点" placement="right" transfer theme="light">
                    <i class="iconfont icon-location" title="123"></i>
                </Tooltip>
                <span class="ellipsis" v-show-tips>{{data.deviceName}}</span>
            </p>
        </div>
         <!-- <div class="operate-bar">
            <p class="operate-content">
                <ui-btn-tip content="档案" icon="icon-dangan2" @click.native="archivesPage(data)" />
                <ui-btn-tip content="分析" icon="icon-fenxi" />
                <ui-btn-tip content="布控" icon="icon-dunpai" transfer />
            </p>
        </div> -->
    </div>
</template>

<script>
export default {
    name: '',
    props:{
        data: {
            type: Object,
            default: () => {
                return {}
            }
        },
        collectType: {
            type: Boolean,
            default: true
        },
        collType: {
            type: [String, Number],
            default: 16
        }
    },
    components:{
            
    },
    data () {
        return {
            humanShow: false,
        }
    },
    watch:{
            
    },
    computed:{
            
    },
    created() {
            
    },
    mounted(){
            
    },
    methods: {
        collection(data, flag) {
            var param = {
                favoriteObjectId: data.recordId,
                favoriteObjectType: this.collType,
            }
            this.$emit('collection', param, flag)
        },
        detailFn(row) {
            this.$emit('grabDetailFn', row)
        },

        archivesPage() {

        }
    }
}
</script>

<style lang='less' scoped>
@import 'style/index';
</style>
