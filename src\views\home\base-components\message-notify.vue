<template>
  <div class="message-box" v-ui-loading="{ loading: loading, tableData: messageList }">
    <div :class="{ anim: animate }">
      <div class="message-item pointer" v-for="item in messageList" :key="item.id" @click="openPage(item)">
        <i v-if="item.markRead === 1" class="icon-font icon-xiaoxi1"></i>
        <img v-else class="icon-xing" :src="imgUrl" alt="" />
        <!-- websocket推送的取 homeContent -->
        <span class="message-content ellipsis" :class="{ 'unread-span': item.markRead === 0 }" :title="getText(item)">{{
          getText(item)
        }}</span>
      </div>
    </div>
  </div>
</template>

<script>
import notification from '@/config/api/notification';
import { mapActions, mapGetters } from 'vuex';
export default {
  components: {},
  data() {
    return {
      messageList: [], // 接口 +  websocket , 最多 20条
      imgUrl: require('@/assets/img/home/<USER>/xing1.png'),
      animate: false,
    };
  },
  created() {
    this.getMessageList();
  },
  computed: {
    ...mapGetters({
      notifyConfig: 'websocket/getNotifyConfig',
    }),
    getText() {
      return (item) => {
        return item.systemContent || item.homeContent;
      };
    },
  },
  watch: {
    notifyConfig: {
      handler(obj) {
        if (!obj.homeContent) return;
        console.log('消息推送notifyConfig:', obj);
        this.setMessageList();
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {
    ...mapActions({
      setNoticeNum: 'websocket/setNoticeNum',
    }),
    async getMessageList() {
      try {
        this.loading = true;
        let data = {
          pageNumber: 1,
          pageSize: 10,
          template: 'home',
        };
        let res = await this.$http.post(notification.messagePageList, data);
        this.messageList = res.data?.data?.entities;
      } catch (error) {
        console.log(error);
      } finally {
        this.loading = false;
      }
    },
    setMessageList() {
      try {
        let flag = this.messageList.some((item) => item.id === this.notifyConfig.id);
        if (flag) return;
        this.animate = true;
        let el = document.querySelectorAll('.message-box');
        if (el.length) {
          el[0].scrollTop = 0;
        }
        setTimeout(() => {
          this.notifyConfig.markRead = 0; // 推送过来的 都是未读
          let arr = this.$util.common.deepCopy(this.messageList);
          if (arr.length >= 20) {
            arr.pop();
          }
          arr.unshift(this.notifyConfig);
          this.messageList = arr;
          this.animate = false;
        }, 1000);
      } catch (error) {
        console.log(error);
      }
    },
    async openPage(messageItem) {
      // 先调接口 标记为已读   在跳转
      await this.setReadFn(messageItem);
      messageItem.markRead = 1;
      await this.setNoticeNum();
      let { uri, redirectParams } = messageItem;
      if (!uri) return;
      this.$router.push({ path: uri, query: JSON.parse(redirectParams) });
    },
    async setReadFn({ id }) {
      try {
        let data = {
          messageIds: id ? [id] : [],
          all: false,
        };
        await this.$http.post(notification.markRead, data);
      } catch (error) {
        console.log(error);
      }
    },
  },
};
</script>

<style lang="less" scope>
.message-box {
  overflow: hidden;
  overflow-y: auto;
  position: relative;
  height: calc(100% - 40px);
}
.message-item {
  color: #ffffff;
  display: flex;
  align-items: center;
  padding: 5px;
  background: rgba(24, 137, 255, 0.1);
  margin: 2px 0;
  .icon-font,
  .icon-xing {
    font-size: 12px;
    width: 16px;
    margin-right: 3px;
    text-align: center;
  }
  .unread-span {
    color: #ffc600;
  }
  .message-content {
    flex: 1;
  }
}
.anim {
  transition: all 2s;
  margin-top: 40px;
}
.test {
  position: absolute;
  right: 0;
  z-index: 1000;
}
</style>
