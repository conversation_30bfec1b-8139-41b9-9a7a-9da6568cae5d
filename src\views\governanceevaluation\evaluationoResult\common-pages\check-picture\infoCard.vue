<template>
  <!-- 目前 personTypes不统一，单独拆分组件-->
  <div class="ui-gather-card">
    <div class="img">
      <div @click="viewBigPic(list.scenePath)">
        <ui-image :src="list.facePath" class="ui-image-card" :key="list.facePath" />
      </div>
      <p v-if="!!list.objectSnapshotId" class="shadow-copy" style="z-index: 11" :title="list.objectSnapshotId">
        <i class="base-text-color f-14">ID:</i>
        <span class="base-text-color id-num ellipsis f-14">{{ list.objectSnapshotId }}</span>
        <span class="copy-text f-14" v-clipboard="list.objectSnapshotId" v-clipboard:callback="copy">复制</span>
      </p>
      <p
        v-permission="{
          route: $route.name,
          permission: 'artificialreviewr',
        }"
        v-if="[2001, 2002].includes(paramsList.indexId)"
        class="shadow-artificial font-blue"
        style="z-index: 11"
        title="人工复核"
        @click="artificialReview(list)"
      >
        <i class="font-blue icon-font icon-xiajikaohedefen vt-middle f-14 mr-xs"></i>
        <span class="artificial-text f-14 font-blue">人工复核</span>
      </p>
      <div class="ui-gather-card-image-item ellipsis" v-for="(item, index) in cardInfo" :key="index">
        <p class="ellipsis" v-if="checkNum == 1 || checkNum == 3">
          <i
            v-if="item.icon"
            :class="item.icon"
            class="icon-font base-text-color vt-middle mr-xs ui-gather-card-right-item-label"
          ></i>
          <span
            v-if="item.value === 'resultTip'"
            :class="item.value === 'resultTip' && list.qualified != 1 ? 'f-r' : 'f-g'"
            :title="list[item.value] || '暂无'"
            >{{ list[item.value] || '暂无' }}</span
          >
          <span
            v-if="item.value !== 'resultTip'"
            class="ui-gather-card-right-item-value"
            :title="list[item.value] || '暂无'"
            >{{ list[item.value] || '暂无' }}</span
          >
        </p>
        <p class="ellipsis" v-if="checkNum == 2 || checkNum == 4">
          <i
            v-if="item.icon"
            :class="item.icon"
            class="icon-font base-text-color vt-middle mr-xs ui-gather-card-right-item-label"
            >{{ item.icon }}</i
          >

          <span
            v-if="item.value === 'resultTip'"
            :class="item.value === 'resultTip' && list.qualified !== 1 ? 'f-r' : 'f-g'"
            :title="list[item.value] || '暂无'"
            >{{ list[item.value] || '暂无' }}</span
          >
          <span
            v-if="item.value !== 'resultTip'"
            class="ui-gather-card-right-item-value"
            :title="list[item.value] || '暂无'"
            >{{ list[item.value] || '暂无' }}</span
          >
        </p>
      </div>
    </div>
    <!-- 人工复核 -->
    <ui-modal v-model="artificialVisible" ref="artificialReview" title="人工复核" :styles="artificialStyles">
      <div class="artificial-data">
        <ui-label class="block" label="人工复核:" :width="80">
          <RadioGroup v-model="artificialData.qualified">
            <Radio label="1">图片可用 </Radio>
            <Radio label="2" class="ml-lg">图片不可用 </Radio>
          </RadioGroup>
        </ui-label>
        <ui-label class="block check-list" label="" :width="0" v-if="artificialData.qualified === '2'">
          <CheckboxGroup v-model="artificialData.errorCode">
            <Checkbox v-for="(item, index) in checkList" :key="index" :label="item.key">
              <span class="check-text">{{ item.value }}</span>
            </Checkbox>
          </CheckboxGroup>
        </ui-label>
        <ui-label class="block mt-sm" label="" :width="0">
          <Input
            type="textarea"
            class="desc"
            v-model="artificialData.reason"
            placeholder="请输入备注信息"
            :rows="5"
            :maxlength="256"
          ></Input>
        </ui-label>
      </div>
      <template #footer>
        <Button type="primary" class="plr-30" @click="artificial">确定复核结果</Button>
      </template>
    </ui-modal>
  </div>
</template>
<script>
import evaluationoverview from '@/config/api/evaluationoverview';
export default {
  props: {
    checkNum: {
      default: '',
    },
    cardInfo: {
      type: Array,
      default: () => [],
    },
    list: {
      type: Object,
      default: () => {},
    },
    paramsData: {
      type: Object,
      default: () => {},
    },
    checkData: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      imgList: [],
      bigPictureShow: false,
      artificialStyles: {
        width: '3rem',
      },
      checkList: [],
      artificialData: { qualified: '1', reason: '', errorCode: [] },
      artificialVisible: false,
      artificialRow: {},
      paramsList: {},
    };
  },
  computed: {},
  created() {},
  methods: {
    // 人工复核
    artificialReview(row) {
      this.artificialData.qualified = '1';
      this.artificialData.reason = '';
      this.artificialData.errorCode = [];
      this.artificialRow = row;
      this.artificialVisible = true;
    },

    async artificial() {
      let data = {};
      if (this.artificialData.qualified == '1') {
        data = {
          data: {
            id: this.artificialRow.id,
            qualified: this.artificialData.qualified,
            reason: this.artificialData.reason,
            type: 'detail',
            deviceDetailId: this.artificialRow.faceDeviceDetailId,
          },
          indexId: this.paramsList.indexId,
          batchId: this.paramsList.batchId,
        };
      } else {
        data = {
          data: {
            id: this.artificialRow.id,
            qualified: this.artificialData.qualified,
            reason: this.artificialData.reason,
            type: 'detail',
            errorCode: this.artificialData.errorCode.toString(),
            deviceDetailId: this.artificialRow.faceDeviceDetailId,
          },
          indexId: this.paramsList.indexId,
          batchId: this.paramsList.batchId,
        };
        if (this.artificialData.errorCode == '') {
          this.$Message.error('请选择异常原因');
          return;
        }
      }
      try {
        let res = await this.$http.post(evaluationoverview.manualRecheck, data);
        this.artificialVisible = false;
        this.$emit('recount');
        this.$Message.success(res.data.msg);
      } catch (err) {
        console.log(err);
      }
    },
    copy() {
      this.$Message.success('复制成功');
    },
    viewBigPic(item) {
      this.$emit('bigImageUrl', item);
    },
  },
  watch: {
    paramsData: {
      handler(val) {
        if (val.batchId) {
          this.paramsList = val;
        }
      },
      deep: true,
      immediate: true,
    },
    checkData: {
      handler(val) {
        if (val) {
          this.checkList = val;
        }
      },
      deep: true,
      immediate: true,
    },
  },
  components: {
    uiImage: require('@/components/ui-image').default,
  },
};
</script>
<style lang="less" scoped>
.ui-gather-card {
  margin-bottom: 10px;
  padding: 10px;
  background: var(--bg-info-card);
  border: 1px solid var(--border-info-card);
  position: relative;
  &-right {
    flex: 1;
    &-item {
      margin-bottom: 8px;
      font-size: 14px;
      &-label {
        color: #8797ac !important;
        font-size: 14px;
      }
      &-value {
        color: #8797ac;
        font-size: 14px;
      }
      .wrapper {
        width: 100%;
      }
    }
  }
}
.img:hover {
  .shadow-copy {
    display: block;
  }
  .shadow-artificial {
    display: block;
  }
}
.shadow-artificial {
  height: 28px;
  line-height: 29px;
  background: rgba(0, 0, 0, 0.5);
  position: absolute;
  top: 172px;
  width: 90%;
  display: none;
  padding-left: 10px;
  .artificial-text {
    color: var(--color-primary);
    cursor: pointer;
    vertical-align: middle;
  }
  @media screen and (max-width: 1366px) {
    .artificial-text {
      color: var(--color-primary);
      cursor: pointer;
      vertical-align: middle;
    }
  }
}
.shadow-copy {
  height: 28px;
  line-height: 28px;
  width: 90%;
  background: rgba(0, 0, 0, 0.5);
  position: absolute;
  top: 80px;
  display: none;
  padding-left: 10px;
  .id-num {
    width: 106px;
    vertical-align: top;
    display: inline-block;
  }
  @media screen and (max-width: 1366px) {
    .id-num {
      width: 70px; /*no*/
    }
  }
  i {
    vertical-align: top;
  }
  .copy-text {
    color: var(--color-primary);
    cursor: pointer;
    vertical-align: sub;
  }
}
.icon-box {
  position: absolute;
  left: 10px;
  width: 90%;
  top: 170px;
  padding: 0 5px;
  line-height: 30px;
  background: rgba(0, 0, 0, 0.39);
  .icon-inner-box {
    background-color: transparent;
  }
  .pointer {
    width: 100%;
    text-align: center;
  }
}
.ui-gather-card-image-item {
  font-size: 14px;
  margin: 4px 0 4px 0;
}
.ui-image-card {
  height: 190px;
  width: 190px;
  cursor: pointer;
}
@{_deep}.ui-image {
  z-index: initial;
  .ui-image-div {
    .tileImage {
      height: 190px !important;
      width: 190px !important;
    }
  }
}
.check-list {
  // width: 460px;
  margin-left: 80px;
  margin-top: 10px;
}
.check-text {
  display: inline-block;
  // width: 110px;
}
.desc {
  margin-left: 80px;
  width: 80%;
}
.f-r {
  color: var(--color-failed) !important;
}
.f-g {
  color: var(--color-success) !important;
}
.artificial-data {
  padding: 0 50px;
}
</style>
