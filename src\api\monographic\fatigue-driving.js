import request from "@/libs/request";
import { monographic } from "../Microservice";
import { driving as domainName } from "./base";

// 疲劳驾驶-未休息足够时间报警
export function fatigueDrivingList (data) {
    return request({
      url: monographic + domainName + '/fdNotSleepEnoughAlarm/pageList',
      method: 'POST',
      data: data
    })
}

// 疲劳驾驶-全天连续驾驶报警
export function fdContinuousDrivingAlarmList (data) {
  return request({
    url: monographic + domainName + '/fdContinuousDrivingAlarm/pageList',
    method: 'POST',
    data: data
  })
}

// 疲劳驾驶-各年龄段报警数量
export function fdNotSleepEnoughAlarmAgeList (data) {
  return request({
    url: monographic + domainName + '/fdNotSleepEnoughAlarm/ageGroup',
    method: 'POST',
    data: data
  })
}

// 疲劳驾驶-按天统计报警数量
export function alarmNumDayStat (data) {
  return request({
    url: monographic + domainName + '/fdAlarm/alarmNumDayStat',
    method: 'POST',
    data: data
  })
}