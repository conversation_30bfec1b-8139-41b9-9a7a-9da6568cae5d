<template>
  <Form :inline="true" :label-width="80">
    <FormItem label="创建人:">
      <Input v-model="queryParam.creator" placeholder="请输入"></Input>
    </FormItem>
    <FormItem label="创建时间:">
      <DatePicker
        class="right20"
        style="width: 350px"
        :value="createTimes"
        type="datetimerange"
        format="yyyy-MM-dd HH:mm:ss"
        @on-change="dateChange"
        placeholder="请选择自定义时间段"
        transfer
      ></DatePicker>
    </FormItem>
    <FormItem label="布控类型:">
      <selectTag
        class="selectTag"
        :list="typeList"
        ref="taskType"
        vModel="taskType"
        @selectItem="selectItem"
      />
    </FormItem>
    <FormItem label="布控级别:">
      <selectTag
        class="selectTag"
        :list="levelList"
        ref="taskLevel"
        vModel="taskLevel"
        @selectItem="selectItem"
      />
    </FormItem>
  </Form>
</template>
<script>
import selectTag from "../../components/select-tag.vue";
export default {
  components: { selectTag },
  data() {
    this.typeList = Object.freeze([
      { name: "单体布控", value: "1" },
      { name: "库布控", value: "2" },
    ]);
    this.levelList = Object.freeze([
      { name: "一级", value: "1" },
      { name: "二级", value: "2" },
      { name: "三级", value: "3" },
    ]);
    return {
      createTimes: [],
      queryParam: {
        createTimeB: "",
        createTimeE: "",
        creator: "",
        taskType: null,
        taskLevel: null,
      },
    };
  },
  methods: {
    /**
     * @description: 重置
     */
    reset() {
      this.createTimes = [];
      this.queryParam.createTimeB = "";
      this.queryParam.createTimeE = "";
      this.queryParam.creator = "";
      this.queryParam.taskType = null;
      this.queryParam.taskLevel = null;
      this.$refs.taskType.currentIndex = -1;
      this.$refs.taskLevel.currentIndex = -1;
    },

    dateChange(val1) {
      if (val1[1].slice(-8) === "00:00:00") {
        val1[1] = val1[1].slice(0, -8) + "23:59:59";
      }
      this.createTimes = val1;
    },

    /**
     * @description: 选中tag值
     * @param {string} key 当前的类别
     * @param {object} item 选中的值
     */
    selectItem(key, item) {
      // 具体业务处理逻辑
      if (item) {
        this.queryParam[key] = item.value;
      } else {
        // 全部选项，不返回数据到后端
        this.queryParam[key] = null;
      }
    },

    /**
     * @description: 获取查询参数，暴露给父组件
     * @return {object}
     */
    getQueryParams() {
      this.queryParam.createTimeB = this.createTimes[0] || "";
      this.queryParam.createTimeE = this.createTimes[1] || "";
      return this.queryParam;
    },
  },
};
</script>
<style lang="less" scoped>
/deep/ .ivu-form-item-content {
  margin-left: 0 !important;
  display: flex;
  align-items: center;
}
</style>