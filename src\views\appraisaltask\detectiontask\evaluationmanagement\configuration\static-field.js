/**
 ** 需要检测内容指标
 ** [重点实时视频可调阅率, 重点历史视频可调阅率, 普通实时视频可调阅率, 普通历史视频可调阅率]
 **/
const checkConfigmodule = [
  'VIDEO_PLAYING_ACCURACY',
  'VIDEO_HISTORY_ACCURACY',
  'VIDEO_GENERAL_PLAYING_ACCURACY',
  'VIDEO_GENERAL_HISTORY_ACCURACY',
  'VIDEO_MONITOR_ONLINE_RATE_PROMOTE', // 视频监控在线率提升
  'VIDEO_COMMAND_IMAGE_ONLINE_RATE_IMPORTANT_SICHUAN',
];
// 检测内容配置
const detectionRules = {
  routine: [
    {
      label: '在线率',
      text: '录像文件存在/设备在国标平台为在线状态',
      value: '1',
    },
    {
      label: '完好率',
      text1: '录像文件存在/设备在线，拉流',
      text2: '秒内有响应',
      isTime: true,
      value: '2',
    },
    {
      label: '可用率',
      text: '成功接收到历史视频流/成功接收到实时视频流',
      value: '3',
    },
  ], // 常规
  onlineRoutine: [
    {
      label: '平台状态',
      text: '录像文件存在/设备在国标平台为在线状态',
      value: '1',
    },
    {
      label: '信令响应',
      text1: '录像文件存在/设备在线，拉流',
      text2: '秒内有响应',
      isTime: true,
      value: '2',
    },
    {
      label: '取流响应',
      text: '成功接收到历史视频流/成功接收到实时视频流',
      value: '3',
    },
    {
      label: '联网质量',
      text: '检测信令时延是否超时、码流时延是否超时、关键帧时延是否超时',
      value: '4',
    },
  ], // 商丘指标
  VIDEO_MONITOR_ONLINE_RATE_PROMOTE: [
    {
      label: '在线率',
      text: '设备在国标平台为在线状态',
      value: '1',
    },
    {
      label: '完好率',
      text: '设备在线，拉流请求有响应',
      value: '2',
    },
    {
      label: '可用率',
      text: '成功接收到实时视频流',
      value: '3',
    },
  ],
  VIDEO_HISTORY_ACCURACY: [
    {
      label: '平台状态',
      text: '录像文件存在',
      value: '1',
    },
    {
      label: '信令响应',
      text1: '录像文件存在，拉流',
      text2: '秒内有响应',
      isTime: true,
      value: '2',
    },
    {
      label: '取流响应',
      text: '成功接收到历史视频流',
      value: '3',
    },
  ],
  VIDEO_GENERAL_HISTORY_ACCURACY: [
    {
      label: '平台状态',
      text: '录像文件存在',
      value: '1',
    },
    {
      label: '信令响应',
      text1: '录像文件存在，拉流',
      text2: '秒内有响应',
      isTime: true,
      value: '2',
    },
    {
      label: '取流响应',
      text: '成功接收到历史视频流',
      value: '3',
    },
  ], // 视频监控在线率提升
  VIDEO_PLAYING_ACCURACY: [
    {
      label: '平台状态',
      text: '设备在国标平台为在线状态',
      value: '1',
    },
    {
      label: '信令响应',
      text1: '设备在线，拉流',
      text2: '秒内有响应',
      isTime: true,
      value: '2',
    },
    {
      label: '取流响应',
      text: '成功接收到实时视频流',
      value: '3',
    },
    {
      label: '联网质量',
      text: '检测信令时延是否超时、码流时延是否超时、关键帧时延是否超时',
      value: '4',
    },
    {
      label: '视频亮度',
      text: '检测视频画面亮度是否正常，画面不过暗。',
      value: '5',
    },
  ],
  VIDEO_GENERAL_PLAYING_ACCURACY: [
    {
      label: '平台状态',
      text: '设备在国标平台为在线状态',
      value: '1',
    },
    {
      label: '信令响应',
      text1: '设备在线，拉流',
      text2: '秒内有响应',
      isTime: true,
      value: '2',
    },
    {
      label: '取流响应',
      text: '成功接收到实时视频流',
      value: '3',
    },
    {
      label: '联网质量',
      text: '检测信令时延是否超时、码流时延是否超时、关键帧时延是否超时',
      value: '4',
    },
    {
      label: '视频亮度',
      text: '检测视频画面亮度是否正常，画面不过暗。',
      value: '5',
    },
  ],
  VIDEO_COMMAND_IMAGE_ONLINE_RATE_IMPORTANT_SICHUAN: [
    {
      label: '平台状态',
      text: '设备在国标平台为在线状态',
      value: '1',
    },
    {
      label: '信令响应',
      text1: '设备在线，拉流',
      text2: '秒内有响应',
      isTime: true,
      value: '2',
    },
    {
      label: '取流响应',
      text: '成功接收到实时视频流',
      value: '3',
    },
    {
      label: '联网质量',
      text: '检测信令时延是否超时、码流时延是否超时、关键帧时延是否超时',
      value: '4',
    },
    {
      label: '视频亮度',
      text: '检测视频画面亮度是否正常，画面不过暗。',
      value: '5',
    },
  ],
};
//更新**状态
const isUpdatePhyStatus = [
  //填报准确率 略
  {
    indexName: '重点时钟准确率',
    indexType: 'VIDEO_CLOCK_ACCURACY',
    label: '更新时钟状态',
  },
  {
    indexName: '普通时钟准确率',
    indexType: 'VIDEO_GENERAL_CLOCK_ACCURACY',
    label: '更新时钟状态',
  },
  {
    indexName: '人脸卡口设备时钟准确率',
    indexType: 'FACE_CLOCK',
    label: '更新时钟状态',
  },
  {
    indexName: '重点字幕标注合规性与时钟准确性',
    indexType: 'VIDEO_OSD_CLOCK_ACCURACY',
    label: '更新时钟状态',
  },
  {
    indexName: '字幕标注合规性与时钟准确性',
    indexType: 'VIDEO_GENERAL_OSD_CLOCK_ACCURACY',
    label: '更新时钟状态',
  },
  {
    indexName: '车辆卡口设备时钟准确率',
    indexType: 'VEHICLE_CLOCK',
    label: '更新时钟状态',
  },
  {
    indexName: '重点指挥图像在线率',
    indexType: 'VIDEO_COMMAND_IMAGE_ONLINE_RATE_IMPORTANT_SICHUAN',
    label: '更新视频在线状态',
  },
  {
    indexName: '重点实时视频可调阅率',
    indexType: 'VIDEO_PLAYING_ACCURACY',
    label: '更新视频在线状态',
  },
  {
    indexName: '普通实时视频可调阅率',
    indexType: 'VIDEO_GENERAL_PLAYING_ACCURACY',
    label: '更新视频在线状态',
  },
  {
    indexName: '重点历史视频可调阅率',
    indexType: 'VIDEO_HISTORY_ACCURACY',
    label: '更新历史视频调阅状态',
  },
  {
    indexName: '普通历史视频可调阅率',
    indexType: 'VIDEO_GENERAL_HISTORY_ACCURACY',
    label: '更新历史视频调阅状态',
  },
  {
    indexName: '重点历史录像完整率',
    indexType: 'VIDEO_HISTORY_COMPLETE_ACCURACY_IMPORTANT_SICHUAN',
    label: '更新历史录像完整状态',
  },
  {
    indexName: '历史录像完整率',
    indexType: 'VIDEO_HISTORY_COMPLETE_ACCURACY',
    label: '更新历史录像完整状态',
  },
  {
    indexName: '视频流质量合格率',
    indexType: 'VIDEO_QUALITY_PASS_RATE',
    label: '更新视频质量状态',
  },
  {
    indexName: '视频流质量合格率（人工复核）',
    indexType: 'VIDEO_QUALITY_PASS_RATE_RECHECK',
    label: '更新视频质量状态',
  },
  {
    indexName: '人脸卡口在线率',
    indexType: 'FACE_ONLINE_RATE',
    label: '更新人卡在线状态',
  },
  {
    indexName: '人脸设备抓拍图片合格率',
    indexType: 'FACE_CAPTURE_PASS',
    label: '更新人脸图片合格状态',
  },
  {
    indexName: '人脸卡口设备及时上传率',
    indexType: 'FACE_UPLOAD',
    label: '更新人脸上传及时性状态',
  },
  {
    indexName: '重点人脸卡口设备及时上传率',
    indexType: 'FACE_FOCUS_UPLOAD',
    label: '更新人脸上传及时性状态',
  },
  {
    indexName: '车辆卡口在线率',
    indexType: 'VEHICLE_ONLINE_RATE',
    label: '更新车卡在线状态',
  },
  {
    indexName: '车辆卡口设备抓拍数据完整率',
    indexType: 'VEHICLE_FULL_INFO',
    label: '更新车辆数据完整性状态',
  },
  {
    indexName: '重点车辆卡口设备抓拍数据完整率',
    indexType: 'VEHICLE_FULL_INFO_IMPORTANT',
    label: '更新车辆数据完整性状态',
  },
  {
    indexName: '车辆卡口设备过车数据准确率',
    indexType: 'VEHICLE_INFO_PASS',
    label: '更新车辆数据准确性状态',
  },
  {
    indexName: '重点车辆卡口设备过车数据准确率',
    indexType: 'VEHICLE_INFO_PASS_IMPORTANT',
    label: '更新车辆数据准确性状态',
  },
  {
    indexName: '车辆卡口设备及时上传率',
    indexType: 'VEHICLE_UPLOAD',
    label: '更新车辆上传及时性状态',
  },
  {
    indexName: '重点车辆卡口设备及时上传率',
    indexType: 'VEHICLE_UPLOAD_IMPORTANT',
    label: '更新车辆上传及时性状态',
  },
];
/**
 * 默认的检测内容
 */
const defaultDetectContent = {
  existsTime: true, //-是否在指定区域标注
  //（右上角）时间信息检测 --时间格式规范检测：时间格式应该为YYYY-MM-DD hh:mm:ss，不能包含年月日星期等字样。
  timeFormCheck: false,
  //（右上角）时间信息检测 --位置规范检测：
  timePositionCheck: false,
  //时钟、区划、地址信息需保持右对齐。 右边距: min
  timePositionCheckMin: null,
  //时钟、区划、地址信息需保持右对齐。 右边距: max
  timePositionCheckMax: null,
  //（右上角）时间信息检测 --准确性规则，
  timeDeviationCheck: true,
  //设备时间与标准时间允许偏差 XX 秒
  timeDeviationCheckValue: 60,

  //（右下角）区划与地址信息检测 --是否在指定区域标注
  existsArea: true,
  //（右下角）区划与地址信息检测 --位置规范检测：
  areaPositionCheck: false,
  //时钟、区划、地址信息需保持右对齐。地址信息只能占用一行。
  areaPositionCheckValueLine: false,
  //时钟、区划、地址信息需保持右对齐。地址信息只能占用一行。 右边距: min
  areaPositionCheckMin: null,
  //时钟、区划、地址信息需保持右对齐。地址信息只能占用一行。 右边距: max
  areaPositionCheckMax: null,
  //地址信息只能占用一行
  areaPositionCheckLine: false,
  //（右下角）区划与地址信息检测 --准确性规则，
  areaDeviationCheck: true,
  // 省/市/区县、地点信息需与档案信息保持一致
  areaDeviationCheckValue: true,
  //队所（派出所）信息需标注准确（与组织机构表匹配）
  areaDeviationCheckLine: false,

  //（右下角）区划与地址信息检测 --是否在指定区域标注
  existsCamera: false,
  //准确性规则
  cameraDeviationCheck: false,
  //精确匹配2、综合匹配1
  areaDeviationCheckValueType: 1,
  // 【省级】信息需与档案信息保持一致
  areaDeviationCheckValueTypeProvince: true,
  // 【地市级】信息需与档案信息保持一致
  areaDeviationCheckValueTypeCity: true,
  // 【区县级】信息需与档案信息保持一致
  areaDeviationCheckValueTypeCounty: true,
  // 【地址】信息需与档案信息保持一致
  areaDeviationCheckValueTypeInfo: true,
};
/**
 * 默认的SDK检测内容
 */
const defaultDetectContentSdk = {
  existsTime: true, //-是否在指定区域标注
  //（右上角）时间信息检测 --时间格式规范检测：时间格式应该为YYYY-MM-DD hh:mm:ss，不能包含年月日星期等字样。
  timeFormCheck: false,
  //（右上角）时间信息检测 --准确性规则，
  timeDeviationCheck: true,
  //设备时间与标准时间允许偏差 XX 秒
  timeDeviationCheckValue: 60,

  //（右下角）区划与地址信息检测 --是否在指定区域标注
  existsArea: true,
  //（右下角）区划与地址信息检测 --准确性规则，
  areaDeviationCheck: true,
  // 省/市/区县、地点信息需与档案信息保持一致
  areaDeviationCheckValue: true,
  //队所（派出所）信息需标注准确（与组织机构表匹配）
  areaDeviationCheckLine: false,

  //（右下角）区划与地址信息检测 --是否在指定区域标注
  existsCamera: false,
  //准确性规则
  cameraDeviationCheck: false,
  //精确匹配2、综合匹配1
  areaDeviationCheckValueType: 1,
  // 【省级】信息需与档案信息保持一致
  areaDeviationCheckValueTypeProvince: true,
  // 【地市级】信息需与档案信息保持一致
  areaDeviationCheckValueTypeCity: true,
  // 【区县级】信息需与档案信息保持一致
  areaDeviationCheckValueTypeCounty: true,
  // 【地址】信息需与档案信息保持一致
  areaDeviationCheckValueTypeInfo: true,
};

// 对历史检测结果比对分析
const historyComparisonConfigModule = [
  'FACE_ONLINE_RATE', // 人脸卡口在线率
  'VEHICLE_ONLINE_RATE', // 车辆卡口在线率
  'VIDEO_PLAYING_ACCURACY', // 重点实时视频可调阅率
  'VIDEO_GENERAL_PLAYING_ACCURACY', // 普通实时视频可调阅率
  'VIDEO_CLOCK_ACCURACY', // 重点时钟准确率
  'VIDEO_GENERAL_CLOCK_ACCURACY', // 普通时钟准确率
  'VIDEO_OSD_ACCURACY', // 重点字幕标注合规率
  'VIDEO_GENERAL_OSD_ACCURACY', //普通字幕标注合规率
  'VIDEO_COMMAND_IMAGE_ONLINE_RATE_IMPORTANT_SICHUAN', // 重点指挥图像在线率
];

// 需要显示【复检次数】
const needRecheckNumIndex = [
  'VIDEO_PLAYING_ACCURACY',
  'VIDEO_GENERAL_PLAYING_ACCURACY',
  'VIDEO_HISTORY_ACCURACY',
  'VIDEO_GENERAL_HISTORY_ACCURACY',
  'VIDEO_OSD_ACCURACY',
  'VIDEO_GENERAL_OSD_ACCURACY',
  'VIDEO_CLOCK_ACCURACY',
  'VIDEO_GENERAL_CLOCK_ACCURACY',
  'VIDEO_OSD_CLOCK_ACCURACY',
  'VIDEO_GENERAL_OSD_CLOCK_ACCURACY',
  'FACE_URL_AVAILABLE',
  'FACE_FOCUS_URL_AVAILABLE',
  'VEHICLE_URL_AVAILABLE',
  'VEHICLE_URL_AVAILABLE_IMPORTANT',
  'VIDEO_HISTORY_COMPLETE_ACCURACY',
  'VIDEO_QUALITY_PASS_RATE',
  'VIDEO_QUALITY_PASS_RATE_RECHECK',
  'VIDEO_COMMAND_IMAGE_ONLINE_RATE_IMPORTANT_SICHUAN',
];

//默认值 禅道 2501 重点 BASIC_EMPHASIS_QUANTITY
const defaultEmphasisData = [
  { key: 'A0101', value: '重大群众性集会露天广场' },
  { key: 'A0102', value: '商业服务露天广场' },
  { key: 'A0103', value: '文化宣传与体育类露天广场' },
  { key: 'A0105', value: '政治历史意义露天广场' },
  { key: 'A0201', value: '城市、乡镇主要路段、路口、立交桥' },
  { key: 'A0301', value: '高速公路主要出入口、卡口、公安检查站、收费站通道、高速公路服务区等' },
  { key: 'A0602', value: '铁路车站外的露天广场主要区域、重要通道、周边路段、路口等' },
  { key: 'A0605', value: '长途汽车站外的露天广场主要区域、重要通道、周边路段、路口等' },
  { key: 'B0701', value: '学校主出入口及其外部一定区域等' },
  { key: 'B0702', value: '学前教育主出入口及其外部一定区域等' },
  { key: 'B0801', value: '医院主出入口,挂号大厅、候诊大厅等开放区域的人员聚集部位及单位外围一定范围的部位' },
  { key: 'B1101', value: '展览场馆出入口、安检区室外人员聚集区域（部位）等' },
];
//普通 BASIC_EMPHASIS_LOCATION
const defaultGeneralData = [
  { key: 'A0101', value: '重大群众性集会露天广场' },
  { key: 'A0102', value: '商业服务露天广场' },
  { key: 'A0103', value: '文化宣传与体育类露天广场' },
  { key: 'A0104', value: '宗教活动露天广场' },
  { key: 'A0105', value: '政治历史意义露天广场' },
  { key: 'A0201', value: '城市、乡镇主要路段、路口、立交桥' },
  { key: 'A0202', value: '城市地下人行通道、隧道、过街天桥等' },
  { key: 'A0301', value: '高速公路主要出入口、卡口、公安检查站、收费站通道、高速公路服务区等' },
  { key: 'A0302', value: '国道主要出入口、卡口、公安检查站、收费站通道等' },
  { key: 'A0303', value: '省际主要出入口、卡口、公安检查站等' },
  { key: 'A0304', value: '市际主要出入口、卡口、公安检查站' },
  { key: 'A0305', value: '县际主要出入口、卡口、公安检查站、收费站通道等' },
  { key: 'A0306', value: '城镇道路主要出入口、卡口、公安检查站等' },
  { key: 'A0307', value: '城市主干道、国道、高速交通状态检测区域等' },
  { key: 'A0308', value: '事故多发、恶劣天气多发、急弯、长下坡等交通重点关注路段' },
  { key: 'A0401', value: '大型桥梁主要通行区域' },
  { key: 'A0402', value: '大型隧道主要通行区域' },
  { key: 'A0501', value: '城镇商业金融聚集区主要出入口、周边主要路段、路口' },
  { key: 'A0601', value: '民用机场外的露天广场主要区域、重要通道、周边路段、路口等' },
  { key: 'A0602', value: '铁路车站外的露天广场主要区域、重要通道、周边路段、路口等' },
  { key: 'A0605', value: '长途汽车站外的露天广场主要区域、重要通道、周边路段、路口等' },
  { key: 'A0701', value: '城市轨道交通车站周边路段、路口等' },
  { key: 'A13', value: '重点关注人群工作、居住场所周边公共区域' },
  { key: 'B0101', value: '党政机关单位主出入口及采集的图像能够覆盖到单位外围一定范围的部位' },
  { key: 'B0102', value: '党政机关单位办事大厅' },
  { key: 'B0201', value: '机场、航站楼安检区以外开放区域和航站楼周边区域的人员聚集部位' },
  { key: 'B0202', value: '铁路车站的出入口、售票大厅、候车大厅等开放区域的人员聚集部位' },
  { key: 'B0204', value: '城市轨道列车及车站出入口、车站通道、安检区、车站站厅、站台等开放区域' },
  { key: 'B0205', value: '长途汽车站的出入口、售票大厅、候车大厅等开放区域的人员聚集部位' },
  { key: 'B0206', value: '城市公共汽电车枢纽站、停车场站站区及周边一定范围' },
  { key: 'B0207', value: '加油（气）站车辆出入口、服务区' },
  { key: 'B0301', value: '营业网点、自助网点主出入口及其外部一定区域, 运钞交接区、营业大厅等' },
  { key: 'B0401', value: '寄递单位营业场所主出入口、营业大厅交寄接收区等' },
  { key: 'B0402', value: '物流园区主出入口等' },
  { key: 'B0403', value: '仓储区主出入口等' },
  { key: 'B0501', value: '电力行业重点单位周边一定区域、重点线路沿线等' },
  { key: 'B0502', value: '电信行业重点单位周边一定区域、重点线路沿线等' },
  { key: 'B0503', value: '广电行业重点单位周边一定区域、重点线路沿线等' },
  { key: 'B0504', value: '油气行业重点单位周边一定区域、重点线路沿线等' },
  { key: 'B0505', value: '水利行业重点单位周边一定区域、重点线路沿线等' },
  { key: 'B0601', value: '大型商贸、会展中心、展览馆中心主出入口、营业场所人员聚集部位、运钞交接区及押运通道等' },
  { key: 'B0602', value: '大型农贸市场主出入口、营业场所人员聚集部位等' },
  { key: 'B0701', value: '学校主出入口及其外部一定区域等' },
  { key: 'B0702', value: '学前教育主出入口及其外部一定区域等' },
  { key: 'B0801', value: '医院主出入口,挂号大厅、候诊大厅等开放区域的人员聚集部位及单位外围一定范围的部位' },
  { key: 'B0802', value: '专业公共卫生服务等开放区域的人员聚集部位及单位外围一定范围的部位' },
  { key: 'B0803', value: '社会工作服务等开放区域的人员聚集部位及单位外围一定范围的部位' },
  { key: 'B0901', value: '歌舞娱乐厅等娱乐业主要出入口及采集的图像能够覆盖到场所外围一定范围的部位等' },
  { key: 'B0902', value: '电子游戏厅出入口及采集的图像能够覆盖到场所外围一定范围的部位等' },
  { key: 'B0903', value: '互联网上网服务营业场所出入口及采集的图像能够覆盖到场所外围一定范围的部位等' },
  { key: 'B0904', value: '新闻和出版业场所出入口及采集的图像能够覆盖到场所外围一定范围的部位等' },
  { key: 'B0905', value: '民营画廊、小剧场等出入口及采集的图像能够覆盖到场外外围一定范围的部位等' },
  {
    key: 'B1001',
    value: '宾馆、酒店等旅馆业营业场所的主出入口、大厅、前台及采集的图像能够覆盖到场所外围一定范围的部位',
  },
  { key: 'B1002', value: '洗浴中心的主出入口、大厅、前台及采集的图像能够覆盖到场所外围一定范围的部位' },
  { key: 'B1101', value: '展览场馆出入口、安检区室外人员聚集区域（部位）等' },
  { key: 'B1102', value: '大型文化、体育场所出入口、安检区室外人员聚集区域（部位）等' },
  { key: 'B1201', value: '旅游景区主出入口、人员聚集区域（部位）' },
  { key: 'B1301', value: '单位出入口及采集的图像能够覆盖到单位外围一定范围的部位等' },
  { key: 'B1401', value: '社区治安保卫部位' },
  { key: 'B1502', value: '野生动植物重点场所出入口及采集的图像能够覆盖到外围一定范围的部位等' },
  { key: 'B1601', value: '住宅小区主要出入口及采集的图像能够覆盖到小区外围一定范围的部位等' },
  { key: 'B1701', value: '公共停车场主要出入口及采集的图像能够覆盖到外围一定范围的部位等' },
  { key: 'B1801', value: '危险物品场所周边、主要出入口及采集的图像能够覆盖到外围一定范围的部位等' },
  { key: 'B19', value: '涉稳重点部位（具体内容另发）' },
  { key: 'B2001', value: '清真寺出入口' },
  { key: 'B2101', value: '危险废物产生、存储、处置企业、场所出入口及周边一定区域、重点线路沿线。' },
  { key: 'B2102', value: '生态资源生产、加工企业及场所出入口及周边一定区域、重点线路沿线。' },
  { key: 'B2201', value: '文物保护单位主出入口及采集的图像能够覆盖到保护范围外一定区域的部位、外围主要通行区域等' },
  { key: 'B2202', value: '博物馆主出入口、安检区、室外人员聚集区域（部位）等' },
  { key: 'B2203', value: '其他不可移动文物主出入口及采集的图像能够覆盖到场所外围一定范围的部位' },
  { key: 'C01', value: '公安留置室' },
  { key: 'C02', value: '公安指挥中心大厅' },
  { key: 'C03', value: '公安检查站内部点位' },
  { key: 'C0401', value: '车管所大厅' },
  { key: 'C0402', value: '违法处理大厅' },
  { key: 'C0403', value: '事故处理大厅' },
  { key: 'C0404', value: '驾驶人考试考场' },
  { key: 'C0405', value: '机动车安全检验机构' },
];

export {
  checkConfigmodule,
  detectionRules,
  isUpdatePhyStatus,
  defaultDetectContent,
  defaultDetectContentSdk,
  historyComparisonConfigModule,
  needRecheckNumIndex,
  defaultEmphasisData,
  defaultGeneralData,
};
