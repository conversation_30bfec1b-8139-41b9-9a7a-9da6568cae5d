<!--
 * @Date: 2025-01-15 14:05:53
 * @LastEditors: liyuhang1 <EMAIL>
 * @LastEditTime: 2025-03-06 10:55:21
 * @FilePath: \icbd-view\src\views\juvenile\components\collect\school-out-alarm.vue
-->
<template>
  <div class="school-card-box">
    <div class="image-box">
      <img :src="data.faceCaptureVo.traitImg" alt="" />
    </div>
    <div class="list-box">
      <div class="item" v-for="item in itemList" :key="item.param">
        <div class="info-name">
          <Tooltip
            :content="item.title"
            placement="right"
            transfer
            theme="light"
          >
            <i class="iconfont" :class="item.icon"></i>
          </Tooltip>
        </div>
        <div class="info-content info-color-sub" :style="item.style">
          {{ data[item.param] || "--" }}
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "SchoolOutAlarm",
  props: {
    // 数据
    data: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      imageTest: require("@/assets/img/face.png") || "",
      itemList: [
        {
          icon: "icon-xingming",
          param: "name",
          title: "姓名",
          style: { color: "#2c86f8" },
        },
        {
          icon: "icon-nianling1",
          param: "age",
          title: "年龄",
          style: {},
        },
        {
          icon: "icon-shenfenzheng",
          param: "idCardNo",
          title: "身份证",
          style: {},
        },
        {
          icon: "icon-time",
          param: "absTime",
          title: "出没时间",
          style: {},
        },
        {
          icon: "icon-location",
          param: "deviceName",
          title: "出没地点",
          style: {},
        },
      ],
    };
  },
};
</script>

<style lang="less" scoped>
.school-card-box {
  padding: 10px;
  width: 320px;
  height: 140px;
  background: #f9f9f9;
  box-shadow: 0px 3px 5px 0px rgba(147, 171, 206, 0.7016);
  border: 1px solid #d3d7de;
  display: flex;
  gap: 10px;
  .image-box {
    width: 120px;
    height: 120px;
    img {
      width: 100%;
      height: 100%;
    }
  }
  .list-box {
    .item {
      display: flex;
      gap: 8px;
      font-size: 14px;

      .info-content {
        max-width: 130px;
        text-overflow: ellipsis;
        text-wrap: nowrap;
        overflow: hidden;
      }
    }
  }
}
</style>
