<template>
  <ui-modal title="标准转化" v-model="visible" :styles="styles">
    <ui-table
      class="ui-table"
      :table-columns="tableColumns"
      :table-data="tableData"
      :minus-height="minusTable"
      :loading="loading"
    >
    </ui-table>
    <ui-page class="page" :pageData="pageData" @changePage="changePage" @changePageSize="changePageSize"> </ui-page>
    <template #footer>
      <Button type="primary" class="plr-30">保存</Button>
    </template>
  </ui-modal>
</template>
<style lang="less" scoped></style>
<script>
export default {
  data() {
    return {
      visible: false,
      styles: {
        // top: ".8rem",
        width: '75%',
      },
      tableColumns: [
        { title: '原字段名', key: 'indexName' },
        { title: '数值', key: 'indexType' },
        { title: '映射字段名', key: 'calculateMethod' },
        { title: '映射字典', key: 'criterion' },
      ],
      tableData: [{}],
      minusTable: 480,
      loading: false,
      pageData: {
        pageNum: 1,
        pageSize: 20,
        totalCount: 0,
      },
    };
  },
  created() {},
  mounted() {},
  methods: {
    changePage() {},
    changePageSize() {},
  },
  watch: {
    visible(val) {
      this.$emit('input', val);
    },
    value(val) {
      this.visible = val;
    },
  },
  props: {
    value: {},
  },
  components: {
    UiTable: require('@/components/ui-table.vue').default,
  },
};
</script>
