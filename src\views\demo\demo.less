.demo {
  padding: 40px;
}
.color-list {
  display: flex;
  flex-direction: row;
  margin-bottom: 20px;
  .color-item {
    margin-right: 10px;
    display: flex;
    flex-direction: column;
    align-items: center;
  }
  .rect {
    width: 100px;
    color: @white;
    padding: 10px;
  }
  // color
  .primary-color {
    background: @link-normal-color;
  }
  .info-color {
    background: @info-color;
  }
  .success-color {
    background: @success-color;
  }
  .warning-color {
    background: @warning-color;
  }
  .error-color {
    background: @error-color;
  }
  .normal-color {
    background: @normal-color;
  }
  .disabled-color {
    background: @disabled-color;
  }
  // link
  .link-normal-color {
    background: @link-normal-color;
  }
  .link-hover-color {
    background: @link-hover-color;
  }
  .link-active-color {
    background: @link-active-color;
  }
  // font
  .title-font-color {
    background: @title-font-color;
  }
  .text-font-color {
    background: @text-font-color;
  }
  .input-font-color {
    background: @input-font-color;
  }
  .secondary-font-color {
    background: @secondary-font-color;
  }
  .auxiliary-font-color {
    background: @auxiliary-font-color;
  }
  // linear-gradient
  .linear-gradient-color {
    background: @linear-color;
  }
  .linear-gradient-hover-color {
    background: @linear-hover-color;
  }
  .linear-gradient-active-color {
    background: @linear-active-color;
  }
  // border-color
  .border-color {
    background: @border-color;
  }
}
.font-list {
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-bottom: 20px;
  .mr {
    margin-right: 10px;
  }
  .font-size12 {
    font-size: @font-size12;
  }
  .font-size14 {
    font-size: @font-size14;
  }
  .font-size16 {
    font-size: @font-size16;
  }
  .font-size18 {
    font-size: @font-size18;
  }
  .font-size20 {
    font-size: @font-size20;
  }
}
.checkbox-list {
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-bottom: 20px;
}