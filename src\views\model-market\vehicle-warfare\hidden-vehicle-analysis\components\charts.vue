<template>
  <div class="charts-box">
    <div class="plateNo">{{ plateNo }}</div>
    <div class="echart-wrap">
      <div ref="echart" class="echarts"></div>
    </div>
  </div>
</template>
<script>
  import * as echarts from 'echarts'
  import defaultOptions from "./echartsOption.js";
  export default {
    props: {
      statisticsList: {
        type: Array,
        default: () => []
      }
    },
    data () {
      return {
        dataAccessObject: {
          names: [],
          values: []
        },
        plateNo: '',
        defaultOptions,
        myEchart: null
      }
    },
    computed: {
      options() {
        this.defaultOptions.xAxis.data = this.dataAccessObject.names;
        this.defaultOptions.series[0].data = this.dataAccessObject.values;
        return this.defaultOptions;
      }
    },
    watch: {
      'statisticsList': {
        handler (val) {
          this.dataAccessObject = this.updataDataAccess(this.statisticsList)
          this.plateNo = this.statisticsList.length ? this.statisticsList[0].plateNo : ''
          this.myEchart && this.myEchart.setOption(this.options)
        },
        immediate: true
      }
    },
    mounted () {
      this.myEchart = echarts.init(this.$refs.echart)
      this.myEchart.setOption(this.options)
    },
    methods: {
      updataDataAccess (list) {
        return {
          names: list.map(item => item.day),
          values: list.map(item => item.num)
        }
      }
    }
  }
</script>
<style lang="less" scoped>
  .charts-box {
    width: 500px;
    height: 200px;
    display: flex;
    flex-direction: column;
    position: absolute;
    bottom: 20px;
    right: 20px;
    z-index: 9;
    background: #fff;
    box-shadow: 0px 3px 5px 0px rgba(0,0,0,0.3);
    border-radius: 4px;
    .plateNo {
      padding: 10px 20px;
    }
  }
  .echart-wrap {
    flex: 1;
    .echarts {
      height: 100%;
      width: 100%;
    }
  }
</style>