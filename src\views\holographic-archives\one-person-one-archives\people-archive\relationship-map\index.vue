<!--
 * @Author: du<PERSON><PERSON>
 * @Date: 2024-06-26 11:22:02
 * @LastEditors: duansen
 * @LastEditTime: 2024-08-13 15:10:54
-->
<template>
  <div class="trajectory">
    <div class="left">
      <Card type="people" :labelType="2" :baseInfo="baseInfo" />
    </div>
    <div class="right">
      <graph
        v-if="graphObj && hasGraphData"
        class="graph"
        :has-link="true"
        :has-toolbar="true"
        :layout-data="{
          linkDistance: 200,
        }"
        @finish="graphLoaded"
      ></graph>
      <ui-empty v-else></ui-empty>
    </div>
  </div>
</template>
<script>
import { mapGetters } from "vuex";
import relativeGraphMixin2 from "@/views/holographic-archives/mixins/relativeGraphMixin2";
export default {
  mixins: [relativeGraphMixin2],
  components: {
    graph: require("@/views/holographic-archives/components/graph").default,
    Card: require("@/views/holographic-archives/components/relationship-map/basic-information")
      .default,
  },
  props: {
    baseInfo: {
      type: Object | String,
      default: {},
    },
  },
  data() {
    return {};
  },
  computed: {
    ...mapGetters({
      graphObj: "systemParam/graphObj", // 是否有图谱
    }),
  },
};
</script>
<style lang="less" scoped>
.trajectory {
  display: flex;
  flex: 1;
  height: 100%;
  padding: 10px;
  padding-top: 16px;
  /deep/.card-content {
    height: calc(~"(100% - 30px)");
    overflow-y: auto;
    overflow-x: hidden;
    padding-top: 0 !important;
  }
  /deep/ .ui-card {
    height: 100%;
  }
  .left {
    margin-right: 10px;
    height: 100%;
    width: 350px;
  }
  .right {
    width: calc(~"100% - 360px");
    background: #fff;
    position: relative;
    .graph {
      position: relative;
    }
  }
}
/deep/ .rel-node {
  background-color: transparent !important;
  border: 0 !important;
}
</style>
