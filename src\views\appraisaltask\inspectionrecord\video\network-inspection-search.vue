<template>
  <div class="base-search">
    <div class="inline">
      <ui-label class="inline mr-lg mb-sm" label="组织机构" :width="70">
        <tree-select
          :tree-data="treeData"
          nodeKey="orgCode"
          class="width-md"
          v-model="searchData.orgCode"
          @current-change="currentChange"
          placeholder="请选择组织机构"
        >
        </tree-select>
      </ui-label>
      <ui-label class="inline mr-lg" label="设备编码" :width="70">
        <Input v-model="searchData.deviceId" class="width-lg" placeholder="请输入设备编码"></Input>
      </ui-label>
      <ui-label class="inline mr-lg" label="设备名称" :width="70">
        <Input v-model="searchData.deviceName" class="width-lg" placeholder="请输入设备名称"></Input>
      </ui-label>
      <ui-label :label="global.filedEnum.sbdwlx" :width="100" class="inline mr-lg">
        <Select
          clearable
          class="width-md"
          v-model="searchData.sbdwlx"
          :max-tag-count="1"
          :placeholder="`请选择${global.filedEnum.sbdwlx}`"
        >
          <Option
            v-for="(sbdwlxItem, bdindex) in dictData['propertySearch_sbdwlx']"
            :key="'sbdwlx' + bdindex"
            :value="sbdwlxItem.dataKey"
            >{{ sbdwlxItem.dataValue }}
          </Option>
        </Select>
      </ui-label>
      <ui-label class="inline mr-lg" label="联网状态" :width="70">
        <Select class="width-md" v-model="searchData.deviceStatus" clearable placeholder="请选择检测结果">
          <Option value="1" label="联网"></Option>
          <Option value="2" label="未联网"></Option>
        </Select>
      </ui-label>
    </div>
    <ui-label :width="0" label="" class="inline mb-sm search-button">
      <Button type="primary" class="mr-sm" @click="startSearch">查询</Button>
      <Button type="default" @click="resetSearchDataMx1(searchData, startSearch)">重置</Button>
    </ui-label>
  </div>
</template>
<script>
import user from '@/config/api/user';
export default {
  props: {
    currentTree: {
      type: Object,
      default() {
        return {};
      },
    },
    width: {
      type: Number,
      default: 155,
    },
    searchList: {
      type: Array,
      default() {
        return [];
      },
    },
    taskObj: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  data() {
    return {
      searchData: {
        orgCode: '',
        deviceId: '',
        deviceName: '',
        deviceStatus: '',
        resultId: '',
      },
      cardSearchList: [],
      dictData: {},
      treeData: [],
    };
  },
  mounted() {
    this.getDictData();
  },
  methods: {
    async getDictData() {
      try {
        const params = ['propertySearch_sbdwlx'];
        let res = await this.$http.post(user.queryDataByKeyTypes, params);
        const datas = res.data.data;
        params.forEach((key) => {
          let obj = datas.find((row) => {
            return !!row[key];
          });
          this.$set(this.dictData, key, obj[key]);
        });
      } catch (error) {
        console.log(error);
      }
    },
    currentChange({ orgCode, resultId }) {
      this.searchData.orgCode = orgCode;
      this.searchData.resultId = resultId;
      this.$emit('params-change', this.searchData);
    },
    startSearch() {
      this.$emit('startSearch', this.searchData);
    },
    resetClick() {
      this.resetSearchDataMx1(this.searchData);
    },
    resetSearchDataMx1() {
      this.searchData = {
        orgCode: '',
        deviceId: '',
        deviceName: '',
        messages: [],
        checkStatus: '',
        resultId: '',
      };
      this.$emit('startSearch', this.searchData);
    },
    getOrgTreeList(val) {
      if (val.orgCodeList && val.orgCodeList.length) {
        let { orgCode, resultId } = val.orgCodeList.find((item) => !item.disabled) || {};
        this.searchData.orgCode = orgCode || '';
        this.searchData.resultId = resultId || '';
        this.treeData = this.$util.common.arrayToJson(JSON.parse(JSON.stringify(val.orgCodeList)), 'id', 'parentId');
      }
    },
  },
  watch: {
    taskObj: {
      deep: true,
      immediate: true,
      handler: function (val) {
        let { orgCode } = this.taskObj.orgCodeList.find((item) => !item.disabled) || {};
        this.searchData = {
          orgCode: orgCode || '',
        };
        this.getOrgTreeList(val);
        this.copySearchDataMx(this.searchData);
        this.$nextTick(() => {
          this.startSearch();
        });
      },
    },
  },
  components: {
    TreeSelect: require('../faceviewdata/component/tree-select').default,
  },
};
</script>
<style lang="less" scoped>
.base-search {
  display: flex;
  justify-content: space-between;
  overflow: hidden;
  padding-bottom: 10px;
  .date-picker-box {
    display: flex;
  }
  .search-button {
    white-space: nowrap;
  }
}
.input-number-list {
  padding: 0px;
  width: 88px;
  /deep/ .ivu-input-number-controls-outside-btn {
    display: none;
  }
}
</style>
