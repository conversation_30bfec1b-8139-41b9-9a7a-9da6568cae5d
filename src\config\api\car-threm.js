export default {
  queryCsList: '/ivdg-ai-service/algorithm/config/select/', // 根据算法类型查询对应的厂商

  queryThremList: '/ivdg-data-governance-service/topic/statistics/queryTopicStatistics/', // 主题任务统计 - 首页接口
  queryComponentList: '/ivdg-data-governance-service/component/list', // 主题组件列表

  // 车辆结构化
  carRun: '/ivdg-data-governance-service/topic/vehicle/execute', // 车辆主题任务执行 - 开始运行
  videoRun: '/ivdg-data-governance-service/topic/video/execute', // 视频主题任务执行 - 开始运行
  queryCarList: '/ivdg-data-governance-service/topic/vehicle/view/', // 查询车辆列表信息（图）  topicId = 3
  queryCarJgh: '/ivdg-data-governance-service/topic/vehicle/viewAlgorithmConfig/', // 查询已配置的车辆结构化信息
  addCarJgh: '/ivdg-data-governance-service/topic/vehicle/algorithmConfig', // 车辆结构化配置

  // 车辆结构化属性完整性与准确检性测优化配置
  queryCarThremSelect: '/ivdg-data-governance-service/topic/vehicle/viewPropertyConfig/', // 车辆结构化属性完整性已选中数据
  addCarThrem: '/ivdg-data-governance-service/topic/vehicle/propertyConfig', // 车辆结构化属性完整性配置

  // 深圳提供
  queryPropertySearch: '/ivdg-asset-app/propertySearch/queryPropertySearchList', // 获取字段信息数据列表 (all)

  // 图像上传及时性检测
  queryCarUpload: '/ivdg-data-governance-service/topic/vehicle/viewTimelinessConfig/', // 图像上传及时性检测查询
  addCarUpload: '/ivdg-data-governance-service/topic/vehicle/timelinessConfig', // 图像上传及时性检测配置

  // 视频主题管理 topicId = 4
  queryHistoryVideo: '/ivdg-data-governance-service/topic/video/viewVideoStreamingCheckConfig/', // 历史视频流通畅检测查询  id 39
  addHistoryVideo: '/ivdg-data-governance-service/topic/video/videoStreamingCheckConfig',

  // 治理主题首页
  queryThremIndexList: '/topic/statistics/queryTopicStatistics/', // 治理主题首页列表查询
  // 任务追踪----车辆试图分页
  queryVehicleResultPageList: '/ivdg-data-governance-service/topic/vehicle/queryVehicleResultPageList', // 查询车辆视图异常库分页数据

  queryComponentStatisticsByTopicId:
    '/ivdg-data-governance-service/topic/statistics/queryComponentStatisticsByTopicId/', // 主题任务统计
  queryTotalAndNewlyAdd: '/ivdg-data-governance-service/topic/vehicle/queryTotalAndNewlyAdd', // 查询车辆视图总数和今日新增数

  idQueryCar: '/ivdg-data-governance-service/topic/vehicle/queryByVehicleInfoId/', // 根据车辆ID查询关联的异常数据
  queryStatisticsByReasonType: '/ivdg-data-governance-service/topic/vehicle/queryStatisticsByReasonType', // 查询每种异常类型对应的数据

  // 车辆导出模块
  carImportAllInfo: '/ivdg-data-governance-service/topic/vehicle/importAllInfo', // 车辆数据输出导出
  // 以下4模块导出
  // 1.图片抓拍时间准确性检测导出2查看图像上传及时性检测3.查看图像上传及时性检测4.查看车牌识别准确性检测优化
  carImportExcel: '/ivdg-data-governance-service/topic/vehicle/importExcel',
  carImportVehicleStructure: '/ivdg-data-governance-service/topic/vehicle/importVehicleStructure', // 车辆结构化属性准确检测导出
  viewVehicleResult: '/ivdg-data-governance-service/topic/vehicle/viewVehicleResult/', // 车辆主题属性详情
  // 视频导出模块
  carDataExport: '/ivdg-data-governance-service/topic/video/dataExport', // 视频数据输出导出
  carOsdexport: '/ivdg-data-governance-service/topic/video/osdexport', // OSD字幕标注合规率检测结果导出
  carRealtimeHistoryExport: '/ivdg-data-governance-service/topic/video/realtimeHistoryExport', // 视频流历史和实时导出

  updateStatus: '/ivdg-data-governance-service/component/updateStatus', // 主题组件开启/关闭

  queryTopicPropertyCheckList: '/ivdg-data-governance-service/topicComponent/queryTopicPropertyCheckList', // 查询规则的字段配置列表
  updateImgUrl: '/ivdg-data-governance-service/topicComponent/updateImgUrl', // 修改图像URL检测配置
  queryVehiclePageList: '/ivdg-data-governance-service/topic/vehicle/queryVehiclePageList', // 设备车辆查询并分页
  importQueryVehicleList: '/ivdg-data-governance-service/topic/vehicle/importQueryVehicleList', // 车辆任务追踪设备模式导出
};
