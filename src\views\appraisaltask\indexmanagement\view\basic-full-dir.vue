<template>
  <common-form v-bind="$props" ref="formData">
    <template #indexDefinition>
      <div class="base-text-color">
        <p>1、如果某个组织机构下有设备数据，则认为该组织机构数据已上报。</p>
        <p>2、如果某个组织机构向下有两个层级的下级组织，则：</p>
        <p>
          全量目录完整率=下一级已上报组织机构数/实有下一级组织机构数 * 下二级已上报组织机构数/实有下二级组织机构数。
        </p>
        <p>3、如果某个组织机构向下只有一个层级的下级组织，则全量目录完整率=下级已上报组织机构数/实有下级组织机构数。</p>
        <p>4、如果某个组织机构为末级组织机构，则：如果该组织机构有设备，则全量目录完整率为1，否则为0。</p>
      </div>
    </template>
  </common-form>
</template>
<script>
export default {
  name: 'basic-full-dir',
  props: {
    /**
     * 模式： edit 编辑 view 查看 add 新增
     */
    formModel: {
      required: true,
      type: String,
      default: 'edit',
    },
    /* 表单 */
    formData: {
      required: true,
      type: Object,
      default() {
        return {};
      },
    },
    value: {},
  },
  data() {
    return {};
  },
  methods: {
    async validate() {
      try {
        return await this.$refs.formData.validate();
      } catch (error) {
        throw new Error(error);
      }
    },
  },
  components: {
    CommonForm: require('@/views/appraisaltask/indexmanagement/components/common-form.vue').default,
  },
};
</script>
<style scoped lang="less"></style>
