<template>
    <div class="map-box">
        <div :id="mapId" class="map"></div>
        <right-button-group @pointMap="pointMap"> </right-button-group>
        <div class="heatingPower">
            <Checkbox v-model="single" @on-change="handleCheckChange">热力图</Checkbox>
        </div>
    </div>
</template>

<script>
import { NPGisMapMain } from '@/map/map.main';
import RightButtonGroup from '@/components/map/right-button-group.vue';
import { mapGetters, mapActions } from 'vuex';
let mapMain = null;
const infoWindowArr = [];
let selectWindowArr = [];
export default {
    name: '',
    components:{
        RightButtonGroup,    
    },
    props: {
        // 档案位置信息
        archivesPositionPoints: {
            type: Array,
            default: () => []
        },
        // 最新位置
        list: {
            type: Array,
            default: () => []
        },
        // 热力图数据
        heatData: {
            type: [],
            default: () => []
        },
        dotIndex: {
            type: [String, Number],
            default: () => 0
        }
    },
    data () {
        return {
            mapId: 'mapId' + Math.random(),
            heatMapLayer: null,
            single: true
        }
    },
    watch:{
        // 档案-位置信息
        archivesPositionPoints: {
            handler (list) {
                this.$nextTick(() =>{
                    if(mapMain){
                        this.positionInformationLayer(list)
                    }
                })
            },
            immediate: true
        },
        // 档案-热力图
        heatData: {
            handler (list) {
                this.$nextTick(() =>{
                    if(mapMain){
                        this.renderHeatMap(list, 20)
                    }
                })
            },
            immediate: true
        },
        dotIndex: {
            handler(val) {
               this.$nextTick(() =>{
                    if(mapMain && val > -1){
                        let item = {
                            ...this.list[val],
                            active: true
                        };
                        const point = new NPMapLib.Geometry.Point((item.geoPoint.lon || item.lon), (item.geoPoint.lat || item.lat))
                        mapMain.map.centerAndZoom(point, 17)
                        this.positionInformationLayer([item]);
                    }
               })
            },
            immediate: true
        } 
    },
    computed:{
         ...mapGetters({
            mapConfig: 'common/getMapConfig',
            mapStyle: 'common/getMapStyle',
            mapObj: 'systemParam/mapObj',
            globalObj: 'systemParam/globalObj',
        })   
    },
    created() {
            
    },
    async activated() {
        // await this.getMapConfig();
    },
    async mounted(){
        await this.getMapConfig();
        this.$nextTick(() => {
            let box = document.querySelector('.map');
            box.onmousewheel = (event) => {
                event = event || window.event;
                // if (event.wheelDelta > 0 || event.detail < 0) {
				// 	box.style.height = box.clientHeight 
				// } else {
				// 	box.style.height = box.clientHeight 
				// }
                box.style.height = box.clientHeight
				//取消火狐浏览器默认行为（因为是用addEventListener,所以必须用此方法来取消）
				event.preventDefault && event.preventDefault();
				//取消浏览器默认行为
				return false;
            };
            //为火狐浏览器绑定鼠标
			this.bind(box, "DOMMouseScroll", box.onmousewheel);
        })
    },
    methods: {
        bind(obj, eventStr, callback) {
            if (obj.addEventListener) {
                obj.addEventListener(eventStr, callback, false);
            } else {
                obj.attachEvent("on" + eventStr, function () {
                    callback.call(obj);
                });
            }
        },
        ...mapActions({
            setMapConfig: 'common/setMapConfig',
            setMapStyle: 'common/setMapStyle'
        }),
        async getMapConfig () {
            try {
                await Promise.all([this.setMapConfig(), this.setMapStyle()])
                this._initMap(this.mapConfig)
            } catch (err) {
                console.log(err)
            }
        },
        _initMap(data, style) {
            this.$nextTick(() =>{
                // 配置初始化层级
                mapMain = new NPGisMapMain()
                const mapId = this.mapId
                mapMain.init(mapId, data, style)
                // 禁止滚动条
                if (this.disableScroll) {
                    mapMain.map.disableScrollWheelZoom()
                }
                // 档案位置信息
                this.positionInformationLayer(this.archivesPositionPoints)
                // 热力图
                this.renderHeatMap(this.heatData, 20);
                setTimeout(() => {
                    this.configDefaultMap();
                },100)
            })
        },
        /**
         * 系统配置的中心点和层级设置
         */
        configDefaultMap () {
            let mapCenterPoint = this.globalObj.mapCenterPoint
            let mapCenterPointArray = !!mapCenterPoint ? this.globalObj.mapCenterPoint.split('_') : ''
            let mapLayerLevel = parseInt(this.globalObj.mapLayerLevel) || 14;
            let point = mapMain.map.getCenter();
            if (!!mapCenterPointArray.length) {
                point = new NPMapLib.Geometry.Point(parseFloat(mapCenterPointArray[0]), parseFloat(mapCenterPointArray[1]));
            }
            setTimeout(() =>{
                mapMain.map.centerAndZoom(point, mapLayerLevel)
            },900)
        },
        //全息档案-位置信息
        positionInformationLayer (points) {
            if (!points || points.length === 0) {
                return false
            }
            points.forEach((item, index) => {
                let temp = {}
                let k = (item.lon || item.geoPoint.lon) + '_' + (item.lat || item.geoPoint.lat);
                temp[k] == null ? (temp[k] = 0) : temp[k]++
                let imgUrl = '',
                label = null,
                icon = null;
                let size = new NPMapLib.Geometry.Size(28, 28)
                item.lon = item.lon ? item.lon : item.geoPoint.lon;
                item.lat = item.lat ? item.lat : item.geoPoint.lat;
                let marker = new NPMapLib.Symbols.Marker(item)
                if (item.type == 'face' || item.type == 'vehicle') {
                    size = new NPMapLib.Geometry.Size(21, 32)
                    // imgUrl = item.markerIconUrl
                    imgUrl = require(`@/assets/img/map/exclamationPoint.png`)
                    icon = new NPMapLib.Symbols.Icon(imgUrl, size)
                    icon.setAnchor(new NPMapLib.Geometry.Size(-size.width / 2, -size.height))
                    marker.setIcon(icon)
                    marker.k = k
                    mapMain.map.addOverlay(marker)
                } else {
                    // imgUrl = require(`@/assets/img/map/trajectory-red.png`)
                    let markerBg = new NPMapLib.Symbols.Marker(item)
                    imgUrl = item.capturePic || item.traitImg;
                    size = new NPMapLib.Geometry.Size(40, 40)
                    icon = new NPMapLib.Symbols.Icon(imgUrl, size)
                    icon.setAnchor(new NPMapLib.Geometry.Size(-size.width / 2, -size.height))
                    label = new NPMapLib.Symbols.Label(`${index + 1}`)
                    label.setStyle({
                        fontSize: 14, //文字大小
                        fontFamily: '宋体', //字体
                        // color: !item.active ? '#EA4A36' : '#2C86F8', //文字前景色
                        color:'#fff',
                        align: 'center', //对方方式
                        isBold: true //是否粗体
                    })
                    label.setOffset(new NPMapLib.Geometry.Size(-12, 32))
                    let imgSize = new NPMapLib.Geometry.Size(44, 52);
                    let imgTitle = item.active ? 'blue' : 'unRed'
                    imgUrl = require(`@/assets/img/map/point_${imgTitle}.png`)
                    let iconbg = new NPMapLib.Symbols.Icon(imgUrl, imgSize)
                    iconbg.setAnchor(new NPMapLib.Geometry.Size((-size.width / 2) -2, -size.height - 2))
                    markerBg.setLabel(label)
                    marker.setIcon(icon)
                    markerBg.setIcon(iconbg)
                    marker.k = k
                    mapMain.map.addOverlay(marker)
                    mapMain.map.addOverlay(markerBg)
                    markerBg.addEventListener(NPMapLib.MARKER_EVENT_CLICK, () => {
                        this.$emit('mapModal', item)
                    })
                }
            })
        },
        // 热力图的显隐
        handleCheckChange(val) {
            if (this.heatData && this.heatData.length > 0) {
                if(this.single) {
                    this.heatMapLayer.toggle(true)
                }else{
                    this.heatMapLayer.toggle(false)
                }
            }
        },
        // 渲染热力图
        renderHeatMap (dataList, radius) {
            if (!dataList || dataList.length === 0) {
                return false
            }
            let opt = {
                isBaseLayer: false,
                opacity: 1.0,
                projection: 'EPSG:900913',
                visible: true,
                radius: radius,
                name: 'heatLayer'
            }
            // 只存在一个 热力图
            let heatLayer = null
            if (this.heatMapLayer) {
                heatLayer = this.heatMapLayer
            } else {
                heatLayer = new NPMapLib.Layers.HeatMapLayer('heatLayer', opt)
            }
            // max 数据的 获取
            let countMax = 0
            let countMin = 0
            let currentCount = 0
            let _dataList = dataList.map(val => {
                currentCount = val.numCount || 1
                countMax = Math.max(countMax, currentCount)
                countMin = Math.min(countMin, currentCount)
                return {
                    lat: val.lat ? val.lat : val.geoPoint.lat,
                    lon: val.lon ? val.lon : val.geoPoint.lon,
                    count: currentCount
                }
            })
            let dataset = {
                max: countMax,
                min: countMin,
                data: _dataList
            }
            if (!this.heatMapLayer) {
                if (!this.addlimitLayerNum) return
                mapMain.map.addLayers([heatLayer])
            }
            heatLayer.setDataset(dataset)
            this.heatMapLayer = heatLayer
        },
        // 地图左右导航
        pointMap (type) {
            this[`${type}Map`]();
        },
        homingMap () {
            this.selectPoint();
        },
        // 固定放大地图
        enlargeMap () {
            mapMain.map.zoomInFixed();
        },
        // 固定缩小地图
        narrowMap () {
            mapMain.map.zoomOutFixed();
        },
        // 当前设备弹框信息内容
        selectPoint () {
            // console.log(this.chooseMapItem)
            // const { longitude: lon, latitude: lat } = this.chooseMapItem
            // this.selectItem({ lon, lat })
        },
        // 配置最大层级
        addlimitLayerNum () {
            if (!!Number(this.mapObj.maxNumberOfLayer) || 600) {
                let allLayers = mapMain.map.getAllLayers.length
                if(allLayers >= this.limitLayerNum) {
                    this.$Message.error('已超过配置最大图层数量')
                    return false
                }
            }
            return true
        },
    }
}
</script>

<style lang='less' scoped>
.map-box {
    width: 100%;
    height: 100%;
    overflow: hidden;
    position: relative;
    .map{
        height: 100%;
        width: 100%;
        position: relative;
    }
    .heatingPower{
        width: 90px;
        height: 32px;
        background: #FFFFFF;
        box-shadow: 0px 3px 5px 0px rgba(0,0,0,0.3);
        border-radius: 4px;
        position: absolute;
        top: 10px;
        left: 10px;
        text-align: center;
        line-height: 32px;
    }
}
</style>
