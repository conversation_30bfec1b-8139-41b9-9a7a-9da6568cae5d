<template>
  <Form ref="formData" :inline="true" :label-width="80">
    <FormItem label="姓名:" prop="name">
      <Input v-model="queryParam.name" placeholder="请输入"></Input>
    </FormItem>
    <FormItem label="身份证号:" prop="idCardNo">
      <Input v-model="queryParam.idCardNo" placeholder="请输入"></Input>
    </FormItem>
  </Form>
</template>
<script>
import { mapActions, mapGetters } from "vuex";
export default {
  data() {
    return {
      queryParam: {
        name: "",
        idCardNo: "", // 身份证号
      },
    };
  },
  computed: {
    ...mapGetters({
      nationTypeList: "dictionary/getNationTypeList", //民族类型
      genderList: "dictionary/getGenderList", // 性别类型
    }),
  },
  async created() {
    await this.getDictData();
  },
  mounted() {
    if (this.$route.query.idCardNo) {
      this.queryParam.idCardNo = this.$route.query.idCardNo;
    }
  },
  methods: {
    ...mapActions({
      getDictData: "dictionary/getDictAllData",
    }),

    /**
     * @description: 重置
     */
    reset() {
      this.queryParam.name = "";
      this.queryParam.idCardNo = "";
    },

    /**
     * @description: 获取查询参数，暴露给父组件
     * @return {object}
     */
    getQueryParams() {
      return { ...this.queryParam };
    },
  },
};
</script>
<style lang="less" scoped>
.separtor {
  margin: 0 10px;
  width: 16px;
  height: 1px;
  background: rgba(0, 0, 0, 0.45);
}
.flex-box {
  display: flex;
  align-items: center;
}
.select-record-kind {
  /deep/.ivu-select-item {
    width: 210px;
    text-overflow: ellipsis;
    overflow: hidden;
  }
}
</style>
