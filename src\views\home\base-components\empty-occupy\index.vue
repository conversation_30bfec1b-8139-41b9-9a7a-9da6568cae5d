<!--  -->
<template>
  <div class="empty-occupy" :class="{ validate: !isValidate && !componentList.length }">
    <tab-title
      class="tab-title"
      @on-change="onChangeTitle"
      v-model="activeValue"
      :data="tabData"
      :style-type="styleType"
      :high-light-id="highLightId"
      :hasHighLight="hasHighLight"
    >
      <div>
        <!-- 返回按鈕（想要有返回按钮，内部组件data必须要有showBack值进行控制 && 组件配置hasBackButton为true） -->
        <i
          class="icon-font icon-fanhui mr-sm"
          v-if="!!activeValueRef?.showBack && activeItem?.hasBackButton"
          @click="activeValueRef.showBack = false"
        ></i>
        <!-- 日期选择组件配置hasDatePicker为true，并且组件内部需要有initAll()函数，进行刷新操作） -->
        <DatePicker
          v-if="activeItem?.hasDatePicker && isHomeStatus && !activeValueRef?.showBack"
          class="mr-sm"
          :open="open"
          :value="year"
          confirm
          transfer
          :options="getDateInfo.options"
          :type="getDateInfo.type"
          :placeholder="getDateInfo.placeholder"
          transfer-class-name="home-date-transfer-box-body"
          @on-change="handleChange"
          @on-ok="handleOk"
          @on-clickoutside="open = false"
          ref="datePickerRef"
        >
          <i @click="handleClick" class="year-color"> {{ getDateInfo.showTime }}<Icon type="md-arrow-dropdown" /> </i>
        </DatePicker>
        <!-- 选择指标-组件配置hasIndexChoose为true，并且组件内部需要有initAll()函数，进行刷新操作） -->
        <index-choose
          class="mr-sm"
          v-if="activeItem?.hasIndexChoose && isHomeStatus"
          :source-data="sourceData"
          @chooseSelectData="chooseSelectData"
        ></index-choose>
        <!-- 选择指标模块-组件配置 handleChangeIndexModule，，并且组件内部需要有initAll()函数，进行刷新操作-->
        <index-module-choose
          class="mr-sm"
          v-if="activeItem?.hasIndexModuleChoose & isHomeStatus"
          @handleChangeIndexModule="handleChangeIndexModule"
        ></index-module-choose>

        <!-- 筛选 -->
        <FilterSelect
          v-if="activeItem?.hasFilterSelect && isHomeStatus"
          :list="filterList"
          :default-selected="defaultFilterSelected"
          class="mr-sm"
          ref="filterSelect"
          @change="onClickDropdown"
        ></FilterSelect>

        <!-- ZDR数据治理 才显示 -->
        <ZdrFilter
          v-if="activeItem?.componentId === 'ZDRDataGovernance' && isHomeStatus"
          class="mr-sm"
          :default-time-info="zdrTimeInfo"
          @zdrSelectedFn="zdrSelectedFn"
        ></ZdrFilter>

        <!-- 更多 -->
        <div v-if="activeItem?.hasMoreButton && isHomeStatus" class="more-btn pointer" @click="openMessage">更多>></div>
        <i
          class="icon-font icon-wenhao vt-middle icon-warning mr-sm icon-details hide"
          v-show="activeItem?.isShowTip"
          @click="$emit('on-index-detail', activeItem)"
        ></i>
      </div>
      <!-- 编辑状态，并且已经有图表再展示 -->
      <div v-show="isEditStatus && !!componentList.length">
        <i class="icon-font icon-yichu2 ml-sm" title="移除" @click.stop="deleteCurrentEchart"></i>
      </div>
    </tab-title>
    <div class="empty-tips" v-if="!componentList.length">
      <p>请从右侧图表库拖拽</p>
      <p>最多{{ isLongEchartBox ? longEchartBoxShowNum : normalEchartBoxShowNum }}个至此处</p>
    </div>
    <template v-for="item in componentList">
      <component
        :ref="`${item.componentId}Ref`"
        :is="item.componentId"
        :key="item.componentId"
        :year="year"
        :select-data="selectData"
        :index-module-select-data="indexModuleSelectData"
        :componentName="item.componentId"
        :cuptureMonthOrDay="filterObj.cuptureMonthOrDay"
        :time-info="zdrTimeInfo"
        @hook:mounted="getComRefs"
        @on-capture-number-details="onCaptureNumberDetails"
        v-bind="$attrs"
        v-on="$listeners"
        :style-type="styleType"
        :component-config="item"
        v-if="item.componentId === activeValue"
      >
      </component>
    </template>
  </div>
</template>
<script>
import home from '@/config/api/home';
import { mapActions, mapGetters } from 'vuex';
export default {
  name: 'EmptyOccupy',
  props: {
    // 是否是首页
    isHomeStatus: {
      default: true,
    },
    // 进入的高亮- tab的id
    highLightId: {
      default: null,
    },
    // 是否有高亮效果，与highLightId一起使用
    hasHighLight: {
      default: false,
    },
    isValidate: {
      default: true,
    },
    componentList: {
      default: () => [],
    },
    isEditStatus: {
      default: false,
    },
    // 默认的组件tab /activeValue
    defaultActiveTab: {},
    // 是否为长图表
    isLongEchartBox: {
      default: false,
    },
    styleType: {},
  },
  components: {
    DatePick: () => import('./date-pick.vue'),
    IndexChoose: () => import('./index-choose.vue'),
    IndexModuleChoose: () => import('./index-module-choose.vue'),
    TabTitle: () => import('@/views/home/<USER>/tab-title.vue'),
    DeviceNumber: () => import('@/views/home/<USER>/device-number.vue'), // 设备数量
    CollectionArea: () => import('@/views/home/<USER>/collection-area.vue'), // 采集区域类型
    DeviceQuality: () => import('@/views/home/<USER>/device-quality.vue'), // 设备质量
    VideoQuality: () => import('@/views/home/<USER>/video-quality/index.vue'), // 视频流质量
    VideoQualityImportant: () => import('@/views/home/<USER>/video-quality/index.vue'), // 视频流质量
    VideoImageQuality: () => import('@/views/home/<USER>/video-image-quality.vue'),
    ZDRDataQuality: () => import('@/views/home/<USER>/zdr-data-quality.vue'), // ZDR数据质量
    QualityFace: () => import('@/views/home/<USER>/quality-face.vue'),
    QualityVehicle: () => import('@/views/home/<USER>/quality-vehicle.vue'),
    GovernTendency: () => import('@/views/home/<USER>/govern-tendency.vue'), //右下echarts
    GovernResult: () => import('@/views/home/<USER>/govern-result.vue'), //右下echarts
    PlatformStability: () => import('@/views/home/<USER>/platform-stability.vue'),
    AssessmentRanking: () => import('@/views/home/<USER>/assessment-ranking.vue'), //右上排名列表
    VideoOnlineChanges: () => import('@/views/home/<USER>/video-online-changes.vue'), // 视频监控在线变化
    FaceOnlineChanges: () => import('@/views/home/<USER>/video-online-changes.vue'), // 人卡在线变化
    VehicleOnlineChanges: () => import('@/views/home/<USER>/video-online-changes.vue'), // 车卡在线变化
    FaceCapturerNumber: () => import('@/views/home/<USER>/capture-number.vue'), // 人脸抓拍数量
    VehicleCapturerNumber: () => import('@/views/home/<USER>/capture-number.vue'), // 车辆抓拍数量
    FilterSelect: () => import('./filter-select.vue'),
    MessageNotify: () => import('@/views/home/<USER>/message-notify.vue'),
    BayonetsOnlineNumber: () => import('@/views/home/<USER>/bayonets-online-number.vue'), // 卡口在线数量
    GovernWorkOrder: () => import('@/views/home/<USER>/govern-work-order.vue'), // 治理工单
    VideoCancelRate: () => import('@/views/home/<USER>/video-cancel-rate.vue'), // 视频监控设备撤销率
    ZDRDataGovernance: () => import('@/views/home/<USER>/zdr-data-governance.vue'), // ZDR数据治理
    PeopleAndCarPictures: () => import('@/views/home/<USER>/PeopleAndCarPictures.vue'), // 人车抓拍图片质量
    EquipmentActivity: () => import('@/views/home/<USER>/equipment-activity.vue'), // 人车设备活跃率
    ZdrFilter: () => import('./zdr-filter.vue'),
  },
  watch: {
    isEditStatus() {
      this.handleEditTabTitle();
    },
    componentList: {
      handler() {
        this.reallyTabData = this.componentList.map((item) => {
          return {
            label: item.name,
            id: item.componentId,
            ...item,
          };
        });
        this.handleEditTabTitle();
        if (!this.tabData.length) return;
        let activeTabIndex = 0;
        // 有默认的activeValue - 切换到默认的tab
        if (this.defaultActiveTab) {
          let Index = this.tabData.findIndex((item) => item.id === this.defaultActiveTab);
          Index !== -1 ? (activeTabIndex = Index) : null;
        }
        // 外部也更新高亮字段的数据
        this.$emit('changeActiveTab', activeTabIndex);
        this.activeValue = this.tabData[activeTabIndex].id;
      },
      immediate: true,
    },
    activeItem: {
      handler(obj) {
        if (obj?.hasDatePicker) {
          this.year = this.isCaptureNumberDetailsEntry ? this.defaultYear : this.getDateInfo.realTime;

          if (this.isCaptureNumberDetailsEntry) {
            this.isCaptureNumberDetailsEntry = false;
            this.defaultYear = null;
          }
        }
      },
      deep: true,
      immediate: true,
    },
  },
  data() {
    return {
      tabData: [],
      open: false,
      year: '',
      options: {
        disabledDate: function (date) {
          let now = new Date().getFullYear();
          return date.getFullYear() > now;
        },
      },
      optionsMonth: {
        disabledDate: (date) => {
          return date.getTime() > new Date().getTime();
        },
      },
      optionsDate: {
        disabledDate: (date) => {
          return date.getTime() > new Date().getTime();
        },
      },
      // 上面都是时间控件
      selectData: [], // 指标选择
      sourceData: {},
      indexModuleSelectData: '1', // 指标模块选择
      activeValue: null,
      activeValueRef: {}, // 当前组件ref
      filterList: [
        {
          key: 'month',
          name: '月趋势',
        },
        {
          key: 'date',
          name: '日趋势',
        },
      ],
      filterObj: {
        cuptureMonthOrDay: 'month', // 抓拍数量
      },
      defaultFilterSelected: 'month',
      isCaptureNumberDetailsEntry: false,
      defaultYear: null,
      zdrTimeInfo: {
        value: 'all',
        startTime: '',
        endTime: '',
      },
    };
  },
  computed: {
    ...mapGetters({
      longEchartBoxShowNum: 'home/getLongEchartBoxShowNum',
      normalEchartBoxShowNum: 'home/getNormalEchartBoxShowNum',
      cacheRouterList: 'tabs/getCacheRouterList',
    }),
    activeItem() {
      return this.componentList.find((item) => item.componentId === this.activeValue);
    },
    getDateInfo() {
      let obj = {
        showTime: '', // 如：xx年xx月xx日
        realTime: '', // 如： 2023-06-23
        options: this.options,
        type: 'year',
        placeholder: '请选择年',
      };
      switch (this.activeItem?.dateType) {
        case 'month':
          obj.showTime = this.$util.common.turnFormat(this.year, 'yyyy-MM');
          obj.realTime = this.$util.common.formatDate(new Date(), 'yyyy-MM');
          obj.options = this.optionsMonth;
          obj.placeholder = '请选择月';
          break;
        case 'date':
          obj.showTime = this.$util.common.turnFormat(this.year, 'yyyy-MM-dd');
          obj.realTime = this.$util.common.formatDate(new Date(), 'yyyy-MM-dd');
          obj.options = this.optionsDate;
          obj.placeholder = '请选择日';
          break;
        case 'year':
        default:
          obj.showTime = this.$util.common.turnFormat(this.year, 'yyyy');
          obj.realTime = `${new Date().getFullYear()}`;
          obj.options = this.options;
          obj.placeholder = '请选择年';
          break;
      }
      obj.type = this.activeItem?.dateType || 'year';
      return obj;
    },
  },
  activated() {
    this.getComRefs();
  },
  methods: {
    ...mapActions({
      setCacheRouterList: 'common/setCacheRouterList',
    }),
    getComRefs() {
      this.$nextTick(async () => {
        this.activeValueRef = this.findActiveValueRef();
        // 有index-choose组件的操作才去执行这个
        if (this.activeItem?.hasIndexChoose) {
          Object.keys(this.sourceData).length ? null : await this.getAllEvaluationIndex();
          this.randomIndex();
        }
        // 解决全屏screenfull下，一些DatePicker组件设置了 transfer弹框不展示问题
        if (this.activeItem?.hasDatePicker) {
          let bigParent = this.$refs.datePickerRef?.$parent?.$parent?.$parent?.$el;
          let dropdownTransfer = document.querySelectorAll('.home-date-transfer-box-body');
          if (!bigParent || !dropdownTransfer) return;
          dropdownTransfer.forEach((dom) => {
            bigParent.appendChild(dom);
          });
        }
      });
    },
    /**
     * 编辑状态的一些处理
     */
    handleEditTabTitle() {
      this.tabData = [...this.reallyTabData];
      // 编辑状态
      if (!this.isEditStatus) return;
      // 没有图表
      let maxLen = this.componentList.length;
      let diffNum = this.isLongEchartBox ? this.longEchartBoxShowNum - maxLen : this.normalEchartBoxShowNum - maxLen;
      if (diffNum > 0) {
        this.tabData.push({
          label: `可再添加${diffNum}个图表`,
          id: '-1',
          disabled: true,
        });
      }
    },
    /**
     * 日期选择筛选
     */
    handleClick() {
      this.open = !this.open;
    },
    handleChange(date) {
      this.year = date;
    },
    async handleOk() {
      this.open = false;
      this.activeValueRef.initAll();
    },
    /**
     * 筛选指标
     */
    chooseSelectData(selectData) {
      this.selectData = selectData;
      this.activeValueRef?.initAll?.();
    },
    /**
     * 筛选指标模块
     */
    handleChangeIndexModule(indexModuleSelectData) {
      this.indexModuleSelectData = indexModuleSelectData;
      this.$nextTick(() => {
        this.activeValueRef?.initAll?.();
      });
    },
    findActiveValueRef() {
      return this?.$refs[`${this.activeValue}Ref`]?.[0];
    },
    // 切换后重新刷新数据
    onChangeTitle(id, val, index) {
      this.open = false;
      this.handlerDateCom();
      this.activeItem?.hasFilterSelect ? this.$refs.filterSelect?.resetSelected() : null;
      this.$emit('changeActiveTab', index);
    },
    /**
     * 删除当前图表
     */
    deleteCurrentEchart() {
      this.$emit('deleteCurrentEchart', this.activeValue);
    },
    /**
     * --------- index-choose组件使用----------
     */
    // 随机选3个指标
    randomIndex() {
      this.selectData = [];
      if (this.sourceData.hasOwnProperty(1)) {
        let indexTypeList = this.sourceData[1].map((item) => item.indexType);
        this.selectData = indexTypeList.splice(0, 3);
      }
      this.chooseSelectData(this.selectData);
    },
    async getAllEvaluationIndex() {
      try {
        let {
          data: { data },
        } = await this.$http.get(home.getAllEvaluationIndex);
        this.sourceData = data || {};
      } catch (error) {
        console.log(error);
      }
    },
    // 日、月趋势  过滤时 触发
    onClickDropdown(filterValue) {
      this.handlerDateCom(filterValue);
      // 重新查询
      this.$nextTick(() => {
        this.activeValueRef.initAll();
      });
    },
    handlerDateCom(val = 'month') {
      if (this.activeItem?.hasDatePicker && this.activeItem?.hasFilterSelect) {
        this.defaultFilterSelected = val;
        this.filterObj.cuptureMonthOrDay = val;
        this.activeItem.dateType = val;
      }
    },
    // 人脸、车辆 抓拍数量组件触发
    onCaptureNumberDetails(detailsItem) {
      let { day } = detailsItem;
      if (!day) return;
      this.onClickDropdown('date');
      this.isCaptureNumberDetailsEntry = true;
      this.defaultYear = this.$util.common.turnFormat(day, '年-月-日');
      this.defaultFilterSelected = 'date';
    },
    // 更多
    openMessage() {
      // 消息通知
      if (this.activeItem.componentId === 'MessageNotify') {
        this.addTab({ applicationName: '我的消息', name: 'mymessage' });
      }
    },
    //添加标签
    addTab(route) {
      const index = this.cacheRouterList.findIndex((row) => row.name === route.name);
      if (index === -1) {
        this.cacheRouterList.push({
          name: route.name,
          path: `/${route.name}`,
          text: route.applicationName,
        });
        this.setCacheRouterList(this.cacheRouterList);
      }
      this.$router.push({
        name: route.name,
      });
    },
    zdrSelectedFn(data) {
      this.zdrTimeInfo = this.$util.common.deepCopy(data);
    },
  },
};
</script>
<style lang="less" scoped>
.empty-occupy {
  position: relative;
  width: 100%;
  height: 100%;
  flex: 1;
  display: flex;
  flex-direction: column;
  &.validate {
    border: 1px solid #ea4a36;
    .empty-tips {
      color: #ea4a36;
    }
    @{_deep}.link-text-disabled {
      color: #ea4a36;
    }
  }
  .empty-tips {
    color: #d1651c;
    text-align: center;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    font-size: 16px;
  }
  @{_deep}.ivu-date-picker-cells-cell {
    margin: 4px 12px;
  }
  .tab-title {
    // height: 30px;
    .icon-fanhui {
      color: #0185f6;
    }
    @{_deep}.ivu-select-dropdown {
      top: 25px !important;
      left: auto !important;
      right: 0 !important;
    }
    @{_deep} .ivu-picker-confirm {
      .ivu-btn-default {
        display: none;
      }
      display: flex;
      justify-content: center;
      align-items: center;
    }
    .year-color {
      color: #63ccfc;
      margin-left: 10px;
    }
    &:hover {
      @{_deep} .icon-warning {
        display: inline;
      }
    }
  }
  .icon-yichu2 {
    color: #ea4a36;
  }
  .more-btn {
    color: #63ccfc;
    margin: 5px 10px 0 0;
  }
}
</style>
