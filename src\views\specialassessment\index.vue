<template>
  <div class="specialassessment">
    <specialassessment-menu
      class="specialassessment-menu"
      :menu-list="menuList"
      placeholder="请输入专题名称检索"
      :default-expanded-keys="defaultExpandedKeys"
      @getIndexData="getIndexData"
      @searchMenuList="searchMenuList"
      ref="specialassessmentMenu"
    ></specialassessment-menu>
    <div class="specialassessment-content ml-sm auto-fill">
      <div class="content-panel auto-fill">
        <component
          :is="componentName"
          :detection-time="detectionTime"
          :index-data="indexData"
          :menu-list="menuList"
          :statistics-list="statisticsList"
          :all-tag-list="allTagList"
          @resetCurrentNode="resetCurrentNode"
        ></component>
      </div>
    </div>
  </div>
</template>
<script>
import dealWatch from '@/mixins/deal-watch';
import taganalysis from '@/config/api/taganalysis';
import evaluationoverview from '@/config/api/evaluationoverview';
import { mapActions } from 'vuex';
export default {
  name: 'specialassessment',
  mixins: [dealWatch],
  data() {
    return {
      componentName: null,
      detectionTime: [],
      menuList: [],
      indexData: {},
      keyword: '',
      defaultExpandedKeys: [],
      statisticsList: [],
      allTagList: [],
    };
  },
  created() {
    this.getAlldicData();
    this.getTagList();
  },
  mounted() {
    this.startWatch(
      '$route',
      () => {
        this.getParams();
      },
      { immediate: true, deep: true },
    );
    this.getCustomTreeData();
  },
  methods: {
    ...mapActions({
      getAlldicData: 'algorithm/getAlldicData',
    }),
    async getCustomTreeData() {
      try {
        const params = {
          keyword: this.keyword,
        };
        const {
          data: { data },
        } = await this.$http.get(evaluationoverview.getCustomTrees, { params });
        if (!data) {
          this.menuList = [];
          return false;
        }
        this.menuList = data.map((item) => {
          // 树结构第一层级的图标由前端控制，子节点的图标由后端返回
          return {
            ...item,
            icon: 'icon-qsb-wenjianjia', // 未展开的图标
            activeIcon: 'icon-qsb-wenjianjiazhankai', // 展开图标
          };
        });
        // 左侧树形控件默认展开第一个
        this.defaultExpandedKeys = [this.menuList[0].id];
      } catch (e) {
        console.log(e);
      }
    },
    async getIndexData(data, flag = false) {
      this.indexData = { ...data };
      const name = this.$route.name;
      // 检测时间：开始就默认 第一个批次的时间， 这样就没必要 在 title-bar组件中监听detectionTime的时候才设置默认第一项，且又触发一遍this.$router.push，这个写法是有bug的，会存在接口调两次，且batchIds不对的情况
      await this.getDetectionTime();
      await this.getStatisticsList();
      this.queryParams = Object.assign({}, this.$route.query, {
        indexName: data.name,
        indexType: data.key,
        cptname: data.componentName,
        dateType: '2',
        examineTime: this.detectionTime[0]?.date || '',
        batchIds: this.detectionTime[0]?.options[0].batchIds
          ? this.detectionTime[0]?.options[0].batchIds.join(',')
          : '',
        title: data.title,
        // 默认 行政区划
        statisticsCode: this.statisticsList.some((item) => item.value === '2')
          ? '2'
          : this.statisticsList[0]?.value || '2',
      });
      if (flag) {
        this.queryParams = {
          dateType: '2',
          title: data.title,
          examineTime: data.examineTime,
          batchIds: data.batchIds,
          cptname: data.componentName,
          indexName: data.indexName,
          indexType: data.indexType,
          statisticsCode: this.statisticsList[0]?.value || '',
        };
      }
      this.$router.push({
        name,
        query: this.queryParams,
      });
      this.componentName = null;
      this.$nextTick(() => {
        this.componentName = require(`./special-modules/${data.componentName}/index.vue`).default;
      });
    },
    searchMenuList(val) {
      this.keyword = val;
      this.getCustomTreeData();
    },
    async getDetectionTime() {
      const details = this.indexData.details || [];
      const taskIndexIds = details.map((item) => item.taskIndexId);
      try {
        // 直接打开专题考核页面，query中不存在showResult字段值，接口入参需要该字段
        let { showResult } = this.$route.query;
        const params = {
          taskIndexIds: taskIndexIds,
          showResult: showResult ? null : 1,
        };
        let {
          data: { data },
        } = await this.$http.post(evaluationoverview.getHistoryBatchInfoByCustom, params);
        this.detectionTime = data;
      } catch (e) {
        console.log(e);
      }
    },
    getParams() {
      if (!Object.keys(this.$route.query).length){
        this.componentName = null;
        this.defaultExpandedKeys = this.menuList.length ? [this.menuList[0].id] : [];
        return;
      }
      let { taskSchemeId } = this.$route.query;
      if (!taskSchemeId) return;
      this.defaultExpandedKeys = [taskSchemeId];
    },
    resetCurrentNode(data) {
      let { batchId, examineTime, taskIndexId, treeId, componentName } = data;
      let arr = this.$util.common.jsonToArray(this.$util.common.deepCopy(this.menuList));
      let arr1 = arr.filter((item) => item.id === treeId);
      let { title, details } = arr1[0];
      let info = {
        indexName: details[0].indexName,
        indexType: details[0].jobType,
        componentName: componentName,
        dateType: '2',
        title: title,
        batchIds: batchId,
        examineTime: examineTime,
        treeId: treeId,
        details: details,
        taskIndexId: taskIndexId,
      };
      this.getIndexData(info, 'reset');
      this.$refs.specialassessmentMenu.$refs.tree.setCurrentKey(null);
      this.defaultExpandedKeys = [treeId];
      this.$nextTick(() => {
        this.$refs.specialassessmentMenu.$refs.tree.setCurrentKey(treeId);
      });
    },
    // 获取统计项列表
    async getStatisticsList() {
      let { details, id } = this.indexData;
      let ids = id.split('#');
      const indexIds = details ? details.map((item) => item.indexId) : [];
      try {
        const params = {
          taskSchemeId: ids[0],
          indexIds,
        };
        let {
          data: { data },
        } = await this.$http.post(evaluationoverview.getStatisticalModel, params);
        this.statisticsList = Object.keys(data).map((key) => {
          return { value: key, name: data[key] };
        });
      } catch (e) {
        console.log(e);
      }
    },
    async getTagList() {
      try {
        let res = await this.$http.post(taganalysis.getDeviceTag, {
          isPage: false,
        });
        let arr = res.data.data || [];
        this.allTagList = arr.map((item) => {
          return { id: item.tagId, ...item };
        });
      } catch (err) {
        console.log(err);
      }
    },
  },
  components: {
    SpecialassessmentMenu: require('./components/specialassessment-menu.vue').default,
  },
};
</script>

<style lang="less" scoped>
.specialassessment {
  width: 100%;
  height: 100%;
  display: flex;
  overflow: hidden;
  &-menu {
    width: 300px;
    height: 100%;
  }
  &-content {
    flex: 1;
    height: 100%;
    padding-bottom: 16px;
    background: var(--bg-content);
  }
}
</style>
