<template>
  <div class="home-view">
    <!-- 江苏省厅 -->
    <AgenciesHome v-if="systemConfig.distinguishVersion === '2'"></AgenciesHome>
    <!--    内江-->
    <neijiang-home v-else-if="systemConfig.distinguishVersion === '5'"></neijiang-home>
    <!-- 济南 -->
    <BaseHome v-else-if="systemConfig.distinguishVersion === '11'"></BaseHome>
    <Home v-else></Home>
  </div>
</template>
<script>
import { mapGetters } from 'vuex';
export default {
  name: 'home',
  components: {
    BaseHome: require('./base-home.vue').default,
    Home: require('./home').default,
    AgenciesHome: require('./agencies-home').default,
    NeijiangHome: require('./neijiang-home').default,
  },
  props: {},
  data() {
    return {};
  },
  computed: {
    // systemConfig.distinguishVersion 1：默认，2:江苏省厅,3:河北(石家庄),4:四川省厅,5:内江市局,6:四川高新区,7:济南市局,8:甘肃省厅,9:兰州,10:乌市
    ...mapGetters({
      systemConfig: 'common/getSystemConfig',
    }),
  },
  watch: {},
  filter: {},
  created() {},
  mounted() {},
  methods: {},
};
</script>
<style lang="less" scoped>
.home-view {
  width: 100%;
  height: 100%;
}
</style>
