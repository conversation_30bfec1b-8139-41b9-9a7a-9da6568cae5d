<template>
  <div class="compare auto-fill">
    <section class="compare-wrapper">
      <div class="wrapper">
        <p class="mb-xs">置信身份</p>
        <div class="indentify-message">
          <ui-frame>
            <ui-image :src="rowData.identityPhoto" class="image" />
          </ui-frame>
          <ul class="message-ul mt-sm ml-lg">
            <li class="message-li">
              <span class="label">姓名：</span>
              <span class="message">{{ rowData.name || '未知' }}</span>
            </li>
            <li class="message-li">
              <span class="label">性别：</span>
              <span class="message">{{ rowData.personGenderText || '未知' }}</span>
            </li>
            <li class="message-li">
              <span class="label">证件号：</span>
              <span class="message">{{ rowData.idCard || '未知' }}</span>
            </li>
            <li class="">
              <tags-more></tags-more>
            </li>
          </ul>
        </div>
      </div>
      <div class="ml-lg mr-lg">
        <double-arrow></double-arrow>
      </div>
      <div class="wrapper">
        <p class="mb-xs">抓拍图片</p>
        <div class="indentify-message">
          <ui-frame>
            <ui-image :src="rowData.facePath" class="image" />
          </ui-frame>
          <ul class="message-ul mt-lg ml-lg">
            <li class="message-li">
              <span class="label">抓拍时间：</span>
              <span class="message"> {{ rowData.logTime }}</span>
            </li>
            <!-- <li class="message-li">
							<span class="label">姓名：</span>
							<span class="message">{{ rowData.name }}</span>
						</li> -->
            <li class="message-li">
              <span class="label"> 抓拍地点： </span>
              <span class="message">
                {{ rowData.address }}
              </span>
            </li>
          </ul>
        </div>
      </div>
    </section>
    <ui-table class="ui-table mt-lg auto-fill" :table-columns="tableColumns" :table-data="tableData" :loading="loading">
    </ui-table>
    <div class="decision-box" v-if="tableData.length">
      <span>综合判定</span>
      <p>
        <span
          class="icon-font icon-zhiliangfen-line standard_icon"
          :class="isUnQuatify ? 'font-warning' : 'font-green'"
        >
          <i class="icon_text_error" :class="isUnQuatify ? 'font-warning' : 'font-green'">
            {{ isUnQuatify ? '不是同一人' : '是同一人' }}
          </i>
        </span>
      </p>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    trailDetailData: {
      default: () => {
        return {};
      },
    },
  },
  computed: {
    isUnQuatify() {
      return this.tableData.some((item) => {
        return !item.flag;
      });
    },
  },
  data() {
    return {
      tableColumns: [
        {
          title: '检测算法',
          key: 'algorithmType',
          className: 'table-action-padding',
        },
        { title: '判定结果', key: 'result', className: 'table-action-padding' },
      ],
      tableData: [],
      minusTable: 600,
      loading: false,
      pageData: {
        pageNum: 1,
        pageSize: 20,
        totalCount: 0,
      },
      rowData: {
        identityPhoto: '',
        name: '',
        gender: '',
        idCard: '',
        facePath: '',
        logTime: '',
        address: '',
      },
    };
  },
  created() {},
  methods: {},
  watch: {
    trailDetailData: {
      handler(val) {
        this.rowData = {
          identityPhoto: '',
          name: '',
          gender: '',
          idCard: '',
          facePath: '',
          logTime: '',
          address: '',
        };
        Object.assign(this.rowData, val.row);
        if (val.extraResultObj) {
          this.tableData = val.extraResultObj.map((item) => {
            return {
              algorithmType: item.algorithmType,
              result: item.score * 100 + '%',
            };
          });
        }
      },
      immediate: true,
    },
  },
  components: {
    UiTable: require('@/components/ui-table.vue').default,
    UiFrame: require('@/components/ui-frame.vue').default,
    DoubleArrow: require('../components/double-arrow.vue').default,
    TagsMore: require('@/components/tags-more.vue').default,
  },
};
</script>
<style lang="less" scoped>
.compare {
  width: 100%;
  height: 100%;
}
.compare-wrapper {
  color: var(--color-content);
  display: flex;
  align-items: center;
  .wrapper {
    background: var(--bg-card-1);
    padding: 15px 30px;
    flex: 1;
    .image {
      display: inline-block;
      width: 180px;
      height: 180px;
    }
  }
  .double-arrow {
    margin: 0 20px;
  }
}
.indentify-message {
  display: flex;
  .message-ul {
    .message-li {
      height: 30px;
      line-height: 30px;
      .label {
        color: var(--color-nav-tag);
      }
    }
  }
}
.decision-box {
  display: flex;
  height: 100px;
  line-height: 100px;
  background: var(--bg-table-header-th);
  color: var(--color-primary);
  padding: 0 20px;
  align-items: center;
  > span,
  > p {
    flex: 1;
  }
}
.standard_icon {
  vertical-align: middle;
  font-size: 120px;
  position: relative;
  .icon_text_error {
    font-size: 16px;
    position: absolute;
    right: 20px;
    top: 10px;
    font-weight: bold;
    transform: rotate(-32deg);
  }
  .font-green {
    right: 25px;
    top: 7px;
  }
}
</style>
