<template>
  <div class="abnormal-details-box">
    <component ref="detailComponent" :is="getComponentName" v-model="visible" v-bind="componentProps" v-on="$listeners">
    </component>
  </div>
</template>
<script>
import { allIndexTypeObject } from '@/views/governanceevaluation/evaluationoResult/util/IndexTypeConfig';
import faceMixins from './faceMixins';
import vehicleMixins from './vehicleMixins';
import videoMixins from './videoMixins';
import { supportVideoIndexIds, supportFaceIndexIds, supportCarIndexIds } from './enumFeild.js';
export default {
  props: {
    editViewAction: {
      default: () => {
        return {
          type: 'view',
          row: {},
        };
      },
    },
    tableData: {
      type: Array,
      default() {
        return [];
      },
    },
    abnormalDetailInfo: {
      type: Object,
      default() {
        return {};
      },
    },
    invokeType: {
      type: String,
      default() {
        return '';
      },
    },
    showAbnomalDetails: {
      type: Boolean,
      default() {
        return false;
      },
    },
  },
  components: {
    CheckPicture: require('@/views/governanceevaluation/evaluationoResult/common-pages/check-picture/index.vue')
      .default,
    reviewAndDetail:
      require('@/views/governanceevaluation/evaluationoResult/result-modules/components/review-and-detail/index.vue')
        .default,
  },
  mixins: [faceMixins, vehicleMixins, videoMixins],
  data() {
    return {
      visible: false,
      activeIndexItem: {},
      entryType: 'deviceInfo', //进入方式,从记录打开为'table'
      entryRowObj: {},
      //所有支持的指标类型
      supportedIndexType: {
        // 视频流
        reviewAndDetail: [...supportVideoIndexIds],
        // 人车
        CheckPicture: [...supportFaceIndexIds, ...supportCarIndexIds],
      },
    };
  },
  computed: {
    getComponentName() {
      const { indexId } = this.editViewAction.row;
      let comName = '';
      Object.keys(this.supportedIndexType).forEach((key) => {
        if (this.supportedIndexType[key].includes(indexId)) {
          comName = key;
        }
      });
      return comName;
    },
    // 处理各个组件的 props
    componentProps() {
      let { indexId } = this.editViewAction.row;
      let propsParams = {};
      // 依次视频流、人脸、车辆
      if (supportVideoIndexIds.includes(indexId)) {
        propsParams = this.getVedioComponentProps();
      }
      if (supportFaceIndexIds.includes(indexId)) {
        propsParams = this.getFaceComponentProps();
      }
      if (supportCarIndexIds.includes(indexId)) {
        propsParams = this.getVehicleComponentProps();
      }
      return propsParams;
    },
  },
  created() {},
  watch: {
    showAbnomalDetails: {
      handler(val) {
        if (val) {
          this.entryType = this.invokeType;
          this.entryRowObj = this.abnormalDetailInfo || {};
          let { indexId } = this.editViewAction.row;
          this.activeIndexItem = allIndexTypeObject[indexId];
          this.$nextTick(() => {
            this.visible = true;
          });
          this.autoChooseUnqualified();
        }
      },
      deep: true,
      immediate: true,
    },
    visible(val) {
      this.$emit('changeVisible', val);
    },
  },
  methods: {
    //自动选择不合格
    async autoChooseUnqualified() {
      if (this.getComponentName !== 'CheckPicture') {
        return;
      }
      await this.$nextTick();
      this.$refs.detailComponent.changeTab('2');
    },
    //视频流props
    getVedioComponentProps() {
      let { indexId } = this.editViewAction.row;
      let styles = ['4004', '4006'].includes(indexId)
        ? {
            width: '7rem',
          }
        : { width: '9.5rem', height: '4.4rem' };
      return {
        activeIndexItem: this.activeIndexItem,
        reviewRowData: this.editViewAction.row,
        styles: styles,
        title: '异常详情',
        reviewVisible: false,
        isGetDetailByapi: true,
        getDetailInfo: this.getDetailInfo, // videoMixins.js
        isShowOnlyLoading: true, // 仅仅显示loading图标
        filedNameMap: {
          smallPicName: 'imageUrl', // 小图
          bigPicName: 'imageUrl', // 大图
          resultTip: 'resultTip', // 图片备注
        },
      };
    },
    //车辆props
    getVehicleComponentProps() {
      let { indexId } = this.editViewAction.row;
      let propsData = {
        list: this.editViewAction.row,
        activeIndexItem: this.activeIndexItem,
        interface: this.interVehicle, // vehicleMixins.js
        routerQuerySubstitution: this.activeIndexItem,
        isUseRouterQuery: false,
        smallImgKey: 'imageUrl',
        imgKey: 'bigImagePath',
        unImmediateInit: true,
        filedNameMap: {
          smallPicName: 'imageUrl', // 小图
          bigPicName: 'bigImagePath', // 大图
          resultTip: 'resultTip', // 图片备注
          qualified: 'qualified', // 不合格
        },
        imageComponents: 'PicAlgorithm',
      };
      if (['3010', '3011'].includes(indexId)) {
        propsData.imageComponents = 'PicAlgorithm';
      }
      return propsData;
    },
    //人脸props
    getFaceComponentProps() {
      let { indexId } = this.editViewAction.row;
      let propsData = {
        list: this.editViewAction.row,
        activeIndexItem: this.activeIndexItem,
        interface: this.interface, // faceMixins.js
        imgKey: 'scenePath',
        routerQuerySubstitution: this.activeIndexItem,
        isUseRouterQuery: false,
        unImmediateInit: true,
        filedNameMap: {
          smallPicName: 'facePath', // 小图
          bigPicName: 'scenePath', // 大图
          resultTip: 'resultTip', // 图片备注
          qualified: 'qualified', // 不合格
        },
      };
      if (['2002', '2001'].includes(indexId)) {
        propsData.imageComponents = 'PicAlgorithm';
      }
      return propsData;
    },
  },
};
</script>
<style lang="less" scoped></style>
