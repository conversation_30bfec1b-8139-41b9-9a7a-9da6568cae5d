<template>
  <div class="height-full width-full" :class="homeStyle">
    <div class="home-container" :class="configFullScreen" v-if="!navigationPageShow && !componentName">
      <!--  顶部统计  -->
      <statistics-top
        v-if="isStyle1"
        class="mt-sm"
        ref="statistics-top"
        :query-access-data="queryAccessData"
        :class="configFullScreen"
      >
        <template #tipslot1>
          <map-dom :outer-data="staticTooltipdata" :has-detail="false"></map-dom>
        </template>
      </statistics-top>
      <statistics-top2
        v-else
        ref="statistics-top"
        class="statistics-top"
        :query-access-data="queryAccessData"
      ></statistics-top2>
      <full-screen ref="full-screen" @changeHome="changeHome" @on-change-full-screen="onChangeFullScreen"></full-screen>
      <div class="location" :class="getConfigFullscreen ? 'full-screen-location' : ''">
        <notice
          ref="noticeRef"
          :key="configFullScreen"
          :width="configFullScreen && isStyle1 ? 300 : 600"
          @click.native="noticeMore"
        >
          <!-- <span @click="noticeMore" class="pointer view-detail ml-sm">更多公告 >></span> -->
        </notice>
      </div>
      <div class="home-animation" v-if="isStyle1">
        <img class="image" src="@/assets/img/base-home/circle-1.png" />
        <img class="image" src="@/assets/img/base-home/circle-2.png" />
        <img class="image" src="@/assets/img/base-home/circle-3.png" />
        <img class="image" src="@/assets/img/base-home/circle-4.png" />
        <img class="left-circular" src="@/assets/img/base-home/left-circular.png" />
        <img class="right-circular" src="@/assets/img/base-home/right-circular.png" />
      </div>
      <top-title v-if="getConfigFullscreen && isStyle1"></top-title>
      <top-title2
        class="top-title2"
        :class="getFullscreen ? 'full-screen-container' : ''"
        v-if="getConfigFullscreen && !isStyle1"
      ></top-title2>
      <draggable
        class="home-base-container"
        drag-class="chosen"
        ghost-class="ghost-class"
        :group="{
          name: 'home', //组名为itxst
          pull: true, //是否允许拖出当前组
          put: true, //是否允许拖入当前组
        }"
        v-model="componentsPositionList"
        :class="configFullScreen"
        :disabled="!isEdit"
      >
        <div
          v-for="item in componentsPositionList"
          :key="item.styleIndex"
          :id="`styleIndex${item.styleIndex}`"
          :style="baseHomePosition[item.styleIndex]"
          class="base-div"
          dragable="true"
          @dragstart="($event) => dragstart($event, item)"
          @drop="($event) => drop($event, item)"
          @dragenter="($event) => dragenter($event, item)"
        >
          <empty-occupy
            :key="styleType"
            class="empty-occupy"
            :is-home-status="false"
            :component-list="item.componentList"
            :is-edit-status="isEdit"
            :evaluation-index-result="evaluationIndexResultData"
            :table-data="evaluationIndexResultData"
            :loading="loading"
            :is-validate="item?.isValidate"
            :default-active-tab="item.activeTabComponentId"
            :has-high-light="item.styleIndex === highLightWrapperIndex"
            :high-light-id="highLightTab"
            :isLongEchartBox="item.isLongEchartBox"
            :home-page-config="homePageConfig"
            :style-type="styleType"
            @changeActiveTab="(tabIndex) => changeActiveTab(tabIndex, item)"
            @deleteCurrentEchart="(componentId) => deleteCurrentEchart(componentId, item)"
          >
          </empty-occupy>
        </div>
        <div class="long-echarts-box" :class="{ 'long-echarts-box-style2': !isStyle1 }"></div>
      </draggable>
      <!-- 底部光效 -->
      <div id="bottom-lottie" v-show="isStyle1"></div>
      <map-echarts
        v-if="!isActivated"
        ref="mapEcharts"
        :class="configFullScreen"
        :home-page-config="homePageConfig"
        :query-access-data="queryAccessData"
        :style-type="styleType"
        @on-jump="onJumpMap"
      ></map-echarts>
    </div>
  </div>
</template>

<script>
import draggable from 'vuedraggable';
import BaseHomeExtends from '@/views/home/<USER>/utils/baseHome.js';
import { getComponentConfig } from '@/views/home/<USER>/utils';

import { mapGetters } from 'vuex';
export default {
  extends: BaseHomeExtends,
  components: {
    draggable,
    EmptyOccupy: require('@/views/home/<USER>/empty-occupy/index.vue').default,
  },
  props: {
    isEdit: {
      default: false,
    },
    initialList: {
      default: () => [],
    },
    styleType: {
      type: String,
    },
  },
  watch: {
    /**
     * 不深度监听
     * 防止外部修改名字整个图表重新加载
     */
    initialList: {
      handler(val) {
        this.componentsList = val;
        this.handleComponentList();
      },
      immediate: true,
    },
    // 风格切换，echarts的地图也要重新加载切换
    styleType() {
      this.$refs.mapEcharts.$echarts.dispose(this.$refs.mapEcharts.$refs.chartsRef);
      this.$refs.mapEcharts.mapCharts = null;
      this.$refs.mapEcharts.viewByParamKey().then((regionCode) => {
        if (!regionCode) {
          this.$refs.mapEcharts.toHomeConfig();
          return;
        }
        this.$refs.mapEcharts.initMap(regionCode);
      });
      // 切换风格后，更改 定位 样式
      this.updateStyle();
    },
  },
  computed: {
    ...mapGetters({
      longEchartBoxShowNum: 'home/getLongEchartBoxShowNum',
      normalEchartBoxShowNum: 'home/getNormalEchartBoxShowNum',
      getFullscreen: 'home/getFullscreen',
    }),
    configFullScreen() {
      return this.getConfigFullscreen ? 'full-screen-container' : '';
    },
    isStyle1() {
      return this.styleType === '1';
    },
    homeStyle() {
      return `home-style${this.styleType}`;
    },
  },
  data() {
    return {
      getConfigFullscreen: true,
      baseHomePosition: {},
      componentsList: [],
      componentsPositionList: [],
      // 正在拖动的组件
      activeDraggedComponent: null,
      // 准备拖动进去的盒子
      activeToContext: null,
      highLightTab: null,
      highLightWrapperIndex: null,
      replaceComponentId: '', // 本盒子 互相拖拽时 ，被替换 的组件名
    };
  },
  created() {},
  methods: {
    dragenter($event, item) {
      let replaceItem = $event.srcElement.id;
      // -1是说明有空位，高亮
      // replaceItem !== '-1';
      this.highLightTab = replaceItem;
      this.highLightWrapperIndex = item.styleIndex;
      this.replaceComponentId = replaceItem;
    },
    dragstart(ev, item) {
      // activeTabIndex 代表现在拖拽的是哪个组件
      this.activeDraggedComponent = item.componentList[item?.activeTabIndex ?? 0];

      if (item?.isLongEchartBox) {
        // 添加边框提示
        this.addClassFn();
      }
    },
    drop($event, item) {
      // 移除边框提示
      this.removeClassFn();

      if (!this.clearAndValidate(item)) {
        return false;
      }
      // 长图表 只能 拖到  3.4区域
      if (this.activeDraggedComponent?.isLongEchart && !['3', '4'].includes(item.styleIndex)) return;
      // 正常图表---》长图表
      let isTurnNormalEchart = item.isLongEchartBox && !this.activeDraggedComponent?.isLongEchart;
      if (isTurnNormalEchart) {
        this.changesPosition('turnNormalEchart');
        item = this.componentsPositionList.find((c) => c.styleIndex === item.styleIndex);
      }

      this.activeToContext = item;
      let toReplaceTab = $event.srcElement.id;
      // 拖拽盒子 - 改变拖拽元素的styleIndex 和 替换的组件
      let dragBoxItem = this.componentsPositionList.find(
        (item) => item.styleIndex === this.activeDraggedComponent.styleIndex,
      );
      // 拖拽的组件在拖拽盒子里componentList数组里的第几个元素
      let dragIndex = dragBoxItem.componentList.findIndex(
        (item) => item.componentId === this.activeDraggedComponent.componentId,
      );
      /**
       * 如果有这个字段[存的组件名称]，代表双方替换组件
       * -1 代表移到了（可添加1个图表的文字）上
       */
      // 替换组件
      let flag = true;
      if (!!toReplaceTab && toReplaceTab !== '-1' && !isTurnNormalEchart) {
        this.replaceComponent(item, toReplaceTab, dragBoxItem, dragIndex);
      } else {
        // 增加组件
        flag = this.addComponent(item, dragBoxItem, dragIndex, isTurnNormalEchart);
      }
      // activeTabComponentId 添加这个字段，默认切换到这个tab
      if (flag) {
        this.$set(item, 'activeTabComponentId', this.activeDraggedComponent.componentId);
      }
      this.activeDraggedComponent = null;
      this.activeToContext = null;
      this.replaceComponentId = '';
    },
    clearAndValidate() {
      // 清空移动时高亮效果
      this.highLightTab = null;
      this.highLightWrapperIndex = null;
      // activeTabComponentId
      // 如果是右侧列表拖拽 - activeDraggedComponent就是空的，禁止
      if (!this.activeDraggedComponent) return false;
      return true;
    },
    /**
     *
     * @param {*} item 目标盒子
     * @param {*} toReplaceTab 准备替换的组件名称
     * @param {*} dragBoxItem 拖拽盒子
     * @param {*} dragIndex 拖拽盒子里componentList数组里的第几个元素
     */
    replaceComponent(item, toReplaceTab, dragBoxItem, dragIndex) {
      // 本盒子交换位置
      if (item.styleIndex === dragBoxItem.styleIndex) {
        let otherIndex = dragBoxItem.componentList.findIndex((c) => c.componentId === this.replaceComponentId);
        let otherItem = dragBoxItem.componentList[otherIndex];
        let dragItem = dragBoxItem.componentList[dragIndex];
        dragBoxItem.componentList.splice(dragIndex, 1, otherItem);
        dragBoxItem.componentList.splice(otherIndex, 1, dragItem);
        return;
      }
      // 不同盒子替换组件
      let toReplaceIndex = this.activeToContext.componentList.findIndex((item) => item.componentId === toReplaceTab);
      let toReplaceItem = this.activeToContext.componentList[toReplaceIndex];
      toReplaceItem.styleIndex = this.activeDraggedComponent.styleIndex;
      this.activeDraggedComponent.styleIndex = this.activeToContext.styleIndex;
      // 拖拽的盒子更新
      dragBoxItem.componentList.splice(dragIndex, 1, toReplaceItem);
      // 目标盒子更新
      item.componentList.splice(toReplaceIndex, 1, this.activeDraggedComponent);
      this.$set(item, 'componentList', item.componentList);
    },
    /**
     *
     * @param {*} item 目标盒子
     * @param {*} dragBoxItem 拖拽盒子
     * @param {*} dragIndex 拖拽盒子里componentList数组里的第几个元素
     * @param {Boolean} isTurnNormalEchart  true:正常图表 ----转化为---》长图表
     */
    addComponent(item, dragBoxItem, dragIndex, isTurnNormalEchart) {
      let maxLen = item.isLongEchartBox ? this.longEchartBoxShowNum : this.normalEchartBoxShowNum;
      if (item.componentList.length >= maxLen && !isTurnNormalEchart) {
        this.$Message.warning('图表已满，请移至标题替换');
        return false;
      }
      this.activeDraggedComponent.styleIndex = this.activeToContext.styleIndex;
      // 拖拽的盒子更新
      dragBoxItem.componentList.splice(dragIndex, 1); // 移走
      // 目标盒子更新
      item.componentList.push(this.activeDraggedComponent);
      return true;
    },
    /**
     * 外部调用
     * 单独修改图表名称
     */
    outControlName(component) {
      let oneComponent = this.componentsList.find((item) => item.componentId === component.componentId);
      let styleIndex = this.componentsPositionList.findIndex((item) => item.styleIndex == oneComponent.styleIndex);
      if (styleIndex === -1) return;
      let newComponent = {
        ...this.componentsPositionList[styleIndex],
        styleIndex: this.componentsPositionList[styleIndex].styleIndex,
        componentList: this.componentsPositionList[styleIndex].componentList.map((item) => {
          if (item.componentId === component.componentId) {
            item.name = component.name;
          }
          return item;
        }),
      };
      this.$set(this.componentsPositionList, styleIndex, newComponent);
    },
    /**
     * 外部调用
     * 单独替换和增加图表
     */
    outControlComponent(styleIndex, draggedItem, activeToContext, activeToItem) {
      /**
       * 判断当前 3.4 区域是 处于  长图表还是正常图表
       * 分情况：
       * 1. 当前 是 正常图表，分别占据 3.4区域，  拖拽对象是 长图表，则需要 合并 3.4 区域，并删除 3.4区域原本的正常图表
       * 2. 当前 是 长图表，占据 3.4区域，  拖拽对象是 正常图表，则需要 拆分 3.4 区域，并删除 3.4区域原本的长图表
       */
      let isLongBox =
        (activeToItem.isLongEchartBox && !activeToItem.componentList.length) ||
        activeToItem.componentList.some((item) => {
          return item.isLongEchart;
        });
      if (!isLongBox && !activeToItem.isLongEchartBox && draggedItem?.isLongEchart) {
        this.changesPosition('turnLongEchart');
      } else if (isLongBox && activeToItem.isLongEchartBox && !draggedItem?.isLongEchart) {
        this.changesPosition('turnNormalEchart');
      }

      // 处理 componentsPositionList 数组数据，判断是 新增 or 替换
      let relaceIndex = -1;
      let toReplaceTab = activeToContext.srcElement.id;
      this.componentsPositionList.map((item) => {
        if (item.styleIndex === styleIndex) {
          // 切换为这个tab - activeTabComponentId
          this.$set(item, 'activeTabComponentId', draggedItem.componentId);

          /**
           * 如果有这个字段[存的组件名称]，代表双方替换组件
           * -1 代表移到了（可添加1个图表的文字）上
           * 'styleIndex3','styleIndex4'  代表在 正常图表 ==转换为==》 长图表 时，当前拖拽对象 拖拽到 3、4区域 边隙 时
           */
          if (!!toReplaceTab && !['-1', 'styleIndex3', 'styleIndex4'].includes(toReplaceTab)) {
            item.componentList.map((comItem, ci) => {
              if (comItem.componentId === toReplaceTab) {
                comItem.visiualShow = false;
                relaceIndex = ci;
              }
            });
            if (relaceIndex !== -1) {
              item.componentList.splice(relaceIndex, 1, draggedItem);
            }
          } else {
            // 直接新增
            item.componentList.push(draggedItem);
          }
        }
      });
    },
    /**
     * 组装组件数据
     */
    handleComponentList() {
      let styleIndexObject = {};
      this.componentsList.forEach((item) => {
        // 有visiualShow字段就展示
        if (item?.visiualShow) {
          if (item.styleIndex in styleIndexObject) {
            styleIndexObject[item.styleIndex].componentList.push(item);
          } else {
            styleIndexObject[item.styleIndex] = {
              componentList: [item],
              styleIndex: item.styleIndex,
            };
          }
          flag = item.isLongEchart;
        }
      });
      const { baseHomePosition, longEchartPosition, normalEchartPosition } = getComponentConfig(
        `style${this.styleType}`,
      );

      // 判断当前应该使用 哪种布局：   若存在长图表 会占据 3.4区域
      let flag = this.componentsList.some((item) => item.isLongEchart && item.visiualShow);
      let dynamicPosition = flag ? longEchartPosition : normalEchartPosition;
      this.baseHomePosition = this.$util.common.deepCopy({ ...baseHomePosition, ...dynamicPosition });

      // 初始化所有组件的位置信息拼接
      let arr = this.$util.common.deepCopy(styleIndexObject);
      this.componentsPositionList = Object.keys(this.baseHomePosition).map((key) => {
        let flag = arr?.[key]?.componentList.some((item) => {
          return item.isLongEchart;
        });
        return {
          componentList: arr?.[key]?.componentList ?? [],
          styleIndex: key,
          isValidate: true, // 验证
          isLongEchartBox: flag, // 是否 为 长图表 区域
        };
      });
    },
    // 长图表 《----》 正常图表 之间的互相转换
    changesPosition(str = '') {
      const { baseHomePosition, longEchartPosition, normalEchartPosition } = getComponentConfig(
        `style${this.styleType}`,
      );
      let dynamicPosition = str === 'turnLongEchart' ? longEchartPosition : normalEchartPosition;
      this.baseHomePosition = this.$util.common.deepCopy({ ...baseHomePosition, ...dynamicPosition });
      // 移除
      let deleteComList = [];
      let copyArr = this.$util.common.deepCopy(this.componentsPositionList);
      let i3 = -1,
        i4 = -1;
      copyArr.map((item, index) => {
        if (item.styleIndex == '3') {
          i3 = index;
        }
        if (item.styleIndex == '4') {
          i4 = index;
        }
      });
      copyArr[i3].componentList.forEach((item) => {
        deleteComList.push(item);
      });
      if (str === 'turnLongEchart') {
        copyArr[i4].componentList.forEach((item) => {
          deleteComList.push(item);
        });
        // 移除
        copyArr.splice(i4, 1);
      } else {
        // 重新 新增 回来
        copyArr.splice(4, 0, {
          componentList: [],
          styleIndex: '4',
          isValidate: true,
          isLongEchartBox: false,
        });
      }
      copyArr[i3].componentList = [];
      copyArr[i3].isLongEchartBox = str === 'turnLongEchart' ? true : false; // 是否 为 长图表 区域

      deleteComList.forEach((item) => {
        this.$emit('deleteVisual', item.componentId);
      });

      this.componentsPositionList = copyArr;
    },
    /**
     * 标记组件当前activeTab
     * @param {*} tabIndex
     * @param {*} item
     */
    changeActiveTab(tabIndex, item) {
      this.$set(item, 'activeTabIndex', tabIndex);
    },
    /**
     * 删除当前图表
     * @param {*} componentId
     * @param {*} item
     */
    deleteCurrentEchart(componentId, item) {
      let Index = item.componentList.findIndex((one) => one.componentId === componentId);
      // 删除当前图表
      item.componentList.splice(Index, 1);
      this.$emit('deleteVisual', componentId);
    },
    addClassFn() {
      this.$Message.warning('请拖拽到红框位置');
      let el = document.querySelector('.long-echarts-box');
      el.classList.add('showBorder');
    },
    removeClassFn() {
      let el_1 = document.querySelector('.long-echarts-box');
      let el_2 = document.querySelector('.showBorder');
      if (el_1 && el_2) {
        el_1.classList.remove('showBorder');
      }
    },
    // 重置时，重新处理 布局
    resetBaseHomePosition() {
      const { baseHomePosition, normalEchartPosition } = getComponentConfig(`style${this.styleType}`);
      this.baseHomePosition = this.$util.common.deepCopy({ ...baseHomePosition, ...normalEchartPosition });
    },
    // 切换风格后，更改 定位 样式
    updateStyle() {
      const { baseHomePosition, longEchartPosition, normalEchartPosition } = getComponentConfig(
        `style${this.styleType}`,
      );
      // 判断当前应该使用 哪种布局：   若存在长图表 会占据 3.4区域
      let flag = this.componentsPositionList.some((item) => item.isLongEchartBox);
      let dynamicPosition = flag ? longEchartPosition : normalEchartPosition;
      this.baseHomePosition = this.$util.common.deepCopy({ ...baseHomePosition, ...dynamicPosition });
    },
  },
};
</script>
<style lang="less" scoped>
@import '~@/views/home/<USER>/base-home.less';
@{_deep}.full-screen-container .map-legend {
  right: 10px !important;
}
// 拖拽的样式修改
@{_deep}.chosen {
  .tab-title-container {
    display: none;
  }
  .body-container {
    border: solid 1px #27eee1 !important;
  }
}
// 拖走后的占位符
@{_deep}.ghost-class {
  .icon-yichu2 {
    display: none;
  }
  .tab-title-container {
    display: block !important;
    .link-text-active {
      color: #27eee1 !important;
    }
  }
  .body-container {
    border: solid 1px #27eee1 !important;
  }
}
.long-echarts-box {
  display: none;
  position: absolute;
  bottom: -5px;
  left: 23%;
  width: 54%;
  height: 35.5%;
  border: 3px solid red;
  &.showBorder {
    display: block;
  }
  &.long-echarts-box-style2 {
    left: 22%;
    width: 56%;
    height: 32.5%;
    bottom: 16px;
  }
}
</style>
