<template>
  <div class="asset-select-container">
    <span class="mr-sm f-14 color-blue">{{ activeName }}</span>
    <Dropdown @on-click="onClickMenu">
      <i class="icon-font icon-shaixuan f-16 color-filter"></i>
      <DropdownMenu slot="list">
        <DropdownItem v-for="(item, index) in dropMenuData" :key="index" :name="item.key">{{ item.name }}</DropdownItem>
      </DropdownMenu>
    </Dropdown>
  </div>
</template>

<script>
export default {
  name: 'type-select',
  components: {},
  props: {
    onlineVisible: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      activeName: '',
      dropMenuData: [
        { name: '功能类型', key: 'function' },
        { name: '点位类型', key: 'point' },
        { name: '重点类型', key: 'important' },
        { name: '资产质量', key: 'assetsquality' },
      ],
    };
  },
  computed: {},
  filter: {},
  methods: {
    onClickMenu(val) {
      this.activeName = this.dropMenuData.find((item) => item.key === val).name;
      this.$emit('getName', val);
    },
  },
  watch: {
    onlineVisible: {
      handler(val) {
        if (val) {
          this.dropMenuData.push({ name: '在线状态', key: 'online' });
        }
        this.activeName = this.onlineVisible ? '在线状态' : '功能类型';
      },
      immediate: true,
    },
  },
};
</script>

<style lang="less" scoped>
.color-blue {
  color: #63ccfc;
}
.color-filter {
  color: #1d84a0;
  &:hover {
    color: #539aea;
  }
}
</style>
