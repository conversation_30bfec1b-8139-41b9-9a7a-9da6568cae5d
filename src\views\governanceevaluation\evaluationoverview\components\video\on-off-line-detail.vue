<template>
  <!-- 在线/离线视频监控  -->
  <div class="on-off-line-detail auto-fill">
    <div class="content auto-fill">
      <div class="evaluation-header">
        <div class="filtrate">
          <span class="f-16 color-filter ml-sm"
            >{{ paramsData.regionName }}-{{ paramsData.isOnline === '1' ? '在线' : '离线' }}视频监控</span
          >
        </div>
        <div>
          <span class="evaluation-time">
            <i class="icon-font icon-shijian1 f-14 mr-x"></i> 评测时间：{{ paramsData.examineTime || '未知' }}
          </span>
        </div>
      </div>
      <div class="abnormal-title">
        <div>
          <i class="icon-font icon-jiancejieguotongji f-16 color-filter"></i>
          <span class="f-16 color-filter ml-sm">检测结果详情</span>
        </div>
        <div>
          <Button type="primary" class="btn_search" @click="getExport" :loading="exportLoading">
            <i class="icon-font icon-daochu font-white f-14 mr-xs"></i>
            <span class="inline ml-xs f-14">导出</span>
          </Button>
        </div>
      </div>
      <div class="search-module bottom-margin20">
        <search-list ref="searchList" :paramsData="paramsData" @startSearch="startSearch"></search-list>
      </div>
      <ui-table class="auto-fill" :tableColumns="tableColumns" :table-data="tableData" :loading="loading">
        <!-- 在线状态 -->
        <template #online="{ row }">
          <span :style="{ color: statusColor[row.online - 1] }">
            {{ onlinetText[row.online - 1] }}
          </span>
        </template>
        <!-- 完好状态 -->
        <template #normal="{ row }">
          <span :style="{ color: statusColor[row.normal - 1] }">
            {{ normalText[row.normal - 1] }}
          </span>
        </template>
        <!-- 可用状态 -->
        <template #canPlay="{ row }">
          <span :style="{ color: statusColor[row.canPlay - 1] }">
            {{ canPlayText[row.canPlay - 1] }}
          </span>
        </template>
        <template #qualified="{ row }">
          <span :style="{ color: statusColor[row.qualified - 1] }">
            {{ qualifiedText[row.qualified - 1] }}
          </span>
        </template>
        <template #option="{ row }">
          <div class="boxCenter">
            <ui-btn-tip
              icon="icon-bofangshipin"
              content="播放视频"
              @click.native="clickRow(row)"
              class="mr-sm"
            ></ui-btn-tip>
            <ui-btn-tip
              icon="icon-chakanjietu"
              content="查看截图"
              class="mr-sm"
              disabled
              v-if="!row.additionalImage && !row.areaImage && !row.dateImage && !row.screenShot"
            ></ui-btn-tip>
            <ui-btn-tip
              v-else
              class="mr-sm"
              icon="icon-chakanjietu"
              content="查看截图"
              @click.native="showResult(row)"
            ></ui-btn-tip>
            <ui-btn-tip
              class="mr-sm"
              icon="icon-tianjiabiaoqian"
              content="添加标签"
              @click.native="addTags(row)"
            ></ui-btn-tip>
          </div>
        </template>
      </ui-table>
      <ui-page class="page" :page-data="pageData" @changePage="changePage" @changePageSize="changePageSize"></ui-page>
    </div>
    <video-player-dialog ref="VideoPlayerDialog"></video-player-dialog>
    <resultModel ref="result"></resultModel>
    <customize-filter
      v-model="customSearch"
      :customize-action="customizeAction"
      :content-style="contentStyle"
      :field-name="fieldName"
      :checkbox-list="deviceTagData"
      :default-checked-list="defaultCheckedList"
      @confirmFilter="confirmFilter"
    >
    </customize-filter>
  </div>
</template>

<script>
import evaluationoverview from '@/config/api/evaluationoverview';
import { tableColumns } from './staticfields';
import downLoadTips from '@/mixins/download-tips';
import { mapActions } from 'vuex';
import taganalysis from '@/config/api/taganalysis';
export default {
  name: 'on-off-line-detail',
  mixins: [downLoadTips],
  data() {
    return {
      exportLoading: false,
      loading: false,
      examineTime: '',
      tableColumns: [],
      tableData: [],
      searchData: {},
      pageData: {
        totalCount: 0,
        pageNum: 1,
        pageSize: 20,
      },
      statusColor: ['#13b13d', '#ff4343', '#e44f22'],
      onlinetText: ['在线', '离线', '无法检测'],
      normalText: ['取流及时响应', '取流超时响应', '无法检测'],
      canPlayText: ['取流成功', '取流失败', '无法检测'],
      qualifiedText: ['合格', '不合格', '无法检测'],
      paramsData: {},
      customSearch: false,
      chooseOne: {
        tagList: [],
      },
      customizeAction: {
        title: '选择导出设备包含标签',
        leftContent: '选择设备标签及排序',
        rightContent: '设备标签显示',
      },
      defaultCheckedList: [],
      contentStyle: {
        height: `${500 / 192}rem`,
      },
      fieldName: {
        id: 'tagId',
        value: 'tagName',
      },
      deviceTagData: [],
    };
  },
  created() {
    this.tableColumns = tableColumns;
    this.getTagList();
  },
  activated() {
    this.$set(this, 'paramsData', this.$route.query);
  },
  methods: {
    ...mapActions({
      getTagList: 'governanceevaluation/getTagList',
    }),
    addTags(row) {
      this.chooseOne = row;
      this.customSearch = true;
      this.customizeAction = {
        title: '添加设备标签',
        leftContent: '选择设备标签及排序',
        rightContent: '设备标签显示',
        moduleStyle: {
          width: '70%',
        },
      };
      let tagList = row.tagList || [];
      this.defaultCheckedList = tagList.map((item) => {
        return item.tagId;
      });
    },
    async confirmFilter(val) {
      const tagIds = val.map((item) => {
        return item.tagId;
      });
      const params = {
        deviceId: this.chooseOne.deviceId,
        tagIds: tagIds,
      };
      try {
        let { data } = await this.$http.post(taganalysis.updateDeviceInfoTag, params);
        this.$Message.success(data.msg);
        this.customSearch = false;
        this.getTableData();
      } catch (err) {
        console.log(err);
      }
    },
    // 导出
    async getExport() {
      this.exportLoading = true;
      let params = {
        orgRegionCode: this.paramsData.orgRegionCode,
        displayType: this.paramsData.displayType,
        indexId: this.paramsData.indexId,
        batchId: this.paramsData.batchId,
        pageSize: this.pageData.pageSize,
        pageNumber: this.pageData.pageNum,
        customParameters: this.searchData,
      };
      try {
        this.$_openDownloadTip();
        const res = await this.$http.post(evaluationoverview.exportOnlineRatePromoteDetailData, params);
        await this.$util.common.transformBlob(res.data.data);
        this.exportLoading = false;
      } catch (err) {
        console.log(err);
      }
    },
    async getTableList() {
      try {
        this.loading = true;
        let params = {
          orgRegionCode: this.paramsData.orgRegionCode,
          displayType: this.paramsData.displayType,
          indexId: this.paramsData.indexId,
          batchId: this.paramsData.batchId,
          pageSize: this.pageData.pageSize,
          pageNumber: this.pageData.pageNum,
          customParameters: this.searchData,
        };
        let res = await this.$http.post(evaluationoverview.getPolyData, params);
        const { data = [] } = res.data;
        this.tableData = data.entities;
        this.pageData.totalCount = data.total;
      } finally {
        this.loading = false;
      }
    },
    startSearch(data) {
      this.searchData = { ...data };
      this.pageData.pageNum = 1;
      this.getTableList();
    },
    changePage(val) {
      this.pageData.pageNum = val;
      this.getTableList();
    },
    changePageSize(val) {
      this.pageData.pageSize = val;
      this.getTableList();
    },
    //视频播放
    async clickRow(row) {
      this.$refs.VideoPlayerDialog.init(row.deviceId);
    },
    //查看截图
    showResult(row) {
      this.$refs.result.showModal(row);
    },
  },
  components: {
    UiTable: require('@/components/ui-table.vue').default,
    SearchList: require('./components/search-list.vue').default,
    VideoPlayerDialog: require('./components/video-player-dialog.vue').default,
    resultModel: require('@/components/result-model.vue').default,
    CustomizeFilter: require('@/components/customize-filter.vue').default,
  },
};
</script>

<style lang="less" scoped>
.on-off-line-detail {
  background: @bg-blue-block;
  background-size: 100% 100%;
  height: 100%;
  padding: 0 15px;

  .evaluation-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    height: 48px;
    border-bottom: 1px solid var(--devider-line);

    .color-filter {
      color: rgba(43, 132, 226, 1);
      vertical-align: middle;
    }

    .evaluation-time {
      color: #a9bed9;
      font-size: 14px;
      margin-left: 10px;

      .mr-x {
        margin-right: 3px;
      }
    }

    .active {
      background: rgba(2, 57, 96, 1);
    }
  }

  .abnormal-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 48px;
    .color-filter {
      color: rgba(43, 132, 226, 1);
      vertical-align: middle;
    }
  }
}
</style>
