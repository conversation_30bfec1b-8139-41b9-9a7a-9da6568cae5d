<!--
    * @FileDescription: 落脚地分析 筛选条件
    * @Author: H
    * @Date: 2023/01/13
    * @LastEditors: 
    * @LastEditTime: 
 -->
<template>
    <div class="peerBox" :style="{top: top + 'px'}">
        <div class="title">
            <p>{{ title }}</p>
            <Icon type="ios-close" @click="handleCancel" />
        </div>
        <div class="box-content">
            <div class="wrapper">
                <p class="wrapper-title">时  间:</p>
                <div class="custom-time">
                    <time-select @dataRangeHandler="dataRangeHandler"></time-select>
                </div>
            </div>
            <div class="wrapper">
                <p class="wrapper-title">落脚时间间隔:</p>
                <Input v-model="formdata.peerSecond" placeholder="请输入" class="wrapper-input"></Input>
                <span class="unit">秒</span>
            </div>
            <div class="wrapper">
                <p class="wrapper-title">最少落脚次数:</p>
                <Input v-model="formdata.peerMinNumber" placeholder="请输入" class="wrapper-input"></Input>
                <span class="unit">次</span>
            </div>
            <Button type="primary" class="btn" @click="handleAnalyse">开始分析</Button>
        </div>
    </div>
</template>

<script>
import TimeSelect from '@/views/holographic-archives/components/time-select.vue'
export default {
    name: '',
    components:{
        TimeSelect 
    },
    props:{
        marginTop:{
            type: Number,
            default: -40
        }
    },
    data () {
        return {
            title: '落脚点分析',
            formdata:{
                startDate: '',
                endDate: '',
                dateType: 2,
                peerSecond: '10',
                peerMinNumber: '2'
            },
        }
    },
    watch:{
            
    },
    computed:{
        top() {
            return this.marginTop
        }  
    },
    created() {
            
    },
    mounted(){
            
    },
    methods: {
        // 开始分析
        handleAnalyse() {
            this.$emit('analyse', this.formdata)
        },
        handleCancel() {
            this.$emit('cancel')
        },
        dataRangeHandler(dataRange, times = []) {
            if (dataRange == 4 && times.length == 0) {
                return
            };
            const [startDate = '', endDate = ''] = times;
            this.formdata.startDate = startDate;
            this.formdata.endDate = endDate;
            this.formdata.dateType = dataRange;
        },
    }
}
</script>

<style lang='less' scoped>
@import '../../components/style/index';
.peerBox{
    width: 370px;
    height: 245px;
    background: #FFFFFF;
    box-shadow: 0px 3px 5px 0px rgba(0,0,0,0.3);
    border-radius: 4px;
    filter: blur(0px);
    position: absolute;
    left: 380px;
    .box-content{
        padding: 15px 15px 20px 20px;
        .wrapper{
            display: flex;
            margin-bottom: 15px;
            align-items: center;
            .wrapper-title{
                font-size: 14px;
                color: rgba(0,0,0,0.45);
                width: 95px;
            }
            .custom-time{
                height: 34px;
                line-height: 34px;
            }
            .wrapper-input{
                flex: 1;
            }
            &:first-child{
                .wrapper-title{
                    width: 40px;
                }
            }
            .unit{
                font-size: 14px;
                color: rgba(0,0,0,0.8);
                margin-left: 5px;
            }
        }
        .btn{
            width: 100%;
            height: 28px;
        }
    }
}
</style>
