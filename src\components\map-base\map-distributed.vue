<template>
  <div class="map-distributed">
    <p class="font-bright mb-sm t-center">高</p>
    <div class="color-div"></div>
    <p class="font-bright mt-sm t-center">低</p>
    <!-- <p class="font-bright mt-sm t-center">视频身份</p> -->
  </div>
</template>
<style lang="less" scoped>
.map-distributed {
  position: absolute;
  bottom: 25px;
  right: 20px;
  z-index: 500;
  .color-div {
    width: 26px;
    height: 166px;
    margin: 0 auto;
    background: linear-gradient(red, yellow, green, blue);
  }
  .font-bright {
    color: #92e0f0;
  }
}
</style>
<script>
export default {
  data() {
    return {};
  },
  created() {},
  mounted() {},
  methods: {},
  watch: {},
  computed: {},
  props: {},
  components: {},
};
</script>
