<template>
  <ui-modal v-model="visible" :styles="styles" title="评测结果" footerHide>
    <div class="result-container auto-fill">
      <dynamic-condition
        :form-item-data="formItemData"
        :form-data="formData"
        @search="startSearch($event, 'search')"
        @reset="startSearch($event, 'reset')"
      >
        <template #otherButton>
          <Button type="primary" :loading="exportLoading" @click="onExport">
            <i class="icon-font icon-piliangshangbao"></i> 导出
          </Button>
        </template>
      </dynamic-condition>
      <ui-select-tabs
        class="ui-select-tabs mb-sm"
        :list="tagListGetter"
        @selectInfo="selectInfo"
        ref="uiSelectTabs"
      ></ui-select-tabs>
      <div class="auto-fill" v-ui-loading="{ loading: tableLoading }">
        <ui-table
          class="ui-table auto-fill"
          :table-columns="tableColumns"
          :table-data="tableData"
          :loading="tableLoading"
        >
          <template #phyStatus="{ row }">
            <span>
              {{ !row.phyStatus ? '--' : row.phyStatusText }}
            </span>
          </template>
          <template #tagNames="{ row }">
            <tags-more :tag-list="row.tagList || []"></tags-more>
          </template>
          <template slot="qualified" slot-scope="{ row }">
            <Tag :color="qualifiedColorConfig[row.qualified].color">
              {{ qualifiedColorConfig[row.qualified].dataValue }}
            </Tag>
          </template>
          <template #videoStartTime="{ row }">
            <span>
              {{ !row.videoStartTime ? '--' : row.videoStartTime }}
            </span>
          </template>
        </ui-table>
        <ui-page
          class="page menu-content-background"
          transfer
          :page-data="pageData"
          @changePage="handlePage"
          @changePageSize="handlePageSize"
        >
        </ui-page>
      </div>
    </div>
    <!-- 导出   -->
    <export-data ref="exportModule" :exportLoading="exportLoading" @handleExport="handleExport"> </export-data>
  </ui-modal>
</template>
<script>
import cascadeListMixin from '@/views/disposalfeedback/cascadelist/mixins/cascadeListMixin.js';
// 外层公共配置
import { qualifiedColorConfig } from '@/views/governanceevaluation/evaluationoResult/util/CommonEnum/ReviewParticular.js';
import { mapActions, mapGetters } from 'vuex';

import {
  tableColumns,
  normalFormData,
} from '@/views/governanceevaluation/evaluationoResult/result-modules/VideoQualityPassRate/util/enum/ReviewParticular.js';
export default {
  name: 'video-quality-pass-rate-result',
  components: {
    DynamicCondition: require('@/views/governanceevaluation/evaluationoResult/components/dynamic-condition.vue')
      .default,
    UiTable: require('@/components/ui-table.vue').default,
    ExportData: require('@/views/governanceevaluation/evaluationoResult/result-modules/components/export-data.vue')
      .default,
    TagsMore: require('@/components/tags-more.vue').default,
    UiSelectTabs: require('@/components/ui-select-tabs.vue').default,
  },
  props: {
    value: {},
    activeItem: {},
  },
  mixins: [cascadeListMixin],
  data() {
    return {
      visible: false,
      styles: {
        width: '8.5rem',
      },
      formData: {
        deviceId: '',
        deviceName: '',
        outcome: '',
        errorCodes: null,
        online: '',
        normal: '',
        canPlay: '',
        tagIds: [],
      },
      formItemData: normalFormData,
      tableData: [],
      tableColumns: [],
      tableLoading: false,
      pageData: {
        pageNum: 1,
        pageSize: 20,
        totalCount: 0,
      },
      exportLoading: false,
      qualifiedColorConfig: Object.freeze(qualifiedColorConfig),
    };
  },
  computed: {
    ...mapGetters({
      tagListGetter: 'governanceevaluation/tagListGetter',
    }),
  },
  created() {
    this.getTagList();

    let arr = tableColumns();
    arr.splice(arr.length - 1, 1);
    this.tableColumns = arr;
  },
  watch: {
    async visible(val) {
      this.$emit('input', val);
      if (val) {
        this.getQualificationList();
        this.initList();
      }
    },
    value: {
      handler(val) {
        this.visible = val;
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {
    ...mapActions({
      getTagList: 'governanceevaluation/getTagList',
    }),
    // 获取不合格原因下拉列表
    async getQualificationList() {
      let res = await this.mixinGetQualificationList({
        statisticType: 'ORG',
        cascadeId: this.activeItem.id,
        ...this.activeItem,
      });
      let findErrorCodes = this.formItemData.find((item) => item.key === 'errorCodes');
      findErrorCodes.options = res;
    },
    startSearch(params, flag) {
      if (flag === 'reset') {
        this.$refs.uiSelectTabs.reset();
      }
      this.pageData.pageNum = 1;
      Object.assign(this.formData, params);
      this.formData = params;
      this.initList();
    },
    selectInfo(infoList) {
      this.pageData.pageNum = 1;
      this.formData.tagIds = infoList.map((item) => item.id);
      this.initList();
    },
    handlePage(val) {
      this.pageData.pageNum = val;
      this.initList();
    },
    handlePageSize(val) {
      this.pageData.pageNum = 1;
      this.pageData.pageSize = val;
      this.initList();
    },
    async initList() {
      try {
        this.tableLoading = true;
        let params = {
          ...this.activeItem,
          ...this.pageData,
          statisticType: 'ORG',
          cascadeId: this.activeItem.id,
          customParameters: this.formData,
        };
        let res = await this.mixinGetFirstModeData(params);
        this.tableData = res.data.data.entities;
        this.pageData.totalCount = res.data.data.total;
      } catch (error) {
        console.log(error);
      } finally {
        this.tableLoading = false;
      }
    },
    // 导出
    onExport: function () {
      this.$refs.exportModule.init(this.activeItem.batchId);
    },
    async handleExport(val) {
      let { id, batchId, indexId } = this.activeItem;
      let params = {
        statisticType: 'ORG',
        cascadeId: id,
        customParameters: this.formData,
        indexId,
        batchId,
        ...val,
      };
      this.mixinGetExport(params);
    },
  },
};
</script>
<style scoped lang="less">
.result-container {
  position: relative;
  height: 700px !important;
}
</style>
