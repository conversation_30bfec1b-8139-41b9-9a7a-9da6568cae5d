<template>
  <div class="layout">
    <Anchor class="anchor" show-ink container=".content" :scroll-offset="30">
      <AnchorLink href="#module1" title="场所管控专题" />
    </Anchor>
    <div class="content">
      <div class="module" id="module1">
        <div class="title">
          <h2>场所管控专题</h2>
          <Button type="primary" @click="savePlaceConfig()">保存</Button>
        </div>
        <div class="row">
          <div class="left-text">重点部位实时监测</div>
          <div class="right-more">
            <div class="right2">
              <div class="childLeft">
                <div class="dashed_line w-220" @click="selectListDevice">
                  选择设备/已选({{ selectDeviceList.length }})
                </div>
              </div>
              <div class="childRight">
                <span class="span">轮巡间隔</span>
                <div class="right-input w-220">
                  <Input
                    placeholder="请输入"
                    v-model="
                      placeConfig.importPartMonitorConfigVo.pollingInterval
                    "
                    type="number"
                    number
                  >
                  </Input>
                  <div class="position">秒</div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="row">
          <div class="left-text">疑似重点人聚集场所</div>
          <div class="right-more">
            <div class="right2">
              <div class="childLeft">
                <span class="span">聚集重点人数≥</span>
                <div class="right-input w-220">
                  <Input
                    placeholder="请输入"
                    v-model="
                      placeConfig.emphasisPersonGatheredPlaceConfigVo
                        .gatheredEmphasisPersonNumber
                    "
                    type="number"
                    number
                  >
                  </Input>
                  <div class="position">人</div>
                </div>
              </div>
              <div class="childRight">
                <span class="span">时间间隔≤</span>
                <div class="right-input w-220">
                  <Input
                    placeholder="请输入"
                    v-model="
                      placeConfig.emphasisPersonGatheredPlaceConfigVo
                        .timeInterval
                    "
                    type="number"
                    number
                  >
                  </Input>
                  <div class="position">分钟</div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="row">
          <div class="left-text">场所事件积分规则</div>
          <div class="right-more">
            <div class="right2">
              <div class="childLeft">
                <span class="span">分析周期</span>
                <div class="right-input w-220">
                  <div class="position-l">近</div>
                  <Input
                    placeholder="请输入"
                    v-model="
                      placeConfig.placeEventScoreConfigVo.analysisDayNumber
                    "
                    type="number"
                    number
                    style="padding-left: 30px"
                  >
                  </Input>
                  <div class="position">天</div>
                </div>
              </div>
            </div>
            <div class="right2">
              <div class="childLeft child32">
                <span class="span">重点人员发现</span>
                <div class="right-input w-140">
                  <Input
                    placeholder="请输入"
                    v-model="
                      placeConfig.placeEventScoreConfigVo
                        .findEmphasisPersonScore
                    "
                    type="number"
                    number
                  >
                  </Input>
                  <div class="position">分</div>
                </div>
              </div>
              <div class="childLeft child32">
                <span class="span">疑似重点人聚集</span>
                <div class="right-input w-140">
                  <Input
                    placeholder="请输入"
                    v-model="
                      placeConfig.placeEventScoreConfigVo
                        .emphasisPersonGatheredScore
                    "
                    type="number"
                    number
                  >
                  </Input>
                  <div class="position">分</div>
                </div>
              </div>
              <div class="childLeft child32">
                <span class="span">场所异常行为</span>
                <div class="right-input w-140">
                  <Input
                    placeholder="请输入"
                    v-model="
                      placeConfig.placeEventScoreConfigVo
                        .placeAbnormalBehaviorScore
                    "
                    type="number"
                    number
                  >
                  </Input>
                  <div class="position">分</div>
                </div>
              </div>
            </div>
            <div class="right2">
              <div class="childLeft child32">
                <span class="span"
                  >关注度:<span style="color: #ea4a36; font-weight: 700"
                    >&nbsp;高&nbsp;</span
                  >≥</span
                >
                <div class="right-input w-140">
                  <Input
                    placeholder="请输入"
                    v-model="
                      placeConfig.placeEventScoreConfigVo.highAttentionScore
                    "
                    type="number"
                    number
                  >
                  </Input>
                  <div class="position">分</div>
                </div>
              </div>
              <div class="childLeft child32">
                <span class="span"
                  >关注度:<span style="color: #f29f4c; font-weight: 700"
                    >&nbsp;中&nbsp;</span
                  >≥</span
                >
                <div class="right-input w-140">
                  <Input
                    placeholder="请输入"
                    v-model="
                      placeConfig.placeEventScoreConfigVo.midAttentionScore
                    "
                    type="number"
                    number
                  >
                  </Input>
                  <div class="position">分</div>
                </div>
              </div>
              <div class="childLeft child32">
                <span class="span"
                  >其他关注度为<span style="color: #1faf81; font-weight: 700"
                    >&nbsp;低&nbsp;</span
                  ></span
                >
              </div>
            </div>
          </div>
        </div>
        <div class="row">
          <div class="left-text">重点人员发现报警</div>
          <div class="table">
            <alarmTable
              type="people"
              :tableData="placeConfig.alarmLevelConfig"
              @colorRefrsh="colorRefrsh"
            />
          </div>
        </div>
        <div class="row">
          <div class="left-text">疑似从业人员</div>
          <div class="right-more">
            <div class="right2">
              <div class="childLeft">
                <span class="span">场所选择</span>
                <div class="dashed_line w-220" @click="selectPlaceHandler">
                  选择场所/已选({{ selectPlaceList.length }})
                </div>
              </div>
              <div class="childRight">
                <span class="span">分析周期</span>
                <div class="right-input w-220">
                  <div class="position-l">近</div>
                  <Input
                    placeholder="请输入"
                    v-model="
                      placeConfig.placeWorkPersonConfigVo.analysisDayNumber
                    "
                    type="number"
                    number
                    style="padding-left: 30px"
                  >
                  </Input>
                  <div class="position">天</div>
                </div>
              </div>
            </div>
            <div class="right2">
              <div class="childLeft child42">
                <span class="span">出现天数≥</span>
                <div class="right-input w-220">
                  <Input
                    placeholder="请输入"
                    v-model="
                      placeConfig.placeWorkPersonConfigVo.appearDayNumber
                    "
                    type="number"
                    number
                  >
                  </Input>
                  <div class="position">天</div>
                </div>
              </div>
              <div class="childRight child12">
                <Select
                  v-model="
                    placeConfig.placeWorkPersonConfigVo
                      .appearDayNumberAndAppearTotalNumberRelation
                  "
                >
                  <Option :value="1">并且</Option>
                  <Option :value="0">或者</Option>
                </Select>
              </div>
              <div class="childRight child42">
                <span class="span">出现次数≥</span>
                <div class="right-input w-220">
                  <Input
                    placeholder="请输入"
                    v-model="
                      placeConfig.placeWorkPersonConfigVo.appearTotalNumber
                    "
                    type="number"
                    number
                  >
                  </Input>
                  <div class="position">次</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 选择场所 -->
    <SelectPlace ref="selectPlace" @selectPlace="selectPlaceConfirm">
    </SelectPlace>
    <!-- 选择设备 -->
    <select-device
      ref="selectDevice"
      showOrganization
      @selectData="selectData"
    />
  </div>
</template>
<script>
import SelectPlace from "@/components/select-modal/select-place.vue";
import alarmTable from "@/views/ipbd-system/basicsConfig/components/alarm-table.vue";
import { placeFormData } from "./placeForm.js";
import { getPlaceConfig, updatePlaceConfig } from "@/api/monographic/place.js";
import { queryDevicePageList } from "@/api/target-control.js";
import { getPlaceArchivesListAPI } from "@/api/placeArchive";
export default {
  name: "ipbd-system",
  components: {
    SelectPlace,
    alarmTable,
  },
  props: {},
  data() {
    return {
      placeConfig: placeFormData, // 场所管控专题
      selectPlaceList: [],
      selectDeviceList: [],
    };
  },
  computed: {},
  async created() {},
  mounted() {
    // 场所管控配置
    this.queryPlaceConfig();
  },
  methods: {
    queryPlaceConfig() {
      getPlaceConfig().then((res) => {
        let placeData = res?.data?.paramValue || "{}";
        let json = JSON.parse(placeData);
        // console.log(json)
        Object.keys(json).forEach((item) => {
          this.placeConfig[item] = json[item];
        });
        if (this.placeConfig.alarmLevelConfig.length == 0) {
          // 给默认值
          this.placeConfig.alarmLevelConfig = [{}, {}, {}];
        }
        // 并且/或 true/false 1/0
        this.placeConfig.placeWorkPersonConfigVo.appearDayNumberAndAppearTotalNumberRelation =
          this.placeConfig.placeWorkPersonConfigVo
            .appearDayNumberAndAppearTotalNumberRelation
            ? 1
            : 0;
        // 选择的设备/场所回填
        let deviceIds = this.placeConfig.importPartMonitorConfigVo.deviceIds;
        // 回填设备信息
        if (deviceIds.length) {
          queryDevicePageList({
            deviceIds: deviceIds,
            pageNumber: 1,
            pageSize: 9999,
          }).then(({ data }) => {
            this.selectDeviceList =
              data?.entities?.map((item) => {
                return {
                  ...item,
                  deviceName: item.name,
                  deviceId: item.id,
                  deviceGbId: item.gbId,
                  deviceType: item.type,
                  select: true,
                };
              }) || [];
          });
        }
        let placeIds = this.placeConfig.placeWorkPersonConfigVo.placeId;
        // 回填场所信息
        getPlaceArchivesListAPI({ ids: placeIds }).then(({ data }) => {
          this.selectPlaceList =
            data?.entities?.map((item) => {
              return {
                ...item,
              };
            }) || [];
        });
      });
    },
    savePlaceConfig() {
      let param = JSON.parse(JSON.stringify(this.placeConfig));
      // 并且/或 true/false 1/0
      param.placeWorkPersonConfigVo.appearDayNumberAndAppearTotalNumberRelation =
        param.placeWorkPersonConfigVo
          .appearDayNumberAndAppearTotalNumberRelation
          ? true
          : false;
      // console.log(param)
      updatePlaceConfig(param).then((res) => {
        this.$Message.success("修改" + res.msg);
      });
    },
    // 场所选择
    selectPlaceHandler() {
      this.$refs.selectPlace.show(this.selectPlaceList);
    },
    selectPlaceConfirm(value) {
      this.selectPlaceList = value;
      this.placeConfig.placeWorkPersonConfigVo.placeId = value.map(
        (item) => item.id
      );
    },
    colorRefrsh(index, name, type) {
      this.placeConfig.alarmLevelConfig[index].alarmColour = name;
      this.$forceUpdate();
    },
    // 设备选择
    selectListDevice() {
      this.$refs.selectDevice.show(this.selectDeviceList);
    },
    selectData(list) {
      this.selectDeviceList = list;
      this.placeConfig.importPartMonitorConfigVo.deviceIds = list.map(
        (item) => item.deviceId
      );
    },
  },
};
</script>
<style lang="less" scoped>
.w-220 {
  width: 220px;
}
.w-140 {
  width: 140px;
}
.padding-10 {
  padding: 10px !important;
}
.layout {
  position: relative;
  width: 100%;
  background: #fff;
  display: flex;
  justify-content: center;
  // overflow-y: auto;
  padding-bottom: 20px;
  .button {
    position: fixed;
    right: 30px;
    top: 110px;
    cursor: pointer;
  }
  .content {
    width: calc(~"50% + 64px");
    overflow: auto;
    scrollbar-width: none;
    .module {
      padding: 0 32px 20px;
      margin-top: 35px;
      border-bottom: 1px dashed #d3d7de;
      .title {
        display: flex;
        justify-content: space-between;
      }
      .row {
        display: flex;
        justify-content: start;
        font-size: 14px;
        min-height: 50px;
        // line-height: 50px;
        margin-top: 20px;
        gap: 15px;
        .left {
          min-width: 80px;
        }
        .left-text {
          width: 70px;
          text-align: right;
        }
        .right-more {
          display: flex;
          flex-direction: column;
          gap: 10px;
          flex: 1;
          /deep/ .ivu-input {
            width: 100%;
          }
        }
        .right-input {
          display: flex;
          align-items: center;
          position: relative;
          .position-l {
            position: absolute;
            left: 0;
          }
          .position {
            position: absolute;
            right: 30px;
          }
        }
        .right {
          flex: 1;
          background: #f9f9f9;
          padding: 0 10px;
          display: flex;
          justify-content: space-between;
          align-items: center;
          .span {
            float: left;
            margin-right: 20px;
            display: inline-block;
          }
          .ivu-checkbox-group {
            float: left;
          }
          .ivu-select {
            flex: 1;
          }
          .btn {
            position: relative;
            display: flex;
            align-items: center;
            .position {
              position: absolute;
              right: 10px;
            }
          }
          .more-datepicke {
            display: flex;
            flex-direction: column;
          }
        }
        .justContent {
          justify-content: space-around;
        }
        .right2 {
          flex: 1;
          display: flex;
          min-height: 50px;
          line-height: 50px;
          justify-content: space-between;
          .childLeft {
            position: relative;
            width: 49%;
            background: #f9f9f9;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 10px;
            .position {
              position: absolute;
              right: 20px;
            }
          }
          .childRight {
            position: relative;
            width: 49%;
            background: #f9f9f9;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 10px;
            .position {
              position: absolute;
              right: 20px;
            }
          }
          .child12 {
            width: 12%;
          }
          .child42 {
            width: 42%;
          }
          .child32 {
            width: 32%;
          }
        }
        .dashed_line {
          border: 1px dashed #3c8bef;
          height: 36px;
          line-height: 36px;
          padding: 0 10px;
          border-radius: 6px;
          color: #2c86f8;
          cursor: pointer;
          background: #ebf4ff;
          text-align: center;
        }
      }
      .aotoHeight {
        height: auto;
        .right {
          padding: 0;
        }
      }
    }
    .module:last-of-type {
      border: none;
      padding-bottom: 40px;
    }
  }
}
.content::-webkit-scrollbar {
  display: none; /* Chrome Safari */
}
.preview {
  position: relative;
  width: 100%;
  .seleList {
    position: absolute;
    z-index: 9;
    display: inline-block;
    font-size: 12px;
    line-height: 22px;
    // pointer-events: none;
    margin-left: 5px;
    top: 50%;
    transform: translate(0, -50%);
    margin-right: 30px;
    .seletabs {
      border: 1px solid #e8eaec;
      background: #f7f7f7;
      padding: 0 6px;
      display: inline-block;
      font-size: 14px;
      border-radius: 5px;
      margin: 3px 4px 3px 0;
      position: relative;
      cursor: move;
      span {
        margin-right: 14px;
      }
      .icon-close {
        cursor: pointer;
        color: #66666675;
        font-size: 14px;
        position: absolute;
        right: 6px;
        top: 2px;
      }
    }
  }
}
/deep/ .ivu-input-group-append {
  width: 30px;
}

/deep/ .ivu-input {
  width: 150px;
}
.people-case {
  display: flex;
  margin-top: 10px;
  .left {
    align-self: center;
  }
  .right {
    margin-left: 20px;
    flex: 1;
  }
  .row {
    width: 100%;
    margin-top: 0px !important;
    justify-content: space-between !important;
    background: #f9f9f9;
    padding: 0 10px;
  }
}
.table {
  flex: 1;
  /deep/ .ivu-input {
    width: 120px;
  }
}
.link-active-color {
  color: #1a74e7;
  cursor: pointer;
}
/deep/ .ivu-select-item-disabled {
  color: #c5c8ce;
}
/deep/ .icon-tishi {
  color: #f29f4c;
}

.anchor-point-infomation {
  width: 100px;
  position: fixed;
  top: 78px;
  right: 18px;
  z-index: 9;
  .export-btn {
    margin-bottom: 10px;
  }
}

.anchor {
  width: 120px;
  position: absolute;
  right: 17%;
  margin-top: 30px;
  /deep/ .ivu-anchor-wrapper {
    box-shadow: 0 0 !important;
  }
}
h2 {
  color: #000;
  font-size: 24px;
}
</style>
