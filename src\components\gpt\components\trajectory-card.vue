<template>
  <div class="trajectory-card">
    <div class="header">
      <div class="header-name" v-show-tips>
        {{ data.plateNo || data.plateNo }}
      </div>
    </div>
    <div class="content">
      <div class="content-left">
        <img v-lazy="data.traitImg" alt="" />
      </div>
      <div class="content-right">
        <span class="ellipsis">
          <ui-icon type="time" :size="14"></ui-icon>
          <span>{{ data.absTime || "--" }}</span>
        </span>
        <span class="ellipsis">
          <ui-icon type="location" :size="14"></ui-icon>
          <span>{{ data.deviceName || "--" }}</span>
        </span>
        <span v-if="data.idScore">
          <ui-icon type="wenjianxiangsidupeizhi" :size="14"></ui-icon>
          <span>相似度：</span>
          <span class="score">{{ `${data.idScore}%` || "--" }}</span>
        </span>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    //搜索条件
    data: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {};
  },
};
</script>

<style lang="less" scoped>
.trajectory-card {
  height: 123px;
  box-shadow: inset 0px -1px 0px 0px #d3d7de;
  cursor: pointer;
  padding: 10px 0;
  .score {
    color: #2c86f8 !important;
    font-weight: bold;
  }
  .header-name {
    width: 300px;
    font-size: 14px;
    font-weight: bold;
    color: #f29f4c;
  }
  .header {
    display: flex;
    justify-content: space-between;
    // height: 42px;
    align-items: center;
    padding-left: 10px;
    &-left {
      display: flex;
      align-items: center;
      line-height: 42px;
      .header-deviceid {
        color: #f29f4c !important;
        font-size: 14px !important;
        vertical-align: center;
        font-weight: 700;
      }
      .serialNumber {
        position: relative;
        display: inline-block;
        width: 32px;
        height: 32px;
        margin-right: 6px;
        color: black;
        background: url("~@/assets/img/map/trajectory-red.png") no-repeat;
        > span {
          position: absolute;
          top: -10px;
          width: 32px;
          font-size: 12px;
          color: #ea4a36;
          text-align: center;
        }
      }
      .activeNumber {
        background: url("~@/assets/img/map/trajectory-blue.png") no-repeat !important;
        > span {
          color: #2c86f8 !important;
        }
      }
      .similarity {
        display: inline-block;
        // width: 41px;
        height: 22px;
        padding: 0 6px;
        line-height: 22px;
        background: linear-gradient(180deg, #5ba3ff 0%, #2c86f8 100%);
        border-radius: 4px;
        color: #ffffff;
        font-size: 12px;
        text-align: center;
      }
    }
    &-name {
      width: 300px;
      font-size: 14px;
      font-weight: bold;
      color: rgba(0, 0, 0, 0.9);
      // overflow: hidden;
      // text-overflow: ellipsis;
    }
  }
  .content {
    display: flex;
    padding: 0 10px;
    margin-top: 9px;
    &-left {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 60px;
      height: 60px;
      border: 1px solid #d3d7de;
      > img {
        max-width: 100%;
        max-height: 100%;
      }
    }
    &-right {
      margin-left: 10px;
      display: flex;
      flex-direction: column;
      width: 280px;
      .iconfont {
        margin-right: 10px;
      }
      .score {
        color: #2c86f8;
        font-weight: bold;
      }
    }
  }
}
</style>
