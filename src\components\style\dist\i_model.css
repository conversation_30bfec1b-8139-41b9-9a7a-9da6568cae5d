@media (max-height: 720px) {
   /deep/ .ivu-modal-content .ivu-modal-body {
    max-height: 480px !important;
  }
}
 /deep/ .ivu-modal-content .ivu-modal-body {
  min-height: 120px;
  max-height: 680px;
  overflow-y: auto;
  padding: 20px 50px 30px 50px;
  color: #fff;
  border-radius: 4px;
}
 /deep/ .list-content > .ivu-modal-wrap > .ivu-modal > .ivu-modal-content > .ivu-modal-body {
  padding: 0px 0 0 0;
}
.header {
  height: 41px;
  font-size: 16px;
  line-height: 40px;
  position: relative;
  top: -40px;
  margin: 0 auto;
  font-weight: 700;
  color: #fff;
  background-size: contain;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
}
.header:after,
.header:before {
  content: '';
  display: block;
  width: 114px;
  height: 41px;
  background-size: 100.5% 100%;
  background-repeat: no-repeat;
}
.header:after {
  background-image: url("../../assets/img/popup_right.png");
}
.header:before {
  background-image: url("../../assets/img/popup_left.png");
}
.header span {
  height: 100%;
  min-width: 143px;
  max-width: 280px;
  overflow: hidden;
  text-overflow: clip;
  background: url('../../assets/img/popup_middle.png') repeat-x;
  background-size: auto 100%;
}
.close {
  position: absolute;
  width: 24px;
  height: 24px;
  top: -28px;
  right: -24px;
  font-size: 30px;
  text-align: center;
  line-height: 24px;
  color: #fff;
  cursor: pointer;
  opacity: 0.5;
}
.ok {
  margin-left: 10px;
  border-radius: 4px;
  border: none;
  background: linear-gradient(180deg, #11AEBC 0%, #0B3358 100%);
}
.ok:hover {
  background: linear-gradient(180deg, #08DFF3 0%, #0F3A63 100%);
}
.ok:active {
  background: linear-gradient(180deg, #09B5C5 0%, #042B4F 100%);
}
.cancel {
  background: #0a1820;
  border-radius: 4px;
  border: 1px solid #09495c;
  font-size: 14px;
  color: #108a94;
  box-sizing: border-box;
}
.cancel:hover {
  background: #08151D;
  color: #15C3D4;
  border-color: #15C3D4;
}
.cancel:active {
  background: #060E13;
  border-color: #00E8FF;
  color: #15C3D4;
}
 /deep/ .ivu-modal-header {
  height: 24px;
  border: 0;
}
 /deep/ .ivu-modal-footer {
  padding-bottom: 16px;
  padding-top: 16px;
  border-top: 1px solid #07355E;
  text-align: center;
}
 /deep/ .ivu-modal-footer .ivu-btn {
  height: 34px;
  padding: 6px 29px;
  font-size: 14px;
}
 /deep/ .ivu-modal-footer .ivu-btn-text {
  background: #010F1F;
  border: 1px solid #03467A;
  color: #037CBE;
}
 /deep/ .ivu-modal-footer .ivu-btn-text:hover {
  color: #23A8F9;
  background-color: #01152C;
  border-color: #23A8F9;
}
 /deep/ .ivu-modal-footer .ivu-btn-text:active {
  color: #0682CD;
  background-color: #000A14;
  border-color: #0682CD;
}
 /deep/ .ivu-modal-footer .ivu-btn-text:focus {
  box-shadow: none;
}
 /deep/ .vertical-center-modal {
  width: 100%;
  min-width: 1124px;
  display: flex;
  align-items: center;
}
 /deep/ .vertical-center-modal .ivu-modal {
  top: 0;
}
