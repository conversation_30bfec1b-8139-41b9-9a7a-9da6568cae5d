.trajectory {
  width: 100%;
  height: 100%;
  .ivu-switch-checked {
    border-color: #09d238;
    background-color: #09d238;
  }
  .rePosition {
    position: absolute;
    bottom: 15px;
    right: 5px;
    z-index: 600;
    cursor: pointer;
  }
  .map {
    width: 100%;
    height: 100%;
    /*有关地图上悬浮信息弹框的*/
    .olFramedCloudPopupContent {
      border: 1px solid #ddd;
      border-radius: 10px;
    }
    .PersonItemInfo {
      width: 270px;
      height: 100px;
      padding: 10px;
    }
    .PersonItemInfo img {
      float: left;
      width: 80px;
      height: 80px;
    }
    .PersonItemInfo ul {
      margin-left: 90px;
      line-height: 26px;
    }
    .PersonItemInfo ul li {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .PersonItemInfo a {
      color: #0a94ff;
    }
    .PersonItemInfo > i {
      margin: 0 5px;
    }
    .PersonItemInfo > i:first-of-type {
      margin-left: 10px;
    }
    .PersonItemInfo .icon-enlarage {
      margin: 0 20px 0 3px;
    }
  }
  .play-trajectory {
    position: absolute;
    top: 5px;
    right: 55px;
    z-index: 500;
  }
  .go-back {
    position: absolute;
    top: 5px;
    left: 35px;
    z-index: 500;
  }
  .ctrl-box {
    width: 1100px;
    height: 200px;
    position: absolute;
    bottom: 10px;
    right: 80px;
    background: #fff;
    border: 1px solid rgba(193, 193, 193, 0.5);
    border-radius: 5px;
    z-index: 500;
    .content-box {
      width: 100%;
      .play-box {
        width: 100%;
        height: 48px;
        display: flex;
        justify-content: space-between;
        line-height: 48px;
        padding-left: 10px;
        .play-ctrl {
          width: 58px;
        }
        .progress {
          width: calc(100% - 58px);
        }
      }
      .image-box {
        width: 100%;
        height: 140px;
        padding: 0 50px;
        .picture-list {
          width: 100%;
          height: 100%;
        }
        .picture-item {
          width: 100px;
          height: 100%;
          img {
            width: 93px;
            height: 100px;
            margin-top: 20px;
            margin-right: 3px;
            &.active {
              border: 2px solid #2db7f5;
            }
          }
          .icon-del {
            position: absolute;
            bottom: 24px;
            right: 8px;
            cursor: pointer;
          }
          .score {
            position: absolute;
            padding: 0 5px;
            top: 20px;
            right: 5px;
            background: #ff9900;
            color: #ffffff;
            border-top-left-radius: 5px;
            border-bottom-left-radius: 5px;
          }
        }
        .flag-count {
          position: absolute;
          width: 100px;
          height: 20px;
          color: #fff;
        }
        .flag-time {
          position: absolute;
          width: 100px;
        }
      }
    }
  }
  .ctrl-box:hover {
    box-shadow: 0 0 8px rgba(193, 193, 193, 0.8);
  }
}
