<template>
  <section class="flex">
    <aside class="menu-container" v-if="menuLoading === false">
      <div class="search-content">
        <Input placeholder="在结果中搜索" v-model="resourceName">
          <Icon type="ios-search" class="font-16 cursor-p" slot="suffix" maxlength="50" @click.prevent="searchName(resourceName)" />
        </Input>
      </div>
      <div v-if="menuList.length !== 0" class="menu">
        <Menu class="menu-content" width="auto " :active-name="menuList[0].resourceList[0].resourceName" :open-names="[0]" @on-select="handleSelect">
          <Submenu v-for="(item, index) of searchMenuList" :name="index" :key="index">
            <template slot="title"> <i class="iconfont icon-shujuyuan color-primary"></i> {{ item.catalogName }} </template>
            <template v-for="(it, i) of item.resourceList">
              <MenuItem v-show="it.num > 0" :name="it.resourceName" :key="i">
                <div  class="layoutBetween">
                  <span class="text-name ellipsis">{{ it.resourceNameCn }}</span>
                  <b class="color-primary">{{ it.num }}</b>
                </div>
              </MenuItem>
            </template>
            <!-- <el-tree :data="item.resourceList" :props="defaultProps" :highlight-current="true" node-key="id" :current-node-key="menuList[0].resourceList[0].resourceName" @node-click="handleNodeClick">
              <div class="custom-tree-node" slot-scope="{ data }">
                <span>{{ data.resourceNameCn }}</span>
                <b class="color-primary">{{ data.num }}</b>
              </div>
            </el-tree> -->
          </Submenu>
        </Menu>
      </div>
      <!-- <ui-empty v-if="menuList.length === 0"></ui-empty> -->
    </aside>
    <section class="main-container" v-if="menuLoading === false">
      <!-- 档案 -->
      <div class="person" v-if="['video', 'name', 'oneByOne', 'vehicle'].includes(selectMenuItemName)">
        <!-- 一车一档查询 -->
        <SearchVehicle ref="search" v-if="selectMenuItemName === 'vehicle'" @searchForm="keyWordSearch" :searchText="'更多条件'" />
        <!-- 一机一档查询 -->
        <SearchDevice ref="search" v-if="selectMenuItemName == 'oneByOne'" @searchForm="keyWordSearch" />
        <!-- 一人一档查询关键字 -->
        <SearchArchives ref="search" v-if="['video', 'name'].includes(selectMenuItemName)" :dataType="selectMenuItemName" @searchForm="keyWordSearch" />
        <div class="card-content">
          <div v-for="(item, index) in list" :key="index" :class="item.type === 'people' ? 'people-card' : 'video-card'" class="card-item">
            <UiListCard v-if="loading === false" :type="item.type" :data="item" :isChange="item.realNameArchiveNo ? true : false" :index="index" @archivesDetailHandle="archivesDetailHandle" @on-change="changeCardHandle(item, index)" @collection="collection" />
          </div>
          <ui-empty v-if="list.length === 0"></ui-empty>
        </div>
        <!-- 分页 -->
        <ui-page class="page" :current="params.pageNumber" :total="total" countTotal :page-size="params.pageSize" @pageChange="pageChange" @pageSizeChange="pageSizeChange" />
      </div>
      <!-- 人/车 -->
      <div class="table-container" v-else>
        <SearchMessage v-if="searchLoading === false" ref="search" :search-list="searchList" @searchForm="keyWordSearch" />
        <div class="list-content">
          <PoliceOther v-if="searchLoading === false" :list="list" @checkDetail="checkDetail" />
          <ui-empty v-if="list.length === 0"></ui-empty>
        </div>
        <!-- 详情 -->
        <PoliceDetail ref="detail" />
        <!-- 分页 -->
        <ui-page class="page" :current="params.pageNumber" :total="total" countTotal :page-size="params.pageSize" @pageChange="pageChange" @pageSizeChange="pageSizeChange" />
      </div>
      <ui-loading v-if="loading"></ui-loading>
    </section>
    <ui-loading v-if="menuLoading"></ui-loading>
  </section>
</template>
<script>
import UiListCard from '@/components/ui-list-card'
import SearchMessage from '../components/search-message.vue'
import SearchArchives from '../components/search-archives.vue'
import PoliceDetail from '../components/police-detail.vue'
import PoliceOther from '../components/police-other.vue'
import SearchPerson from '@/views/holographic-archives/one-person-one-archives/real-name-file/components/search.vue'
import SearchVehicle from '@/views/holographic-archives/one-vehicle-one-archives/components/search.vue'
import SearchDevice from '@/views/holographic-archives/one-plane-one-archives/components/search.vue'
import { personBaseInfo } from '@/api/realNameFile'
import { selectCatalog, realRecordSearch, querySearch, queryPoliceData } from '@/api/wisdom-cloud-search'
export default {
  name: 'policeServiceContent',
  components: { UiListCard, SearchPerson, SearchVehicle, SearchDevice, SearchArchives, SearchMessage, PoliceDetail, PoliceOther },
  props: {
    // 首页参数
    indexSearchData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      selectMenuItemName: null,
      formRight: {},
      searchLoading: false, //资源查询条件
      resourceId: '', //信息表需要的资源id,
      searchList: [], //信息表查询条件
      menuLoading: false,
      resourceName: '', //左侧树查询值
      menuList: [],
      cardType: 'people', //卡片默认类型
      list: [], //列表
      loading: false,
      //档案搜索项
      seachForm: {},
      total: 0,
      params: {
        pageNumber: 1,
        pageSize: 20
      },
      defaultProps: {
        children: 'children',
        label: 'label'
      },
      buttonProps: {
        type: 'default',
        size: 'small'
      },
      queryRecord: {},   // 菜单查询记录
    }
  },
  computed: {
  //   ...mapGetters({
  //     pageType: 'common/getPageType' // 页面类型
  //   })
    searchMenuList () {
        var searchList = []
        if (this.resourceName == "") {
            searchList = JSON.parse(JSON.stringify(this.menuList))
            return searchList
        }
        this.menuList.forEach(item => {
            var arr = item.resourceList.filter(ite => {
                return ite.resourceNameCn.includes(this.resourceName)
            })
            if (arr.length > 0) {
                var row = JSON.parse(JSON.stringify(item))
                row.resourceList = arr
                searchList.push(row)
            }
        })
        return searchList
    }
  },
  async activated() {
    await this.selectCatalog()
  },
  methods: {
    // 左侧树
    async selectCatalog() {
        // 查看关键字是否为空
        // if (!this.indexSearchData.keyWords) {
        //   this.$Message.warning('请输入关键词，多个关键词请用空格隔开')
        //   return false
        // }
        // this.menuLoading = true
        let data = {
            treeResourceName: this.resourceName
        }
        let res = await selectCatalog(data)
        this.menuList = res.data || []
        // 获取第一个资源列表
        this.getInfo(this.menuList[0].resourceList[0].resourceName)
        // 树循环获取对应资源列表
        if (this.menuList.length > 0) {
            for (const e of this.menuList) {
                if (e.resourceList && e.resourceList.length > 0) {
                    // 循环资源,添加资源个数
                    for (let item of e.resourceList) {
                        // 当所有条件为空时
                        if(this.indexSearchData.keyWords == '' && this.indexSearchData.features.length == 0){
                            let arr = await this.realRecordSearch(item.resourceName, item.id)
                            this.$set(item, 'num', arr[0])
                        }else{
                            // 判断搜索图片还是文字
                            //1, 文字
                            if(this.indexSearchData.keyWords && this.indexSearchData.keyWords !== '') {
                                let arr = await this.realRecordSearch(item.resourceName, item.id)
                                this.$set(item, 'num', arr[0])
                            } else {
                                // 2,图片
                                if(this.indexSearchData.algorithmType == 1) { //人脸
                                    if(['video', 'name'].includes(item.resourceName)){
                                        let arr = await this.realRecordSearch(item.resourceName, item.id)
                                        this.$set(item, 'num', arr[0])
                                    }
                                } else { //车辆
                                    if(['vehicle'].includes(item.resourceName)){
                                        let arr = await this.realRecordSearch(item.resourceName, item.id)
                                        this.$set(item, 'num', arr[0])
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        this.menuLoading = false
    },
    // 列表查询
    async realRecordSearch(val, id = null) {
        let data = {
            ...this.seachForm,
            ...this.params,
            treeResourceName: val,
            resourceId: id ? id : this.resourceId
        }
        if (['video', 'name'].includes(val)) {
            data = {
                ...this.seachForm,
                ...this.params,
                treeResourceName: val,
                ...this.indexSearchData,
                searchValue: this.indexSearchData.keyWords,
                dataType: val === 'name' ? 1 : 2,
                resourceId: id ? id : this.resourceId
            }
            this.$delete(data, 'keyWords')
        }else if(['vehicle'].includes(val)){
            data = {
                ...this.seachForm,
                ...this.params,
                treeResourceName: val,
                ...this.indexSearchData,
                searchValue: this.indexSearchData.keyWords,
                // dataType: val === 'name' ? 1 : 2,
                resourceId: id ? id : this.resourceId
            }
            this.$delete(data, 'keyWords')
        } else if (val == 'oneByOne') {
            data = {
                ...this.seachForm,
                ...this.params,
                treeResourceName: val,
                searchValue: this.indexSearchData.keyWords,
                resourceId: id ? id : this.resourceId
            }
        } else {
            data = {
                ...this.seachForm,
                ...this.params,
                treeResourceName: val,
                keyWords: this.indexSearchData.keyWords,
                searchValue: this.indexSearchData.keyWords,
                resourceId: id ? id : this.resourceId
            }
        }
        if (this.selectMenuItemName && this.queryRecord[this.selectMenuItemName]) {
            data.params = this.queryRecord[this.selectMenuItemName].params
        }

        if (Array.isArray(data.name)) {
            data.name = ''
        }
        let res = await realRecordSearch(data)
        const { entities, pageNumber, pageSize, total } = res.data
        let arr = entities || []
        let params = {
            pageNumber: pageNumber,
            pageSize: pageSize
        }
        return [total, arr, params]
    },
    // 警务信息表查询条件
    async querySearch() {
      this.searchLoading = true
      let res = await querySearch(this.resourceId)
      this.searchList = res.data
      this.searchLoading = false
    },
    // 渲染列表的返回值
    async getInfo(val) {
      this.loading = true
      this.selectMenuItemName = val
      let list = await this.realRecordSearch(val)
      if (val === 'name' || val === 'video' || val === 'oneByOne' || val === 'vehicle') {
        // 档案添加卡片type类型
        if (list[1] && list[1].length > 0) {
          list[1].map(item => {
            item.type = val === 'name' ? 'people' : val === 'video' ? 'video' : val === 'oneByOne' ? 'device' : 'vehicle'
          })
        }
      }
      this.list = list[1]
      this.params = list[2]
      this.total = list[0]
      this.loading = false

      // 更新左侧菜单最新数量
      // this.menuList.forEach((item) => {
      //   return item.resourceList.forEach(row => {
      //     if (row.resourceName == val) {
      //       row.num = list[0]
      //     }
      //   })
      // })
    },
    // 跳转档案详情
    archivesDetailHandle(item) {
        let query = {}
        let name = ''
        if (this.selectMenuItemName === 'name' || this.selectMenuItemName === 'video') {
            // 实名和视频跳转参数处理
            name = item.type === 'video' ? 'video-archive' : 'people-archive'
            let type = item.type === 'people' ? 'people' : 'video'
            let archiveNo = this.selectMenuItemName === 'name' ? item.archiveNo : item.type === 'people' ? item.realNameArchiveNo : item.archiveNo
            query = {
                archiveNo: archiveNo,
                source: type,
                initialArchiveNo: archiveNo
            }
        } else if (this.selectMenuItemName === 'oneByOne') {
            // 设备档案跳转参数
            name = 'device-archive'
            query = {
                archiveNo: item.deviceId
            }
        } else if(this.selectMenuItemName === 'vehicle') {
            // 车辆档案
            name = 'vehicle-archive'
            query = {
                archiveNo: JSON.stringify(item.archiveNo),
                plateNo: JSON.stringify(item.plateNo),
                source: 'car',
                idcardNo: item.idcardNo
            }
        }
        const { href } = this.$router.resolve({
            name: name,
            query: query
        })
        window.open(href, '_blank')
    },
    // 警务左侧树查询
    searchName(val) {
      this.resourceName = val
      this.selectCatalog()
    },
    // 左侧树切换
    handleSelect(e) {
      this.seachForm = {}
      this.menuList.forEach((item) => {
        return item.resourceList.forEach(row => {
          if (row.resourceName == e) {
            this.handleNodeClick(row)
            return
          }
        })
      })

    },
    // 左侧树切换
    handleNodeClick(e) {
        this.selectMenuItemName = e.resourceName
        this.resourceId = e.id
        if (['video', 'name', 'oneByOne', 'vehicle'].includes(e.resourceName)) {
            this.$nextTick(() => {
                this.$refs.search.setData(this.queryRecord[this.selectMenuItemName])
                // this.$refs.search.resetHandle()
            })
        } else {
            this.querySearch()
            this.getInfo(e.resourceName)
        }
    },
    // 关键字搜索
    indexSearchHandle() {
      this.$nextTick(() => {
        this.queryRecord = {}
        this.selectCatalog()
        // this.$refs.search.resetHandle()
      })
    },
    // 关键字查询
    keyWordSearch(form) {
        var labelIds = [];
        let formData = {...form};
        if (form.labelIds && form.labelIds.length > 0) {
            form.labelIds.forEach(item => {
                if (item && item != undefined) {
                    labelIds.push(item.id)
                }
            })
            formData.labelIds = labelIds
        }
        this.queryRecord[this.selectMenuItemName] = JSON.parse(JSON.stringify(form))
        this.seachForm = {}
        if (this.selectMenuItemName === 'name') {
            //实名
            this.seachForm = {
                ...formData
            }
            // this.$delete(this.seachForm, 'archiveNo')
            this.queryRecord[this.selectMenuItemName] = JSON.parse(JSON.stringify(form))
        } else if (this.selectMenuItemName === 'video') {
            //视频
            this.seachForm = {
                archiveNo: formData.archiveNo,
                labelIds: formData.labelIds
            }
            this.queryRecord[this.selectMenuItemName] = JSON.parse(JSON.stringify(
                {
                    'archiveNo': formData.archiveNo,
                    'labelIds': form.labelIds
                }
            ))
        } else {
            this.seachForm = {
                ...formData
            }
        }
        this.params.pageNumber = 1;
        this.getInfo(this.selectMenuItemName)
    },
    // 视频卡片切实名获取基本信息
    async personBaseInfo(val) {
      let info = {}
      let data = {
        archiveNo: val,
        dataType: 1
      }
      await personBaseInfo(data).then(res => {
        info = res.data
        info.type = 'people'
      })
      return info
    },
    // 视频卡片切换
    async changeCardHandle(item, index) {
      if (this.list[index].type === 'video') {
        let obj = await this.personBaseInfo(item.realNameArchiveNo)
        this.list[index].type = 'people'
        this.list[index].xm = obj.xm
        this.list[index].xbdm = obj.xbdm
        this.list[index].mzdm = obj.mzdm
        this.list[index].jgDzmc = obj.jgDzmc
        this.list[index].gmsfhm = obj.gmsfhm
        this.list[index].photos = obj.photos
        // this.list[index].lableIds = obj.lableIds
      } else {
        this.list[index].type = 'video'
      }
    },
    /**
     * 点击收藏返回方法
     */
    collection() {
    //   this.$refs.search.setData(this.queryRecord[this.selectMenuItemName])
    },
    // 改变页码
    pageChange(pageNumber) {
      this.params.pageNumber = pageNumber
      this.getInfo(this.selectMenuItemName)
    },
    // 改变分页个数
    pageSizeChange(size) {
      this.params.pageSize = size
      this.params.pageNumber = 1
      this.getInfo(this.selectMenuItemName)
    },
    // 人/车详情
    checkDetail(item) {
      var param = {
        resourceId: this.resourceId,
        treeResourceName: this.selectMenuItemName,
        params: {
          id: {
            searchValue: item.id,
            searchType: 1
          }
        }
      }

      queryPoliceData(param).then(res => {
        this.$refs.detail.show(res.data)
      })
      // this.$refs.detail.show(item)
    },
    getCurrentMenuInfo () {
      if (this.selectMenuItemName && this.queryRecord[this.selectMenuItemName]) {
        return this.queryRecord[this.selectMenuItemName].params
      }else {
        return null
      }
    }
  }
}
</script>
<style lang="less" scoped>
@import 'style/index';
.menu-container {
  border-right: 1px solid #d3d7de;
  width: 280px;
  padding: 16px 0 0;
  z-index: 10;
  .search-content {
    padding: 0 15px;
    margin-bottom: 10px;
  }
  .menu {
    height: 100%;
    padding-bottom: 60px;
    overflow: auto;
  }
  .menu-content {
    height: 100%;
    overflow-y: auto;
    &:after {
      display: none;
    }
    /deep/ .ivu-menu-submenu {
      .ivu-menu-submenu-title {
        background: #f9f9f9;
        box-shadow: inset 0px -1px 0px 0px #d3d7de;
        font-weight: bold;
        color: rgba(0, 0, 0, 0.9);
        font-size: 16px;
        padding-left: 16px;
        padding-top: 10px;
        padding-bottom: 10px;
      }
      .ivu-menu-item {
        display: flex;
        justify-content: space-between;
        padding-top: 7px;
        padding-bottom: 7px;
        &:after {
          display: none;
        }
        b {
          color: #2c86f8;
        }
        &.ivu-menu-item-active {
          background: #2c86f8;
          color: #fff;
          b {
            color: #fff;
          }
        }
      }
    }
    .text-name {
      width: 180px;
      display: inline-block;
    }
  }
}

/deep/.el-tree {
  .el-tree-node__content {
    padding-top: 6px;
    padding-bottom: 6px;
    height: auto;
    & > .el-tree-node__expand-icon {
      margin-left: 15px;
      color: #888888;
      &.is-leaf {
        color: transparent;
      }
    }
  }
  .el-tree-node {
    &.is-current {
      & > .el-tree-node__content {
        background-color: #2c86f8;
        .el-tree-node__expand-icon {
          color: #fff;
          &.is-leaf {
            color: transparent;
          }
        }
        .custom-tree-node {
          span {
            color: #fff;
            flex: 1;
          }
          b {
            color: #fff;
          }
        }
      }
    }
  }
  .custom-tree-node {
    display: flex;
    align-items: center;
    width: 100%;
    padding-right: 20px;
    span {
      color: rgba(0, 0, 0, 0.8);
      flex: 1;
    }
  }
}
.main-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
  position: relative;
  .person {
    flex: 1;
    display: flex;
    flex-direction: column;
    height: 100%;
    padding: 16px 20px 0 20px;
    .card-content {
      display: flex;
      flex-wrap: wrap;
      overflow: auto;
      flex: 1;
      margin: 0 -5px;
      align-content: flex-start;
      position: relative;
      .card-item {
        width: 25%;
        padding: 0 5px;
        box-sizing: border-box;
        // margin-bottom: 10px;
        transform-style: preserve-3d;
        transition: transform 0.6s;
        .list-card {
          width: 100%;
          backface-visibility: hidden;
          height: 198px;
          /deep/.list-card-content-body {
            height: 115px;
          }
          /deep/.content-img {
            width: 115px;
            height: 115px;
          }
          /deep/.tag-wrap {
            margin-top: 7px;
            .ui-tag {
              margin: 0 5px 0 0 !important;
            }
          }
        }
      }
    }
  }
  .table-container {
    height: 100%;
    .list-content {
      flex: 1;
      display: flex;
      flex-direction: column;
      height: 100%;
      overflow-y: auto;
      position: relative;
    }
  }
  .page {
    padding: 0px 20px 0px 19px;
  }
}

.layoutBetween {
  display: flex;
  justify-content: space-between;
}
/deep/ .ivu-icon-md-arrow-dropdown{
    color: #888888;
}
</style>
