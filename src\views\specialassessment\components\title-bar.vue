<template>
  <div class="title-bar">
    <div class="title-bar-header">
      <span class="title-bar-title">
        <span class="title-bar-rect"></span>
        <span class="ml-sm">{{ queryParams.title }}评测统计</span>
      </span>
    </div>
    <div class="filter-box mb-md">
      <div class="filter-criteria">
        <slot name="filterCriteria" :examineMonth="examineMonth">
          <RadioGroup class="mr-lg" v-model="dateType" @on-change="changeDateType" v-show="isShowDateType">
            <Radio :label="item.value" class="mr-md" v-for="(item, index) in dateTypeList" :key="index">
              <span>{{ item.label }}</span>
            </Radio>
          </RadioGroup>
          <ui-label label="检测时间：" v-if="detectionTime.length && dateType === '2'">
            <DatePicker
              type="date"
              class="select-width"
              v-model="examineDate"
              placeholder="Select date"
              :options="timeOption"
              @on-change="changeDatePicker"
            >
            </DatePicker>
            <span class="ml-xs mr-xs form-line" v-if="dateType === '2'">—</span>
            <Select
              v-if="dateType === '2'"
              v-model="batchIds"
              clearable
              class="select-width"
              @on-change="changeRouteUrl"
            >
              <Option v-for="item in rounds" :value="item.batchIds" :key="item.batchIds">{{ item.desc }}</Option>
            </Select>
          </ui-label>
          <ui-label label="检测时间：" v-if="dateType === '1'">
            <DatePicker
              type="month"
              v-model="examineMonth"
              placeholder="请选择月"
              class="select-width"
              @on-change="changeMonthPicker"
            ></DatePicker>
          </ui-label>
        </slot>
      </div>
      <div>
        <slot name="filterStatistics">
          <ui-label label="统计项：" v-if="isShowStatistics">
            <Select v-model="statisticsCode" class="select-width" @on-change="changeRouteUrl">
              <Option v-for="item in statisticsList" :value="item.value" :key="item.value">{{ item.name }}</Option>
            </Select>
          </ui-label>
        </slot>
      </div>
    </div>
    <div class="tag-bar mb-md">
      <slot></slot>
      <icontab-group
        v-if="tabList.length && tabList.length > 2"
        :default-tab="defaultCurTab"
        :tab-list="tabList"
        @onChangeTab="tabChange"
      ></icontab-group>
      <div v-if="tabList.length < 3"></div>
      <Button slot="export" type="default" class="button-export" @click="getExport" :loading="exportLoading">
        <i class="icon-font icon-daochu mr-xs f-14"></i>
        <span class="ml-xs">导出</span>
      </Button>
    </div>
  </div>
</template>
<script>
import dealWatch from '@/mixins/deal-watch';
import downLoadTips from '@/mixins/download-tips';
import evaluationoverview from '@/config/api/evaluationoverview';
export default {
  name: 'title-bar',
  mixins: [dealWatch, downLoadTips],
  props: {
    detectionTime: {
      type: Array,
      default: () => [],
    },
    indexData: {
      type: Object,
      default: () => {},
    },
    // sbgnlxEnum: {
    //   type: String,
    //   default: ''
    // },
    tabList: {
      type: Array,
      default: () => [],
    },
    //日统计 or 月统计
    dateTypeList: {
      type: Array,
      default: () => [
        { label: '日统计', value: '2' },
        { label: '月统计', value: '1' },
      ],
    },
    sortData: {
      type: Object,
      default: () => {},
    },
    // 统计项列表
    statisticsList: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      dateType: '2',
      batchIds: '',
      queryParams: {},
      defaultCurTab: 'DEVICE_ALL',
      dataDimensionEnum: 'DEVICE_ALL',
      exportLoading: false,
      timeOption: {
        disabledDate: (date) => {
          const dateArr = this.clickableDates.map((item) => {
            const dateFormat = new Date(`${item} 00:00:00`);
            return dateFormat.getTime();
          });
          const dateTime = date.valueOf();
          return !dateArr.includes(dateTime);
        },
      },
      clickableDates: [],
      dateOptions: {},
      rounds: [],
      examineTime: '',
      examineDate: '',
      examineMonth: '',
      statisticsCode: '',
      isShowStatistics: true,
    };
  },
  computed: {
    /**
     * 是否显示 日统计月统计 当 只有日统计or月统计时默认不显示
     * @returns {boolean}
     */
    isShowDateType() {
      return this.dateTypeList.length > 1;
    },
  },
  mounted() {
    this.showStatisticsFn();
    this.statisticsCode = this.$route.query.statisticsCode || this.statisticsList[0]?.value;
    this.startWatch(
      '$route',
      () => {
        this.getParams();
      },
      { immediate: true, deep: true },
    );
  },
  methods: {
    changeRouteUrl() {
      this.queryParams = this.$route.query;
      this.$router.push({
        query: {
          ...this.queryParams,
          dateType: this.dateType,
          examineTime: this.examineTime || '',
          batchIds: this.batchIds,
          statisticsCode: this.statisticsCode,
        },
      });
    },
    changeDateType() {
      if (this.dateType === '1') {
        this.examineMonth = this.getMonthDate();
        this.examineTime = this.getMonthDate();
      } else {
        this.examineTime = this.detectionTime[0].date;
        this.examineDate = new Date(this.examineTime);
        this.rounds = this.dateOptions[this.examineTime];
        this.batchIds = this.rounds[0].batchIds;
      }
      this.changeRouteUrl();
      this.$emit('onDateTypeChange');
    },
    tabChange(item) {
      this.dataDimensionEnum = item.value;
      this.$emit('handleChangeTab', item);
    },
    changeDatePicker(date) {
      this.rounds = this.dateOptions[date];
      this.batchIds = this.rounds[0].batchIds;
      this.examineTime = date;
      this.changeRouteUrl();
    },
    changeMonthPicker(date) {
      this.examineTime = date;
      this.$router.push({
        query: {
          ...this.queryParams,
          dateType: this.dateType,
          examineTime: this.examineTime || '',
        },
      });
    },
    // 获取当前年月
    getMonthDate() {
      const date = new Date();
      const year = date.getFullYear();
      let month = date.getMonth() + 1;
      month = month >= 1 && month <= 9 ? '0' + month : month;
      return year + '-' + month;
    },
    // 导出
    async getExport() {
      try {
        this.exportLoading = true;
        let { examineTime, dateType, indexType, batchIds, statisticsCode } = this.queryParams;
        let params = {
          nodeDetails: this.indexData.details,
          examineTime: examineTime,
          type: dateType || '2',
          dataDimensionEnum: this.dataDimensionEnum,
          nodeEnum: indexType,
          statisticalModel: statisticsCode ? Number(statisticsCode) : null,
          displayType:
            statisticsCode === '1' ? 'ORG' : statisticsCode === '2' ? 'REGION' : statisticsCode === '3' ? 'TAG' : '',
        };
        if (dateType === '2') {
          params.batchIds = batchIds ? batchIds.split(',') : [];
        }
        // if(!!this.sbgnlxEnum){
        //   params.sbgnlxEnum = this.sbgnlxEnum
        // }
        if (this.sortData.sortField) {
          params.paramForm = {
            ...params.paramForm,
            indexId: this.indexData.details[0].indexId,
            ...this.sortData,
          };
        }
        this.$_openDownloadTip();
        const res = await this.$http.post(evaluationoverview.exportStatInfoList, params);
        await this.$util.common.transformBlob(res.data.data);
      } catch (err) {
        console.log(err);
      } finally {
        this.exportLoading = false;
      }
    },
    getParams() {
      this.showStatisticsFn();
      this.queryParams = this.$route.query;
      this.dateType = this.queryParams.dateType || '2';
      this.examineDate = new Date(this.queryParams.examineTime);
      this.rounds = this.dateOptions[this.queryParams.examineTime];
      if (this.queryParams.dateType === '1') {
        this.examineMonth = this.examineDate;
        return false;
      }
      if (this.queryParams.batchIds) {
        this.batchIds = this.queryParams.batchIds;
      } else {
        this.batchIds = this.rounds ? this.rounds[0].batchIds : '';
      }
      this.statisticsCode = this.queryParams.statisticsCode || this.statisticsList[0]?.value;
    },
    // 资产考核-资产质量、目录一致  后端没实现，先不显示
    showStatisticsFn() {
      let { indexType } = this.$route.query;
      this.isShowStatistics = ['VEHICLE_CATALOGUE_SAME', 'FACE_CATALOGUE_SAME'].includes(indexType) ? false : true;
    },
  },
  watch: {
    detectionTime: {
      handler(val) {
        this.dateOptions = {};
        if (!val.length) {
          this.clickableDates = [];
          this.rounds = [];
          this.dateOptions = {};
          this.examineTime = '';
          this.batchIds = '';
          this.dateType = '2';
        } else {
          const queryParams = this.$route.query;
          this.clickableDates = val.map((item) => {
            this.dateOptions[item.date] = item.options.map((opItem) => {
              return {
                batchIds: opItem.batchIds.join(','),
                desc: opItem.desc,
              };
            });
            return item.date;
          });
          if (queryParams.dateType === '1') {
            this.examineTime = queryParams.examineTime ? queryParams.examineTime : val[0].date;
          } else {
            this.examineTime = queryParams.examineTime || val[0].date;
            this.rounds = this.dateOptions[this.examineTime];
            this.batchIds = this.queryParams.batchIds ? this.queryParams.batchIds : this.rounds[0].batchIds;
          }
          this.dateType = queryParams.dateType || '2';
        }
        // this.changeRouteUrl()
      },
      immediate: true,
      deep: true,
    },
    tabList: {
      handler(val) {
        if (!val.length) return false;
        this.defaultCurTab = val[0].value;
      },
      immediate: true,
      deep: true,
    },
  },
  components: {
    IcontabGroup: require('@/views/specialassessment/components/icontab-group.vue').default,
  },
};
</script>

<style lang="less" scoped>
.title-bar {
  &-header {
    width: 100%;
    height: 50px;
    padding: 0 20px;
    display: flex;
    align-items: center;
    background: var(--bg-navigation);
  }
  &-title {
    display: flex;
    align-items: center;
    font-size: 16px;
    font-weight: bold;
    color: var(--color-table-header-th);
  }
  &-rect {
    display: inline-block;
    width: 5px;
    height: 20px;
    background: var(--bg-title-rect);
  }
  .filter-box {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 20px;
    border-bottom: 1px solid var(--devider-line);
  }
  .filter-criteria {
    display: flex;
    align-items: center;
  }
  .tag-bar {
    width: 100%;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
  }
  .select-width {
    width: 160px;
  }
  .form-line {
    color: var(--border-table);
  }
  .icon-daochu {
    color: var(--bg-btn-primary);
  }
  @{_deep} .ivu-radio-group-item {
    color: var(--color-content);
  }
  @{_deep} .ivu-icon-ios-calendar-outline:before {
    color: var(--color-el-tree-node__expand-icon) !important;
  }
}
</style>
