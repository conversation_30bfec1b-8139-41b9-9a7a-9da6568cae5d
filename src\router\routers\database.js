/** When your routing table is too long, you can split it into small modules**/
import BasicLayout from '@/layouts/basic-layout'
export default [
  {
    path: '/target-control1',
    name: 'target-control1',
    component: BasicLayout,
    children: [{
      path: '/target-control/static-library/personnel-thematic-database',
      name: 'personnel-thematic-database',
      tabShow: true,
      parentName: 'target-control/static-library',
      component: (resolve) => require(['@/views/target-control/static-library/personnel-thematic-database/index.vue'], resolve),
      meta: {
        title: '静态库-人员专题库'
      }
    }]
  },
  {
    path: '/target-control2',
    name: 'target-control2',
    component: BasicLayout,
    children: [{
      path: '/target-control/static-library/vehicle-thematic-database',
      name: 'vehicle-thematic-database',
      tabShow: true,
      parentName: 'target-control/static-library',
      component: (resolve) => require(['@/views/target-control/static-library/vehicle-thematic-database/index.vue'], resolve),
      meta: {
        title: '静态库-车辆专题库'
      }
    }]
  }
]