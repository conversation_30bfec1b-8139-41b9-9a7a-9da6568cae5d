let postDatas;
module.exports = {
    openTrackWindow(param) {
        postDatas = param;
        if(!!(window.videoTrackWindow && window.videoTrackWindow.window && !window.videoTrackWindow.closed)) {
            window.videoTrackWindow.postMessage(JSON.stringify(param), "*");
        } else {
            window.videoTrackWindow = window.open("page.html#/videoTrack", "_blank");
            this.receiveMessage(param);
        }
    },
    receiveMessage(param) {
        window.addEventListener("message", this.sendPostMessage)
    },
    sendPostMessage(e) {
        typeof e.data === "boolean" && window.videoTrackWindow.postMessage(JSON.stringify(postDatas), "*");
    }
}