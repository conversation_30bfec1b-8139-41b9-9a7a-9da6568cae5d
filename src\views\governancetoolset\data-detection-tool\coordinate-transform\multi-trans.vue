<template>
  <div class="multi-trans-box">
    <Form :model="formData" :label-width="200" class="trans-form-box">
      <FormItem label="1、上传文件">
        <div class="upload-temp-box d_flex">
          <Upload
            action="/ivdg-device-data-service/governance/importExcel"
            :beforeUpload="beforeUpload"
            ref="upload"
            :show-upload-list="false"
          >
            <Button
              :custom-icon="fileName ? '' : 'icon-font icon-shangchuan'"
              :loading="uploadLoading"
              type="dashed"
              class="ivu-btn-dashed"
            >
              <span class="btn-text ellipsis">
                {{ fileName ? fileName : '上传按模板填写的excel文件' }}
              </span>
            </Button>
          </Upload>
          <div class="upload-extend-box" v-if="fileName">
            <i class="icon-font icon-shangchuan ml-sm font-blue pointer" @click.stop="handleUploadBtn"></i>
            <i class="icon-font icon-shanchu3 ml-sm pointer" @click.stop="delUploadFile"></i>
            <Divider type="vertical" />
          </div>
          <Button type="text" class="ml-sm" :loading="downloadTempLoading" @click.stop="downloadTemp"
            ><span class="underline">下载模板</span></Button
          >
        </div>
      </FormItem>
      <FormItem label="2、原始坐标系:">
        <RadioGroup v-model="formData.type">
          <Radio v-for="(value, key) in coordinateTypes" :key="key" :label="key">{{ value }}</Radio>
        </RadioGroup>
      </FormItem>
      <FormItem label="3、转换后坐标系:">
        <CheckboxGroup v-model="formData.targets">
          <Checkbox v-for="(value, key) in coordinateTypes" :key="key" :label="key">{{ value }} </Checkbox>
        </CheckboxGroup>
      </FormItem>
    </Form>
    <div class="trans-btn-box mb-lg">
      <Button type="primary" :loading="transLoading" @click.stop="startTrans">{{
        transLoading ? '坐标转换中' : '坐标转换'
      }}</Button>
    </div>
    <div class="result-box d_flex" v-if="hasResult">
      <p class="result-name">
        <i class="icon-font icon-chenggong1 font-green mr-xs vt-middle"></i
        ><span class="ellipsis" :title="resultData.fileName">{{ resultData.fileName }}</span>
      </p>
      <Button type="text" class="ml-lg" :loading="downloadTempLoading" @click.stop="downloadTemp"
        ><span class="underline" @click.stop="downloadResult">下载结果</span></Button
      >
      <Divider type="vertical" />
      <Button type="text" class="ml-sm" @click.stop="toDownloadCenter"
        ><span class="underline">去【我的下载】中下载</span></Button
      >
    </div>
  </div>
</template>
<script>
import governancetoolset from '@/config/api/governancetoolset';
import { mapActions, mapGetters } from 'vuex';
export default {
  data() {
    return {
      coordinateTypes: {
        WGS84: 'WGS84(国际通用)',
        GCJ02: 'GCJ02(高德、QQ地图)',
        BD09: 'BD09(百度地图)',
        CGCS2000: 'CGCS2000(2000国家大地坐标)',
      },
      downloadTempLoading: false, //下载模板loading
      uploadLoading: false, //上传loading
      fileName: '', //上传文件的名称
      file: null,
      formData: {
        file: null, //文件
        type: 'WGS84', //原始坐标系
        targets: ['WGS84', 'GCJ02', 'BD09', 'CGCS2000'], //目标坐标系
      },
      transLoading: false, //坐标转换loading
      hasResult: false, //是否已经生成了结果
      resultData: {
        fileName: '', //转换的结果
        url: '',
      },
    };
  },
  name: '',
  components: {},
  computed: {
    ...mapGetters({
      cacheRouterList: 'tabs/getCacheRouterList',
    }),
  },
  methods: {
    ...mapActions({
      setCacheRouterList: 'tabs/setCacheRouterList',
    }),
    // 下载模板
    async downloadTemp() {
      try {
        this.downloadTempLoading = true;
        let params = {};
        let res = await this.$http.post(governancetoolset.coordinateTempDownload, params, {
          responseType: 'blob',
        });
        this.$util.common.exportfile(res, '坐标转换模板.xlsx');
      } catch (err) {
        console.log(err);
      } finally {
        this.downloadTempLoading = false;
      }
    },
    // 上传文件之前
    beforeUpload(file) {
      if (!/\.(xlsx|xls)$/.test(file.name)) {
        this.$Message.error('文件格式错误，请上传“.xls”或“.xlsx”结尾的excel文件！');
        return false;
      }
      const isLt5M = file.size / 1024 / 1024 < 5;
      if (!isLt5M) {
        this.$Message.error('上传文件大小不能超过 5MB!');
        return false;
      }
      this.file = file;
      this.fileName = file.name;
      return false;
    },
    //点击上传小图标
    handleUploadBtn() {
      this.$refs?.upload?.handleClick();
    },
    //删除上传的文件
    delUploadFile() {
      this.file = null;
      this.fileName = '';
    },
    //点击转换
    async startTrans() {
      if (!this.file || !this.fileName) {
        this.$Message.warning('请上传文件');
        return;
      }
      try {
        this.transLoading = true;
        let formParams = new FormData();
        formParams.append('type', this.formData.type);
        formParams.append('targets', this.formData.targets);
        formParams.append('file', this.file);
        let config = {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        };
        let res = await this.$http.post(governancetoolset.coordinateBatchTrans, formParams, config);
        let data = res.data;
        //若转换成功则显示转换结果
        if (data.code == 200) {
          this.$Message.success(data.msg);
          this.hasResult = true;
          this.dealResult(data.data); //处理下进行展示结果
        } else {
          this.$Message.error(data.msg);
          this.hasResult = false;
        }
      } catch (error) {
        console.log(error);
      } finally {
        this.transLoading = false;
      }
    },
    //给结果赋值，格式化地址
    dealResult(fullUrl) {
      this.resultData.url = fullUrl; //下载的结果地址
      let rawFileName = fullUrl?.split('/')?.at(-1); //获取到最后如‘设备经纬度装换结果_1690016244655.xlsx’
      if (rawFileName.includes('_')) {
        //不考虑多个下划线的情况
        let rawFileNameArr = rawFileName.replace('_', '.')?.split('.');
        this.resultData.fileName = rawFileNameArr[0] + '.' + rawFileNameArr?.at(-1);
        return;
      }
      this.resultData.fileName = rawFileName; //显示的转换后文件名称
    },
    //下载结果
    downloadResult() {
      if (!this.resultData.url) {
        return;
      }
      window.location.href = this.resultData.url;
    },
    //去我的下载
    toDownloadCenter() {
      const index = this.cacheRouterList.findIndex((row) => row.name === 'downloadcenter');
      if (index === -1) {
        this.cacheRouterList.push({
          name: 'downloadcenter',
          path: `/downloadcenter`,
          text: '我的下载',
        });
        this.setCacheRouterList(this.cacheRouterList);
      }
      this.$router.push({ name: 'downloadcenter' });
    },
  },
  mounted() {},
};
</script>
<style lang="less" scoped>
[data-theme='dark'] {
  .result-box {
    .result-name {
      span {
        color: #a9bed9;
      }
    }
  }
}
[data-theme='light'],
[data-theme='deepBlue'] {
  .result-box {
    .result-name {
      span {
        color: rgba(0, 0, 0, 0.8);
      }
    }
  }
}
.underline {
  text-decoration: underline;
}
.trans-form-box {
  @{_deep} .ivu-form-item {
    margin-bottom: 20px;
  }
  .upload-temp-box {
    .upload-btn {
      display: flex;
      align-items: center;
      border: 1px dashed var(--border-btn-default);
      @{_deep} .icon-font {
        font-size: 16px;
        line-height: 20px;
      }
      @{_deep} span {
        line-height: 1;
        font-size: 14px;
      }
      .btn-text {
        max-width: 200px;
        display: inline-block;
      }
    }
    .upload-extend-box {
      .ivu-divider {
        margin: 0 0 0 10px;
        background: var(--devider-line);
      }
    }
  }
}
.trans-btn-box {
  margin-left: 200px;
  .ivu-btn {
    width: 300px;
    background: var(--bg-btn-primary);
  }
}
.result-box {
  display: flex;
  align-items: center;
  margin-left: 200px;
  .result-name {
    display: flex;
    align-items: center;
    font-size: 14px;
    line-height: 14px;
    span {
      max-width: 300px;
    }
  }
  .ivu-divider {
    margin: 0 0 0 10px;
    background: var(--devider-line);
  }
}
</style>
