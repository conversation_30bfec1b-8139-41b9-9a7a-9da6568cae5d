export default {
  getSystemConfig: '/json/system-name-config/system-config.json?t=' + Date.now(), //获取全局系统配置名称
  getMenus: '/json/system-router-config/menus.json?t=' + Date.now(), //获取菜单
  getNavHeader: '/json/system-router-config/nav-header.json?t=' + Date.now(), //获取头部导航栏
  getNavVideoLib: '/json/system-router-config/nav-videolib.json?t=' + Date.now(), //获取视频身份管理的侧边菜单栏
  getNavActualPerson: '/json/system-router-config/nav-actualperson.json?t=' + Date.now(), //获取实口管控的侧边菜单栏
  getNavTactics: '/json/system-router-config/nav-tactics.json?t=' + Date.now(), //获取战法应用的侧边菜单栏
  getNavSetting: '/json/system-router-config/nav-setting.json?t=' + Date.now(), //获取配置管理的侧边菜单栏
  getNavMaintain: '/json/system-router-config/nav-maintain.json?t=' + Date.now(), //获取运维管理的侧边菜单栏
  getNavVerificationcontrol: '/json/system-router-config/nav-verificationcontrol.json?t=' + Date.now(), //获取系统管理的侧边菜单栏
  getNavSystemmanagement: '/json/system-router-config/nav-systemmanagement.json?t=' + Date.now(), //获取系统管理的侧边菜单栏
  getNavSpecialPeople: '/json/system-router-config/nav-specialpeople.json?t=' + Date.now(), //特殊人员管控的侧边菜单栏
  getNavControltask: '/json/system-router-config/nav-controltask.json?t=' + Date.now(), //获取综合布防的侧边菜单栏
  getNavSocietyhabit: '/json/system-router-config/nav-societyhabit.json?t=' + Date.now(), //获取社康社戒侧边菜单栏
  getNavUsercenter: '/json/system-router-config/nav-usercenter.json?t=' + Date.now(), //获取用户中心侧边菜单栏
  getCityAll: '/json/others/city.json?t=' + Date.now(), // 获取所有城市列表
  getAreaType: '/json/system-param-config/area-type-list.json?t=' + Date.now(), // 获取所有区域类型
  getMarkIconList: '/json/map-icon-config/mark-icon-list.json?t=' + Date.now(), //获取

  getDeviceList: '/ivdg-asset-app/device/queryDeviceList', //获取全部摄像头列表
  getMapSelectTreeList: '/ivdg-asset-app/deviceMap/conditionQuery', // 设备地图左侧树
  getMapSelectAllDevice: '/ivdg-asset-app/deviceMap/selectAllDevice', // 设备地图有权限设备
  getConditionQueryPageList: '/ivdg-asset-app/deviceMap/conditionQueryPageList', // 设备地图设备筛选
  getOrgsTreeNew: '/ivdg-asset-app/deviceMap/orgsTreeNew',
  getChildByOrgCode: '/ivdg-asset-app/deviceMap/getChildByOrgCode',
  getDeviceInfo: '/ivdg-asset-app/deviceMap/queryDeviceInfo',

  getLicenseInfo: '/qsdi-auth-service/license/getLicenseInfo?applicationCode=00000002', //获取授权证书
  getAuthorUrl: '/qsdi-system-service/token/getParamDataByKeys?key=AUTH_URL', // 获取授权跳转地址
  getAuthorNoticeDay: '/qsdi-system-service/token/getParamDataByKeys?key=AUTH_NOTICE', // 获取授权通知天数
  getAssetFieldCheck: '/qsdi-system-service/token/getParamDataByKeys?key=ASSET_STORAGE_FIELD_CHECK', // 获取资产库编辑设备必填选项
  getOrgView: '/qsdi-system-service/system/org/view', //获取组织机构详情
  getDownloadCenterList: '/qsdi-system-service/downloadCenter/pageList', //获取下载文件列表
  delDownloadCenterList: '/qsdi-system-service/downloadCenter/remove', //删除下载文件
  getApplicationResourceList: '/qsdi-system-service/system/resource/applicationResourceList',
  getOrganizationListByRegionCode: '/qsdi-system-service/system/region/convertRegion', //行政区划转化成对应的关联的组织机构。【不包含用户数据权限过滤】
};
