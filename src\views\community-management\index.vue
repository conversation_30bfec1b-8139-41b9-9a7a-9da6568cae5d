<!--
 * @Date: 2025-01-13 18:35:00
 * @LastEditors: liyuhang1 <EMAIL>
 * @LastEditTime: 2025-03-05 15:45:08
 * @FilePath: \icbd-view\src\views\juvenile\index.vue
 * @description: 未成年人专题首页
-->
<template>
  <div class="juvenile-home-page">
    <div class="juvenile-left-box">
      <ui-card
        title="社区实有人口统计"
        padding="10,0"
        class="analysis-card flex-1 m-b10 h-520"
      >
        <ul class="analysis-list-box">
          <li
            class="analysis-item"
            v-for="(item, index) in analysisList"
            :key="index"
          >
            <img :src="item.imgUrl" :alt="item.title" />
            <p class="count-num">
              <CountTo
                :start-val="0"
                :end-val="item.num"
                :duration="1000"
                class="h1"
              ></CountTo>
            </p>
            <p class="data-name">{{ item.title }}</p>
          </li>
        </ul>
      </ui-card>
      <ui-card
        title="重点人员发现报警"
        padding="10,0"
        class="criminal-record-card flex-1 m-b10"
      >
        <div slot="extra" class="card-extra">
          <div class="extra-left">
            <span
              >报警总数：<span class="primary-color">{{
                alarmTotal
              }}</span></span
            >
          </div>
          <div class="extra-right">
            <span class="more" @click="handleMore(1)"
              >更多 <i class="iconfont icon-more"></i
            ></span>
          </div>
        </div>
        <AlarmTab
          ref="alarmTab"
          :list="alarmList"
          :loading="alarmListLoading"
        ></AlarmTab>
      </ui-card>
      <ui-card title="社区异常行为预警" padding="15,20" :style="{ flex: 1.2 }">
        <div slot="extra" class="card-extra">
          <div class="extra-right">
            <span class="more" @click="handleMore(2)"
              >更多 <i class="iconfont icon-more"></i
            ></span>
          </div>
        </div>
        <RiskPersonnel
          :list="riskPersonnelList"
          :loading="riskPersonnelLoading"
        ></RiskPersonnel>
      </ui-card>
    </div>
    <div class="juvenile-main-box">
      <div class="map-box">
        <CommunityMap :alarmList="alarmList"></CommunityMap>
      </div>
      <ui-card title="社区实有人口专题库" padding="0,20" class="main-ui-card">
        <div class="center-image">
          <div class="center-text" @click="goToLibFace()">
            <div class="num">{{ allRecordNum }}</div>
            <div class="title">社区实有人口专题库</div>
          </div>
          <div
            class="six-start-box position-one"
            v-for="(item, index) in criminalRecord"
            :class="item.class"
            :key="index"
            v-show="item.show"
            @click="goToLibFace"
          >
            <div class="num">{{ item.num }}</div>
            <div class="title">{{ item.title }}</div>
          </div>
        </div>
      </ui-card>
    </div>
    <div class="juvenile-right-box">
      <ui-card title="陌生人频繁出入小区" :padding="5" class="flex-1 m-b10">
        <div slot="extra" class="card-extra">
          <div class="extra-right">
            <span class="more" @click="handleMore(4)"
              >更多 <i class="iconfont icon-more"></i
            ></span>
          </div>
        </div>
        <FrequentTab
          ref="FrequentTab"
          :list="frequentList"
          :loading="frequentListLoading"
        ></FrequentTab>
      </ui-card>
      <ui-card title="夜间出入多小区" :padding="0" class="flex-big m-b10">
        <div slot="extra" class="card-extra">
          <div class="extra-right">
            <span class="more" @click="handleMore(4)"
              >更多 <i class="iconfont icon-more"></i
            ></span>
          </div>
        </div>
        <NightOutCommunityTab
          ref="nightOutCommunityTab"
          :list="nightOutCommunityList"
          :loading="nightOutCommunityLoading"
        ></NightOutCommunityTab>
      </ui-card>
      <ui-card
        title="孤寡老人多日未出现预警"
        padding="10,10,0,10"
        class="darktime-card flex-1 m-b10"
      >
        <div slot="extra" class="card-extra">
          <div class="extra-right">
            <span class="more" @click="handleMore(3)"
              >更多 <i class="iconfont icon-more"></i
            ></span>
          </div>
        </div>
        <LivingAlonePeopleTab
          :list="livingAloneList"
          :loading="livingAloneLoading"
        ></LivingAlonePeopleTab>
      </ui-card>
      <ui-card
        title="社区人员与重点人同行"
        :padding="0"
        class="together-card flex-1"
      >
        <div slot="extra" class="card-extra">
          <div class="extra-right">
            <span class="more" @click="handleMore(6)"
              >更多 <i class="iconfont icon-more"></i
            ></span>
          </div>
        </div>
        <TogetherTab
          ref="togetherTab"
          :list="recordWithList"
          :loading="recordWithListLoading"
        ></TogetherTab>
      </ui-card>
    </div>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import CountTo from "vue-count-to";
import AlarmTab from "@/views/juvenile/components/alarm-tab.vue";
import FrequentTab from "./components/frequent-tab.vue";
import NightOutCommunityTab from "./components/night-out-community-tab.vue";
import TogetherTab from "@/views/juvenile/components/together-tab.vue";
import LivingAlonePeopleTab from "./components/living-alone-people-tab.vue";
import CommunityMap from "./components/community-map/index.vue";
import { MenuItem } from "view-design";
import {
  personBizLabelStatistics,
  getFaceAlarmPageList,
} from "@/api/monographic/juvenile.js";
import {
  getStatisticsPageList,
  getNightEnterPageList,
  getStrangerPageList,
  getElderlyPageList,
  travelAlongDetailPageList,
  abnormalBehaviorPageList,
} from "@/api/monographic/community-management.js";
import RiskPersonnel from "@/views/importantPerson-management/components/risk-personnel.vue";
import { getConfigDate } from "@/util/modules/common.js";
export default {
  name: "CommunityManagement",
  components: {
    CountTo,
    AlarmTab,
    FrequentTab,
    NightOutCommunityTab,
    TogetherTab,
    LivingAlonePeopleTab,
    CommunityMap,
    MenuItem,
    RiskPersonnel,
  },
  data() {
    return {
      analysisList: [
        {
          imgUrl: require("@/assets/img/community-management/active-people.png"),
          num: 0,
          title: "活动人员",
          key: "total",
        },
        {
          imgUrl: require("@/assets/img/community-management/confirm-people.png"),
          num: 0,
          title: "已实名",
          key: "realNameNumber",
        },
        {
          imgUrl: require("@/assets/img/community-management/no-confirm-people.png"),
          num: 0,
          title: "未实名",
          key: "noRealNameNumber",
        },
      ],
      alarmTotal: 0,
      criminalRecord: [
        { num: "0", title: "", class: "position-one", show: false },
        { num: "0", title: "", class: "position-two", show: false },
        { num: "0", title: "", class: "position-three", show: false },
        { num: "0", title: "", class: "position-four", show: false },
        { num: "0", title: "", class: "position-five", show: false },
        { num: "0", title: "", class: "position-six", show: false },
      ],
      allRecordNum: 0,
      alarmList: [],
      alarmListLoading: false,
      tableLoading: false,
      frequentList: [],
      frequentListLoading: false,
      nightOutCommunityList: [],
      nightOutCommunityLoading: false,
      livingAloneList: [],
      livingAloneLoading: false,
      recordWithList: [],
      recordWithListLoading: false,
      alarmConfigInfo: {},
      riskPersonnelList: [],
      riskPersonnelLoading: false,
      dateRange: {},
    };
  },
  computed: {
    ...mapGetters({
      targetObj: "systemParam/targetObj",
    }),
  },
  created() {},
  mounted() {
    this.initDate();
    this.getAlarmList();
    this.getNowDateStatistics();
    this.getRecordStatistics();
    this.getLivingAloneList();
    this.getRecordWithList();
    this.getFrequentList();
    this.getNightOutCommunityList();
    // 高危人员风险行为
    this.getRiskPersonnelList();
  },
  methods: {
    initDate() {
      const [startTime, endTime] = getConfigDate(-7);
      this.dateRange = {
        startTime: startTime + " 00:00:00",
        endTime: endTime + " 23:59:59",
      };
    },
    // 社区实有人口统计
    getNowDateStatistics() {
      getStatisticsPageList({ placeId: "" }).then((res) => {
        this.analysisList[0].num = res?.data?.total || 0;
        this.analysisList[1].num = res?.data?.realNameNumber || 0;
        this.analysisList[2].num = res?.data?.noRealNameNumber || 0;
      });
    },
    getRecordStatistics() {
      personBizLabelStatistics().then(({ data }) => {
        // 不足就能展示几个就几个，超过就取top6
        if (!data) {
          data = [];
        }
        this.allRecordNum = data.count;
        delete data.count;
        if (Object.keys(data).length <= 6) {
          Object.keys(data).forEach((key, index) => {
            this.criminalRecord[index].title = key;
            this.criminalRecord[index].num = data[key];
            this.criminalRecord[index].show = true;
          });
        } else {
          this.allRecordNum = 0;
          Object.keys(data).forEach((key, index) => {
            if (index > 5) {
              return;
            }
            this.criminalRecord[index].title = key;
            this.criminalRecord[index].num = data[key];
            this.criminalRecord[index].show = true;
            this.allRecordNum += data[key];
          });
        }
      });
    },
    getAlarmList() {
      this.alarmListLoading = true;
      const date = new Date();
      let param = {
        pageNumber: 1,
        pageSize: 3,
        alarmTimeB: "2000-01-01 00:00:00",
        alarmTimeE: date.format("yyyy-MM-dd") + " 23:59:59",
      };
      getFaceAlarmPageList(param)
        .then(({ data }) => {
          let arr = data?.entities || [];
          arr?.forEach((item) => {
            let info = this.targetObj.alarmLevelConfig.find(
              (ite) => ite.alarmLevel == item.taskLevel
            );
            item.bgIndex = Number(info.alarmColour);
          });
          this.alarmList = arr.length == 0 ? [] : arr;
          this.alarmTotal = data.total || 0;
        })
        .finally(() => {
          this.alarmListLoading = false;
        });
    },
    // 频繁出入小区
    async getFrequentList() {
      this.frequentListLoading = true;
      let param = {
        pageNumber: 1,
        pageSize: 3,
        ...this.dateRange,
      };
      try {
        const res = await getStrangerPageList(param);
        this.frequentList = res?.data?.entities || [];
      } catch (e) {
        console.log(e);
      } finally {
        this.frequentListLoading = false;
      }
    },
    async getNightOutCommunityList() {
      this.nightOutCommunityLoading = true;
      let param = {
        pageNumber: 1,
        pageSize: 3,
        // ...this.dateRange,
      };
      try {
        const res = await getNightEnterPageList(param);
        this.nightOutCommunityList = res?.data?.entities || [];
      } catch (e) {
        console.log(e);
      } finally {
        this.nightOutCommunityLoading = false;
      }
    },
    async getRiskPersonnelList() {
      this.riskPersonnelLoading = true;
      let param = {
        pageNumber: 1,
        pageSize: 3,
        ...this.dateRange,
      };
      const res = await abnormalBehaviorPageList(param);
      this.riskPersonnelList =
        res?.data?.entities?.map(({ faceCapture, ...item }) => ({
          ...item,
          ...faceCapture,
        })) || [];
      this.riskPersonnelLoading = false;
    },
    // 孤寡老人多日未出现预警
    async getLivingAloneList() {
      this.livingAloneLoading = true;
      let param = {
        pageNumber: 1,
        pageSize: 3,
      };
      try {
        const res = await getElderlyPageList(param);
        this.livingAloneList =
          res?.data?.entities.map((item) => {
            const { faceCaptureVo, sex, ...otherData } = item;
            return {
              ...faceCaptureVo,
              ...otherData,
              gender: sex,
            };
          }) || [];
      } catch (e) {
      } finally {
        this.livingAloneLoading = false;
      }
    },
    async getRecordWithList() {
      this.recordWithListLoading = true;
      let param = {
        pageNumber: 1,
        pageSize: 3,
        ...this.dateRange,
      };
      try {
        const res = await travelAlongDetailPageList(param);
        this.recordWithList =
          res?.data?.entities.map(
            ({
              emphasisFaceCaptureVo,
              emphasisPersonInfo,
              srcFaceCaptureVo,
              srcPersonInfo,
              ...item
            }) => ({
              srcFaceCaptureVo,
              srcPersonInfo,
              criminalFaceCaptureVo: emphasisFaceCaptureVo,
              criminalPersonInfo: emphasisPersonInfo,
              ...item,
            })
          ) || [];
      } catch (e) {
      } finally {
        this.recordWithListLoading = false;
      }
    },
    // 更多跳转到目标管控
    handleMore(value) {
      this.$router.push({
        path: "/community-target-control/alarm-manager",
        query: {
          // 直接跳到人员报警
          compareType: value,
        },
      });
    },
    // 跳转人脸档案
    goToLibFace() {
      this.$router.push({
        path: "/community-data-storage/special-library",
        query: {},
      });
    },
  },
};
</script>

<style lang="less" scoped>
.m-b10 {
  margin-bottom: 10px;
}

.h-520 {
  height: 520px;
}

.flex-1 {
  flex: 1;
}

.flex-big {
  flex: 1.2;
}

.primary-color {
  color: #2c86f8;
}

.juvenile-home-page {
  display: flex;
  flex: 1;
  background: #dde1ea;
  position: relative;

  /deep/ .card-head .title {
    text-wrap: nowrap;
  }

  .card-extra {
    display: flex;
    color: #515a6e;
    justify-content: space-between;
    font-size: 14px;
    height: 30px;
    line-height: 30px;

    .more {
      margin-right: 6px;
      font-size: 14px;
      color: rgba(0, 0, 0, 0.35);
      cursor: pointer;
    }

    .extra-left {
      width: 200px;
    }

    .entertainment {
      width: unset;
      flex: 1;
      padding-right: 15px;
      display: flex;

      /deep/ .ivu-menu-horizontal {
        height: 100%;
        line-height: unset;
        display: flex;
        gap: 5px;
      }

      /deep/ .ivu-menu {
        &::after {
          background: none;
        }

        .ivu-menu-item {
          padding: 0;
          text-wrap: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          width: 40px;
        }
      }

      .more-list {
        position: relative;

        .icon {
          cursor: pointer;
        }

        .more-list-menu {
          position: absolute;
          box-shadow: 0px 3px 5px 0px rgba(0, 0, 0, 0.3);
          border-radius: 4px;
          width: 80px;

          /deep/ .ivu-menu {
            flex-direction: column;
            width: 100%;
            padding: 5px;

            .ivu-menu-item {
              width: 100%;
            }
          }
        }
      }
    }

    .extra-right {
      margin-right: 10px;
      width: 50px;
      text-wrap: nowrap;
      flex: 1;
    }

    .active-analysis {
      width: 150px;
      margin-right: 40px;

      .tabs-ui {
        display: flex;
        justify-content: space-between;
        font-weight: bold;

        .tab-item {
          color: #515a6e;
          cursor: pointer;
        }

        .active-item {
          color: #2c86f8;
          border-bottom: 2px solid #2c86f8;
        }
      }
    }
  }

  .juvenile-left-box {
    width: 450px;
    height: 100%;
    display: flex;
    flex-direction: column;
    padding-right: 10px;

    .analysis-card {
      flex: 0.75;
    }

    .analysis-list-box {
      display: flex;
      justify-content: space-around;
      width: 100%;
      height: 100%;
      align-items: center;

      .analysis-item {
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;

        img {
          width: 90px;
          height: 90px;
        }

        .count-num {
          font-size: 20px;
          font-weight: bold;
          color: #2c86f8;
          margin: 5px 0;
        }
      }
    }

    .criminal-record-card {
      .criminal-record-extra {
        display: flex;
        color: black;
        width: 40%;
        justify-content: space-between;
      }
    }
  }

  .juvenile-main-box {
    flex: 1;
    height: 100%;
    display: flex;
    flex-direction: column;

    .map-box {
      width: 1000px;
      height: calc(~"100% - 280px");
      position: relative;

      .title {
        text-align: center;
        position: absolute;
        top: -20px;
        left: 50%;
        transform: translateX(-50%);
        z-index: 50;

        h2 {
          margin-top: -42px;
        }
      }
    }

    .main-ui-card {
      height: 280px;
      background-image: url("../../assets/img/juvenile/juvenile-bg.png");
      background-repeat: no-repeat;
      background-size: contain;
      background-position: 100% 70%;

      .center-image {
        width: 100%;
        height: 100%;
        background-image: url("../../assets/img/juvenile/juvenile-center-bg.png");
        background-repeat: no-repeat;
        background-size: contain;
        background-position: center;
        position: relative;
        display: flex;
        justify-content: center;
        align-items: center;

        .center-text {
          position: absolute;
          width: 12%;
          top: 20.7%;
          text-align: center;
          color: #fff;
          cursor: pointer;

          .num {
            font-size: 24px;
            font-weight: bold;
          }

          .title {
            font-size: 14px;
            margin-top: 5px;
          }
        }

        .six-start-box {
          width: 108px;
          height: 108px;
          clip-path: polygon(
            50% 0%,
            100% 25%,
            100% 75%,
            50% 100%,
            0% 75%,
            0% 25%
          );
          background-image: url("../../assets/img/juvenile/six-ploy-bg.png");
          background-repeat: no-repeat;
          background-size: contain;
          background-position: center;
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          cursor: pointer;
          .num {
            font-size: 24px;
            font-weight: bold;
            color: #2c86f8;
          }

          .title {
            font-size: 14px;
            margin-top: 5px;
            color: rgba(0, 0, 0, 0.6);
          }
        }

        .position-one {
          position: absolute;
          top: 40px;
          left: 62px;
        }

        .position-two {
          position: absolute;
          top: 0;
          left: 220px;
        }

        .position-three {
          position: absolute;
          top: 130px;
          left: 180px;
        }

        .position-four {
          position: absolute;
          top: 0;
          left: 652px;
        }

        .position-five {
          position: absolute;
          top: 130px;
          left: 692px;
        }

        .position-six {
          position: absolute;
          top: 40px;
          left: 810px;
        }
      }
    }
  }

  .juvenile-right-box {
    width: 450px;
    height: 100%;
    display: flex;
    flex-direction: column;
    padding-left: 10px;

    .together-card {
      // height: 280px;
      flex: 1.25;
    }

    .darktime-card {
    }
  }
}

/deep/.ui-card {
  .card-content {
    width: 100%;
    display: flex;
    align-items: center;
  }
}
</style>
