<template>
  <div style="height: 100%; width: 100%">
    <div class="darktime-out" v-if="list.length > 0">
      <div
        class="darktime-item"
        v-for="(item, index) in list"
        @click="handleDetailFn(item, index)"
      >
        <div class="right-text-box">
          <div class="top-content">
            <div class="name-item">
              <span class="mian-title" :title="item.placeName"
                >{{ item.placeName || "--" }}</span
              >
              <span class="tag">（{{ item.gatheredPersonTotal || 0 }}人）</span>
            </div>
            <div class="type">
              场所类型：<span>{{ item.placeType }}</span>
            </div>
          </div>

          <div class="bottom-content">
            <div class="location">
              <i class="iconfont icon-location"></i>
              {{ item.address || "--" }}
            </div>
            <div class="time">
              <i class="iconfont icon-time"></i>
              {{ item.alarmTime || "--" }}
            </div>
          </div>
        </div>
      </div>
    </div>
    <ui-empty v-if="list.length == 0"></ui-empty>
    <ui-loading v-if="loading" />
    <CaptureDetail
      ref="videoDetail"
    ></CaptureDetail>
  </div>
</template>

<script>
import CaptureDetail from "@/views/placeControl-management/components/detail/important-person-cluster-detail.vue";
export default {
  name: "nightOut",
  components: {
    CaptureDetail,
  },
  props: {
    idCardNo: {
      type: String,
      default: null,
    },
    loading: {
      type: Boolean,
      default: false,
    },
    list: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      imageTest: require("@/assets/img/face.png") || "",
      selectIdCard: "",
      tableIndex: -1,
    };
  },
  methods: {
    // 详情
    handleDetailFn(item, index) {
      this.$refs.videoDetail.showList(item);
    },
  },
};
</script>

<style lang="less" scoped>
.darktime-out {
  height: 100%;
  overflow: scroll;
  display: flex;
  flex-direction: column;
  gap: 5px;
  position: relative;

  .darktime-item {
    display: flex;
    font-size: 16px;
    font-weight: bold;
    background: #f9f9f9;
    padding: 2px;
    cursor: pointer;
    .left-image-box {
      width: 48px;
      height: 48px;
      margin-right: 12px;
    }
    .right-text-box {
      flex: 1;
      .top-content {
        display: flex;
        justify-content: space-between;
        border-bottom: 1px #d3d7de dashed;
        padding: 2px;
        .name-item {
          font-size: 16px;
          align-items: center;
          display: flex;
          .mian-title {
            color: rgba(0, 0, 0, 0.9);
            max-width: 200px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            display: inline-block;
          }
          .sub-title {
            color: rgba(0, 0, 0, 0.6);
          }
          .tag {
            font-size: 14px;
            color: #ea4a36;
            font-weight: bold;
          }
          .location {
            font-size: 14px;
            color: rgba(0, 0, 0, 0.75);
          }
        }
        .type {
          max-width: 180px;
          font-size: 14px;
          color: rgba(0, 0, 0, 0.6);
        }
      }
      .bottom-content {
        display: flex;
        justify-content: space-between;
        font-size: 12px;
        padding: 1px;
        i {
          font-size: 12px;
        }
        color: rgba(0, 0, 0, 0.6);
        .location {
          max-width: 250px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          display: inline-block;
        }
      }
    }
  }
}
</style>
