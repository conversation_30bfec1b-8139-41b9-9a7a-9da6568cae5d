<template>
  <div class="keypersonlibrary auto-fill">
    <slot name="search"></slot>
    <div class="keypersonlibrary-content auto-fill" v-ui-loading="{ loading: loading, tableData: cardList }">
      <div class="keypersonlibrary-content-wrap">
        <template v-for="(item, index) in cardList">
          <slot name="card" :row="item" :index="index"></slot>
        </template>
      </div>
    </div>
    <div class="btn">
      <Button @click="add_page" :disabled="next_btn">下一页</Button>
      <Button @click="reduce_page" :disabled="form_btn">上一页</Button>
    </div>

    <!-- <ui-page class="page" :page-data="pageData" @changePage="changePage" @changePageSize="changePageSize"> </ui-page> -->
  </div>
</template>
<script>
import { mapActions, mapGetters } from 'vuex';
import governanceevaluation from '@/config/api/governanceevaluation';
export default {
  props: {
    cardInfo: {
      type: Array,
      default() {},
    },
    loadData: {
      // 接口
      type: Function,
      default() {},
    },
    msg: {
      type: Object,
    },
    listKey: {
      // list取值
      type: String,
      default: 'entities',
    },
  },
  data() {
    return {
      loading: false,
      searchData: {
        params: {
          pageNumber: 1,
          pageSize: 10,
        },
      },
      pageData: {
        pageNum: 1,
        pageSize: 10,
        totalCount: 0,
      },
      cardList: [],
      form_btn: true,
      next_btn: false,
    };
  },
  computed: {
    ...mapGetters({
      personTypeList: 'algorithm/person_type',
    }),
  },
  async mounted() {
    if (this.personTypeList.length == 0) await this.getAlldicData();
  },
  methods: {
    ...mapActions({
      getAlldicData: 'algorithm/getAlldicData',
    }),
    // 此方法在父组件调用表格方法
    // async info(boolen) {
    //   this.loading = true
    //   if (boolen) {
    //     this.reset()
    //   }
    //   const result = this.loadData(this.searchData)
    //   if (typeof result == 'object' && typeof result.then == 'function') {
    //     result
    //       .then((res) => {
    //         if (res) {
    //           this.cardList = res.data[this.listKey]
    //           for (let i of this.cardList) {
    //             if (i.personType) {
    //               i.personType = i.personType.split(',')
    //             }
    //           }
    //           // this.pageData.totalCount = res.data.total
    //           // console.log(this.pageData.totalCount)
    //           console.log(res.data)
    //         }
    //       })
    //       .finally(() => {
    //         this.loading = false
    //       })
    //   } else {
    //     this.cardList = []
    //     this.pageData.totalCount = 0
    //     this.loading = false
    //   }
    // },
    info() {
      this.loading = true;
      this.$http.post(governanceevaluation.personArchives, this.msg).then((res) => {
        this.cardList = res.data;
        this.loading = false;
        if (this.cardList.length < 10) {
          this.next_btn = true;
        } else {
          this.next_btn = false;
        }
      });
    },
    add_page() {
      if (this.cardList.length >= 10) {
        this.next_btn = false;
        this.form_btn = false;
        this.msg.pageNumber = this.msg.pageNumber + 1;
        this.info();
      } else {
        this.next_btn = true;
      }
    },
    reduce_page() {
      this.msg.pageNumber = this.msg.pageNumber - 1;
      if (this.msg.pageNumber <= 1) {
        this.form_btn = true;
      } else {
        this.form_btn = false;
        this.info();
      }
    },
    reset() {
      // this.pageData = { totalCount: 0, pageNum: 1, pageSize: 10 }
      // this.searchData = { params: { pageNumber: 1, pageSize: 10 } }
    },
    // changePage(val) {
    //   this.searchData.params.pageNumber = val
    //   this.pageData.pageNum = val
    //   this.info()
    // },
    // changePageSize(val) {
    //   this.searchData.params.pageNumber = 1
    //   this.pageData.pageNum = 1
    //   this.searchData.params.pageSize = val
    //   this.pageData.pageSize = val
    //   this.info()
    // },
    // 详情
    detail(info) {
      this.$emit('detailInfo', info);
    },
  },
  watch: {},
  components: {},
};
</script>
<style lang="less" scoped>
.keypersonlibrary {
  // padding-top: 15px;
  background-color: var(--bg-content);
  height: calc(100vh - 340px);
  &-content {
    position: relative;
    // margin: 20px 0 0;
    height: 100%;
    overflow-y: auto;
    &-wrap {
      width: 100%;
      display: flex;
      display: -webkit-flex;
      flex-wrap: wrap;
    }
  }
  .page {
    padding-right: 0;
  }
  .btn {
    padding: 10px 0;
    .ivu-btn {
      float: right;
      margin-right: 10px;
    }
  }
}
</style>
