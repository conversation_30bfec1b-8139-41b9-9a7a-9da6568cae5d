<template>
  <div class="overview-evaluation-content auto-fill height-full">
    <div class="evaluation-header">
      <div class="evaluation-title">
        <div class="fl filtrate" v-if="!isCascadelist">
          <div class="inline index-screen">
            <Dropdown trigger="click" @on-click="onClickIndex">
              <i class="icon-font icon-wentitongji-01 f-16 color-filter mr-xs"></i>
              <span class="mr-sm f-14 color-filter">{{ indexName }}</span>
              <Icon type="md-arrow-dropdown" class="color-filter f-20" />
              <DropdownMenu slot="list">
                <DropdownItem
                  v-for="(item, index) in indexList"
                  :key="index"
                  :name="item.name"
                  :disabled="item.clickable !== true"
                  :selected="item.name === indexName"
                  >{{ item.name }}</DropdownItem
                >
              </DropdownMenu>
            </Dropdown>
          </div>
          <div class="line inline"></div>
          <div class="inline strative-tree" v-clickoutside="dropHide">
            <Dropdown trigger="custom" :visible="visible">
              <div @click="show">
                <i class="icon-font icon-hangzhengquhua f-14 color-filter mr-sm"></i>
                <span class="mr-sm f-14 color-filter">{{ strativeName.regionName }}</span>
                <Icon type="md-arrow-dropdown" class="color-filter f-20" />
              </div>
              <DropdownMenu slot="list" transfer>
                <el-tree
                  v-if="!!treeData.length"
                  class="tree"
                  @node-click="handleNodeClick"
                  :props="defaultProps"
                  :data="treeData"
                >
                </el-tree>
              </DropdownMenu>
            </Dropdown>
          </div>
        </div>
        <div class="inline" v-else>
          <span class="icon-font icon-wentitongji-01 f-16 color-index mr-sm"></span>
          <span class="mr-sm f-14 color-index">{{ $route.query.indexName }}</span>
          <span class="icon-font icon-hangzhengquhua f-14 color-index mr-sm"></span>
          <span class="mr-sm f-14 color-index">{{ $route.query.orgName }}</span>
        </div>
        <Button
          type="primary"
          v-if="statisticShow"
          class="btn_search fr ml-lg"
          @click="updateStatistics"
          v-permission="{
            route: $route.name,
            permission: 'statisticalresult',
          }"
        >
          <i class="icon-font icon-daochu font-white f-14 mr-xs"></i>
          <span class="inline ml-xs">更新统计</span>
        </Button>
        <span class="fr evaluation-time"
          ><i class="icon-font icon-shijian1 f-14 mr-x"></i> 评测时间：
          <Select class="width-md" v-model="batchId" placeholder="请选择时间" @on-change="onChangTaskDate" filterable>
            <Option
              v-for="(item, index) in batchInfoList"
              :value="item.batchId"
              :label="item.examineTime"
              :key="index"
            ></Option>
          </Select>
        </span>
      </div>
    </div>
    <network-inspection v-if="indexId === 87" :strative-name="strativeName" :orgCode="orgCode"> </network-inspection>
    <!-- 填报准确率 -->
    <basic-information
      ref="basicInformation"
      v-if="[1001, 2017, 3021, 4019].includes(indexId)"
      :statistical-data="statisticalData"
      :rank-list="rankList"
      :params-data="paramsData"
    ></basic-information>
    <!-- 建档率 -->
    <bookbuilding
      ref="bookbuilding"
      v-if="indexId === 1002"
      :statistical-data="statisticalData"
      :rank-list="rankList"
      :params-data="paramsData"
    ></bookbuilding>
    <!-- 数量达标率 -->
    <!-- 区 -->
    <county-quantity-reach
      ref="countyQuantityReach"
      v-if="['0', '6', '7', '8', '9', '10', '', null].includes(strativeName.regionType) && indexId === 1004"
      :params-data="paramsData"
    ></county-quantity-reach>
    <!-- 市 -->
    <city-quantity-reach
      ref="cityQuantityReach"
      v-if="['2', '3', '4', '5', '11'].includes(strativeName.regionType) && indexId === 1004"
      :params-data="paramsData"
    ></city-quantity-reach>
    <!-- 省 -->
    <province-quantity-reach
      ref="provinceQuantityReach"
      v-if="strativeName.regionType === '1' && indexId === 1004"
      :params-data="paramsData"
    ></province-quantity-reach>
    <!-- 重点位置类型视频图像设备数量达标率 -->
    <!-- 区 -->
    <county-emphasis-ouantity
      ref="countyEmphasisOuantity"
      v-if="['0', '6', '7', '8', '9', '10', '', null].includes(strativeName.regionType) && indexId === 1006"
      :params-data="paramsData"
    ></county-emphasis-ouantity>
    <!-- 市 -->
    <city-emphasis-ouantity
      ref="cityEmphasisOuantity"
      v-if="['2', '3', '4', '5', '11'].includes(strativeName.regionType) && indexId === 1006"
      :params-data="paramsData"
    ></city-emphasis-ouantity>
    <!-- 省 -->
    <province-emphasis-ouantity
      ref="provinceEmphasisOuantity"
      v-if="strativeName.regionType === '1' && indexId === 1006"
      :params-data="paramsData"
    ></province-emphasis-ouantity>
    <!-- 全量目录完整率 -->
    <whole-quantity
      ref="wholeQuantity"
      v-if="indexId === 1003 && strativeName.regionType === '1'"
      :statistical-data="statisticalData"
      :rank-list="rankList"
      :params-data="paramsData"
      :region-name="strativeName.regionName"
    ></whole-quantity>
    <city-whole-quantity
      ref="cityWholeQuantity"
      v-if="indexId === 1003 && ['2', '3', '4', '5', '11'].includes(strativeName.regionType)"
      :statistical-data="statisticalData"
      :rank-list="rankList"
      :params-data="paramsData"
      :region-name="strativeName.regionName"
    ></city-whole-quantity>
    <district-whole-quantity
      ref="districtWholeQuantity"
      v-if="['0', '6', '7', '8', '9', '10', '', null].includes(strativeName.regionType) && indexId === 1003"
      :statistical-data="statisticalData"
      :rank-list="rankList"
      :params-data="paramsData"
      :region-name="strativeName.regionName"
    ></district-whole-quantity>
    <!--  视频图像设备位置类型完整率 -->
    <video-image-device
      v-if="[1005, 2018, 3022, 4023].includes(indexId)"
      ref="videoImageDevice"
      :statistical-data="statisticalData"
      :rank-list="rankList"
      :params-data="paramsData"
      :video-title="strativeName.regionName"
    ></video-image-device>
    <!-- 普通实时视频可调阅率 4009，4001 -->
    <video-access
      ref="videoAccess"
      v-if="[4009, 4001, 4021].includes(indexId)"
      :rank-list="rankList"
      :params-data="paramsData"
      :statistical-data="statisticalData"
      @update="updateVideo"
    ></video-access>
    <!-- 历史视频可调阅率 4002,4008-->
    <video-history
      ref="videoHistory"
      v-if="[4008, 4002].includes(indexId)"
      :rank-list="rankList"
      :params-data="paramsData"
      :statistical-data="statisticalData"
      @update="updateVideo"
    ></video-history>
    <!-- 字幕标注合规率 4003，4007-->
    <video-subtitle
      ref="videoSubtitle"
      v-if="[4003, 4007].includes(indexId)"
      :rank-list="rankList"
      :params-data="paramsData"
      :statistical-data="statisticalData"
      @update="updateVideo"
    ></video-subtitle>
    <!-- 时钟准确率 4004，4006-->
    <video-clock
      ref="videoClock"
      v-if="[4004, 4006].includes(indexId)"
      :rank-list="rankList"
      :params-data="paramsData"
      :statistical-data="statisticalData"
      @update="updateVideo"
    ></video-clock>
    <!-- 视频质量 -->
    <video-quality
      ref="videoClock"
      v-if="indexId === 88"
      :rank-list="rankList"
      :params-data="paramsData"
      :statistical-data="statisticalData"
    ></video-quality>
    <!-- 历史录像完整 -->
    <video-complete
      ref="videoClock"
      v-if="indexId === 66"
      :rank-list="rankList"
      :params-data="paramsData"
      :statistical-data="statisticalData"
    ></video-complete>
    <!-- 历史录像完整率 -->
    <history-videotape
      ref="historyVideotape"
      v-if="[4010, 4020].includes(indexId)"
      :params-data="paramsData"
      :rank-list="rankList"
      :statistical-data="statisticalData"
    >
    </history-videotape>

    <!-- 视频监控联网率 -->
    <video-surveillance
      ref="videoSurveillance"
      v-if="indexId === 4011"
      :params-data="paramsData"
      :rank-list="rankList"
      :statistical-data="statisticalData"
    >
    </video-surveillance>
    <!-- 视频流质量合格率 -->
    <video-quality-rate
      ref="videoQualityRate"
      v-if="[4012, 4024].includes(indexId)"
      :params-data="paramsData"
      :rank-list="rankList"
      :statistical-data="statisticalData"
      @update="updateVideo"
    >
    </video-quality-rate>
    <capture-rationality
      ref="CaptureRationality"
      v-if="[82, 83].includes(indexId)"
      :params-data="paramsData"
      :rank-list="rankList"
      :statistical-data="statisticalData"
    >
    </capture-rationality>
    <!-- 人脸 -->
    <!-- 人脸卡口设备URL可用率  重点人脸卡口设备URL可用率-->
    <FaceDeviceAvailability
      ref="faceDeviceAvailability"
      v-if="[2001, 2002].includes(indexId)"
      :statistical-data="statisticalData"
      :rank-list="rankList"
      :params-data="paramsData"
      :indexName="indexName"
      @update="showRecountBtn"
    />
    <!-- 人脸卡口设备抓拍合格率 -->
    <FacePhotographQualified
      v-if="indexId === 2003"
      ref="facePhotographQualified"
      :statistical-data="statisticalData"
      :rank-list="rankList"
      :params-data="paramsData"
      :indexName="indexName"
    />
    <!-- 人脸卡口设备及时上传率  重点人脸卡口设备及时上传率-->
    <FaceDeviceTimelyUploading
      v-if="[2004, 2005].includes(indexId)"
      ref="faceDeviceTimelyUploading"
      :statistical-data="statisticalData"
      :rank-list="rankList"
      :params-data="paramsData"
      :indexName="indexName"
    />

    <!-- 人脸卡口设备时钟准确率 -->
    <FaceDeviceClockAccuracy
      v-if="indexId === 2006"
      ref="faceDeviceClockAccuracy"
      :statistical-data="statisticalData"
      :rank-list="rankList"
      :params-data="paramsData"
      :indexName="indexName"
    />
    <!-- 人脸卡口联网率 -->
    <FaceNetworking
      v-if="indexId === 2007"
      ref="faceNetworking"
      :statistical-data="statisticalData"
      :rank-list="rankList"
      :params-data="paramsData"
      :indexName="indexName"
    />
    <!-- 人脸卡口在线率 -->
    <FaceOnline
      v-if="indexId === 2008"
      ref="faceOnline"
      :statistical-data="statisticalData"
      :rank-list="rankList"
      :params-data="paramsData"
    />
    <FaceCaptureRationality
      v-if="indexId === 2014"
      ref="faceCaptureRationality"
      :statistical-data="statisticalData"
      :rank-list="rankList"
      :params-data="paramsData"
      :indexName="indexName"
    />
    <!-- ZDR -->
    <!-- 填报准确率 -->
    <ZdrBasicInformation
      ref="zdrBasicInformation"
      v-if="indexId === 5001"
      :statistical-data="statisticalData"
      :params-data="paramsData"
    />
    <!-- 轨迹准确性 -->
    <TrajectoryAccuracy
      ref="trajectoryAccuracy"
      v-if="indexId === 5002"
      :statistical-data="statisticalData"
      :params-data="paramsData"
    />
    <FocusDeviceRelatedRate
      ref="trajectoryAccuracy"
      v-if="indexId === 5006"
      :statistical-data="statisticalData"
      :params-data="paramsData"
    />
    <FocusRepeatRate
      ref="trajectoryAccuracy"
      v-if="indexId === 5007"
      :statistical-data="statisticalData"
      :params-data="paramsData"
    />
    <!-- 实时轨迹上传及时性 -->
    <ActualTrackUpload
      ref="actualTrackUpload"
      v-if="indexId === 5003"
      :statistical-data="statisticalData"
      :params-data="paramsData"
    />
    <!-- 轨迹图片可访问率 -->
    <TrackPictureAddressable
      ref="trackPictureAddressable"
      v-if="indexId === 5005"
      :statistical-data="statisticalData"
      :params-data="paramsData"
    />
    <!--车辆 -->
    <carVehicle
      v-if="[3001, 3002, 3003, 3004, 3005, 3006, 3007, 3008, 3009, 3010, 3011, 3024].includes(indexId)"
      ref="carVehicle"
      :statistical-data="statisticalData"
      :rank-list="rankList"
      :params-data="paramsData"
      :indexName="indexName"
      @update="showRecountBtn"
    />
    <!-- 车辆卡口在线率 -->
    <carOlineRate
      v-if="indexId === 3013"
      ref="carOlineRate"
      :statistical-data="statisticalData"
      :rank-list="rankList"
      :params-data="paramsData"
      :indexName="indexName"
    />
    <!-- 车辆卡口联网率 -->
    <carNetworkRate
      v-if="indexId === 3012"
      ref="carNetworkRate"
      :statistical-data="statisticalData"
      :rank-list="rankList"
      :params-data="paramsData"
      :indexName="indexName"
    />
    <!--车辆抓拍数量合理性检测 -->
    <carDataCompliance
      v-if="indexId === 3018"
      ref="carDataCompliance"
      :statistical-data="statisticalData"
      :rank-list="rankList"
      :params-data="paramsData"
      :indexName="indexName"
    />
    <!-- 档案数据 -->
    <!-- 人像档案置信率 -->
    <FileConfidence
      v-if="indexId === 6001"
      :statistical-data="statisticalData"
      ref="fileConfidence"
      :rank-list="rankList"
      :params-data="paramsData"
    />
    <!-- 人像档案准确率  -->
    <FileAccuracy
      v-if="indexId === 6002"
      :statistical-data="statisticalData"
      ref="fileAccuracy"
      :rank-list="rankList"
      :params-data="paramsData"
    />
    <!-- 接口稳定性  -->
    <InterfaceStability
      v-if="indexId === 7001"
      :statistical-data="statisticalData"
      ref="fileAccuracy"
      :params-data="paramsData"
    ></InterfaceStability>
    <!-- 轨迹设备关联率  -->
    <trackCorrelation
      v-if="indexId === 78"
      :statistical-data="statisticalData"
      ref="trackCorrelation"
      :rank-list="rankList"
      :params-data="paramsData"
    />
    <!-- 重复轨迹  -->
    <repeatLocus
      v-if="indexId === 79"
      :statistical-data="statisticalData"
      ref="repeatLocus"
      :rank-list="rankList"
      :params-data="paramsData"
    ></repeatLocus>
    <place-accuracy
      v-if="indexId === 8001"
      :statistical-data="statisticalData"
      ref="placeAccuracy"
      :params-data="paramsData"
    ></place-accuracy>
    <!--  人脸/车辆卡口在线率提升 公用同一个页面  -->
    <face-car-online-rate-advance
      v-if="[3020, 2016].includes(indexId)"
      :statistical-data="statisticalData"
      ref="face-car-online-rate-advance"
      :params-data="paramsData"
      :orgRegionName="strativeName.regionName"
      :examineTime="examineTime"
    ></face-car-online-rate-advance>
    <!-- 人脸视图可在线率/车辆视图可在线率 -->
    <FaceViewOnline
      v-if="[7002, 7003, 7004].includes(indexId)"
      ref="face-view-online"
      :statistical-data="statisticalData"
      :params-data="paramsData"
    />
    <!--  视频监控在线率提升  -->
    <video-online
      v-if="indexId === 4017"
      ref="VideoOnline"
      :params-data="paramsData"
      :examineTime="examineTime"
      :regionName="strativeName.regionName"
    ></video-online>
    <face-car-valid-submit-quantity
      ref="face-car-valid-submit-quantity"
      v-if="[4018, 3019, 2015].includes(indexId)"
      :statistical-data="statisticalData"
      :params-data="paramsData"
      :examineTime="examineTime"
    ></face-car-valid-submit-quantity>
    <!--  视频监控资产匹配率  -->
    <video-assetmatch-rate
      v-if="indexId === 4022"
      ref="VideoOnline"
      :params-data="paramsData"
      :regionName="strativeName.regionName"
      @handleChangeCode="handleChangeCode"
    ></video-assetmatch-rate>
    <!--联网车辆/人脸卡口目录一致率（四川）| 人脸/车辆抓拍数据一致性-->
    <!--  FACE_CATALOGUE_SAME/2019、VEHICLE_CATALOGUE_SAME/3023 |  FACE_CAPTURE_DATA_CONSISTENCY  / 2022、VEHICLE_CAPTURE_DATA_CONSISTENCY/3037-->
    <catalogue-same
      v-if="[3023, 3037, 2019, 2022].includes(indexId)"
      ref="face-view-online"
      :statistical-data="statisticalData"
      :params-data="paramsData"
    />
  </div>
</template>

<style lang="less" scoped>
.overview-evaluation-content {
  .evaluation-header {
    height: 48px;
    line-height: 48px;
    width: 100%;
    border-bottom: 1px solid var(--devider-line);
    .color-filter {
      color: var(--color-primary);
      vertical-align: middle;
      cursor: pointer;
    }
    .evaluation-time {
      color: var(--color-label);
      font-size: 14px;
      margin-left: 10px;
      .mr-x {
        margin-right: 3px;
      }
    }
    .btn_search {
      margin-top: 5px;
    }
    .index-screen {
      height: 520px;

      @{_deep}.ivu-select-dropdown {
        height: 520px !important;
        width: 240px;
        overflow-y: auto;
        transform-origin: center top;
        position: absolute;
        will-change: top, left;
        top: 50px !important;
        left: 0px !important;
      }
    }
    .strative-tree {
      max-height: 500px;
      @{_deep}.ivu-select-dropdown {
        max-height: 500px;
        width: 200px;
        overflow-y: auto;
        transform-origin: center top;
        position: absolute;
        will-change: top, left;
        top: 50px !important;
      }
    }
    .line {
      height: 18px;
      width: 1px;
      vertical-align: middle;
      margin: 0 20px;
      background-color: rgba(7, 66, 119, 1);
    }
    .active {
      background: rgba(2, 57, 96, 1);
    }
  }
}
.none {
  display: none;
}
.disabled {
  user-select: none;
  cursor: not-allowed;
  pointer-events: none;
}
.color-index {
  color: var(--color-primary);
}
</style>

<script>
import {
  allIndexTypeObject,
} from '@/views/governanceevaluation/evaluationoResult/util/IndexTypeConfig.js';
import { mapGetters, mapActions } from 'vuex';
import evaluationoverview from '@/config/api/evaluationoverview';
import dealWatch from '@/mixins/deal-watch';
import { superiorinjectfunc } from '@/views/disposalfeedback/cascadelist/util/proxyInterface.js';
export default {
  name: 'overviewEvaluation',
  mixins: [dealWatch],
  props: {
    value: {},
    statisticsList: {},
  },
  data() {
    return {
      statisticShow: false,
      examineTime: '',
      visible: false,
      defaultProps: {
        label: 'name',
        children: 'children',
      },
      strativeName: {},
      indexName: '',
      indexList: [], //指标
      administrativeList: [], //行政区划
      defaultKeys: [],
      dropdownList: {},
      strative: {},
      resultLinkages: [],
      resultList: [],
      taskList: [],
      batchId: '',
      indexId: '',
      uuid: '',
      qualified: '',
      statisticalData: {}, //统计数据
      rankList: [], //排名
      paramsData: {
        orgRegionCode: '',
        indexId: '',
        batchId: '',
        access: '',
        displayType: null,
      },
      isDone: false,
      displayType: null,
      orgType: null,
      orgRegionCode: null,
      treeData: [],
      defaultOrgRegionCode: '',
      rankLoading: false,
      batchInfoList: [],
    };
  },
  components: {
    basicInformation: require('@/views/governanceevaluation/evaluationoverview/components/basics/basic-information.vue')
      .default,
    bookbuilding: require('@/views/governanceevaluation/evaluationoverview/components/basics/bookbuilding.vue').default,
    wholeQuantity: require('@/views/governanceevaluation/evaluationoverview/components/basics/whole-quantity.vue')
      .default,
    cityWholeQuantity:
      require('@/views/governanceevaluation/evaluationoverview/components/basics/city-whole-quantity.vue').default,
    districtWholeQuantity:
      require('@/views/governanceevaluation/evaluationoverview/components/basics/district-whole-quantity.vue').default,
    countyQuantityReach:
      require('@/views/governanceevaluation/evaluationoverview/components/basics/county-quantity-reach.vue').default,
    cityQuantityReach:
      require('@/views/governanceevaluation/evaluationoverview/components/basics/city-quantity-reach.vue').default,
    provinceQuantityReach:
      require('@/views/governanceevaluation/evaluationoverview/components/basics/province-quantity-reach.vue').default,
    countyEmphasisOuantity:
      require('@/views/governanceevaluation/evaluationoverview/components/basics/county-emphasis-ouantity.vue').default,
    cityEmphasisOuantity:
      require('@/views/governanceevaluation/evaluationoverview/components/basics/city-emphasis-ouantity.vue').default,
    provinceEmphasisOuantity:
      require('@/views/governanceevaluation/evaluationoverview/components/basics/province-emphasis-ouantity.vue')
        .default,
    videoImageDevice:
      require('@/views/governanceevaluation/evaluationoverview/components/basics/video-image-device.vue').default,
    networkInspection:
      require('@/views/governanceevaluation/evaluationoverview/components/video/network-inspection.vue').default,
    FaceDeviceAvailability:
      require('@/views/governanceevaluation/evaluationoverview/components/face/face-device-availability.vue').default,
    FacePhotographQualified:
      require('@/views/governanceevaluation/evaluationoverview/components/face/face-photograph-qualified.vue').default,
    FaceNetworking: require('@/views/governanceevaluation/evaluationoverview/components/face/face-networking.vue')
      .default,
    FaceOnline: require('@/views/governanceevaluation/evaluationoverview/components/face/face-online.vue').default,
    FaceViewOnline: require('@/views/governanceevaluation/evaluationoverview/components/face/face-view-online.vue')
      .default,
    FaceDeviceClockAccuracy: require('../components/face/face-device-clock-accuracy.vue').default,
    FaceDeviceTimelyUploading: require('../components/face/face-device-timely-uploading.vue').default,
    ZdrBasicInformation:
      require('@/views/governanceevaluation/evaluationoverview/components/zdr/zdr-basic-information.vue').default,
    TrajectoryAccuracy:
      require('@/views/governanceevaluation/evaluationoverview/components/zdr/trajectory-accuracy.vue').default,
    FocusRepeatRate: require('@/views/governanceevaluation/evaluationoverview/components/zdr/focus-repeat-rate')
      .default,
    FocusDeviceRelatedRate:
      require('@/views/governanceevaluation/evaluationoverview/components/zdr/focus-device-related-rate').default,
    ActualTrackUpload: require('@/views/governanceevaluation/evaluationoverview/components/zdr/actual-track-upload.vue')
      .default,
    TrackPictureAddressable:
      require('@/views/governanceevaluation/evaluationoverview/components/zdr/track-picture-addressable.vue').default,
    CaptureRationality: require('../components/face/capture-rationality').default,
    FaceCaptureRationality: require('../components/face/face-capture-rationality').default,

    FileAccuracy: require('@/views/governanceevaluation/evaluationoverview/components/fileData/file-accuracy.vue')
      .default,
    FileConfidence: require('@/views/governanceevaluation/evaluationoverview/components/fileData/file-confidence.vue')
      .default,
    InterfaceStability:
      require('@/views/governanceevaluation/evaluationoverview/components/fileData/interface-stability.vue').default,
    repeatLocus: require('../components/fileData/repeat-locus.vue').default,
    trackCorrelation: require('../components/fileData/track-correlation.vue').default,
    videoAccess: require('../components/video/videoAccess.vue').default,
    PlaceAccuracy: require('../components/place/place-accuracy.vue').default,
    videoClock: require('../components/video/videoClock.vue').default,
    videoComplete: require('../components/video/videoComplete.vue').default,
    videoHistory: require('../components/video/videoHistory.vue').default,
    videoQuality: require('../components/video/videoQuality.vue').default,
    videoSubtitle: require('../components/video/videoSubtitle.vue').default,
    historyVideotape: require('../components/video/historyVideotape.vue').default,
    videoSurveillance: require('../components/video/videoSurveillance.vue').default,
    videoQualityRate: require('../components/video/videoQualityRate.vue').default,
    carVehicle: require('@/views/governanceevaluation/evaluationoverview/components/car/carVehicle.vue').default,
    carOlineRate: require('@/views/governanceevaluation/evaluationoverview/components/car/car-online-rate.vue').default,
    carNetworkRate: require('@/views/governanceevaluation/evaluationoverview/components/car/car-network-rate.vue')
      .default,
    carDataCompliance: require('@/views/governanceevaluation/evaluationoverview/components/car/car-data-compliance.vue')
      .default,
    faceCarOnlineRateAdvance:
      require('@/views/governanceevaluation/evaluationoverview/components/face/face-car-online-rate-advance').default,
    VideoOnline: require('../components/video/video-online.vue').default,
    faceCarValidSubmitQuantity:
      require('@/views/governanceevaluation/evaluationoverview/components/face/face-car-valid-submit-quantity').default,
    VideoAssetmatchRate:
      require('@/views/governanceevaluation/evaluationoverview/components/video/video-assetmatch-rate.vue').default,
    CatalogueSame: require('@/views/governanceevaluation/evaluationoverview/components/face/catalogue-same.vue')
      .default,
  },
  computed: {
    ...mapGetters({
      initialAreaList: 'common/getInitialAreaList',
      initialOrgList: 'common/getInitialOrgList',
      cacheRouterList: 'tabs/getCacheRouterList',
      configData: 'governanceevaluation/configData',
    }),
    //是否为级联清单跳转过来的
    isCascadelist() {
      return this.$route.name === 'cascadelist' && !!this.$route.query.cascadeId;
    },
  },
  async activated() {
    await this.$nextTick();
    // 判断有没有componentName,有才继续往下
    if (
      [
        'taskToOverview',
        'detectionToOverview',
        'evaluationDetail-taskToOverview',
        'AssessmentResult-detectionToOverview',
        'overviewEvaluation',
        'evaluationDetail-taskToOverview-Placemanagement',
        'evaluationDetail-taskToOverview-FaceCarOnlineNum',
        'evaluationDetail-taskToOverview-FaceCarTopOnline',
        'evaluationDetail-taskToOverview-FaceCarReportRate',
        'AssessmentResult-overviewEvaluation',
        'evaluationoResult-overviewEvaluation',
      ].includes(this.$route.query.componentName)
    ) {
      await this.$store.dispatch('common/setAreaList');
      await this.$store.dispatch('common/setOrganizationList');
      this.paramsData = {};
      this.indexName = '';
      this.examineTime = '';
      if (this.$route.query.displayType) {
        this.orgType = this.$route.query.displayType;
        this.orgRegionCode = this.$route.query.code;
      }
      //页面刷新自动会把indexId转为字符串
      this.$route.query.indexId = Number(this.$route.query.indexId);
      const {
        query: { indexId, batchId, access, startTime, uuid = '' },
      } = this.$route;
      if (this.isCascadelist) {
        this.setParamsData();
      } else {
        await this.getDropdownInfo({
          indexId,
          batchId,
          access,
          uuid,
        });
        this.treeText();
        this.handelRegionCode();
      }
      this.indexId = Number(indexId); //页面刷新自动会把indexId转为字符串
      await this.getHistoryTask();
      this.batchId = batchId;
      if (startTime) {
        this.examineTime = startTime;
      }
      this.getStaticList();
    }
  },
  methods: {
    /**
     * 过滤在评测结果中实现的指标
     * @param indexes 指标列表
     * @returns {*}
     */
    filterIndexList(indexes) {
      return indexes.filter((item) =>
        Object.keys(allIndexTypeObject).some(
          (value) => value === `${item.id}` && !allIndexTypeObject[value]['componentName'],
        ),
      ); //id 类型统一
    },
    getStaticList() {
      if (
        [3010, 3011, 2001, 2002, 4001, 4002, 4009, 4008, 4003, 4004, 4006, 4007, 3024].includes(this.paramsData.indexId)
      ) {
        this.updateVideo();
        return;
      }
      if ([1004, 1006, 87, 5001, 4022].includes(this.indexId)) return;
      if (
        [6002, 5002, 5003, 5005, 6001, 78, 79, 7001, 7002, 7003, 3023, 3037, 2019, 2022, 8001].includes(
          this.paramsData.indexId,
        )
      ) {
        this.getStatInfo(); //数据统计
        return;
      }
      this.getStatInfo(); //数据统计
      this.getRankInfo(); //排名
    },
    onChangTaskDate(val) {
      if (!val) return false;
      this.paramsData.batchId = val;
      this.$router.push({
        name: this.$route.name,
        query: {
          ...this.$route.query,
          batchId: val,
        },
      });
      this.getStaticList();
    },
    async getHistoryTask() {
      let params = {
        indexId: this.paramsData.indexId,
        batchId: this.paramsData.batchId,
        access: this.paramsData.access,
        orgRegionCode: this.paramsData.orgRegionCode,
        uuid: this.paramsData.batchId,
      };
      try {
        /**------级联清单特殊替换处理接口(后端转发)-------**/
        let {
          data: { data },
        } = await superiorinjectfunc(
          this,
          evaluationoverview.getHistoryBatchInfo,
          params,
          'post',
          this.$route.query.cascadeId,
          {},
        );
        this.batchInfoList = data || [];
      } catch (e) {
        console.log(e);
      }
    },
    ...mapActions({
      setCacheRouterList: 'tabs/setCacheRouterList',
    }),
    /**
     * 如果是从级联清单跳转过来
     */
    setParamsData() {
      let { access, displayType, batchId, indexId, code } = this.$route.query;
      this.paramsData = {
        orgRegionCode: code,
        indexId,
        batchId,
        displayType,
        access: access || 'REPORT_MODE',
      };
      this.displayType = displayType;
    },
    // 人工复核更新统计接口
    async updateStatistics() {
      let data = {
        batchId: this.batchId,
      };
      try {
        await this.$http.post(evaluationoverview.pushRecheckQueue, data);
      } catch (err) {
        console.log(err);
      }
    },
    async updateVideo() {
      if (this.$route.name !== 'cascadelist') {
        await this.showRecountBtn();
      }
      await this.getStatInfo();
      await this.getRankInfo();
    },
    // 判断更新统计是否显示
    async showRecountBtn() {
      try {
        let res = await this.$http.get(evaluationoverview.showRecountBtn, {
          params: { batchId: this.batchId },
        });
        this.statisticShow = res.data.data || false;
      } catch (err) {
        console.log(err);
      }
    },
    dropHide() {
      this.visible = false;
    },
    treeText() {
      let administrativeObject = {};
      let initialList = this.displayType === 'REGION' ? this.initialAreaList : this.initialOrgList;
      let filedType = this.displayType === 'REGION' ? 'region' : 'org'; // 拼接动态字段名称
      this.administrativeList.forEach((item) => {
        administrativeObject[item] = item;
      });
      let data = initialList.filter((row) => {
        return row[`${filedType}Code`] in administrativeObject;
        // 如果包含regionCode/orgCode
      });
      let tempArr = this.$util.common.deepCopy(data);
      this.displayType === 'REGION'
        ? (this.treeData = this.$util.common.arrayToJson(tempArr, 'regionCode', 'parentCode'))
        : (this.treeData = this.$util.common.arrayToJson(tempArr, 'id', 'parentId'));
      if (data.length > 0) {
        this.strativeName.regionName = data[0][`${filedType}Name`];
        this.strativeName.regionCode = data[0][`${filedType}Code`];
        this.strativeName.regionType = data[0].regionType;
      }
      // let data = []
      // // 判断是行政区划
      // if (this.displayType === 'REGION') {
      //   this.defaultProps = { label: 'regionName', children: 'children' }
      //   this.initialAreaList.find((row) => {
      //     this.administrativeList.map((item) => {
      //       if (row.regionCode == item) {
      //         this.strativeName.regionName = row.regionName
      //         this.strativeName.regionCode = row.regionCode
      //         this.strativeName.regionType = row.regionType
      //         data.push(row)
      //         let tempArr = this.$util.common.deepCopy(data)
      //         this.treeData = this.$util.common.arrayToJson(tempArr, 'regionCode', 'parentCode')
      //       }
      //     })
      //   })
      // } else {
      //   this.defaultProps = { label: 'orgName', children: 'children' }
      //   this.initialOrgList.find((row) => {
      //     this.administrativeList.map((item) => {
      //       if (item == row.orgCode) {
      //         this.strativeName.regionName = row.orgName
      //         this.strativeName.regionCode = row.orgCode
      //         this.strativeName.regionType = row.regionType
      //         data.push(row)
      //         let tempArr = this.$util.common.deepCopy(data)
      //         this.treeData = this.$util.common.arrayToJson(
      //           this.$util.common.deepCopy(tempArr),
      //           'id',
      //           'parentId'
      //         )
      //       }
      //     })
      //   })
      // }
      // console.log(this.treeData,this.strativeName,data)
    },
    async getDropdownInfo({ indexId, batchId, uuid, access = 'REPORT_MODE' }) {
      let params = {
        displayType: this.orgType,
        orgRegionCode: this.orgRegionCode,
        indexId,
        uuid: uuid ? uuid : batchId,
        access,
      };
      try {
        let res = await this.$http.post(evaluationoverview.getDropdownInfo, params);
        const {
          data: { displayType, indexes, orgRegionCodes, resultLinkages },
        } = res.data;
        this.displayType = displayType;
        this.indexList = indexes;
        this.resultLinkages = resultLinkages;
        this.administrativeList = orgRegionCodes;

        if (this.$route.query.code) {
          this.strativeName.regionCode = this.$route.query.code;
        } else {
          this.strativeName.regionCode = res.data.data.defaultOrgRegionCode;
        }
        const { name } = this.indexList.filter((e) => e.id == indexId)[0];
        this.indexName = name;
        if (!this.examineTime) {
          const examineTime = this.resultLinkages.filter((e) => e.key == `${this.strativeName.regionCode}#${indexId}`);
          this.examineTime = examineTime.length > 0 ? examineTime[0]['examineTime'] : '';
        }
        this.paramsData = {
          orgRegionCode: this.strativeName.regionCode,
          indexId,
          batchId: batchId,
          displayType: this.displayType,
          access: this.$route.query.access || 'REPORT_MODE',
          indexType: this.$route.query.indexType || '',
        };
      } catch (err) {
        console.log(err);
      }
    },
    show() {
      this.visible = true;
    },
    handleNodeClick(data) {
      if (this.displayType === 'REGION') {
        if (!this.administrativeList.includes(data.regionCode)) return;
      } else {
        if (!this.administrativeList.includes(data.orgCode)) return;
      }
      this.treeText();
      this.strativeName = data ? JSON.parse(JSON.stringify(data)) : null;
      this.resultLinkages.map((item) => {
        this.resultList = item.key.split('#');
        if (this.resultList[0] == this.strativeName.regionCode && this.resultList[1] == this.indexId) {
          this.batchId = item.batchId;
          this.examineTime = item.examineTime;
        }
      });
      if (this.displayType === 'REGION') {
        this.$set(this.paramsData, 'orgRegionCode', this.strativeName.regionCode);
      } else {
        this.strativeName.regionName = this.strativeName.orgName;
        this.paramsData.orgRegionCode = this.strativeName.orgCode;
      }
      this.paramsData.batchId = this.batchId;
      this.getStaticList();
      this.visible = false;
    },
    onClickIndex(val) {
      this.indexName = val;
      this.indexList.map((item) => {
        if (val == item.name) {
          this.indexId = item.id;
        }
      });
      this.resultLinkages.map((item) => {
        this.resultList = item.key.split('#');
        if (this.resultList[0] == this.strativeName.regionCode && this.resultList[1] == this.indexId) {
          this.batchId = item.batchId;
          this.examineTime = item.examineTime;
        }
      });
      this.paramsData.indexId = this.indexId;
      this.paramsData.batchId = this.batchId;
      this.handleCacheRouterList();
      this.getStaticList();
    },
    // 数量统计
    async getStatInfo() {
      const batchid = this.$route.query.batchId;
      if (!batchid || !this.indexId) {
        return;
      }
      this.statisticalData = {};
      const importantIdList = [4001, 4002, 4003, 4004];
      let data = {
        indexId: this.indexId,
        batchId: batchid,
        access: this.$route.query.access || 'REPORT_MODE',
        displayType: this.displayType,
        orgRegionCode: this.paramsData.orgRegionCode,
        isImportant: importantIdList.includes(this.indexId) ? '1' : undefined,
      };
      try {
        /**------级联清单特殊替换处理接口(后端转发)-------**/
        let res = await superiorinjectfunc(
          this,
          evaluationoverview.getStatInfo,
          data,
          'post',
          this.$route.query.cascadeId,
          {},
        );
        // let res = await this.$http.post(evaluationoverview.getStatInfo, data)

        this.statisticalData = res.data.data || {};
      } catch (err) {
        console.log(err);
      }
    },
    // 排名
    async getRankInfo() {
      const batchid = this.$route.query.batchId;
      if (!batchid || !this.indexId) {
        return;
      }
      this.rankLoading = true;
      let data = {
        indexId: this.indexId,
        batchId: batchid,
        access: this.$route.query.access || 'REPORT_MODE',
        displayType: this.displayType,
        orgRegionCode: this.paramsData.orgRegionCode,
        sortField: 'RESULT_VALUE',
      };
      try {
        /**------级联清单特殊替换处理接口(后端转发)-------**/
        let res = await superiorinjectfunc(
          this,
          evaluationoverview.getRankInfo,
          data,
          'post',
          this.$route.query.cascadeId,
          {},
        );
        // let res = await this.$http.post(evaluationoverview.getRankInfo, data)
        this.rankList = res.data.data;
        this.rankLoading = false;
      } catch (err) {
        console.log(err);
        this.rankLoading = false;
      }
    },
    handelRegionCode() {
      if (this.displayType == 'REGION') {
        this.defaultProps = { label: 'regionName', children: 'children' };
        this.initialAreaList.find((item) => {
          if (item.regionCode == this.$route.query.code) {
            this.strativeName.regionName = item.regionName;
            this.strativeName.regionCode = item.regionCode;
            this.strativeName.regionType = item.regionType;
          }
        });
      } else {
        this.defaultProps = { label: 'orgName', children: 'children' };
        this.initialOrgList.find((item) => {
          if (item.orgCode == this.$route.query.code) {
            this.strativeName.regionName = item.orgName;
            this.strativeName.regionCode = item.orgCode;
            this.strativeName.regionType = item.regionType;
          }
        });
      }
    },
    handleChangeCode(code) {
      this.$set(this, 'orgRegionCode', code);
      this.paramsData.orgRegionCode = code;
      this.handelRegionCode();
    },
    handleCacheRouterList() {
      const querys = this.$router.history.current.query;
      const path = this.$router.history.current.path;
      let newQueryObj = { ...querys, batchId: this.batchId, indexId: this.indexId, startTime: this.examineTime };
      const newQuery = JSON.parse(JSON.stringify(newQueryObj));
      this.$router.push({ path, query: newQuery });
      // 获取缓存标签页路由值并且修改相对应的code值
      let cacheRouterList = this.$util.common.deepCopy(this.cacheRouterList);
      let cacheRouterMatchRateIndex = null;
      // 找到匹配当前路由对象
      let cacheRouterMatchRate = cacheRouterList.find((item, index) => {
        // 缓存路由添加参数
        let preComponentNames = ['taskToOverview', 'detectionToOverview', 'overviewEvaluation']; // 当前组件的上一级组件名
        if (preComponentNames.includes(item.name)) {
          item.meta.queryParams.batchId = this.batchId;
          item.meta.queryParams.indexId = this.indexId;
          item.meta.queryParams.startTime = this.examineTime;
          cacheRouterMatchRateIndex = index;
          return item;
        }
      });
      cacheRouterList.splice(cacheRouterMatchRateIndex, 1, cacheRouterMatchRate);
      this.setCacheRouterList(cacheRouterList);
    },
  },
  watch: {},
};
</script>
