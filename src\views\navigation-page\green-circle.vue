<template>
  <div class="container"></div>
</template>

<script>
export default {
  name: 'green-circle',
  props: {},
  data() {
    return {};
  },
  created() {},
  methods: {},
  watch: {},
  components: {},
};
</script>

<style lang="less" scoped>
.container {
  display: inline-block;
  line-height: 18px;
  vertical-align: middle;
  width: 12px;
  height: 12px;
  background: #07dbf6;
  border-radius: 50%;
  border: 4px solid #07415f;
}
</style>
