<template>
  <div class="assessmen-task auto-fill">
    <div v-if="!componentName" class="evaluat_div">
      <div class="right-content auto-fill">
        <div class="search-wrapper">
          <ui-label label="任务名称" :width="70" class="fl">
            <Input class="width-md" v-model="searchData.taskName" placeholder="请输入任务名称"></Input>
          </ui-label>
          <ui-label class="ml-lg fl" label="任务状态" :width="70">
            <Select class="width-md" placeholder="请选择" clearable v-model="searchData.taskRunState">
              <Option v-for="(item, index) in resultList" :key="index" :value="item.value">
                {{ item.name }}
              </Option>
            </Select>
          </ui-label>
          <ui-label :width="0" label="" class="fl ml-lg">
            <Button type="primary" class="mr-sm fl" @click="search"> 查询 </Button>
            <Button class="mr-lg fl" @click="resetSearchDataMx()"> 重置 </Button>
          </ui-label>
          <ui-label :width="0" label=" " class="fr">
            <Button type="primary" @click="operationTask('add', {})" class="fr">
              <i class="icon-font icon-tianjia f-12 mr-sm vt-middle" title="添加"></i>
              <span class="vt-middle">新增考核任务</span>
            </Button>
          </ui-label>
        </div>
        <div class="table-box auto-fill">
          <ui-table class="ui-table auto-fill" :table-columns="tableColumns" :table-data="tableData" :loading="loading">
            <template #examCycleType="{ row }">
              <span>
                {{ row.examCycleType === '1' ? '月考核' : '周考核' }}
              </span>
            </template>
            <template #taskRunState="{ row }">
              <span
                class="check-status"
                :class="[
                  row.taskRunState == 0 ? 'bg-787878' : '',
                  row.taskRunState == 1 ? 'bg-D66418' : '',
                  row.taskRunState == 2 ? 'bg-success' : '',
                  row.taskRunState == 3 ? 'bg-failed' : '',
                  row.taskRunState == 4 ? 'bg-failed' : '',
                ]"
              >
                {{ handleCheckStatus(row.taskRunState) }}
              </span>
            </template>
            <template #option="{ row }">
              <i
                class="icon-font icon-bianjirenwu deleteicon mr-lg"
                v-if="row.taskRunState === '1'"
                title="编辑任务"
              ></i>
              <ui-btn-tip
                icon="icon-bianjirenwu"
                content="编辑任务"
                v-if="row.taskRunState !== '1'"
                class="mr-lg"
                @click.native="operationTask('edit', row)"
              ></ui-btn-tip>
              <i
                class="icon-font icon-shanchurenwu deleteicon mr-lg"
                v-if="
                  row.taskName === '视图数据全量月考核' ||
                  row.taskName === '视图数据上报月考核' ||
                  row.taskRunState === '1'
                "
                title="删除任务"
              ></i>
              <ui-btn-tip
                class="mr-lg"
                v-else
                icon="icon-shanchurenwu"
                content="删除任务"
                @click.native="deleteTask(row)"
              ></ui-btn-tip>
              <ui-btn-tip
                v-if="row.taskRunState != 1"
                icon="icon-zhongxinzhihang"
                content="立即执行"
                class="mr-lg"
                @click.native="execute(row)"
              ></ui-btn-tip>
              <i v-else class="icon-font icon-zhongxinzhihang deleteicon mr-lg" title="立即执行"></i>
              <i-switch
                class="mr-md"
                :value="row.taskEnableState === '1' ? true : false"
                :before-change="() => handleBeforeChange(row)"
                size="small"
              ></i-switch>
              <ui-btn-tip
                icon="icon-lishijilu-01"
                :styles="{ color: 'var(--color-active)', 'font-size': '14px' }"
                content="考核记录"
                @click.native="record(row)"
              ></ui-btn-tip>
            </template>
          </ui-table>
        </div>
        <ui-page
          class="page menu-content-background"
          :page-data="searchData"
          @changePage="changePage"
          @changePageSize="changePageSize"
        ></ui-page>
      </div>
      <!-- 新增/编辑 -->
      <addor-edit-modal
        ref="addOrUpdate"
        v-if="addOrUpdateVisible"
        :moduleList="moduleList"
        :title="title"
        @update="update"
        @onClose="onClose"
      ></addor-edit-modal>
    </div>
    <keep-alive v-else>
      <component :is="componentName" :id="id" @changeComponent="changeComponentHandle"></component>
    </keep-alive>
    <record v-model="recordShow" :record-params="recordParams"></record>
  </div>
</template>
<script>
import governanceevaluation from '@/config/api/governanceevaluation';
import AssessmentResult from './assessment-result.vue';
import detectionToOverview from '@/views/governanceevaluation/evaluationoverview/overview-evaluation/index.vue';
import dealWatch from '@/mixins/deal-watch';
export default {
  mixins: [dealWatch],
  name: 'assessmenTask',
  components: {
    UiTable: require('@/components/ui-table.vue').default,
    UiSearchTree: require('@/components/ui-search-tree.vue').default,
    AddorEditModal: require('./components/addor-edit-modal.vue').default,
    OperationBtnMore: require('@/components/operation-btn-more.vue').default,
    CreateTabs: require('@/components/create-tabs/create-tabs.vue').default,
    AssessmentResult,
    detectionToOverview,
    Record: require('./components/record.vue').default,
  },
  data() {
    return {
      title: '新增考核任务',
      addOrUpdateVisible: false,
      componentName: null,
      btnList: [],
      id: 1,
      themData: {
        componentName: 'AssessmentResult', // 需要跳转的组件名
        text: '考核结果', // 跳转页面标题
        title: '考核结果',
        type: 'view',
      },
      loading: false,
      switchLoading: false,
      tableColumns: [
        {
          type: 'index',
          tooltip: true,
          width: 50,
          title: '序号',
          align: 'center',
        },
        {
          title: '考核任务名称',
          key: 'taskName',
          tooltip: true,
          align: 'left',
          minWidth: 200,
        },
        {
          title: '考核对象',
          key: 'regionName',
          tooltip: true,
          align: 'left',
          minWidth: 150,
        },
        {
          title: '考核方案',
          key: 'schemeName',
          tooltip: true,
          align: 'left',
          minWidth: 120,
        },
        {
          title: '考核类型',
          slot: 'examCycleType',
          tooltip: true,
          align: 'left',
          width: 120,
        },
        {
          title: '考核内容数',
          key: 'count',
          tooltip: true,
          align: 'left',
          width: 100,
        },
        {
          title: '考核时间',
          key: 'scheduleCron',
          tooltip: true,
          align: 'left',
          minWidth: 150,
        },
        {
          title: '任务状态',
          slot: 'taskRunState',
          width: 150,
        },
        {
          title: '最近考核时间',
          key: 'taskEndTime',
          tooltip: true,
          align: 'left',
          minWidth: 150,
        },
        {
          title: '操作',
          slot: 'option',
          align: 'center',
          tooltip: true,
          width: 250,
          fixed: 'right',
          className: 'table-action-padding',
        },
      ],
      resultList: [
        { name: '未开始', value: '0', index: 0 },
        { name: '进行中', value: '1', index: 1 },
        { name: '已完成', value: '2', index: 2 },
        { name: '已暂停', value: '3', index: 3 },
        { name: '任务异常', value: '4', index: 4 },
      ],
      tableData: [],
      searchData: {
        taskName: '',
        taskRunState: '', //考核结果
        pageNum: 1,
        pageSize: 20,
        totalCount: 0,
      },
      moduleList: [], //考核方案
      editList: {}, //任务详情
      componentLevel: 0, //组件标签层级 如果是标签组件套用标签组件需要此参数
      // 记录
      recordShow: false,
      recordParams: null,
    };
  },
  created() {
    this.getTableList();
    this.getParams();
    this.getAllExamScheme();
  },
  activated() {
    this.startWatch('$route', () => {
      this.getParams();
    });
  },
  methods: {
    async getTableList() {
      this.loading = true;
      let data = {
        taskName: this.searchData.taskName,
        taskRunState: this.searchData.taskRunState,
        pageNumber: this.searchData.pageNum,
        pageSize: this.searchData.pageSize,
      };
      try {
        let res = await this.$http.post(governanceevaluation.taskPageList, data);
        this.tableData = res.data.data.entities ? res.data.data.entities : [];
        this.searchData.totalCount = res.data.data.total ? res.data.data.total : 0;
      } catch (err) {
        // console.log(err);
      }
      this.loading = false;
    },
    // 检索
    search() {
      this.searchData.pageNum = 1;
      this.getTableList();
    },
    handleCheckStatus(row) {
      let flag = {
        0: '未开始',
        1: '运行中',
        2: '已完成',
        3: '失败',
        4: '任务异常',
      };
      return flag[row];
    },
    resultShow() {
      this.componentName = 'AssessmentResult';
    },
    // 新增和编辑弹窗
    async operationTask(action, row) {
      let list = [];
      if (action === 'edit') {
        this.title = '编辑考核任务';
        await this.getTaskEdit(row);
        list = {
          ...this.editList,
        };
      } else {
        this.title = '新增考核任务';
      }
      this.addOrUpdateVisible = true;
      this.$nextTick(() => {
        this.$refs.addOrUpdate.init(list);
      });
    },
    // 编辑回显
    async getTaskEdit(row) {
      let data = {
        id: row.id,
        schemeId: row.schemeId,
        orgCode: row.orgCode,
      };
      try {
        let res = await this.$http.post(governanceevaluation.queryEvaluationTaskById, data);
        this.editList = res.data.data;
      } catch (err) {
        // console.log(err);
      }
    },
    // 立即执行
    execute(row) {
      this.$UiConfirm({
        content: '您确认要立即执行此任务嘛?',
        title: '警告',
      })
        .then(() => {
          this.executeNowInit(row);
        })
        .catch(() => {});
    },
    async executeNowInit(val) {
      let params = {
        jobId: val.jobId,
      };
      try {
        let res = await this.$http.get(governanceevaluation.executeNow, {
          params,
        });
        this.$Message.success(res.data.msg);
        this.search();
      } catch (err) {
        // console.log(err);
      }
    },
    // 删除
    deleteTask(row) {
      this.$UiConfirm({
        content: '您确认要删除此任务嘛?',
        title: '警告',
      })
        .then(() => {
          this.deleteInit(row);
        })
        .catch(() => {});
    },
    async deleteInit(val) {
      let params = {
        jobId: val.jobId,
        taskId: val.id,
      };
      try {
        let res = await this.$http.get(governanceevaluation.deleteTask, {
          params,
        });
        this.$Message.success(res.data.msg);
        this.search();
      } catch (err) {
        // console.log(err);
      }
    },
    // 启动、暂停
    async handleBeforeChange(row) {
      let params = {
        jobId: row.jobId,
        num: row.taskEnableState === '1' ? '0' : '1',
      };
      try {
        let res = await this.$http.get(governanceevaluation.pauseStart, {
          params,
        });
        this.$Message.success(res.data.msg);
        this.search();
      } catch (err) {
        // console.log(err);
      }
    },
    // 考核方案列表
    async getAllExamScheme() {
      try {
        let res = await this.$http.post(governanceevaluation.getAllExamScheme);
        this.moduleList = res.data.data ? res.data.data : [];
      } catch (err) {
        // console.log(err);
      }
    },
    changePage(val) {
      this.searchData.pageNum = val;
      this.getTableList();
    },
    changePageSize(val) {
      this.searchData.pageSize = val;
      this.getTableList();
    },
    update() {
      this.addOrUpdateVisible = false;
      this.search();
    },
    onClose(){
      this.addOrUpdateVisible = false;
    },
    // 重置
    resetSearchDataMx() {
      this.searchData.taskName = '';
      this.searchData.taskRunState = '';
      this.search();
    },
    changeComponentHandle() {
      this.componentName = 'detectionToOverview';
    },
    selectModule(name) {
      if (this.$route.query.componentName) {
        let componentNames = name.split('-');
        if (componentNames.includes('detectionToOverview')) {
          this.componentName = name.split('-')[1];
        } else {
          this.componentName = name.split('-')[this.componentLevel];
        }
      } else {
        this.componentName = null;
      }
    },
    getParams() {
      // 根据路由地址参数来判断加载组件或者路由本身
      this.selectModule(this.$route.query.componentName);
    },
    record(row) {
      this.recordParams = row;
      this.recordShow = true;
    },
  },
};
</script>

<style lang="less" scoped>
.assessmen-task {
  height: 100%;
  .color_qualified {
    color: var(--color-success);
  }
  .color_unqualified {
    color: var(--color-tips);
  }
  .color_cannot {
    color: var(--color-warning);
  }
  .deleteicon {
    color: var(--color-btn-primary-disabled);
    cursor: no-drop;
  }
  .evaluat_div {
    height: 100%;
    .right-content {
      width: 100%;
      height: 100%;
      background: var(--bg-content);
      @{_deep}.search-wrapper {
        // overflow: hidden;
        padding: 0 20px !important;
        margin: 20px 0px;
        .ui-label {
          line-height: 30px;
        }
      }
      .table-box {
        padding: 0 20px 20px 20px;
        position: relative;
        .sucess {
          color: var(--color-success);
        }
        .error {
          color: var(--color-failed);
        }
        .no-data {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
        }
        @{_deep} .ivu-table-tbody {
          td {
            padding: 10px 0 10px 0;
          }
        }
        @{_deep} .ivu-table-body {
          td {
            padding: 10px 0 10px 0;
          }
        }
      }
    }
  }
}

.operat {
  width: 100%;
  li {
    padding: 6px 18px;
    color: #ffffff;
    cursor: pointer;
    &:hover {
      background: var(--bg-tag);
    }
  }
  .disable-text-color {
    cursor: not-allowed !important;
  }
}
</style>
