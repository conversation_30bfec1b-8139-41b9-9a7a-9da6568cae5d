<!--
    * @FileDescription: 失控人员推荐
    * @Author: H
    * @Date: 2024/08/12
    * @LastEditors: 
    * @LastEditTime: 
-->
<template>
    <div class="out-person">
        <div class="left-search">
            <div class="title">
                <p>失控人员推荐</p>
            </div>
            <div class="form-box">
                <Form :inline="true" :label-width="75">
                    <FormItem label="布控库:">
                        <Select v-model="queryParam.libIds" placeholder="请选择" filterable transfer :max-tag-count="10" style="width: 259px">
							<Option :value="item.taskId" v-for="item in libList" :key="item.taskId">{{ item.taskName }}</Option>
						</Select>
                    </FormItem>
                    <FormItem label="开始时间:">
                        <DatePicker v-model="queryParam.startTime" type="datetime" format="yyyy-MM-dd HH:mm:ss" :clearable="false" placeholder="开始时间" transfer style="width: 259px"></DatePicker>
                    </FormItem>
                    <FormItem label="结束时间:">
                        <DatePicker v-model="queryParam.endTime" type="datetime" format="yyyy-MM-dd HH:mm:ss" :clearable="false" placeholder="结束时间" transfer style="width: 259px"></DatePicker>
                    </FormItem>
                </Form>
                <div class="btn-group">
                    <Button type="primary" style="width: 248px" @click="searchHandle">查询</Button>
                    <Button @click="resetHandle">重置</Button>
                </div>
            </div>
        </div>
        <div class="right-list">
            <div class="title" v-if="dataList.length !== 0">
                <p>查询结果</p>
            </div>
            <div class="box-content">
                <div
                    class="list-card box-1"
                    v-for="(item, index) in dataList"
                    :key="index"
                >
                    <div class="img-content">
                        <div class="similarity" v-if="item.idScore">
                            <span
                                class="num"
                                v-if="item.idScore"
                            >{{ item.idScore }}%</span
                            >
                        </div>
                        <ui-image
                            :src="item.photo"
                            alt="动态库"
                            @click.native="faceDetailFn(item, index)"
                        />
                    </div>
                    <!-- 动态库 -->
                    <div class="bottom-info">
                        <time>
                            <Tooltip content="姓名" placement="right" transfer theme="light">
                                <i class="iconfont icon-xingming"></i>
                            </Tooltip>
                            {{ item.name }}
                        </time>
                        <p>
                            <Tooltip content="身份证" placement="right" transfer theme="light">
                                <i class="iconfont icon-shenfenzheng1"></i>
                            </Tooltip>
                            <ui-textOver-tips
                                refName="idCardNo"
                                :content="item.idCardNo"
                            ></ui-textOver-tips>
                        </p>
                    </div>
                    <div class="fast-operation-bar">
                        <Poptip trigger="hover" placement="right-start">
                            <i class="iconfont icon-gengduo"></i>
                            <div class="mark-poptip" slot="content">
                                <p @click="handleSearchPhoto(item, 1)">
                                    <i class="iconfont icon-renlian1"></i>以图搜图
                                </p>
                            </div>
                        </Poptip>
                    </div>
                </div>
            </div>
            <div
                class="empty-card-1"
                v-for="(item, index) of 9 - (dataList.length % 9)"
                :key="index + 'demo'">
            </div>
            <ui-empty v-if="dataList.length == 0 && !loading"></ui-empty>
            <p class="tips" v-if="dataList.length == 0 && !loading">
                请在左侧操作后查询结果
            </p>
            <ui-loading v-if="loading"></ui-loading>
            <!-- 分页 -->
            <ui-page
                :current="pageInfo.pageNumber"
                :total="total"
                countTotal
                :page-size="pageInfo.pageSize"
                :page-size-opts="[24, 48, 96]"
                @pageChange="pageChange"
                @pageSizeChange="pageSizeChange"
            >
            </ui-page>
        </div>
    </div>
</template>
<script>
import { taskPageList } from '@/api/target-control';
import { getOutOfControlPersonList } from "@/api/recommend";
export default {
    props: {
        taskParams: {
        type: Object,
        default: () => ({})
        }
    },
    data() {
        return {
            queryParam: {
                startTime: '',
                endTime: '',
                libIds: ''
            },
            loading: false,
            dataList: [],
            pageInfo: {
                pageNumber: 1,
                pageSize: 24,
            },
            total: 0,
            libList: [],
        }
    },
    mounted() {
        this.init();
        // 推荐中心查看
        console.log('taskParams: ', this.taskParams)
        if (!Toolkits.isEmptyObject(this.taskParams)) {
            this.$nextTick(() => {
                if (this.taskParams.queryStartTime) this.queryParam.startTime = this.taskParams.queryStartTime
                if (this.taskParams.queryEndTime) this.queryParam.endTime = this.taskParams.queryEndTime
                if (this.taskParams.params.taskId) this.queryParam.libIds = this.taskParams.params.taskId
                if (this.taskParams.taskResult) this.searchHandle()
            })
        }
    },
    methods: {
        init() {
            let params = {
                compareType: 1,
                taskType: "2",
                pageNumber: 1,
                pageSize: 999,
            };
            taskPageList(params)
            .then(res => {
                this.libList = res.data.entities;
            })
            // .fil
        },
        // 查询
        searchHandle() {
            this.pageInfo.pageNumber = 1;
            if(this.queryParam.startTime == '' && this.queryParam.endTime == '') {
                this.$Message.warning('开始时间或结束时间不能为空')
                return;
            }
            if(this.queryParam.startTime > this.queryParam.endTime) {
                this.$Message.warning('开始时间不能大于结束时间！')
                return;
            }
            this.getDataList();
        },
        getDataList() {
            this.loading = true;
            let startDate =  this.$dayjs(this.queryParam.startTime).format('YYYY-MM-DD HH:mm:ss');
            let endDate =  this.$dayjs(this.queryParam.endTime).format('YYYY-MM-DD HH:mm:ss');
            let params = {
                endDate: endDate,
                startDate: startDate,
                taskId: this.queryParam.libIds,
                ...this.pageInfo,
            }
            getOutOfControlPersonList(params)
            .then(res => {
                this.dataList = res.data.entities;
                this.total = res.data.total;
            })
            .finally(() => {
                this.loading = false;
            })  
        },
        // 页数改变
        pageChange(size) {
            this.pageInfo.pageNumber = size;
            this.getDataList();
        },
        // 页数量改变
        pageSizeChange(size) {
            this.pageInfo.pageNumber = 1;
            this.pageInfo.pageSize = size;
            this.getDataList();
        },
        handleSearchPhoto(item) {
            const { href } = this.$router.resolve({
                path: "/wisdom-cloud-search/search-center?sectionName=faceContent&noMenu=1",
                query: {
                    imgUrl: item.photo,
                    startTime: this.queryParam.startTime,
                    endTime: this.queryParam.endTime,
                },
            });
            this.$util.openNewPage(href, "_blank");
        },
        resetHandle() {
            this.dataList = [];
            this.total = 0;
            this.queryParam = {
                startTime: '',
                endTime: '',
                libIds: ''
            };
        },

    }
}
</script>
<style lang='less' scoped>
.out-person{
    padding: 0;
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    .left-search {
        width: 370px;
        height: inherit;
        background: #ffffff;
        box-shadow: 0px 3px 5px 0px rgba(0, 0, 0, 0.298);
        border-radius: 4px 4px 4px 4px;
        margin-right: 10px;
        .form-box {
            padding: 10px 5px;
            .btn-group {
                padding: 0 10px;
            }
        }
    }
    .right-list {
        background: #ffffff;
        box-shadow: 0px 3px 5px 0px rgba(0, 0, 0, 0.298);
        border-radius: 4px 4px 4px 4px;
        flex: 1;
        position: relative;
        display: flex;
        flex-direction: column;
        .box-content {
            padding: 20px;
            flex: 1;
            overflow-y: auto;
            display: flex;
            flex-wrap: wrap;
            justify-content: start;
            align-content: flex-start;
            .list-card{
                position: relative;
                background-color: #f9f9f9;
                margin-bottom: 10px;
                height: min-content;
                box-sizing: border-box;
            }
            .box-1 {
                width: 12%;
                padding: 10px;
                border-radius: 4px;
                border: 1px solid #d3d7de;
                box-shadow: 1px 1px 7px #cdcdcd;
                margin-left: 6px;
                &:hover {
                    border: 1px solid #2c86f8;
                    .operate-bar {
                        right: -6px;
                        bottom: -1px;
                    }
                    &:before {
                        border-color: #2c86f8;
                    }
                }
                .img-content {
                    width: 100%;
                    position: relative;
                    border: 1px solid #cfd6e6;
                    height: 167px;
                    img {
                        width: 100%;
                        height: 100%;
                        display: block;
                    }
                    .num {
                        position: absolute;
                    }
                    .num {
                        position: absolute;
                        top: 0;
                        left: 0;
                        z-index: 8;
                        font-size: 12px;
                        padding: 2px 5px;
                        border-radius: 4px;
                        color: #fff;
                        background: linear-gradient(180deg, #5ba3ff 0%, #2c86f8 100%);
                    }
                }
                .bottom-info {
                    padding-top: 5px;
                    time,
                    p {
                        display: flex;
                        align-items: center;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        color: rgba(0, 0, 0, 0.8);
                        white-space: nowrap;
                        width: 100%;
                        .iconfont {
                            margin-right: 2px;
                            color: #888;
                        }
                    }
                    .device-name{
                        cursor: pointer;
                    }
                }
                .fast-operation-bar{
                    position: absolute;
                    right: 10px;
                    bottom: 35px;
                    color: #2C86F8;
                    width: 20px;
                    height: 20px;
                    text-align: center;
                    line-height: 20px;
                    .icon-gengduo{
                        transform: rotate(90deg);
                        transition: 0.1s;
                        display: inline-block;
                    }
                    p:hover{
                        color: #2C86F8;
                    }
                    &:hover{
                        background: #2C86F8;
                        color: #fff;
                        .icon-gengduo{
                            transform: rotate(0deg);
                            transition: 0.1s;
                        }
                        border-radius: 10px;
                    } 
                    /deep/ .ivu-poptip-popper{
                        min-width: 150px !important;
                        width: 40px !important;
                        height: auto;
                    }
                    /deep/.ivu-poptip-body{
                        height: auto !important;
                    }
                    .mark-poptip{
                        color: #000;
                        cursor: pointer;
                        text-align: left;
                        /deep/ .ivu-icon-ios-add-circle-outline{
                            font-weight: 600;
                        }
                    }
                }
            }
        }
        .pages {
            padding: 0 20px;
        }
        .data-export {
            margin: 10px 0 0 20px;
        }
    }
    /deep/.ivu-form-item {
        margin-bottom: 10px;
    }
    .title {
        font-size: 16px;
        font-weight: bold;
        color: rgba(0, 0, 0, 0.9);
        height: 40px;
        position: relative;
        line-height: 40px;
        padding-left: 20px;
        border-bottom: 1px solid #d3d7de;
        display: flex;
        justify-content: space-between;
        align-items: center;
        top: 0;
        z-index: 1;
        background: #fff;
        border-top-left-radius: 4px;
        border-top-right-radius: 4px;
        &:before {
            content: "";
            position: absolute;
            width: 3px;
            height: 20px;
            top: 50%;
            transform: translateY(-50%);
            left: 10px;
            background: #2c86f8;
        }
        span {
            color: #2c86f8;
        }
        /deep/.ivu-icon-ios-close {
            font-size: 30px;
            cursor: pointer;
        }
    }
}
.empty-card-1 {
    width: 10.7%;
}
.tips {
    position: absolute;
    top: 60%;
    left: 50%;
    transform: translate(-50%, 50%);
    font-weight: 400;
    color: rgba(0, 0, 0, 0.45);
}
</style>