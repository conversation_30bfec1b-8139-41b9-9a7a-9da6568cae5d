<template>
  <div class="trajectory height-full">
    <div class="map height-full" :id="mapId"></div>
    <div v-if="playBoxStatu && playBox" class="play-box flex-row">
      <div class="play-operate">
        <Icon
          :type="operateStatus ? 'ios-pause' : 'ios-play'"
          color="#fff"
          size="18"
          title="暂停/播放"
          class="play-pause pointer mr-xs"
          @click.native="controlDraw"
        />
        <Icon
          type="ios-skip-forward"
          color="#fff"
          size="18"
          title="快进"
          class="play-pause pointer"
          @click.native="fastDraw"
        />
      </div>
      <div class="play-progress">
        <Progress :percent="percent" status="active" />
      </div>
      <ul class="speed-list" v-show="showSpeedList">
        <li
          class="speed-item pointer"
          v-for="(item, index) of timesSpeed"
          :key="index"
          :class="{ selected: item.selected }"
          @click="setSpeedFunc(item)"
        >
          {{ item.label }}
        </li>
      </ul>
      <p class="times-btn pointer" @click="showSpeedList = !showSpeedList" :class="{ active: showSpeedList }">倍速</p>
    </div>
    <look-scene v-model="visibleScence" :img-list="imgList" :view-index="viewIndex"></look-scene>
    <!-- <i-switch
      v-if="playBox"
      size="small"
      v-model="playBoxStatu"
      @on-change="switchModel"
      class="playBox-switch"
    />-->
  </div>
</template>
<style lang="less" scoped>
.trajectory {
  position: relative;
  .map {
    width: 100%;
    height: 100%;
    /*有关地图上悬浮信息弹框的*/
    @{_deep} .olFramedCloudPopupContent {
      border-radius: 5px;
      background-color: #0f1821;
      border: 1px solid#49badb;
      width: 270px;
      height: 100px;
      padding: 10px;
      color: #49badb;
      .item-info {
        img {
          float: left;
          width: 80px;
          height: 80px;
        }
        ul {
          margin-left: 90px;
          line-height: 26px;
          li {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
          & > i {
            margin: 0 5px;
            &:first-of-type {
              margin-left: 10px;
            }
          }
          .icon-enlarage {
            margin: 0 20px 0 3px;
          }
        }
      }
    }
  }
  .play-box {
    position: absolute;
    z-index: 500;
    left: 50%;
    transform: translateX(-50%);
    bottom: 30px;
    width: 913px;
    height: 40px;
    border-radius: 5px;
    background-color: rgba(0, 0, 0, 0.4);
    padding: 0 10px;
    .play-pause {
      &:hover {
        color: #ffba00 !important;
      }
    }
    .play-progress {
      width: 780px;
      @{_deep} .ivu-progress-text {
        color: #fff;
      }
    }
    .speed-list {
      position: absolute;
      right: 0;
      bottom: 40px;
      padding: 10px 0;
      background-color: rgba(0, 0, 0, 0.4);
      color: #fff;
      border-radius: 5px;
      .speed-item {
        padding: 10px 20px;
        text-align: center;
        &.selected {
          color: #ffba00;
        }
      }
    }
    .times-btn {
      border-radius: 8px;
      background-color: #7a7a7a;
      padding: 2px 10px;
      color: #fff;
      &.active {
        background-color: #ffba00;
      }
    }
  }
  .playBox-switch {
    position: absolute;
    z-index: 500;
    right: 20px;
    top: 20px;
  }
}
</style>
<script>
import { NPGisMapMain } from '@/map/map.main';
import { mapGetters, mapActions } from 'vuex';
let mapMain = null;
export default {
  data() {
    return {
      visible: false,
      trailStyle: {}, //轨迹的样式
      operateStatus: false,
      showSpeedList: false,
      playBoxStatu: true,
      traceLayer: null,
      animationLine: null,
      ispause: false,
      percent: 0, //进度条的百分比
      timerProgress: null,
      currentTimes: 2, //当前播放倍速
      fullScreen: false,
      newDataList: [],
      visibleScence: false,
      imgList: [],
      viewIndex: 0,
    };
  },
  created() {},
  async mounted() {
    await this._getMapConfig();
  },
  methods: {
    ...mapActions({
      setMapConfig: 'common/setMapConfig',
      setMapStyle: 'common/setMapStyle',
    }),
    full() {
      this.fullScreen = !this.fullScreen;
      this.mapScroll = this.fullScreen ? 100 : 220;
    },
    async _getMapConfig() {
      try {
        await Promise.all([this.setMapConfig(), this.setMapStyle()]);
        // console.log(this.mapConfig.mapOpts, this.mapStyle);
        if (this.mapConfig) {
          this._initMap(this.mapConfig, this.mapStyle);
        } else {
          setTimeout(() => {
            this._initMap(this.mapConfig, this.mapStyle);
          }, 500);
          // this.$nextTick(()=>{
          //   this._initMap(this.mapConfig, this.mapStyle);
          // })
        }
      } catch (err) {
        console.log(err);
      }
    },
    _initMap(data, style) {
      mapMain = new NPGisMapMain();
      mapMain.init(this.mapId, data, style);
      this.$emit('mapObj', mapMain);
      mapMain.map.addEventListener('click', () => {
        //给地图注册点击让弹框消失
        if (this.infoWindow) {
          this.infoWindow.close();
          this.infoWindow = null;
        }
      });
      if (this.playBox) {
        //证明是视频身份详情的一条轨迹
        this.trailStyle = this.lineStyle[0];
        this.machData(this.dataList);
        this.controlDraw();
      }
    },
    switchModel(status) {
      this.playBoxStatu = status;
    },
    _drawTrail(points) {
      if (!points || points.length === 0) {
        return false;
      }
      //每次只要如果重新画轨迹都要把之前轨迹清掉
      if (this.playBox || !!this.flagOne) {
        console.log('有清除');
        this.clearTraceAnalyze();
      }
      points = points.reverse();
      let markers = [];
      let temp = {};
      points.forEach((item, index) => {
        let k = item.lon + '_' + item.lat;
        if (temp[k] == null) {
          temp[k] = 0;
        } else {
          temp[k]++;
        }
        let imgUrl = '',
          size = null;
        if (index === 0) {
          imgUrl = require('@/assets/img/map/start-point.png');
          size = new NPMapLib.Geometry.Size(20, 28);
        } else if (index === points.length - 1) {
          imgUrl = require('@/assets/img/map/end-point.png');
          size = new NPMapLib.Geometry.Size(20, 28);
        } else {
          imgUrl = require('@/assets/img/map/circle-point.png');
          size = new NPMapLib.Geometry.Size(14, 14);
        }
        let icon = new NPMapLib.Symbols.Icon(imgUrl, size);
        icon.setAnchor(new NPMapLib.Geometry.Size(-size.width / 2, -size.height));
        let marker = new NPMapLib.Symbols.Marker(item);
        marker.setIcon(icon);
        marker.k = k;
        markers.push(marker);
      });
      this.traceLayer = new NPMapLib.Layers.OverlayLayer('trail');
      mapMain.map.addLayer(this.traceLayer);
      this.animationLine = new NPMapLib.Symbols.AnimationLine(mapMain.map.id, points, this.trailStyle);
      this.animationLine.setLayer(this.traceLayer);
      // 轨迹事件监听
      this.animationLine.events.register('preDraw', (evt) => {
        let _marker = markers[evt.index];
        if (evt.index >= 1) {
          let p = _marker.getPosition();
          let k = p.lon + '_' + p.lat;
          for (let i = 0; i < evt.index; i++) {
            if (markers[i].k === k) {
              markers[i].show();
            }
          }
        }
        if (evt.index === points.length - 1) {
          this.percent = 100;
          this.rePosition(points);
          // if (this.calcDistance <= 2000) {
          //   mapMain.map.setZoom(18);
          //   this.rePosition(points);
          // }
        }
        this.traceLayer && this.traceLayer.addOverlay(_marker);
        //给_marker绑定鼠标悬浮事件  NPMapLib.MARKER_EVENT_MOUSE_OVER
        _marker.addEventListener('mouseover', (point) => {
          console.log('触发了弹框', point._position);
          this.addInfoWindow(point._position, evt.index);
        });
      });
      this.animationLine.setSpeed(100 * this.currentTimes);
      this.animationLine.start();
    },
    //清除轨迹
    clearTraceAnalyze() {
      if (this.traceLayer) {
        this.traceLayer.removeAllOverlays();
        mapMain.map.removeOverlay(this.traceLayer);
        this.traceLayer = null;
      }
      /*清除当前轨迹的信息窗 */
      if (this.infoWindow) {
        this.infoWindow.close();
        this.infoWindow = null;
      }
    },
    controlDraw() {
      this.operateStatus = !this.operateStatus;
      if (this.operateStatus) {
        if (!this.ispause) {
          console.log('第一次播放 || 播完后的重新开始播放');
          //开始播放并且不是中途暂停的开始播放（即第一次播放）
          this.percent = 0;
          this._drawTrail(this.newDataList);
          this.trailProgress();
        } else {
          //中途续播
          console.log('中途续播');
          this.animationLine.start();
          this.trailProgress(100, this.percent);
        }
      } else {
        //中途暂停
        console.log('中途暂停');
        this.ispause = true;
        this.animationLine.stop();
        clearInterval(this.timerProgress);
      }
    },
    //播放控件速度xuanze
    setSpeedFunc(item) {
      this.timesSpeed.forEach((row) => {
        row.selected = false;
        if (item.value === row.value) {
          row.selected = true;
          this.currentTimes = row.value;
          this.operateStatus = true;
          if (this.percent > 0 && this.percent < 100) {
            //中途换速
            console.log('中途换速*' + this.currentTimes + '倍');
            this.animationLine.stop();
            this.animationLine.setSpeed(100 * this.currentTimes);
            this.animationLine.start();
            console.log(this.percent, 'percent');
            this.trailProgress(100 * this.currentTimes, this.percent);
          } else {
            //一开始就换速或该次播完后换速重新播
            console.log('一开始就换速或该次播完后换速重新播');
            this.percent = 0;
            this._drawTrail(this.newDataList);
            this.trailProgress(100 * this.currentTimes);
          }
        }
      });
    },
    fastDraw() {
      if (this.percent >= 0 && this.percent < 100) {
        console.log('中途加速');
        this.animationLine.pause();
        this.animationLine.setSpeed(5000);
        this.animationLine.start();
        this.trailProgress(5000, this.percent);
      }
    },
    //监测轨迹运动的进度条
    trailProgress(step = 100, breakpoint = 0) {
      let minInterval = this.calcDistance / step,
        currentWidth = 0; //动画轨迹实时走的距离
      if (this.timerProgress) {
        clearInterval(this.timerProgress);
        this.timerProgress = null;
        // this.percent = 0;
      }
      this.timerProgress = setInterval(() => {
        if (minInterval <= 2 || this.percent >= 100) {
          //播放轨迹完成（要让播放开关自动关闭，并且是否是手动暂停开关置为否）
          console.log('该次轨迹播放已完成', this.timerProgress);
          this.percent = 100;
          clearInterval(this.timerProgress);
          this.timerProgress = null;
          this.operateStatus = false;
          this.ispause = false;
          currentWidth = this.calcDistance;
          this.animationLine.setSpeed(10000);
        } else {
          this.percent = breakpoint;
          currentWidth += step;
          this.percent += Math.round((currentWidth / this.calcDistance) * 100);
        }
      }, 10);
    },
    addInfoWindow(pointItem, index) {
      if (this.infoWindow) {
        this.infoWindow.close();
        this.infoWindow = null;
      }
      let targetPoint = new NPMapLib.Geometry.Point(pointItem.lon, pointItem.lat);
      let div = document.createElement('div');
      let opts = {
        width: 50, // 信息窗宽度
        height: 100, // 信息窗高度
        offset: new NPMapLib.Geometry.Size(0, 0), // 信息窗位置偏移值
        iscommon: false, // 是否为普通的信息窗，普通的不带箭头,默认false
        enableCloseOnClick: true, // 默认false，是否开启改变地图（移动或缩放）关闭信息窗口（默认关闭）
        autoSize: true, // 默认true, 窗口大小是否自适应
        // paddingForPopups: NPMapLib.Geometry.Extent, // 信息窗自动弹回后，距离四边的值。isAdaptation为true时，该设置有效
        isAdaptation: false, // false, 信息窗打开时，地图是否平滑移动，默认不平滑移动
        // useDomStyle: true,
        positionBlock: {
          // 箭头样式
          // imageSrc: require("@/assets/img/datasituation/triangle.png"),
          imageSize: new NPMapLib.Geometry.Size(20, 10), //NPMapLib.Geometry.Size
          offset: new NPMapLib.Geometry.Size(-130, -15),
        },
      };
      div.className = 'item-info';
      div.innerHTML =
        `<img src='${pointItem.facePath}'>` +
        '<ul><li title="' +
        pointItem.logTime +
        '"><i class="icon-info icon-time"></i>' +
        pointItem.logTime +
        '</li>' +
        '<li title="' +
        pointItem.address +
        '"><i class="icon-info icon-position"></i>' +
        pointItem.address +
        '</li>' +
        '<li><i class="pointer icon-see expansion" title="查看大图"></i><i class="pointer icon-video expansion" title="查看视频"></i></li>' +
        '</ul>';
      let obj = div.getElementsByClassName('expansion');
      obj[0].onclick = (e) => {
        e.stopPropagation();
        this.visibleScence = true;
        if (this.infoWindow) {
          this.infoWindow.close();
          this.infoWindow = null;
        }
        if (!index) {
          this.imgList.forEach((item, i) => {
            if (item.scenePath === pointItem.scenePath) {
              this.viewIndex = i;
            }
          });
        } else {
          this.viewIndex = index;
        }
      };
      // obj[1].onclick = () => {
      //   this.$Message.warning("此功能后续开发");
      // };
      this.infoWindow = new NPMapLib.Symbols.InfoWindow(targetPoint, null, null, opts);
      this.infoWindow.setContentDom(div);
      mapMain.map.addOverlay(this.infoWindow);
      this.infoWindow.open(null, false);
    },
    //轨迹回到可视区域
    rePosition(arrList) {
      //目的是将这段轨迹全部呈现在用户视野内
      let minLat, minLon, maxLon, maxLat, sortLat, sortLon;
      if (!arrList || arrList.length === 0) {
        return false;
      }
      sortLat = arrList.sort(function (a, b) {
        return a.lat - b.lat;
      });
      minLat = sortLat[0].lat;
      maxLat = sortLat[sortLat.length - 1].lat;
      sortLon = arrList.sort(function (a, b) {
        return a.lon - b.lon;
      });
      minLon = sortLon[0].lon;
      maxLon = sortLon[sortLon.length - 1].lon;
      let extent = new NPMapLib.Geometry.Extent(minLon, minLat, maxLon, maxLat);
      mapMain.zoomToExtend(extent);
    },
    machData(data) {
      // console.log(data)
      //加工处理datalist,将字符串经纬度转化为数字经纬度并删除无效点
      let i = 0,
        newList = [];
      data.forEach((item) => {
        if (!item.latitude || !item.longitude) {
          i++;
        }
      });
      if (i === data.length) {
        // this.$Message.warning("无效经纬度！！");
        newList = [];
        return;
      } else {
        data.forEach((row) => {
          if (!!row.latitude && !!row.longitude) {
            row.lat = +row.latitude;
            row.lon = +row.longitude;
            newList.push(row);
          }
        });
      }
      this.newDataList = newList;
      this.imgList = newList.map((row) => row.scenePath);
    },
    //返回上一级
    backDetail() {
      this.$emit('backDetail');
    },
    //播放条显示
    showTailBox(status) {
      this.playBoxStatu = status;
    },
  },
  watch: {
    flagOne() {
      if (this.newDataList.length === 0) {
        this.machData(this.dataList);
      }
      this._drawTrail(this.newDataList);
      if (this.playBox) {
        this.percent = 0;
        this.operateStatus = true;
        this.trailProgress();
      }
    },
    flagTwo() {
      this.trailStyle = this.lineStyle[0];
      this._drawTrail(this.machData(this.dataList[0]));
      let time = setTimeout(() => {
        this.trailStyle = this.lineStyle[1];
        this._drawTrail(this.machData(this.dataList[1]));
        time = null;
      }, 2000);
    },
    imgPosition: {
      handler(val) {
        if (Object.keys(val).length !== 0) {
          let pointItem = {
            lon: val.longitude,
            lat: val.latitude,
            logTime: val.logTime,
            address: val.address,
            facePath: val.facePath,
          };
          this.addInfoWindow(pointItem);
        }
      },
      deep: true,
    },
    // mapConfig:{
    //   handler(val) {
    //     if (val.mapOpts) {
    //       this.$nextTick(()=>{
    //         this._initMap(this.mapConfig, this.mapStyle);
    //       })
    //     }
    //   },
    //   deep: true,
    // }
  },
  computed: {
    //计算轨迹分析点集之间的距离
    calcDistance() {
      let totalDistance = 0,
        points = this.newDataList;
      if (!points || points.length === 0) {
        return false;
      }
      for (var i = 0, len = points.length; i < len - 1; i++) {
        if (!mapMain) return [];
        totalDistance += mapMain.map.getDistance(
          { lat: points[i].lat, lon: points[i].lon },
          { lat: points[i + 1].lat, lon: points[i + 1].lon },
        );
      }
      return totalDistance;
    },
    ...mapGetters({
      mapConfig: 'common/getMapConfig',
      mapStyle: 'common/getMapStyle',
    }),
  },
  props: {
    /*接收地图Id*/
    mapId: {
      default: () => {
        return 'mapId' + Math.random();
      },
      type: String,
    },
    /*
    战法一条轨迹时切换标志字段
    */

    flagOne: {
      default: '',
    },
    /*
    战法两条轨迹时切换标志字段
    */

    flagTwo: {
      default: '',
    },
    /*轨迹数据
    (对象数组点集，单条轨迹用一维数组，多条轨迹用多维数组)
    */
    dataList: {
      require: true,
      type: Array,
    },
    /*是否显示轨迹播放控件盒子
    是否是视频身份管理轨迹
    */
    playBox: {
      default: false,
    },
    //可自定义倍速播放控制菜单
    timesSpeed: {
      default: () => {
        return [
          { label: '0.5X', value: 0.5, selected: false },
          { label: '1.0X', value: 1, selected: false },
          { label: '1.25X', value: 1.25, selected: false },
          { label: '1.5X', value: 1.5, selected: false },
          { label: '2.0X', value: 2, selected: true },
        ];
      },
    },
    //可传入轨迹线的样式 {}
    lineStyle: {
      type: Array,
      default: () => {
        return [
          {
            color: '#2ea8d9',
            opacity: 1,
            weight: 4,
          },
          {
            color: '#e1453c',
            opacity: 1,
            weight: 4,
          },
        ];
      },
    },
    // 点击轨迹时间轴上的某个数据，展示到地图上的
    imgPosition: {},
  },
  components: {},
  beforeDestroy() {
    if (this.timerProgress) {
      this.animationLine.pause();
      clearInterval(this.timerProgress);
      this.timerProgress = null;
    }
    if (mapMain) {
      mapMain.destroy();
      mapMain = null;
    }
  },
};
</script>
