<template>
  <div class="keypersonlibrary auto-fill">
    <div class="keypersonlibrary-header">
      <ui-label class="inline" label="关键词">
        <Input v-model="searchData.keyWord" class="width-md" placeholder="请输入关键词"></Input>
      </ui-label>
      <ui-label class="inline ml-lg" label="抓拍数量">
        <Input v-model="searchData.snapDownLimit" class="width-sm" placeholder="请填写数量"></Input>
        <span class="horizontalbar">—</span>
        <Input v-model="searchData.snapUpperLimit" class="width-sm" placeholder="请填写数量"></Input>
      </ui-label>
      <!-- <ui-label class="inline ml-lg" label="抓拍异常数量" :width="98">
        <Input
          v-model="searchData.abnormalUpperLimit"
          class="width-sm"
          placeholder="请填写数量"
        ></Input>
        <span class="horizontalbar">—</span>
        <Input
          v-model="searchData.abnormalDownLimit"
          class="width-sm"
          placeholder="请填写数量"
        ></Input>
      </ui-label> -->
      <div class="inline ml-lg">
        <Button type="primary" @click="search">查询</Button>
        <Button class="ml-sm" @click="clear">重置</Button>
      </div>
    </div>
    <div class="keypersonlibrary-content auto-fill" v-ui-loading="{ loading: loading, tableData: cardList }">
      <div class="keypersonlibrary-content-wrap">
        <ui-gather-card
          class="card"
          v-for="(item, index) in cardList"
          :key="index"
          :list="item"
          :card-info="cardInfo"
          @detail="detail"
        ></ui-gather-card>
      </div>
    </div>
    <ui-page class="page" :page-data="pageData" @changePage="changePage" @changePageSize="changePageSize"> </ui-page>
    <capture-details ref="CaptureDetails" title="检测结果"></capture-details>
  </div>
</template>
<script>
import equipmentassets from '@/config/api/equipmentassets';
import persontype from './persontype.js';
export default {
  name: 'keypersonlibrary',
  props: {},
  data() {
    return {
      loading: false,
      searchData: {
        snapUpperLimit: null,
        snapDownLimit: null,
        abnormalUpperLimit: null,
        abnormalDownLimit: null,
        keyWord: '',
        pageNumber: 1,
        pageSize: 20,
        orgCode: '',
      },
      pageData: {
        pageNum: 1,
        pageSize: 20,
        totalCount: 0,
      },
      cardList: [],
      cardInfo: [
        { name: '姓名：', value: 'name' },
        { name: '证件号：', value: 'idCard' },
        { name: '轨迹总数：', value: 'snapCount' },
        { name: '异常轨迹：', value: 'abnormalCount', color: 'var(--color-warning)' },
      ],
    };
  },
  created() {
    this.copySearchDataMx(this.searchData);
    this.search();
  },
  methods: {
    search() {
      this.searchData.pageNumber = 1;
      this.pageData.pageNum = 1;
      this.getList();
    },
    async getList() {
      try {
        this.cardList = [];
        this.loading = true;
        let params = this.searchData;
        let res = await this.$http.post(equipmentassets.queryPersonLibPageGather, params);
        const datas = res.data.data;
        this.cardList = datas.entities.map((item) => {
          const personTypes = item.personTypeText.split(',');
          item.personTypes = persontype.filter((item) => {
            return personTypes.includes(item.tagName);
          });
          return item;
        });
        this.pageData.totalCount = datas.total;
        this.loading = false;
      } catch (error) {
        console.log(error);
      }
    },
    detail(id) {
      this.$refs.CaptureDetails.init(id);
    },
    changePage(val) {
      this.searchData.pageNumber = val;
      this.pageData.pageNum = val;
      this.getList();
    },
    changePageSize(val) {
      this.searchData.pageSize = val;
      this.search();
    },
    clear() {
      this.resetSearchDataMx(this.searchData, this.search);
    },
  },
  watch: {},
  components: {
    UiGatherCard: require('@/components/ui-gather-card.vue').default,
    CaptureDetails: require('./components/capture-details.vue').default,
  },
};
</script>
<style lang="less" scoped>
.keypersonlibrary {
  padding: 20px 15px 0;
  background-color: var(--bg-content);
  &-header {
    margin: 0 5px;
  }
  &-content {
    position: relative;
    margin: 20px 0 0;
    overflow-y: auto;
    &-wrap {
      width: 100%;
      display: flex;
      display: -webkit-flex;
      flex-wrap: wrap;
    }
    .card {
      width: calc(calc(100% - 40px) / 4);
      margin: 0 5px 10px;
    }
  }
}
.horizontalbar {
  margin: 0 9px;
  color: #1b82d2;
}
</style>
