<template>
  <div class="add-modal">
    <div class="title-box">
      <div class="goback" @click="closeHandler">
        <i class="iconfont icon-return"></i>
      </div>
      {{ subTaskType === "history" ? "历史解析" : "实时布控" }} >
      <span class="now-state">{{ title }}</span>
    </div>
    <div class="content">
      <div class="video-left mr-10">
        <Form
          ref="analysisTaskForm"
          :show-message="false"
          :model="taskInfo"
          class="form"
          :label-width="80"
          hide-required-mark
        >
          <FormItem
            label="任务名称:"
            prop="taskName"
            class="search-input"
            required
          >
            <Input
              placeholder="请输入"
              clearable
              v-model="taskInfo.taskName"
              maxlength="50"
            />
          </FormItem>
          <FormItem
            label="有效时间:"
            class="search-input"
            v-if="subTaskType === 'real'"
          >
            <div class="datepicker-wrap">
              <!-- <DatePicker type="datetime" placeholder="选择开始时间" style="width: 200px"></DatePicker>
              <DatePicker type="datetime" placeholder="选择结束时间" style="width: 200px"></DatePicker> -->
              <hl-daterange
                class="mb-10"
                v-model="taskInfo.effectiveStartTime"
                key="1"
              ></hl-daterange>
              <hl-daterange
                v-model="taskInfo.effectiveEndTime"
                key="2"
              ></hl-daterange>
            </div>
          </FormItem>
          <FormItem
            label="处理时段:"
            class="search-input"
            v-if="subTaskType === 'real'"
          >
            <div class="timepicker-wrap">
              <el-checkbox class="handle-range" v-model="isTaskSection" />
              <el-time-select
                class="ml-5"
                size="small"
                :disabled="!isTaskSection"
                v-model="taskInfo.workStartTime"
                align="left"
                placeholder="开始时段"
                @change="(vale) => (taskInfo.workStartTime = vale + ':00')"
                :picker-options="{
                  start: '00:00',
                  step: '01:00',
                  end: '23:00',
                  maxTime: taskInfo.workEndTime,
                }"
              />
              <div class="line"></div>
              <el-time-select
                size="small"
                :disabled="!isTaskSection"
                v-model="taskInfo.workEndTime"
                @change="(vale) => (taskInfo.workEndTime = vale + ':00')"
                align="left"
                placeholder="结束时段"
                :picker-options="{
                  start: '00:00',
                  step: '01:00',
                  end: '24:00',
                  minTime: taskInfo.workStartTime,
                }"
              />
            </div>
          </FormItem>
          <FormItem
            label="执行时间:"
            class="search-input"
            prop="planExecTime"
            v-if="subTaskType !== 'real'"
          >
            <hl-daterange
              class="mb-10"
              v-model="taskInfo.planExecTime"
              key="3"
            ></hl-daterange>
          </FormItem>
          <FormItem
            label="报警级别:"
            prop="taskLevel"
            class="search-input"
            required
          >
            <RadioGroup v-model="taskInfo.taskLevel" clearable>
              <Radio :label="1">
                <span>一级</span>
              </Radio>
              <Radio :label="2">
                <span>二级</span>
              </Radio>
              <Radio :label="3">
                <span>三级</span>
              </Radio>
            </RadioGroup>
          </FormItem>
          <FormItem
            label="算法名称:"
            prop="compareAlgorithmIds"
            class="search-input"
            required
          >
            <Select v-model="taskInfo.compareAlgorithmIds" clearable multiple>
              <Option
                :value="item.id"
                :key="item.id"
                v-for="item in algorithmList"
                >{{ item.name }}</Option
              >
            </Select>
          </FormItem>
          <FormItem
            label="处理类型:"
            prop="taskDataType"
            class="search-input"
            required
          >
            <RadioGroup v-model="taskInfo.taskDataType" clearable>
              <Radio :label="0" :disabled="deviceList.length > 0">
                <span>
                  {{ subTaskType === "real" ? "视频流" : "历史视频" }}
                </span>
              </Radio>
              <Radio :label="1" :disabled="deviceList.length > 0">
                <span>
                  {{ subTaskType === "real" ? "图片流" : "历史抓拍" }}
                </span>
              </Radio>
            </RadioGroup>
          </FormItem>
          <div v-if="subTaskType === 'history'" class="sac-config-item-split">
            ------------分割线---------------
          </div>
          <FormItem
            label="历史时段:"
            class="search-input"
            v-if="subTaskType === 'history'"
          >
            <div class="datepicker-wrap">
              <hl-daterange
                class="mb-10"
                v-model="taskInfo.videoStartTime"
                key="3"
              ></hl-daterange>
              <hl-daterange
                v-model="taskInfo.videoEndTime"
                key="4"
              ></hl-daterange>
            </div>
          </FormItem>
          <FormItem label="选择设备:" class="search-input">
            <div class="select-tag-button" @click="selectDevice()">
              选择设备/已选（{{ deviceList.length }}）
            </div>
          </FormItem>
          <FormItem
            label="通知用户:"
            props="userScopeType"
            class="search-input"
          >
            <RadioGroup v-model="taskInfo.userScopeType" clearable>
              <Radio :label="1">
                <span>所有</span>
              </Radio>
              <Radio :label="2"><span></span></Radio>
              <span
                class="select-tag-button"
                :class="{
                  'select-tag-active': taskInfo.userScopeType != '2',
                }"
                @click="selectUserHandler"
              >
                选择用户/已选（{{ userIds.length }}）
              </span>
            </RadioGroup>
          </FormItem>
        </Form>
        <div class="sac-config-item-split">----分割线----</div>
        <!--实时结构化设备列表-->
        <div v-show="deviceList.length > 0" class="sac-config-device-list">
          <div v-scroll style="height: 100%">
            <div
              v-for="(item, index) in deviceList"
              :key="index"
              :style="{ width: 'auto' }"
              class="sac-config-item"
              :class="{ 'sac-active-playing': curCIndex == index }"
            >
              <span class="sac-config-device-info dib">
                <span
                  class="sac-camera-name dib"
                  :title="item.deviceName"
                  @dblclick="playVideo(item, '', index)"
                >
                  <i
                    :class="getDeviceType(item)"
                    class="iconfont nui-iconfont mr-5"
                  ></i
                  ><span>{{ item.deviceName }}</span>
                </span>
                <i
                  class="iconfont icon-shanchu1 sac-icon-delete"
                  @click="deleteDevice(index)"
                ></i>
              </span>
            </div>
          </div>
        </div>
      </div>
      <div class="right-content">
        <!-- 实时和历史结构化 都需要工具栏 -->
        <h5-player
          ref="H5Player"
          :options="{ layout: 1 * 1 }"
          :isShowToolbar="subTaskType === 'history' || subTaskType === 'real'"
        ></h5-player>
      </div>
    </div>
    <div class="viewanalysis-footer">
      <Button class="mr-20" @click="closePanel">取消</Button>
      <Button type="primary" @click="saveTask" :disabled="isSaveMuiltForbit"
        >保存</Button
      >
    </div>

    <!-- 选择设备 -->
    <select-device
      ref="selectDevice"
      showOrganization
      :formItemList="propsList"
      :formDataProp="{
        sbgnlxs: deviceSbgnlx,
      }"
      @selectData="selectData"
    />
    <!-- 选择用户 -->
    <select-user
      @selectData="handleUserData"
      showOrganization
      ref="selectUser"
    ></select-user>
  </div>
</template>

<script>
import UploadImg from "@/components/ui-upload-img-static-library";
import { mapGetters, mapActions } from "vuex";
import {
  addLLMCompareTask,
  modifyLLMCompareTask,
} from "@/api/semantic-placement.js";
import hlDaterange from "@/components/hl-daterange/index.vue";
import { queryCameraDeviceList } from "@/api/player.js";
import { userPageListNew } from "@/api/user.js";
import SelectUser from "@/components/select-modal/select-user.vue";
export default {
  name: "addModal",
  components: {
    hlDaterange,
    UploadImg,
    SelectUser,
  },
  props: {
    title: {
      type: String,
      default: "新增任务",
    },
    value: {
      type: Boolean,
      default: true,
    },
    subTaskId: {
      type: String | Number,
      default: "",
    },
    subTaskType: {
      type: String,
      default: "",
    },
    subDeviceId: {
      type: String,
      default: "",
    },
    algorithmList: {
      type: Array,
      default: () => [],
    },
  },
  computed: {
    ...mapGetters({
      sbgnlxList: "dictionary/getSbgnlxList", //摄像机功能类型
    }),
    // 结构化解析类型: 1实时解析 3历史解析 5文件解析
    taskParsingType() {
      return this.subTaskType == "real"
        ? 1
        : this.subTaskType == "history"
        ? 3
        : 5;
    },
    // 处理类型 选择不同类型设备
    deviceSbgnlx() {
      // 0 视频流 => 视频监控
      // 1 图片流 => 卡口设备 抓拍机
      return this.taskInfo.taskDataType == 0 ? ["1"] : ["2", "3"];
    },
  },
  data() {
    return {
      taskInfo: {
        taskName: "",
        taskDataType: 1, // 处理类型 0 视频流 1 图片流
        workEndTime: "", // 处理时段结束
        workStartTime: "", // 处理时间段开始
        resourceIds: [], // 结构话资源id
        compareAlgorithmIds: "", // 解析类型 结构化算法ID
        planExecTime: "", // 执行时间
        effectiveEndTime: "", // 任务结束时间
        effectiveStartTime: this.$dayjs(new Date()).format(
          "YYYY-MM-DD HH:mm:ss"
        ), // 任务开始时间
        videoEndTime: "", // 录像结束时间
        videoStartTime: "", // 录像开始时间
        userScopeType: 1, // 通知用户
        validTimeRangeL: "",
        taskLevel: 1,
      },
      isTaskSection: false,
      deviceList: [],
      userIds: [],
      histime: [
        this.$dayjs(new Date().getTime() - 2 * 60 * 60 * 1000).format(
          "YYYY-MM-DD HH:mm:ss"
        ),
        this.$dayjs(new Date()).format("YYYY-MM-DD HH:mm:ss"),
      ], //录像时段 历史结构化使用
      curCIndex: -1, //当前设备索引
      isSaveMuiltForbit: false,
      defaultList: [],
      propsList: [
        {
          type: "select",
          key: "sbgnlxs",
          label: "功能类型",
          options: [],
          colSpan: "8",
          multiple: true,
          disabled: true,
        },
        {
          type: "input",
          key: "deviceName",
          label: "设备名称",
        },
        {
          type: "input",
          key: "deviceId",
          label: "设备编码",
        },
      ],
    };
  },
  watch: {
    "taskInfo.userScopeType": {
      handler(val) {
        this.userIds = [];
      },
    },
  },
  mounted() {
    this.propsList[0].options = this.sbgnlxList.map((item) => {
      return { value: item.dataKey, label: item.dataValue };
    });
  },
  methods: {
    ...mapActions({ getInitData: "multimodal/getInitData" }),
    init(data) {
      if (!this.subTaskId) {
        //新建 给个默认任务名
        this.taskInfo.taskName = new Date().format("yyyyMMddhhmmss");
      } else {
        this.initData(data);
      }
    },
    //初始化数据
    async initData(data) {
      Object.keys(this.taskInfo).forEach((key) => {
        this.taskInfo[key] = data[key];
      });
      const validTimeRange = JSON.parse(data?.validTimeRange || "[]");
      if (validTimeRange.length > 0) {
        this.taskInfo.workStartTime = validTimeRange[0];
        this.taskInfo.workEndTime = validTimeRange[1];
        this.isTaskSection = true;
      }
      this.taskInfo.compareAlgorithmIds = data?.compareAlgorithmIds
        .split(",")
        .map((item) => Number(item));
      this.taskInfo.taskDataType = data.taskType % 2 ? 0 : 1;
      this.deviceList = data.taskResourceList.map((item) => {
        return {
          deviceName: item.deviceInfo.name,
          deviceId: item.deviceInfo.gbid,
          ...item.deviceInfo,
        };
      });
      if (data?.userIds?.length > 0) {
        const { data: userData } = await userPageListNew({
          userIds: data?.userIds,
        });
        this.userIds = userData.entities?.map((item) => {
          return {
            ...item,
          };
        });
      } else {
        this.userIds = [];
      }
    },
    //设备列表修改设备时，需要播放选中设备
    singleDevicePlay: function () {
      if (!this.subDeviceId && this.taskInfo.taskList[0]) {
        this.playVideo(this.taskInfo.taskList[0], !this._isRealStr(), 0);
        return;
      }
      let index = this.taskInfo.taskList.findIndex(
        (v) => v.deviceGbId == this.subDeviceId
      );
      this.playVideo(this.taskInfo.taskList[index], !this._isRealStr(), index);
    },
    //当前是否为实时结构化
    _isRealStr: function () {
      return this.subTaskType === "real";
    },
    /**
     * 获取设备对应的icon
     */
    getDeviceType(item) {
      return Toolkits.getDeviceIconType(item);
    },
    /**
     * 选择设备
     */
    selectDevice() {
      this.$refs.selectDevice.show(this.deviceList, "");
    },
    // 选择用户
    selectUserHandler() {
      this.taskInfo.userScopeType == 2 &&
        this.$refs.selectUser.show(this.userIds);
    },
    // 通知用户
    handleUserData(list) {
      this.userIds = list;
    },
    /**
     * 已选择设备数据返回
     */
    selectData(list) {
      this.closeVideo();
      this.deviceList = list;
    },
    pickerClose() {
      if (this.taskInfo.taskList.length < 1) {
        return;
      } //无设备不做响应
      this.getDeviceHis();
    },
    //查询摄像机是否有录像和录像时间区域
    getDeviceHis: function () {
      if (this._isRealStr()) {
        return;
      } //[仅历史结构化下触发]
      this.waitForList(this.taskInfo.taskList, 0, 5);
    },
    async waitForList(arr, index, step) {
      if (!this.histime[0] || !this.histime[1]) {
        this.$Message.error("录像开始时间和结束时间不能为空");
        return;
      }
      arr.forEach((v) => (v.hasHis = false));
      for (let i = index; i < arr.length; i += step) {
        const batch = arr.slice(i, i + step);
        const promises = batch.map(({ deviceGbId }) =>
          Toolkits.searchVodList(
            this.$refs.H5Player.videoObj,
            deviceGbId,
            this.histime
          )
        );
        const results = await Promise.all(promises);
        results.forEach((item, index) => {
          let obj = arr[i + index];
          obj.hasHis = !!item.length;
          this.$set(arr, i + index, obj);
        });
      }
      console.log(arr);
    },
    async playVideo(cameraInfo, isHis, index, callback) {
      //存储当前播放视频的index
      this.curCIndex = index;
      if (isHis) {
        this.playHis(cameraInfo);
        return;
      }
      let { deviceName, deviceGbId, deviceChildType } = { ...cameraInfo };

      // 播放设备工具栏 需要设备的详细信息包括 经纬度/设备类型等
      let deviceInfo = {};
      const data = await queryCameraDeviceList({
        deviceId: cameraInfo.deviceId || cameraInfo.deviceGbId,
      });
      if (data.code === 200) {
        deviceInfo = data.data[0];
      }
      let param = {
        ...deviceInfo,
        deviceName,
        deviceGbId,
        devicetype: liveType,
      };
      this.$refs.H5Player.playStream(param, "live");
      callback && callback();
    },
    async playHis(item) {
      if (!item.hasHis) return;
      let params = {
        deviceId: item.deviceGbId,
        deviceName: item.deviceName,
        geoPoint: {
          lon: item.Lon,
          lat: item.Lat,
        },
        devicetype: vodType,
        playType: "vod",
        begintime: this.histime[0],
        endtime: this.histime[1],
      };
      // 视频入参无设备类型 地图定位会出现错误
      const data = await queryCameraDeviceList({
        deviceId: params.deviceId,
      });
      let deviceInfo = {};
      if (data.code === 200) {
        deviceInfo = data.data[0];
      }
      let param = {
        ...deviceInfo,
        ...params,
        geoPoint: {
          lon: deviceInfo.longitude,
          lat: deviceInfo.latitude,
        },
      };
      this.$refs.H5Player.playStream(param, params.playType);
    },
    closeVideo() {
      if (this.curCIndex != -1 && this.$refs.H5Player) {
        this.curCIndex = -1;
        this.$refs.H5Player.closeAll();
      }
    },
    closePanel() {
      this.closeVideo();
      this.isShow = false;
      this.isSaveMuiltForbit = false; //放开禁用的按钮
    },
    deleteDevice(index) {
      this.deviceList.splice(index, 1);
    },
    saveTask() {
      this.$refs.analysisTaskForm.validate((valid) => {
        if (valid) {
          let param = {};
          if (this.taskParsingType == 1) {
            // 实时结构化参数
            param = {
              effectiveEndTime: this.taskInfo?.effectiveEndTime || "",
              effectiveStartTime: this.taskInfo?.effectiveStartTime || "",
            };
            // 布控时间类型，1:永久，2:自定义
            param.taskTimeType = this.taskInfo.effectiveEndTime ? 2 : 1;
            this.isTaskSection &&
              (param.validTimeRange = JSON.stringify([
                this.taskInfo?.workStartTime,
                this.taskInfo?.workEndTime,
              ]));
          }
          if (this.taskParsingType == 3) {
            // 历史结构化参数
            param = {
              videoEndTime: this.taskInfo?.videoEndTime || "",
              videoStartTime: this.taskInfo?.videoStartTime || "",
              planExecTime: this.taskInfo?.planExecTime || "",
            };
          }
          // 公共字段
          param.taskName = this.taskInfo.taskName;
          param.resourceIds = this.deviceList.map(
            (item) => item?.deviceId || ""
          );
          param.compareAlgorithmIds =
            this.taskInfo.compareAlgorithmIds.join(",");
          param.taskDataType = this.taskInfo.taskDataType;
          param.taskParsingType = this.taskParsingType;
          param.userIds = this.userIds?.map((item) => item.id) || [];
          param.taskLevel = this.taskInfo.taskLevel;
          param.userScopeType = this.taskInfo.userScopeType;
          // 布控范围类型，1:永久，2:自定义
          param.scopeType = 2;
          const errorMsg = this.validateForm({ ...param });
          if (errorMsg) {
            return this.$Message.error(errorMsg);
          }

          if (this.subTaskId) {
            param.id = this.subTaskId;
            // 修改
            modifyLLMCompareTask(param).then((res) => {
              this.$Message.success("编辑任务成功");
              this.$emit("updated");
            });
          } else {
            // 新增
            addLLMCompareTask(param).then((res) => {
              this.$Message.success("新增任务成功");
              this.$emit("updated");
            });
          }
        } else {
          const errorMsg = this.validateForm({ ...this.taskInfo });
          if (errorMsg) {
            return this.$Message.error(errorMsg);
          }
        }
      });
    },
    // 表单校验
    validateForm(param) {
      let errorMsg = "";
      if (typeof param.taskName === "undefined" || param.taskName === "") {
        errorMsg += "任务名称不能为空!";
      }
      if (!new RegExp(/^[\u4E00-\u9FA5a-zA-Z0-9]+$/).test(param.taskName)) {
        //注意: 空格也不支持
        errorMsg += "任务名称不支持特殊字符，只支持数字、字母、汉字!";
      }
      // 有效时段 实时解析
      if (this.taskParsingType == 1) {
        if (param.effectiveEndTime && param.effectiveStartTime) {
          const _sSTime = new Date(param.effectiveStartTime).getTime(),
            _sETime = new Date(param.effectiveEndTime).getTime();
          if (_sSTime >= _sETime) {
            errorMsg += "有效时段开始时间必须小于结束时间!";
          }
        }
      }
      //处理时段校验
      if (this.isTaskSection) {
        const timeArr = JSON.parse(param.validTimeRange);
        if (!timeArr[0] || !timeArr[1]) {
          errorMsg += "处理时段未填写完整!";
        }
      }
      // 解析类型
      if (!param.compareAlgorithmIds) {
        errorMsg += "请选择算法!";
      }
      // 历史时段 历史解析
      if (this.taskParsingType == 3) {
        if (!param.videoEndTime || !param.videoStartTime) {
          errorMsg += "历史时段未填写完整!";
        }
        const _sSTime = new Date(param.videoStartTime).getTime(),
          _sETime = new Date(param.videoEndTime).getTime();
        if (_sSTime >= _sETime) {
          errorMsg += "历史时段开始时间必须小于结束时间!";
        }
      }
      // 设备选择
      if (param.resourceIds?.length == 0) {
        errorMsg += "请选择设备!";
      }
      return errorMsg;
    },
    closeHandler() {
      this.$emit("close");
    },
  },
};
</script>

<style lang="less" scoped>
.add-modal {
  width: 100%;
  height: 100%;
  background: white;
  position: absolute;
  top: 0;
  left: 0;
  border-radius: 5px;
  z-index: 999;
  overflow: hidden;
  display: flex;
  flex-direction: column;

  .title-box {
    height: 40px;
    border-bottom: 1px solid #d3d7de;
    font-size: 14px;
    color: rgba(0, 0, 0, 0.35);

    .now-state {
      color: #3d3d3d;
    }

    .goback {
      cursor: pointer;
      width: 70px;
      height: 100%;
      background-color: #2c86f8;
      color: white;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      font-size: 14px;
      margin-right: 10px;
    }
  }

  .content {
    flex: 1;
    padding: 10px;
    display: flex;
    overflow: hidden;

    .color-blue {
      color: #2c86f8;
    }

    .video-left {
      width: 416px;
      height: 100%;
      padding: 10px 10px 0 10px;
      display: flex;
      flex-direction: column;

      /deep/ .ivu-form-item {
        margin-bottom: 15px;
      }

      .timepicker-wrap {
        display: flex;
        align-items: center;

        .line {
          height: 3px;
          width: 20px;
          background: #d2d8db;
          margin: 0 5px;
        }
      }

      .sac-config-item-split {
        width: 100%;
        padding: 0;
        margin: 10px 0 20px 0;
        border-top: 1px dotted #ccc;
        height: 1px;
        font-size: 0;
      }

      .sac-config-device-type {
        display: flex;
        align-items: center;
        justify-content: space-between;
      }

      .fr {
        float: right;
      }

      .nonmotorstyle {
        width: 48px !important;
      }

      /*设备展示列表*/
      .sac-config-device-type-name,
      .sac-config-device-type-name-check {
        height: 100%;
        width: max-content;

        i,
        .sac-config-check-all,
        .sac-config-check {
          padding-left: 10px;
          width: 38px;
          position: relative;
          vertical-align: middle;
          margin-left: 0;
          text-align: center;
          color: #4a4a4a;
          font-style: normal;
          // margin-left: 20px ;
          line-height: 28px;
          float: left;
          cursor: pointer;

          &.checked {
            &.cl {
              color: #15997d;
            }

            &.fjdc {
              color: #49bf00;
            }

            &.rl {
              color: #d36c29;
            }

            &.rt {
              color: #08c6d4;
            }
          }

          &.disabled {
            color: #999;
          }
        }

        i {
          text-align: right;
          white-space: nowrap;
        }
      }

      .sac-config-device-type-name-check.sac-checks {
        white-space: nowrap;
        overflow: visible;
        flex-shrink: 0;

        &.his-to-right {
          left: 200px;
        }
      }

      .sac-config-device-list {
        flex: 1;
        overflow-y: auto;
        overflow-x: hidden;
        width: 100%;
        box-sizing: border-box;

        .sac-active-playing {
          background: rgba(44, 134, 248, 0.1028);
        }

        .sac-config-item {
          padding: 0 10px;
          height: 30px;
          line-height: 30px;
          cursor: pointer;
          font-size: 12px;
          color: rgba(0, 0, 0, 0.8);

          &:hover {
            background: rgba(44, 134, 248, 0.1028);
          }
        }
      }

      .sac-config-device-info {
        position: relative;
        display: inline-flex;
        width: 100%;

        .sac-icon-delete {
          display: block;
          position: relative;
          width: 30px;
          text-align: right;
          color: #4a4a4a;
          font-style: normal;
          top: 2px;
          cursor: pointer;
        }

        .sac-camera-name {
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          flex: 1;

          i {
            color: #2c86f8;
          }
        }
      }

      .uploadImg {
        display: flex;

        .upload-img {
          justify-content: flex-start;

          /deep/ .upload-item {
            height: 80px !important;
            width: 80px !important;
            margin: 0 !important;
          }
        }
      }
    }

    .right-content {
      flex: 1;
      background-color: #fff;
      display: flex;
      flex-direction: column;
      position: relative;
    }
  }

  .viewanalysis-footer {
    height: 50px;
    text-align: center;
    padding-top: 5px;
    border-top: 1px solid #d3d7de;
  }

  .handle-range {
    /deep/ .el-checkbox__inner {
      border: 1px solid #515a6e !important;
    }
  }
}

.timepicker-wrap {
  /deep/ .el-date-editor.el-input {
    height: 100% !important;

    .el-input__inner {
      padding-left: 35px;
    }

    .el-input__prefix {
      top: 1px;
    }
  }
}
.mb-10 {
  margin-bottom: 10px;
}
.select-tag-active {
  cursor: not-allowed;
}
.select-tag-button {
  display: inline-block;
  text-align: center;
  line-height: 34px;
}
</style>
