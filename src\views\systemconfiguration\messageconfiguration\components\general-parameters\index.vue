<template>
  <div class="general-parameters auto-fill">
    <div class="mb-sm">
      <ui-label class="mb-sm" label="短信允许发送时间">
        <div v-for="(item, index) in formData['0000'].sendTime" :key="index">
          <TimePicker
            type="time"
            placeholder="请选择开始时间"
            class="width-md"
            format="HH:mm"
            v-model="item.startTime"
            :disabled="!isEdit"
          ></TimePicker>
          <span> - </span>
          <TimePicker
            type="time"
            placeholder="请选择结束时间"
            class="width-md"
            format="HH:mm"
            v-model="item.endTime"
            :disabled="!isEdit"
          ></TimePicker>
        </div>
      </ui-label>
      <!-- <ui-label label="通知接收人：">
        <RadioGroup vertical v-model="formData['0000'].markEnable">
          <Radio :label="1" :disabled="!isEdit">
            <span>
              统一配置
              <span v-show="formData['0000'].markEnable === 1">
                <span class="font-D66418">（说明：接收人接收所有通知消息）</span>
                <span class="delimiter">|</span>
                <span class="font-active-color" @click="selectNotification">
                  <i class="icon-font icon-xuanzetongzhiduixiang"></i>
                  <span class="ml-xs notification">请选择通知对象</span>
                </span>
              </span>
            </span>
          </Radio>
          <Radio :label="0" :disabled="!isEdit">
            <span>按通知类型配置</span>
            <span v-show="formData['0000'].markEnable === 0" class="font-D66418"
              >（说明：在具体的通知内容中配置联系人）</span
            >
          </Radio>
        </RadioGroup>
      </ui-label> -->
      <ui-label label="统一接收人：">
        <span class="font-active-color pointer" :class="isEdit ? '' : 'disabled'" @click="selectNotification">
          <i class="icon-font icon-xuanzetongzhiduixiang"></i>
          <span class="ml-xs notification">{{
            formData['0000'].receiveConfig?.length ? '重新选择' : '选择通知对象'
          }}</span>
        </span>
        <p class="color-warning notification-desc">
          （说明：如果每种通知类型未配置通知接收人，通知消息将发送给统一接收人！）
        </p>
      </ui-label>
    </div>
    <div class="table-module auto-fill" v-show="formData['0000'].markEnable === 1">
      <ui-table class="ui-table auto-fill" :table-columns="tableColumns" :table-data="formData['0000'].receiveConfig">
        <template #people="{ row }">
          <div class="base-text-color">{{ row.name }}（{{ row.username }}）</div>
        </template>
        <template #phone="{ row, index }">
          <Input
            v-if="isEdit"
            class="width-md"
            placeholder="请输入联系电话"
            v-model="formData['0000'].receiveConfig[index].phone"
          ></Input>
          <span v-else>{{ row.phone }}</span>
        </template>
      </ui-table>
    </div>
    <people-select-module
      v-model="peopleSelectShow"
      title="选择通知对象"
      :default-people-list="defaultPeopleList"
      @pushPeople="pushPeople"
    ></people-select-module>
  </div>
</template>
<script>
import notification from '@/config/api/notification';
import async from '../../mixins/async';
export default {
  mixins: [async],
  props: {
    // 判断编辑状态
    action: {
      type: String,
    },
  },
  data() {
    return {
      peopleSelectShow: false,
      tableColumns: [
        {
          title: '序号',
          width: 50,
          align: 'center',
          fixed: 'left',
          type: 'index',
        },
        {
          title: '通知对象',
          minWidth: 200,
          slot: 'people',
        },
        {
          title: '所属单位',
          key: 'orgName',
        },
        {
          width: 400,
          title: '联系电话',
          slot: 'phone',
        },
      ],
      formData: {
        '0000': {
          markEnable: '1', //写死统一接收人方式
          receiveConfig: [],
          sendTime: [{ startTime: '00:00', endTime: '00:00' }],
        },
      },
      defaultPeopleList: [],
    };
  },
  async created() {
    /**
     * 0000：通用参数
     * 0101：外部对接异常
     * 0201：系统运维异常
     * 0301：接口异常-人脸相关接口
     * 0302：接口异常-车辆相关接口
     * 0401：平台离线-联网平台离线
     * 0402：平台离线-人脸视图库离线
     * 0403：平台离线-车辆视图库离线
     * 0501：设备离线-视频监控设备离线
     * 0502：设备离线-人脸卡口设备离线
     * 0503：设备离线-车辆卡口设备离线
     */
    await this.initMx(['0000']);
    this.dealSendTime();
  },
  methods: {
    dealSendTime() {
      if (!this.formData['0000'].sendTime || this.formData['0000'].sendTime.length === 0) {
        this.formData['0000'].sendTime = [{ startTime: '00:00', endTime: '00:00' }];
      }
    },
    selectNotification() {
      if (this.isEdit) {
        this.defaultPeopleList = this.getDefaultPeopleMx(this.formData['0000'].receiveConfig);
        this.peopleSelectShow = true;
      }
    },
    pushPeople(list) {
      // 如果已经保存的数据中的联系电话已经有，则以已保存的电话为填写值
      const peopleList = list.map((row) => {
        const people = this.defaultPeopleList.find((people) => people.id === row.id);
        if (people && people.phone) {
          this.$set(row, 'phone', people.phone);
        } else {
          this.$set(row, 'phone', row.phoneNumber);
        }
        return row;
      });
      this.formData['0000'].receiveConfig = peopleList;
    },
    reset() {
      this.resetMx();
    },
    async save() {
      //联系人未输入手机号码，禁止提交
      if (!this.checkSmsReceiveHasPhoneMx()) {
        this.$Message.error('您选择的通知对象未填入联系电话，无法以短信方式通知');
        return;
      }
      try {
        const res = await this.$http.post(notification.updateGeneralConfig, this.formData['0000']);
        this.$Message.success(res.data.msg);
        this.updateInitialMx();
      } catch (err) {
        console.log(err);
      }
    },
  },
  watch: {},
  computed: {
    isEdit() {
      return this.action === 'edit';
    },
  },
  components: {
    UiTable: require('@/components/ui-table.vue').default,
    PeopleSelectModule: require('@/components/people-select-module.vue').default,
  },
};
</script>
<style lang="less" scoped>
.general-parameters {
  padding: 0 20px 10px 20px;
  .delimiter {
    color: var(--devider-line);
  }
  .notification {
    text-decoration: underline;
  }
  .notification-desc {
    margin-left: 84px;
  }
  .disabled {
    cursor: not-allowed;
  }
}
</style>
