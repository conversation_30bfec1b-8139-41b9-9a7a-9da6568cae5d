<!--
    * @FileDescription: 人体详情
    * @Author: H
    * @Date: 2023/5/16
 * @LastEditors: liyuhang1 <EMAIL>
 * @LastEditTime: 2024-09-09 15:44:17
-->
<template>
  <div class="dom-wrapper">
    <div class="dom" @click="($event) => $event.stopPropagation()">
      <header>
        <span>抓拍列表</span>
        <Icon
          type="md-close"
          size="14"
          @click.native="() => $emit('close', $event)"
        />
      </header>
      <section class="dom-content">
        <!-- <div class="table-content"> -->
        <div class="info-box">
          <div
            class="list-card box-1"
            v-for="(item, index) in captureList"
            :key="index"
            :class="{ checked: item.isChecked }"
          >
            <p class="img-content">
              <span class="num" v-if="item.similarity"
                >{{ item.similarity || "0" }}%</span
              >
              <ui-image :src="item.sceneImg" v-viewer />
              <b class="shade vehicle">
                <ui-plate-number
                  :plateNo="item.plateNo"
                  :color="item.plateColor"
                  size="mini"
                ></ui-plate-number>
              </b>
            </p>
            <div class="bottom-info">
              <time>
                <Tooltip
                  content="抓拍时间"
                  placement="right"
                  transfer
                  theme="light"
                >
                  <i class="iconfont icon-time"></i>
                </Tooltip>
                {{ item.absTime }}
              </time>
              <p>
                <Tooltip
                  content="抓拍地点"
                  placement="right"
                  transfer
                  theme="light"
                >
                  <i class="iconfont icon-location"></i>
                </Tooltip>
                <span class="ellipsis" v-show-tips>{{ item.deviceName }}</span>
              </p>
            </div>
          </div>
          <div
            class="empty-card-1"
            v-for="(item, index) of 5 - (captureList.length % 5)"
            :key="index + 'demo'"
          ></div>
          <ui-empty
            v-if="captureList.length === 0 && loading == false"
          ></ui-empty>
          <ui-loading v-if="loading"></ui-loading>
        </div>
        <!-- </div> -->
      </section>
      <footer></footer>
    </div>
  </div>
</template>

<script>
export default {
  name: "",
  // mixins: [commonMixins, cutMixins], //全局的mixin
  components: {},
  props: {
    captureList: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      loading: false,
    };
  },
  watch: {},
  computed: {},
  async created() {},
  mounted() {},
  methods: {
    pageChange() {},
    pageSizeChange() {},
    close() {
      this.$emit("close");
    },
  },
};
</script>

<style lang="less" scoped>
@import "./style/modal.less";
.dom-content {
  display: flex;
  flex-direction: column;
  .info-box {
    flex: 1;
    overflow-y: auto;
    flex-wrap: wrap;
  }
  .box-1 {
    width: 19%;
  }
}
</style>
