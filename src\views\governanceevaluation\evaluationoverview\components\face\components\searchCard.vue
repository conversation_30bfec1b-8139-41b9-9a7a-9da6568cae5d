<template>
  <div class="base-search">
    <div class="image-model" v-if="isImageModel">
      <ui-label class="fl" label="抓拍时间" :width="70">
        <div class="date-picker-box">
          <DatePicker
            class="input-width mb-md"
            v-model="imageSearchData.customParameters.minLogTime"
            type="datetime"
            placeholder="请选择开始时间"
            :options="startTimeOption"
            confirm
            @on-change="
              (formatTime, timeType) =>
                changeTimeMx(formatTime, timeType, imageSearchData.customParameters, 'minLogTime')
            "
          ></DatePicker>
          <span class="ml-sm mr-sm">--</span>
          <DatePicker
            class="input-width"
            v-model="imageSearchData.customParameters.maxLogTime"
            type="datetime"
            placeholder="请选择结束时间"
            :options="endTimeOption"
            confirm
            @on-change="
              (formatTime, timeType) =>
                changeTimeMx(formatTime, timeType, imageSearchData.customParameters, 'maxLogTime')
            "
          ></DatePicker>
        </div>
      </ui-label>
      <ui-label class="fl ml-lg" label="抓拍设备" :width="70">
        <select-camera
          @pushCamera="pushCamera"
          :device-ids="imageSearchData.customParameters.deviceIds"
        ></select-camera>
      </ui-label>
      <ui-label class="fl ml-lg" label="检测结果" :width="70">
        <Select placeholder="请选择检测结果" v-model="imageSearchData.customParameters.qualified" style="width: 200px">
          <Option v-for="(item, index) in checkStatus" :key="index" :value="item.checkKey">{{ item.name }}</Option>
        </Select>
      </ui-label>
      <ui-label
        v-permission="{ route: $route.name, permission: 'artificialreviewr' }"
        class="fl ml-lg"
        label="异常原因"
        :width="70"
        v-if="
          [2001, 2002].includes(resultId.indexId) && ['2', '3'].includes(imageSearchData.customParameters.qualified)
        "
      >
        <Select placeholder="请选择异常原因" v-model="imageSearchData.customParameters.causeError" style="width: 200px">
          <Option v-for="(item, index) in checkData" :key="index" :value="item.key">{{ item.value }}</Option>
        </Select>
      </ui-label>
    </div>
    <div class="file-model" v-else>
      <ui-label class="inline mr-lg fl" label="设备编码" :width="70">
        <Input
          class="input-width"
          placeholder="请输入设备编码"
          v-model="fileSearchData.customParameters.deviceId"
        ></Input>
      </ui-label>
      <ui-label class="inline mr-lg fl" label="设备名称" :width="70">
        <Input
          class="input-width"
          placeholder="请输入设备名称"
          v-model="fileSearchData.customParameters.deviceName"
        ></Input>
      </ui-label>
      <ui-label class="inline mr-lg fl" label="检测状态" :width="70">
        <Select placeholder="请选择检测状态" v-model="fileSearchData.customParameters.outcome" style="width: 200px">
          <Option value="1">合格</Option>
          <Option value="2">不合格</Option>
        </Select>
      </ui-label>
    </div>
    <ui-label :width="1" class="fl ml-lg" label=" ">
      <Button type="primary" class="mr-sm" @click="startSearch">查询</Button>
      <Button
        type="default"
        class="mr-lg"
        @click="resetSearchDataMx(isImageModel ? imageSearchData : fileSearchData, startSearch)"
        >重置</Button
      >
    </ui-label>
  </div>
</template>
<script>
export default {
  props: {
    isImageModel: {
      default: () => true,
    },
    modular: {
      default: 1,
    },
    taskObj: {
      type: Object,
      default() {
        return {};
      },
    },
    treeData: {
      type: Array,
      default: () => [],
    },
    checkStatus: {
      type: Array,
      default: () => [],
    },
    checkList: {
      type: Array,
      default: () => [],
    },
    resultId: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      imageSearchData: {
        customParameters: {
          deviceIds: [],
          minLogTime: null,
          maxLogTime: null,
          qualified: '',
          causeError: null,
        },
      },
      fileSearchData: {
        customParameters: {
          deviceId: '',
          deviceName: '',
          outcome: '',
        },
      },
      startTimeOption: {
        disabledDate: (date) => {
          let endTime = new Date(this.imageSearchData.customParameters.maxLogTime);
          if (endTime) {
            return date > endTime;
          }
          return false;
        },
      },
      endTimeOption: {
        disabledDate: (date) => {
          let startTime = new Date(this.imageSearchData.customParameters.minLogTime);
          if (startTime) {
            return date < startTime;
          }
          return false;
        },
      },
      checkData: [],
    };
  },
  created() {},
  mounted() {},
  methods: {
    pushCamera(list) {
      this.imageSearchData.customParameters.deviceIds = list.map((row) => {
        return row.deviceId;
      });
    },
    startSearch() {
      this.$emit('startSearch', this.isImageModel ? this.imageSearchData : this.fileSearchData);
    },
    resetSearchDataMx() {
      this.fileSearchData = {
        customParameters: {
          deviceId: '',
          deviceName: '',
          outcome: '',
        },
      };
      this.imageSearchData = {
        customParameters: {
          deviceIds: [],
          minLogTime: null,
          maxLogTime: null,
          qualified: '',
          causeError: [],
        },
      };
      this.$emit('startSearch', this.isImageModel ? this.imageSearchData : this.fileSearchData);
    },
  },
  watch: {
    checkList: {
      handler(val) {
        if (val) {
          this.checkData = val;
        }
      },
      deep: true,
      immediate: true,
    },
  },
  components: {
    SelectCamera: require('@/components/select-camera.vue').default,
    // ApiOrganizationTree: require('../components/api-tree.vue').default,
  },
};
</script>
<style lang="less" scoped>
.small-width {
  width: 70px;
}
.base-search {
  overflow: hidden;
  padding-bottom: 0;
  .date-picker-box {
    display: flex;
  }
  .input-width {
    width: 200px;
  }
}
</style>
