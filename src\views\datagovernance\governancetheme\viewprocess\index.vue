<template>
  <div id="viewprocess">
    <div class="viewprocess-left">
      <div class="viewprocess-left-optbtn">
        <Button type="primary" @click="start">开始运行</Button>
        <!-- <Button type="primary">暂停</Button> -->
      </div>
      <div v-for="(item, index) in procedureDatas" :key="index">
        <aggre-connect :propData="item" :dWidth="dWidth" :bWidth="bWidth"></aggre-connect>
      </div>
    </div>
    <div class="viewprocess-right">
      <component-package></component-package>
    </div>
    <data-input ref="dataInput"></data-input>
    <field-mapping ref="fieldMapping" topicType="1" :topicId="topicId" @render="getView"></field-mapping>
    <testing ref="testing" @render="getView"></testing>
    <formawarehouse
      ref="formawarehouse"
      :warehousData="warehousData"
      :isSet="warehousData.isDefault === '1' ? true : false"
      @render="getView"
    ></formawarehouse>
    <dictionarymapping ref="dictionarymapping" topicType="1" :topicId="topicId" @render="getView"></dictionarymapping>
    <empty ref="Empty" topicType="1" :topicId="topicId" @render="getView"></empty>
    <repeat ref="Repeat" topicType="1" :topicId="topicId" @render="getView"></repeat>
    <timetest ref="Timetest" @render="getView"></timetest>
    <data-detection ref="DataDetection" :warehousData="warehousData" @render="getView"></data-detection>
    <!-- <warehouse-setting></warehouse-setting> -->
    <loading v-if="loading"></loading>
  </div>
</template>
<script>
import governancetheme from '@/config/api/governancetheme';
export default {
  name: 'viewprocess',
  props: {
    type: {
      type: String,
      default: '',
    },
    aggregateOptions: {
      type: Array,
      default: () => {},
    },
  },
  data() {
    return {
      currentView: 1,
      warehousData: {},
      procedure: [],
      procedureDatas: [],
      queryId: '',
      topicType: '',
      topicId: 0,
      loading: false,
      dWidth: '13.82%',
      bWidth: '15.23%',
    };
  },
  mounted() {
    this.loading = true;
    this.getView();
  },
  methods: {
    // 获取流程图数据
    async getView() {
      try {
        const id = this.$route.query.id;
        let res = await this.$http.get(governancetheme.view, {
          params: { id: id }, // 主题ID
        });
        this.topicType = res.data.data.topicType;
        this.topicId = res.data.data.id;
        this.procedure = res.data.data.componentList;
        this.procedureDatas = this.handleData(this.procedure);
        this.warehousData = JSON.parse(JSON.stringify(res.data.data));
        delete this.warehousData.componentList;
        this.loading = false;
      } catch (error) {
        console.log(error);
      }
    },
    // 将后端流程图数据与前端流程图数据整合
    handleData(procedure) {
      let group = [];
      procedure.forEach((item) => {
        group.push(item.sort);
      });
      group = Array.from(new Set(group)); // 去重
      let newDatas = group.map((item) => {
        let arr = [];
        procedure.forEach((dItem) => {
          if (dItem.sort === item) {
            arr.push(dItem);
          }
        });
        return arr;
      });
      return this.aggregateOptions.map((item, index) => {
        let obj = JSON.parse(JSON.stringify(item));
        obj.datas = item.datas.map((childItem, childIndex) => {
          childItem = { ...newDatas[index][childIndex], ...childItem };
          childItem['isConfigure'] = newDatas[index][childIndex]['isConfigure'];
          return childItem;
        });
        return obj;
      });
    },
    // 开始运行 isDefault： 1（正式） | 0（临时）
    async start() {
      try {
        if (this.loading) {
          return false;
        }
        this.loading = true;
        let url =
          this.warehousData.isDefault === '1' ? governancetheme.checkDeviceInfo : governancetheme.checkDeviceInfoTemp;
        let res = await this.$http.post(url);
        if (res.data.code == '200') {
          this.loading = false;
          this.$Message.success('运行成功！');
          // this.$Message["success"]({
          //   background: true,
          //   content: "运行成功！",
          // });
        }
      } catch (error) {
        console.log(error);
      }
    },
  },
  watch: {
    $route() {
      this.getView();
    },
  },
  components: {
    DataInput: require('./components/dataInput.vue').default,
    FieldMapping: require('./components/fieldmap.vue').default,
    ComponentPackage: require('@/views/datagovernance/governancetheme/components/componentPackage.vue').default,
    AggreConnect: require('../components/aggre-connect.vue').default,
    Testing: require('./components/testing.vue').default,
    Formawarehouse: require('./components/formawarehouse.vue').default,
    Dictionarymapping: require('./components/dictionarymapping.vue').default,
    Empty: require('./components/empty.vue').default,
    Repeat: require('./components/repeat.vue').default,
    Timetest: require('./components/timetest.vue').default,
    DataDetection: require('./components/data-detection.vue').default,
    // WarehouseSetting: require("./components/warehouse-setting.vue").default,
  },
};
</script>
<style lang="less" scoped>
#viewprocess {
  position: relative;
  display: flex;
  width: 100%;
  height: 100%;
  background-image: url('../../../../assets/img/thememanagement/process-bg.png');
  background-color: #03142d;
  background-size: 100% 100%;
  background-position: left;
  background-repeat: no-repeat;
  .viewprocess-left {
    position: relative;
    flex: 1;
    height: 100%;
    overflow-y: auto;
    // overflow: auto;
    // border-right: 1px solid var(--border-color);
    &-optbtn {
      position: absolute;
      top: 20px;
      right: 28px;
      // button:first-child{
      //   margin-right: 20px;
      // }
    }
  }
  .viewprocess-right {
    width: 243px;
    height: 100%;
    background-color: var(--bg-content);
  }
}
</style>
<style lang="less">
.viewprocess {
  .ivu-input {
    border-color: var(--color-primary) !important;
  }
}
</style>
