<template>
  <!-- 轨迹重复弹框 -->
  <ui-modal class="track-repeat" v-model="visible" title="查看详情" :styles="styles" :footer-hide="true">
    <div class="content auto-fill">
      <div class="container auto-fill">
        <div class="title_text">
          <span class="fl text_left mr-lg">指标名称：{{ this.$parent.row.indexName }}</span>
          <span class="fl text_left">评测时间：{{ this.$parent.row.createTime }}</span>
          <div class="export fr">
            <!-- <Button type="primary" class="btn_search">
              <i class="icon-font icon-daochu font-white f-14 mr-xs"></i>
              <span class="inline ml-xs">导出</span>
            </Button> -->
          </div>
        </div>
        <line-title title-name="检测结果统计"></line-title>
        <div class="statistics_list">
          <div class="statistics">
            <div class="sta_item_left">
              <draw-echarts
                :echart-option="echartRing"
                :echart-style="ringStyle"
                ref="zdryChart"
                class="charts"
              ></draw-echarts>
            </div>
            <div class="line"></div>
            <no-standard :module-data="moduleData"></no-standard>
          </div>
        </div>
        <!-- <line-title title-name="轨迹准确性存疑列表">
          <template slot="content">
            <slot name="content">
              <tagView
                class="tagView fr"
                ref="tagView"
                :list="['图像模式', '聚档模式']"
                @tagChange="tagChange1"
              />
            </slot>
          </template>
        </line-title> -->

        <line-title title-name="检测结果详情"></line-title>
        <track-device-search ref="faceSearchRef" @startSearch="startSearch" :selectList="selectList">
        </track-device-search>
      </div>
      <!-- 分组模式 ---------------------------------------------------------------------------------------------------- -->
      <div v-ui-loading="{ loading: loading, tableData: tableData }">
        <div :style="styleScroll6">
          <div class="carItem" v-for="item in tableData" :key="item.id">
            <div class="item">
              <!-- <div class="num" v-if="item.similarity">{{item.similarity}}%</div> -->
              <div class="num" v-if="item.sameCount">{{ item.sameCount }}</div>
              <div class="img" @click="viewBigPic(item)">
                <ui-image :src="item.trackImage" />
                <p class="shadow-box" title="查看检测结果">
                  <i
                    class="icon-font icon-yichang search-icon mr-xs base-text-color"
                    @click.stop="checkReason(item)"
                  ></i>
                </p>
              </div>

              <div class="group-message">
                <p class="marginP" :title="`抓拍时间：${item.shotTime || '暂无数据' | filterDateFun}`">
                  <i class="icon-font icon-shijian"></i>
                  <span class="group-text inline vt-middle ml-xs ellipsis onlys">{{
                    item.shotTime || '暂无数据' | filterDateFun
                  }}</span>
                </p>
                <p :title="item.catchPlace">
                  <i class="icon-font icon-dizhi"></i>
                  <span class="group-text inline vt-middle ml-xs ellipsis onlys">{{
                    item.catchPlace ? item.catchPlace : '暂无数据'
                  }}</span>
                </p>
              </div>
              <!-- <i
                    class="icon-font icon-buhege"
                    v-if="!item.trackImage || !item.trackLargeImage"
                  ></i> -->
            </div>
          </div>
        </div>
        <!-- 分页 -->
        <ui-page class="page" :page-data="searchData" @changePage="changePage" @changePageSize="changePageSize">
        </ui-page>
      </div>
      <look-scene v-model="bigPictureShow" :img-list="imgList"></look-scene>
    </div>
    <!-- <div class="no-box" v-else>
      <div class="no-data">
        <img src="@/assets/img/common/nodata.png" alt />
      </div>
    </div> -->
    <all-tracks ref="allTracks"></all-tracks>
  </ui-modal>
</template>

<style lang="less" scoped>
.track-repeat {
  @{_deep} .ivu-modal {
    .ivu-modal-header {
      margin-bottom: 0;
    }
    .ivu-modal-body {
      padding: 10px 20px;
    }
  }
  .no-box {
    width: 1686px;
    min-height: 820px;
    max-height: 820px;
  }
  .content {
    color: #fff;
    background: var(--bg-content);
    min-height: 820px;
    max-height: 820px;
    border-radius: 4px;
    position: relative;
    .container {
      .list_item {
        margin-top: 10px;
      }
      .title_text {
        width: 100%;
        height: 50px;
        display: inline-block;
        line-height: 50px;
        // background-color: #239df9;
        .text_left {
          margin-top: 10px;
        }
        .export {
          margin-top: 10px;
          //   position: absolute;
          //   top: 20px;
          //   right: 20px;
        }
      }
      .scheme_header {
        height: 30px;
        line-height: 30px;
        width: 100%;
        display: inline-block;
        margin-top: 10px;
        .scheme_line {
          width: 8px;
          height: 30px;
          vertical-align: middle;
          display: inline-block;
          background: #239df9;
          opacity: 1;
        }
        .scheme_title {
          width: calc(100% - 18px);
          height: 30px;
          vertical-align: middle;
          display: inline-block;
          margin-left: 10px;
          padding-left: 10px;
          background: linear-gradient(90deg, #0a4f8d 0%, rgba(9, 40, 77, 0) 100%);
        }
      }
      .statistics_list {
        width: 100%;
        height: 180px;
        line-height: 180px;
        background-color: var(--bg-sub-content);
        margin-top: 10px;
      }
      .statistics {
        width: 100%;
        height: 180px;
        line-height: 180px;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        .sta_item_left {
          width: 48%;
          display: flex;
          justify-content: flex-start;
          align-items: flex-start;
        }
        .line {
          width: 1px;
          height: 70%;
          background-color: #0d477d;
        }
      }
    }
    .list {
      position: relative;
      margin-top: 10px;
      //   height: 450px;
      //   overflow-y: auto;

      .no-data {
        position: absolute;
        top: 50%;
        left: 50%;
      }
      img {
        width: 56px;
        height: 56px;
      }
      @{_deep}.ivu-table-cell-slot {
        margin-top: 10px;
      }
    }
    .report {
      color: #175cb2;
      cursor: pointer;
    }
  }
  /deep/.base-search {
    margin: 15px 0 0;
  }
  .carItem {
    height: 236px;
    margin: 10px 10px 0px 0;
    width: 188px;
    display: inline-block;
    // max-width: 188px;
    .item {
      position: relative;
      height: 100%;
      background: #0f2f59;
      .num {
        position: absolute;
        right: 0;
        z-index: 100;
        padding: 10px;
        border-radius: 5px;
        background: rgba(42, 95, 175, 0.6);
      }
      .img {
        position: relative;
        cursor: pointer;
        position: relative;
        width: calc(100% - 28px);
        height: 167px;
        padding-top: 14px;
        margin-left: 14px;
        display: flex;
        align-items: center;
        .shadow-box {
          height: 28px;
          width: 100%;
          background: rgba(0, 0, 0, 0.3);
          position: absolute;
          bottom: 0;
          display: none;
          padding-left: 10px;
          z-index: 100;
          > i:hover {
            color: var(--color-primary);
          }
        }
        &:hover {
          .shadow-box {
            display: block;
          }
        }
        span {
          position: absolute;
          top: 50%;
          left: 40%;
          z-index: 100;
          cursor: pointer;
        }
        .percent {
          position: absolute;
          top: 1px;
          left: 1px;
          display: inline-block;
          padding: 0 2px;
          min-width: 32px;
          text-align: center;
          background: #ea800f;
          color: #ffffff;
          z-index: 99;
        }
      }
      img {
        width: 100%;
        max-width: 100%;
        max-height: 156px;
        background: #999;
      }

      .group-message {
        padding-left: 12px;
        margin-top: 12px;
      }
      .icon-URLbukefangwen1 {
        color: #bc3c19;
        position: absolute;
        bottom: 54px;
        right: 14px;
        font-size: 60px;
        z-index: 10;
      }
    }
  }
  .check {
    width: 100px;
    margin-top: 10px;
  }
  /deep/.auto-fill {
    overflow: visible;
  }
  /deep/.deft {
    .color_green {
      color: #bc3c19;
    }
  }
  /deep/.ui-page {
    display: block !important;
  }
}
.onlys {
  width: 80%;
}
</style>
<script>
import governanceevaluation from '@/config/api/governanceevaluation';
import imgSrc from '@/assets/img/load-error-img.png';
export default {
  props: {
    value: {},
    statisticsList: {},
  },
  data() {
    return {
      noImg: imgSrc,
      echartRing: {},
      ringStyle: {
        width: '650px',
        height: '180px',
      },
      zdryChartObj: {
        xAxisData: ['未重复轨迹图像数量', '重复轨迹图像数量'],
        showData: [
          { name: '未重复轨迹图像数量', value: 0 },
          { name: '重复轨迹图像数量', value: 0 },
        ],
        zdryTimer: null,
        count: 0,
        formData: {},
        color: [
          [
            { offset: 0, color: '#32A19E' },
            { offset: 1, color: '#05CE98' },
          ],
          [
            { offset: 0, color: '#983C0D' },
            { offset: 1, color: '#E9410F' },
          ],
        ],
      },
      moduleData: {
        rate: '轨迹重复率',
        rateValue: 0,
        price: '达标值',
        priceValue: 0,
        result: '考核结果',
        resultValue: '',
        remarkValue: '',
      },
      styleScroll6: {
        position: 'relative',
        width: '100%',
        height: '1.77rem',
        'overflow-y': 'scroll',
      },
      tableColumns: [
        { type: 'index', width: 70, title: '序号', align: 'center' },
        {
          title: '人脸抓拍',
          key: 'trackImage',
          slot: 'trackImage',
          tooltip: true,
          minWidth: 150,
        },
        { title: '抓拍时间', key: 'shotTime', tooltip: true, minWidth: 150 },
        { title: '抓拍点位', key: 'catchPlace', tooltip: true, minWidth: 160 },
        {
          title: '档案照',
          key: 'identityPhoto',
          slot: 'identityPhoto',
          tooltip: true,
          minWidth: 150,
        },
        { title: '姓名', key: 'name', tooltip: true, minWidth: 150 },
        { title: '证件号', key: 'idCard', tooltip: true, minWidth: 250 },
        {
          title: '是否与其他轨迹重复',
          key: 'isguan',
          minWidth: 150,
        },
        {
          title: '相同轨迹数量',
          key: 'sameAmount',
          minWidth: 150,
        },
        {
          title: '检测结果',
          key: 'synthesisResult',
          slot: 'synthesisResult',
          tooltip: true,
          width: 150,
        },
        {
          title: '操作',
          key: 'option',
          slot: 'option',
          width: 150,
        },
      ],
      selectList: [
        { id: 0, label: '轨迹未重复' },
        { id: 1, label: '轨迹重复' },
      ],
      tableData: [],
      minusTable: 600,
      searchData: {
        totalCount: 0,
        pageNum: 1,
        pageSize: 20,
        params: {
          pageNumber: 1,
          pageSize: 20,
        },
      },
      styles: {
        width: '9rem',
      },
      visible: true,
      loading: false,
      bigPictureShow: false,
      imgList: [require('@/assets/img/navigation-page/systemmanagement.png')],
      trackList: {},
      cardInfo: [
        { name: '姓名：', value: 'name' },
        { name: '证件号：', value: 'idCard' },
        { name: '轨迹总数：', value: 'catchAmount' },
        { name: '异常轨迹：', value: 'unqualifiedAmount', color: '#BC3C19' },
      ],
    };
  },
  async mounted() {
    await this.init();
    await this.getStatistics();
    await this.initRing();
  },

  methods: {
    viewBigPic(item) {
      if (!item.trackLargeImage) {
        this.$Message.warning('大图URL缺失');
        return;
      }
      this.imgList = [item.trackLargeImage];
      this.bigPictureShow = true;
    },
    viewBig(item) {
      if (!item.identityPhoto) {
        this.$Message.warning('大图URL缺失');
        return;
      }
      this.imgList = [item.identityPhoto];
      this.bigPictureShow = true;
    },
    // 列表
    async init() {
      this.searchData.resultId = this.$parent.row.resultId;
      this.searchData.taskId = this.$route.query.id;
      try {
        this.loading = true;
        this.tableData = [];
        let res = await this.$http.post(governanceevaluation.repeatList, this.searchData);
        this.tableData = res.data.data.entities;
        for (let i of this.tableData) {
          if (i.synthesisResult === '0') {
            i.synthesisResult = '未重复';
            i.isguan = '否';
          } else if (i.synthesisResult === '1') {
            i.synthesisResult = '重复';
            i.isguan = '是';
          }
        }
        this.searchData.totalCount = res.data.data.total;
        this.loading = false;
      } catch (err) {
        console.log(err);
        this.loading = false;
      } finally {
        this.loading = false;
      }
    },
    show(row) {
      this.$refs.allTracks.info(row);
    },
    checkReason(item) {
      item.resultId = this.$parent.row.resultId;
      item.indexId = this.$parent.row.indexId;
      item.taskId = this.$route.query.id;
      this.$refs.allTracks.info(item);
    },
    startSearch(searchData) {
      this.searchData.deviceIds = searchData.deviceIds;
      this.searchData.endTime = searchData.endTime;
      this.searchData.startTime = searchData.startTime;
      this.searchData.pageNum = 1;
      this.searchData.params.pageNumber = 1;
      this.searchData.synthesisResult = searchData.deviceResult;
      this.init();
    },
    // 统计
    async getStatistics() {
      try {
        let res = await this.$http.post(governanceevaluation.repeatStatistics, {
          resultId: this.$parent.row.resultId,
          indexId: this.$parent.row.indexId,
          taskId: this.$route.query.id,
        });
        this.trackList = res.data.data;
        this.moduleData.rateValue = this.trackList.repeatRate || 0;
        this.moduleData.priceValue = this.$parent.row.standardsValue || 0;
        this.moduleData.resultValue = this.$parent.row.qualifiedDesc || 0;
      } catch (error) {
        console.log(error);
      }
    },
    // echarts图表
    initRing() {
      let xAxisData = this.zdryChartObj.xAxisData;
      this.zdryChartObj.showData.map((item) => {
        if (item.name === '未重复轨迹图像数量') {
          item.value = this.trackList.detectionAmount - this.trackList.repeatAmount;
        } else {
          item.value = this.trackList.repeatAmount;
        }
      });
      this.zdryChartObj.count = this.trackList.detectionAmount;
      let formatData = {
        seriesName: '检测轨迹图像',
        xAxisData: xAxisData,
        showData: this.zdryChartObj.showData,
        count: this.zdryChartObj.count,
        color: this.zdryChartObj.color,
      };
      this.zdryChartObj.formatData = formatData;
      this.echartRing = this.$util.doEcharts.evaluationPageRin(this.zdryChartObj.formatData);
    },
    choseGrop() {
      this.$refs.faceSearchRef.searchData = {
        deviceIds: [],
        startTime: '',
        endTime: '',
        deviceResult: '',
      };
      this.init();
    },
    changePage(val) {
      this.searchData.pageNum = val;
      this.searchData.params.pageNumber = val;
      this.init();
    },
    changePageSize(val) {
      this.searchData.pageSize = val;
      this.searchData.params.pageSize = val;
      this.searchData.pageNum = 1;
      this.searchData.params.pageNumber = 1;
      this.init();
    },
  },
  watch: {
    visible(val) {
      this.$emit('input', val);
    },
    value(val) {
      this.visible = val;
    },
  },
  components: {
    DrawEcharts: require('@/components/draw-echarts').default,
    lineTitle: require('@/components/line-title').default,
    noStandard: require('@/views/appraisaltask/detectiontask/evaluationmanagement/components/no-standard.vue').default,
    allTracks: require('./components/all-tracks.vue').default,
    TrackDeviceSearch: require('@/components/track-detail-search.vue').default,
  },
};
</script>
