<!--
 * @Date: 2024-09-02 10:11:16
 * @LastEditors: liyuhang1 <EMAIL>
 * @LastEditTime: 2024-09-27 17:47:35
 * @FilePath: \icbd-view\src\views\video-application\video-play\components\add-to-group.vue
-->
<template>
  <ui-modal
    v-model="visible"
    title="添加到我的分组"
    :r-width="450"
    @onOk="comfirmHandle"
    @onCancel="comfirmCancle"
  >
    <div class="list">
      <RadioGroup v-model="groupId" vertical>
        <Radio :label="item.id" v-for="item in groupList" :key="item.id">{{
          item.groupName
        }}</Radio>
      </RadioGroup>
    </div>
  </ui-modal>
</template>
<script>
import { queryMyVideoGroupList, addVideoToGroup } from "@/api/player";
import { mapGetters } from "vuex";

export default {
  data() {
    return {
      visible: false,
      groupList: [],
      deviceId: "",
      groupId: "",
    };
  },
  computed: {
    ...mapGetters({ userInfo: "userInfo" }),
  },
  methods: {
    // 初始化
    show(deviceId) {
      this.visible = true;
      this.deviceId = deviceId;
      this.getGroupList();
    },
    getGroupList() {
      this.groupList = []
      queryMyVideoGroupList({ userId: this.userInfo.id }).then((res) => {
        // 部分环境接口没有返回权限信息
        if (res.data.grouplist[0].permission) {
          res.data.grouplist.forEach((v) => {
            if (v.permission === "write") this.groupList.push(v);
          });
        } else {
          this.groupList = res.data.grouplist;
        }
      });
    },
    joinGroup() {
      if (!this.groupId) return;
      addVideoToGroup({
        groupId: this.groupId,
        deviceId: this.deviceId,
      }).then((res) => {
        if (res.data.code == 200) {
          this.$Message.success("添加成功");
          this.visible = false;
        } else {
          this.$Message.error(res.data.msg);
        }
      });
    },
    // 确认提交
    comfirmHandle() {
      this.joinGroup();
    },
    comfirmCancle() {
      this.visible = false;
    },
  },
};
</script>
<style lang="less" scoped>
.list {
  height: 300px;
  overflow-y: auto;
}
</style>
