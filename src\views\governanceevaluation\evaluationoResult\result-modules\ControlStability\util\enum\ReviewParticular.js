import { qualifiedColorConfig } from '@/views/governanceevaluation/evaluationoResult/util/CommonEnum/ReviewParticular.js';
export const iconStaticsList = [
  {
    name: '本月检测总次数',
    count: '0',
    countStyle: {
      color: '#EBB225',
    },
    iconName: 'icon-shijijianceshebeishuliang',
    fileName: 'actualNum',
  },
  {
    name: '检测不合格次数',
    count: '0',
    countStyle: {
      color: '#B113B1',
    },
    iconName: 'icon-shijijianceshebeishuliang',
    fileName: 'unqualifiedNum',
  },
  {
    name: '轨迹查询接口稳定性',
    count: '0',
    countStyle: {
      color: '#269F26',
    },
    iconName: 'icon-shipinjiankongjianshezongliang',
    fileName: 'resultValue',
    type: 'percent', // 百分比
  },
];
export const normalFormData = [
  {
    type: 'select',
    key: 'outcome',
    label: '检测结果',
    placeholder: '请选择检测结果',
    options: Object.keys(qualifiedColorConfig).map((key) => {
      return {
        value: key,
        label: qualifiedColorConfig[key].dataValue,
      };
    }),
  },
  {
    type: 'select',
    key: 'errorCodes',
    label: '不合格原因',
    selectMutiple: true,
    placeholder: '请选择异常原因',
    options: [],
  },
];
export const tableColumns = [
  {
    type: 'index',
    width: 70,
    title: '序号',
    align: 'center',
  },
  {
    title: '行政区划',
    key: 'civilName',
    align: 'left',
    tooltip: true,
    minWidth: 200,
  },
  {
    title: '行政区划代码',
    key: 'civilCode',
    align: 'left',
    tooltip: true,
    minWidth: 150,
  },
  {
    title: '接口名称',
    key: 'apiName',
    align: 'left',
    tooltip: true,
    minWidth: 150,
  },
  {
    title: '检测时间',
    key: 'createTime',
    tooltip: true,
    width: 150,
  },
  {
    title: '使用图片',
    slot: 'useUrl',
    tooltip: true,
    width: 120,
  },
  {
    title: '返回结果',
    slot: 'result',
    tooltip: true,
    width: 380,
  },
  {
    title: '耗时',
    key: 'waitTime',
    tooltip: true,
    width: 100,
  },
  {
    title: '检测结果',
    key: 'qualified',
    slot: 'qualified',
    tooltip: true,
    minWidth: 120,
  },
  {
    title: '不合格原因',
    key: 'errorMessageName',
    tooltip: true,
    minWidth: 120,
  },
];
