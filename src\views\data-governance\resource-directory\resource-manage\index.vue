<template>
    <div class="container box">
        <div class="person">
            <!-- 查询 -->
            <Search @searchForm="searchForm" :list="directoryList" @add="add" @delet="delet" @createResourceByExcel="createResourceByExcel" @exportData="exportData" />
            <ui-table ref="seletction" :columns="columns" :data="tableData" :loading="loading" @on-select="handleSelect" @on-select-cancel="handleSelectCancel" @on-select-all="handleSelectAll" @on-select-all-cancel="handleSelectAll">
                <template #action="{ row }">
                    <div style="display: flex; gap: 10;justify-content: space-between;">                     
                        <ui-btn-tip content="生成数据" icon="icon-daoru" class="primary" @click.native="generateData(row)"></ui-btn-tip>
                        <ui-btn-tip content="编辑" icon="icon-bianji" class="primary" @click.native="edit(row)"></ui-btn-tip>
                        <ui-btn-tip content="添加字段" icon="icon-tianjia" class="primary" @click.native="addItem(row)"></ui-btn-tip>
                        <ui-btn-tip content="配置" icon="icon-shezhi3" class="primary" @click.native="handleConfig(row)"></ui-btn-tip>
                    </div>
                </template>
            </ui-table>
            <!-- 分页 -->
            <ui-page :current="params.pageNumber" :total="total" :page-size="params.pageSize" @pageChange="pageChange" @pageSizeChange="pageSizeChange"></ui-page>
        </div>
        <!-- 新增/编辑 -->
        <addModal :list="directoryList" :resourceAll="resourceAll" ref="addModal" @refreshDataList="refreshDataList" />
        <!-- 资源配置 -->
        <configModal ref="configModal" :dictTypedata="dictTypedata" @refreshDataList="refreshDataList" />
        <!-- 导入Excel，一键创建资源 -->
        <importExcel ref="importExcel" :directoryList="directoryList" @refreshDataList="refreshDataList" />
        <!-- 已有资源新增数据项 -->
        <addItem ref="addItem" @refreshDataList="refreshDataList" />
        <!-- 生成数据 -->
        <generateModal ref="generateModal" :dictTypedata="dictTypedata" @refreshDataList="refreshDataList" />
    </div>
</template>
<script>
import { mapActions } from 'vuex'
import Search from './components/search'
import addModal from './components/addModal'
import configModal from './components/configModal'
import importExcel from './components/importExcel'
import addItem from './components/addItem'
import generateModal from './components/generateModal'
import { getResourcePageList, getResourceAll, getSelectCatalogList, removeResource, getDictTypeList, exportResource } from '@/api/dataGovernance'
export default {
    components: { Search, addModal, configModal, importExcel, addItem, generateModal },
    props: {},
    data() {
        return {
            loading: false,
            pageForm: {
                //默认入参必要条件
                resourceNameCn: '',
                resourceName: '',
                catalogId: []
            },
            params: {
                pageNumber: 1,
                pageSize: 20
            },
            total: 0,
            tableData: [], //列表
            directoryList: [], //所有目录
            resourceAll: [], //所有资源
            dictTypedata: [], //对应字典
            seletctedIds: [], //列表选中的id
            columns: [
                { title: '', width: 40, type: 'selection', key: 'index' },
                { title: '序号', width: 90, type: 'index', key: 'index' },
                { title: '资源名称',  key: 'resourceName' },
                { title: '中文名称',  key: 'resourceNameCn' },
                { title: '操作', slot: 'action', width: 140 }
            ]
        }
    },
  created() {
    this.getDictData()
    this.getList()
    this.getResourceAll()
    this.getSelectCatalogList()
    this.getDictTypeList()
  },
  methods: {
    ...mapActions({
      getDictData: 'dictionary/getDictAllData'
    }),
    // 列表
    getList() {
      this.loading = true
      getResourcePageList(Object.assign(this.pageForm, this.params)).then(res => {
        const { entities, pageSize, pageNumber, total } = res.data
        this.tableData = entities
        this.params = {
          pageSize: pageSize,
          pageNumber: pageNumber
        }
        this.total = total
        this.loading = false
      })
    },
    // 获取所有资源
    getResourceAll() {
      getResourceAll().then(res => {
        this.resourceAll = res
      })
    },
    // 获取对应字典
    getDictTypeList() {
      getDictTypeList().then(res => {
        this.dictTypedata = res.data
      })
    },
    // 获取所有目录
    getSelectCatalogList() {
      getSelectCatalogList({
        catalogName: ''
      }).then(res => {
        let list = res.data
        this.directoryList = this.getTree(list)
      })
    },
    // 递归修改树字段名
    getTree(tree = []) {
      let arr = []
      if (!!tree && tree.length !== 0) {
        tree.forEach(item => {
          // 目录树重新定义字段格式
          let obj = {
            title: item.catalogName,
            id: item.id,
            parentId: item.parentId,
            children: this.getTree(item.list)
          }
          arr.push(obj)
        })
      }
      return arr
    },
    // 新增/编辑
    refreshDataList() {
      this.getList()
      this.params.pageNumber = 1
    },
    // 查询
    searchForm(search) {
      const { resourceNameCn, resourceName, catalogId } = search
      this.pageForm = {
        resourceNameCn: resourceNameCn,
        resourceName: resourceName,
        catalogId: catalogId ? catalogId.toString().split(',') : [] //入参要求数组
      }
      this.params.pageNumber = 1
      this.getList()
    },
    // 新增资源
    add() {
      this.$refs.addModal.show()
    },
    // 编辑资源
    edit(item) {
      this.$refs.addModal.show(item)
    },
    // 配置
    handleConfig(item) {
      this.$refs.configModal.show(item)
    },
    // 新增属性
    addItem(item) {
      this.$refs.addItem.show(item)
    },
     // 生成资源数据
     generateData(item) {
      this.$refs.generateModal.show(item)
    },
    // 改变页码
    pageChange(pageNumber) {
      this.params.pageNumber = pageNumber
      this.getList()
    },
    // 改变分页个数
    pageSizeChange(size) {
      this.params.pageSize = size
      this.params.pageNumber = 1
      this.getList()
    },
    // 选中
    handleSelect(selection, row) {
      this.seletctedIds.push(row.id)
    },
    // 取消选中
    handleSelectCancel(selection, row) {
      let index = this.seletctedIds.findIndex(item => {
        return item === row.id
      })
      this.seletctedIds.splice(index, 1)
    },
    // 全选
    handleSelectAll(selection) {
      // 取消全选判断
      if (selection.length === 0) {
        this.seletctedIds = []
        // let data = this.$refs.seletction.data
        // data.forEach(item => {
        //   if (this.seletctedIds.indexOf(item.id) !== -1) {
        //     let index = this.seletctedIds.findIndex(it => {
        //       return it === item.id
        //     })
        //     this.seletctedIds.splice(index, 1)
        //   }
        // })
      } else {
        // 全部选中
        selection.forEach(e => {
          this.seletctedIds.push(e.id)
        })
      }
    },
    // 删除
    delet() {
      if (this.seletctedIds.length !== 0) {
        this.$Modal.confirm({
          title: '提示',
          closable: true,
          content: `确定删除吗？`,
          onOk: () => {
            removeResource(this.seletctedIds).then(res => {
              this.$Message.success(res.msg)
              this.getList()
            })
          }
        })
      } else {
        this.$Message.warning('删除项不能为空！')
      }
    },
    // Excel一键创建
    createResourceByExcel() {
      this.$refs.importExcel.show()
    },
    // 导出
    exportData () {
      if (this.seletctedIds.length !== 0) {
        exportResource(this.seletctedIds).then(res => {
          let aLink = document.createElement('a')
          aLink.href = res.data
          aLink.click()
        })
      }else{
        this.$Message.warning('请选择导出的资源！')
      }
      this.seletctedIds = []
      this.getList()
    }
  }
}
</script>
<style lang="less" scoped>
.box{
    overflow: hidden;
}
.person {
    display: flex;
    flex: 1;
    height: 100%;
    width: 100%;
    // width: 1820px;
    flex-direction: column;
    .card-content {
        display: flex;
        flex-wrap: wrap;
        // overflow: auto;
        flex: 1;
        margin: 0 -5px;
        align-content: flex-start;
        .card-item {
            width: 20%;
            padding: 0 5px;
            box-sizing: border-box;
            margin-bottom: 10px;
            transform-style: preserve-3d;
            transition: transform 0.6s;
            .list-card {
                width: 100%;
                backface-visibility: hidden;
                /deep/.tag-wrap {
                    margin-top: 10px;
                    .ui-tag {
                        margin: 0 5px 0 0 !important;
                    }
                }
            }
        }
    }
}
</style>
