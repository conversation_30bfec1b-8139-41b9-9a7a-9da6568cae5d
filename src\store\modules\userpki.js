import user from '@/config/api/user';

export default {
  namespaced: true,
  state: {
    pkiOriginal: '',
    pkiUserInfo: {},
  },
  mutations: {
    setPkiOriginal(state, data) {
      state.pkiOriginal = data;
    },
    setPkiUserInfo(state, info) {
      state.pkiUserInfo = info;
    },
  },
  getters: {
    getPkiOriginal(state) {
      return state.pkiOriginal;
    },
    getPkiUserInfo(state) {
      return state.pkiUserInfo;
    },
  },
  actions: {
    //jit-1、获取随机数
    async getOriginal({ commit }) {
      try {
        let { data } = await this._vm.$http.post(user.jitGWRandom, {});
        commit('setPkiOriginal', data);
        return data;
      } catch (e) {
        console.log(e, '获取随机数失败');
      }
    },
    // jit-2 获取用户认证信息
    async getJitPKIAuth({ commit }, params) {
      try {
        let { data } = await this._vm.$http.post(user.jitGwAuthen, params);
        if (Object.values(data.certAttributeNodeMap).length === 0) {
          return false;
        }
        let userInfoList = Object.values(data.certAttributeNodeMap)[0].split(',');
        let userInfo = {};
        userInfoList.forEach((item) => {
          let [key, value] = item.split('=');
          userInfo[key] = value;
        });
        commit('setPkiUserInfo', userInfo);
        return userInfo;
      } catch (err) {
        console.log(err);
        return false;
      }
    },
    //jit-3 登录，设置token
    async jitPkiLoigin({ commit }, params) {
      try {
        commit('user/setIsThirdParty', '1', { root: true });
        let {
          data: { data },
        } = await this._vm.$http.post(user.thirdPartyLoginFroQXN, params);
        commit('user/setToken', data, { root: true });
      } catch (err) {
        console.log(err);
        commit('user/setIsThirdParty', '0', { root: true });
      }
    },
  },
};
