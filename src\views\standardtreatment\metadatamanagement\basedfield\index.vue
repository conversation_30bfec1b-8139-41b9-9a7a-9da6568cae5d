<template>
  <div class="based-field" ref="mains">
    <!-- <Row class="based-field-header">
			<Col :span="12" class="title">
				<div class="border-header"></div>
				<div class="title-header">基础字段管理</div>
			</Col>
			<Col :span="12" align="right">
				<span class="back" @click="back()">
					<Icon type="ios-arrow-back" />返回
				</span>
			</Col>
		</Row>
		<Divider />-->
    <div class="form">
      <label for="关键词" class="based-field-label">关键词</label>
      <Input v-model="searchData.searchValue" placeholder="请输入字段名称" style="width: 200px" />
      <div class="inline ml-sm">
        <Button type="primary" @click="search()">查询</Button>
        <Button class="ml-sm" @click="resetSearchDataMx1(searchData, search)">重置</Button>
      </div>
      <Button
        type="primary"
        @click="edit({})"
        class="fr"
        v-permission="{ route: 'basedfield', permission: 'basedfieldAdd' }"
      >
        <i class="icon-font icon-tianjia f-12 mr-sm vt-middle" title="添加"></i>
        <span class="vt-middle">新增字段</span>
      </Button>
    </div>

    <div class="left-div">
      <ui-table
        class="ui-table"
        :loading="loading"
        :table-columns="tableColumns"
        :table-data="tableData"
        ref="table"
        :minus-height="240"
      >
        <template slot-scope="{ row }" slot="tableNum">
          <a @click="infoMenu(row)" v-if="row.tableNum && Number(row.tableNum) > 0">{{ row.tableNum }}</a>
          <span v-else style="color: rgba(255, 255, 255, 0.95); cursor: no-drop">--</span>
        </template>
        <template slot-scope="{ row }" slot="fieldType">{{ metaFieldType[row.fieldType] }}</template>

        <template slot-scope="{ row }" slot="isNull">
          <span v-if="row.isNull === 1">是</span>
          <span v-else>否</span>
        </template>
        <template #creator="{ row }">
          <span>{{ row.creator || '--' }}</span>
        </template>
        <template slot-scope="{ row }" slot="action">
          <i
            class="icon-font icon-bianji2 mr-md"
            v-if="row.tableNum && Number(row.tableNum) > 0"
            style="color: rgba(255, 255, 255, 0.25); cursor: no-drop"
            title="编辑"
          ></i>
          <ui-btn-tip
            v-else
            icon="icon-bianji2"
            class="mr-md"
            content="编辑"
            :row="row"
            @click.native="edit(row)"
            v-permission="{ route: 'basedfield', permission: 'basedfieldEdit' }"
          ></ui-btn-tip>
          <i
            class="icon-font icon-shanchu3"
            v-if="row.tableNum && Number(row.tableNum) > 0"
            style="color: rgba(255, 255, 255, 0.25); cursor: no-drop"
            title="删除"
          ></i>
          <ui-btn-tip
            v-else
            icon="icon-shanchu3"
            content="删除"
            :row="row"
            @click.native="deleteItem(row)"
            v-permission="{ route: 'basedfield', permission: 'basedfieldDel' }"
          ></ui-btn-tip>
        </template>
      </ui-table>
      <loading v-if="loading"></loading>
    </div>
    <ui-page class="page" :page-data="pageData" @changePage="changePage" @changePageSize="changePageSize"></ui-page>

    <!-- 查看关联表 -->
    <basedFieldInfoModal ref="basedFieldInfoModal" />
    <!-- 编辑与新增 -->
    <basedFieldAddOrEdit :metaFieldType="metaFieldType" @search="search" ref="basedFieldAddOrEdit" />
  </div>
</template>
<script>
import metadatamanagement from '@/config/api/metadatamanagement';
import algorithm from '@/config/api/algorithm';
export default {
  name: 'basedfield',
  data() {
    return {
      loading: false,
      pageData: {
        totalCount: 0,
        pageNum: 1,
        pageSize: 20,
      },
      copySearch: {},
      searchData: {
        searchValue: '',
        params: {
          pageNumber: 1,
          pageSize: 20,
        },
      },
      tableColumns: [
        { type: 'index', width: 50, title: '序号', align: 'center' },
        { title: '字段名', key: 'fieldName' },
        { title: '注释', key: 'fieldComment' },
        { title: '字符类型', slot: 'fieldType' },
        { title: '字符长度', key: 'fieldLength' },
        { title: '已关联表数量', slot: 'tableNum' },
        { title: '是否可空', slot: 'isNull' },
        { title: '标识代码', key: 'identCode' },
        { title: '创建人', slot: 'creator' },
        { title: '创建时间', key: 'createTime' },
        {
          title: '操作',
          slot: 'action',
          width: 80,
          align: 'center',
          fixed: 'right',
          className: 'table-action-padding',
        },
      ],
      tableData: [],
      metaFieldType: {},
    };
  },
  created() {},
  mounted() {
    this.loading = true;
    this.$nextTick(() => {
      this.info();
    });
  },
  methods: {
    async info() {
      await this.getAlgorithmList();
      await this.infoList();
    },
    async getAlgorithmList() {
      await this.$http.get(algorithm.dictData + 'metaFieldType').then((res) => {
        if (res.data.code === 200) {
          res.data.data.map((val) => {
            this.metaFieldType[val.dataKey] = val.dataDes;
          });
        }
      });
    },

    // 重置（ui-page与后台参数不统一，单独制空pageNumber）
    resetSearchDataMx1() {
      this.pageData = { totalCount: 0, pageNum: 1, pageSize: 20 };
      this.searchData = {
        searchValue: '',
        params: { pageNumber: 1, pageSize: 20 },
        totalCount: 0,
        pageNum: 1,
        pageSize: 20,
      };
      this.infoList();
    },
    async infoList() {
      this.loading = true;
      try {
        let res = await this.$http.post(metadatamanagement.managementPageList, this.searchData);
        this.tableData = res.data.data.entities || [];
        this.pageData.totalCount = res.data.data.total;
        this.loading = false;
      } catch (err) {
        console.log(err);
        this.loading = false;
      }
    },
    // 检索
    search() {
      this.searchData.params.pageNumber = 1;
      this.pageData.pageNum = 1;
      // this.searchData.searchValue = searchValue
      this.infoList();
    },
    // 查看已关联表
    infoMenu(row) {
      this.$refs.basedFieldInfoModal.open(row);
    },
    // 删除数据
    deleteItem(item) {
      // this.$Modal.confirm({
      // 	title: "警告",
      // 	content: `您将要删除字段 ${item.fieldName}，是否确认?`,
      // 	onOk: () => {
      // 		this.remove(item)
      // 	}
      // });
      this.$UiConfirm({
        content: `您将要删除字段 ${item.fieldName}，是否确认?`,
        title: '警告',
      })
        .then(() => {
          this.remove(item);
        })
        .catch((res) => {
          console.log(res);
        });
    },
    // 删除
    async remove(item) {
      try {
        let res = await this.$http.delete(metadatamanagement.managementRemove + item.id);
        if (res.data.code === 200) {
          this.$Message.success('删除成功');
          this.infoList();
        } else {
          this.$Message.error('删除失败');
        }
      } catch (err) {
        console.log(err);
      }
    },
    // 编辑or新增字段模态框
    edit(row) {
      this.$refs.basedFieldAddOrEdit.open(row, JSON.stringify(row) == '{}' ? '新增字段' : '编辑字段');
    },
    // 返回
    back() {
      // this.$emit('back')
      // this.$router.push('standardtreatment/metadatamanagement/basicelements')
    },
    changePage(val) {
      this.searchData.params.pageNumber = val;
      this.pageData.pageNum = val;
      this.infoList();
    },
    changePageSize(val) {
      this.searchData.params.pageNumber = 1;
      this.pageData.pageNum = 1;
      this.searchData.params.pageSize = val;
      this.pageData.pageSize = val;
      this.infoList();
    },
  },
  watch: {},
  computed: {},
  props: {},
  components: {
    UiTable: require('@/components/ui-table.vue').default,
    basedFieldInfoModal: require('./basedfield-infomodal').default,
    basedFieldAddOrEdit: require('./basedfield-addoredit').default,
  },
};
</script>
<style lang="less" scoped>
.based-field {
  height: 100%;
  background: var(--bg-content);
  .based-field-label {
    color: var(--color-label);
    font-size: 14px;
    padding-right: 12px;
  }
  .based-field-header {
    padding: 20px;
    .title {
      display: flex;
      .border-header {
        width: 8px;
        height: 30px;
        background: #239df9;
        margin-right: 6px;
      }
      .title-header {
        color: var(--color-title);
        width: 394px;
        height: 30px;
        line-height: 30px;
        padding-left: 10px;
        background: linear-gradient(90deg, #0a4f8d 0%, rgba(9, 40, 77, 0) 100%);
        font-size: 16px;
      }
    }
    .back {
      color: #2d8cf0;
      font-size: 16px;
      cursor: pointer;
      height: 30px;
      line-height: 30px;
      &:hover {
        color: #57a3f3;
      }
    }
  }
  .form {
    padding: 20px;
    button {
      margin-left: 12px;
    }
  }
  .left-div {
    padding: 0 20px;
    position: relative;
  }
  .ui-table {
    // padding: 0 20px;
  }
  /deep/ .ivu-divider-horizontal {
    margin: 0;
    background: #1e3f61;
  }
  .mr30 {
    margin-right: 30px !important;
  }
}
</style>
