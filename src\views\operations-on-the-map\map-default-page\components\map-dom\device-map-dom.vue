<template>
  <div class="dom">
    <section class="dom-content" v-if="!isSimpleDevice">
      <div
        class="dom-content-left"
        :class="{ 'dom-content-left-showInfo': detailVisible }"
      >
        <div class="playVideo" v-if="currentDevice.type == 'camera'">
          <h5-player
            ref="H5Player"
            sceneFrom="mapVideo"
            :options="{ layout: '1*1' }"
            :deviceObj="deviceObj"
            dbFullScreen
            v-if="isopen"
          />
        </div>
        <div class="device-info" v-else>
          <div class="title">
            <span>{{
              currentDevice.type == "vehicle"
                ? "车辆抓拍"
                : currentDevice.type == "face"
                ? "人脸抓拍"
                : "设备采集"
            }}</span>
            <div>
              <span class="num"
                >今日：
                <span>{{ total }}</span>
              </span>
              <span class="more" @click="seeMoreHandler"
                >更多
                <ui-icon type="more"></ui-icon>
              </span>
            </div>
          </div>
          <div class="wrapper">
            <div
              class="content-one"
              v-if="
                currentDevice.type == 'vehicle' || currentDevice.type == 'face'
              "
            >
              <Scroll :height="scrollHeight">
                <div class="content-one-box">
                  <img v-lazy="firstItem['sceneImg']" alt="" />
                  <div class="operate" v-if="list.length > 0">
                    <span class="ml-10">
                      <ui-icon class="mr-5" type="time" :size="16"></ui-icon>
                      <span>{{ firstItem["absTime"] }}</span>
                    </span>
                    <p class="wrap">
                      <ui-icon
                        class="mr-5"
                        type="location"
                        :size="16"
                      ></ui-icon>
                      <span v-show-tips
                        >{{ firstItem["detailAddress"] || "--" }}
                      </span>
                    </p>
                    <operate-bar
                      class="operate-bar"
                      :list="list[0]"
                      :tabType="{ type: collectType }"
                      @collection="collection($event, list[0])"
                    ></operate-bar>
                  </div>
                </div>
              </Scroll>
            </div>
            <Scroll
              v-else
              :height="scrollHeight"
              :on-reach-edge="handleReachEdge"
              :loading-text="loadingText"
            >
              <template v-if="!!total">
                <div>
                  <div
                    class="content-wrapper"
                    v-if="
                      currentDevice.type != 'camera' &&
                      currentDevice.type != 'vehicle' &&
                      currentDevice.type != 'face'
                    "
                  >
                    <div
                      class="content mr-10"
                      :key="i"
                      v-for="(item, i) in list"
                    >
                      <div class="content-left">
                        <ui-icon
                          :type="currentDevice.picUrl"
                          color="#2C86F8"
                          :size="40"
                        ></ui-icon>
                      </div>
                      <div class="content-right">
                        <div class="name">
                          <span>{{
                            currentDevice.type == "wifi"
                              ? item.mac
                              : currentDevice.type == "rfid"
                              ? item.rfidCode
                              : item.imsi
                          }}</span>
                        </div>
                        <span>
                          <ui-icon type="time" :size="14"></ui-icon>
                          <span>{{ deviceInfo.deviceName || "--" }}</span>
                        </span>
                        <p class="wrap">
                          <ui-icon type="location" :size="14"></ui-icon>
                          <span v-show-tips>{{
                            firstItem["detailAddress"] || "--"
                          }}</span>
                        </p>
                        <operate-bar
                          class="operate-bar"
                          :list="item"
                          :tabType="{
                            type:
                              collectType == 7
                                ? 10
                                : collectType == 8
                                ? 11
                                : 12,
                          }"
                          @collection="collectionListFunc($event, item)"
                        ></operate-bar>
                      </div>
                    </div>
                  </div>
                </div>
              </template>
            </Scroll>
          </div>
          <ui-loading v-if="loading" />
          <ui-empty v-if="!total && currentDevice.type != 'camera'" />
        </div>
        <div
          class="toggle"
          :class="{ hide: !detailVisible }"
          @click="detailVisible = !detailVisible"
        >
          <i class="iconfont icon-doubleright"></i>
        </div>
      </div>
      <div class="dom-content-right" v-if="detailVisible">
        <div class="point-detail">
          <div class="title">
            <span>设备信息</span>
            <div>
              <!-- 设备类型是camera时隐藏 -->
              <operate-bar
                v-if="currentDevice.type != 'camera' && list.length > 0"
                class="operate-bar"
                :list="list[0]"
                :tabType="{ type: collectType }"
                @collection="collection($event, list[0])"
              ></operate-bar>
              <!-- <ui-btn-tip class="collection-icon" v-if="deviceInfo.myFavorite == '1'" content="取消收藏" icon="icon-yishoucang" transfer @click.native="collectionTab(2)" />
              <ui-btn-tip class="collection-icon" v-else content="收藏" icon="icon-shoucang" transfer @click.native="collectionTab(1)" /> -->
            </div>
          </div>
          <div class="dom-content-detail">
            <div class="dom-content-p">
              <span class="label">点位名称</span>
              <span class="message" v-show-tips>{{
                deviceInfo.deviceName || "未知"
              }}</span>
            </div>
            <div class="dom-content-p">
              <span class="label">点位地址</span>
              <span
                class="message"
                :title="firstItem['detailAddress'] || '未知'"
                >{{ firstItem["detailAddress"] || "未知" }}</span
              >
            </div>
            <div class="dom-content-p">
              <span class="label">经度</span>
              <span class="message" v-show-tips>{{
                deviceInfo.geoPoint.lon ? deviceInfo.geoPoint.lon : "未知"
              }}</span>
            </div>
            <div class="dom-content-p">
              <span class="label">纬度</span>
              <span class="message" v-show-tips>{{
                deviceInfo.geoPoint.lat ? deviceInfo.geoPoint.lat : "未知"
              }}</span>
            </div>
            <div class="dom-content-p">
              <span class="label">联系人</span>
              <span class="message" v-show-tips>{{ "未知" }}</span>
            </div>
            <div class="dom-content-p">
              <span class="label">联系电话</span>
              <span class="message" v-show-tips>{{ "未知" }}</span>
            </div>
            <div class="dom-content-p">
              <span class="label">所属机构</span>
              <span class="message" v-show-tips>{{ "未知" }}</span>
            </div>
            <div class="dom-content-p">
              <span class="label">维护单位</span>
              <span class="message" v-show-tips>{{ "未知" }}</span>
            </div>
            <div class="dom-content-p">
              <span class="label">设备状态</span>
              <span class="message" v-show-tips>{{ "未知" }}</span>
            </div>
            <div class="dom-content-p">
              <span class="label">建设完成时间</span>
              <span class="message" v-show-tips>{{ "未知" }}</span>
            </div>
            <div class="dom-content-p">
              <span class="label">行业属性</span>
              <span class="message" v-show-tips>{{ "未知" }}</span>
            </div>
            <div class="dom-content-p">
              <span class="label">监控点位类型</span>
              <span class="message" v-show-tips>{{
                currentDevice.name || "未知"
              }}</span>
            </div>
            <div class="dom-content-p">
              <span class="label">维修情况</span>
              <span class="message" v-show-tips>{{ "未知" }}</span>
            </div>
            <div class="dom-content-p">
              <span class="label">维修统计</span>
              <span class="message" v-show-tips>{{ "未知" }}</span>
            </div>
            <div class="dom-content-p">
              <span class="label">工单ID</span>
              <span class="message" v-show-tips>{{ "未知" }}</span>
            </div>
            <div class="dom-content-p">
              <span class="label">工单名称</span>
              <span class="message" v-show-tips>{{ "未知" }}</span>
            </div>
            <div class="dom-content-p">
              <span class="label">派发单位</span>
              <span class="message" v-show-tips>{{ "未知" }}</span>
            </div>
            <div class="dom-content-p">
              <span class="label">所属机构</span>
              <span class="message" v-show-tips>{{ "未知" }}</span>
            </div>
            <div class="dom-content-p">
              <span class="label">流程环节</span>
              <span class="message" v-show-tips>{{ "未知" }}</span>
            </div>
            <div class="dom-content-p">
              <span class="label">优先级</span>
              <span class="message" v-show-tips>{{ "未知" }}</span>
            </div>
            <div class="dom-content-p">
              <span class="label">故障原因</span>
              <span class="message" v-show-tips>{{ "未知" }}</span>
            </div>
            <div class="dom-content-p">
              <span class="label">故障详情</span>
              <span class="message" v-show-tips>{{ "未知" }}</span>
            </div>
            <div class="dom-content-p">
              <span class="label">派发人</span>
              <span class="message" v-show-tips>{{ "未知" }}</span>
            </div>
            <div class="dom-content-p">
              <span class="label">处理人</span>
              <span class="message" v-show-tips>{{ "未知" }}</span>
            </div>
            <div class="dom-content-p">
              <span class="label">进度状态</span>
              <span class="message" v-show-tips>{{ "未知" }}</span>
            </div>
            <div class="dom-content-p">
              <span class="label">超期时长(s)</span>
              <span class="message" v-show-tips>{{ "未知" }}</span>
            </div>
            <div class="dom-content-p">
              <span class="label">工单时长(s)</span>
              <span class="message" v-show-tips>{{ "未知" }}</span>
            </div>
          </div>
        </div>
      </div>
    </section>
    <section v-else class="otherDevice">
      <div class="otherDevice-detail">
        <div class="otherDevice-detail-p">
          <span class="label"
            ><ui-icon
              class="mr-5"
              :type="currentDevice.picUrl"
              color="#2C86F8"
              :size="14"
            ></ui-icon
            >设备名称：</span
          >
          <span class="message" v-show-tips>{{
            deviceInfo.deviceName || "--"
          }}</span>
        </div>
        <div class="otherDevice-detail-p">
          <span class="label"
            ><ui-icon
              class="mr-5"
              type="shuxing2"
              color="#2C86F8"
              :size="14"
            ></ui-icon
            >设备类型：</span
          >
          <span class="message" v-show-tips>{{
            currentDevice.name || "--"
          }}</span>
        </div>
        <div class="otherDevice-detail-p">
          <span class="label"
            ><ui-icon
              class="mr-5"
              type="location"
              color="#2C86F8"
              :size="14"
            ></ui-icon
            >经&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;度：</span
          >
          <span class="message" v-show-tips>{{
            deviceInfo.geoPoint.lon ? deviceInfo.geoPoint.lon : "--"
          }}</span>
        </div>
        <div class="otherDevice-detail-p">
          <span class="label"
            ><ui-icon
              class="mr-5"
              type="location"
              color="#2C86F8"
              :size="14"
            ></ui-icon
            >纬&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;度：</span
          >
          <span class="message" v-show-tips>{{
            deviceInfo.geoPoint.lat ? deviceInfo.geoPoint.lat : "--"
          }}</span>
        </div>
      </div>
    </section>
  </div>
</template>
<script>
import {
  getWifiDetailByDeviceId,
  getBaseStationDetailByDeviceId,
  getDeviceDetailByDeviceId,
  getRfdiDetailByDeviceId,
} from "@/api/operationsOnTheMap";
import operateBar from "@/components/mapdom/operate-bar.vue";
import { addCollection, deleteMyFavorite } from "@/api/user";
import { queryFaceRecordSearch } from "@/api/wisdom-cloud-search";
import { vehicleRecordSearchEx } from "@/api/wisdom-cloud-search";
import { mapMutations, mapGetters } from "vuex";

export default {
  components: {
    operateBar,
  },
  props: {
    isopen: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    scrollHeight() {
      let htmlFontSize = parseFloat(
        window.document.documentElement.style.fontSize
      );
      if (!!htmlFontSize) {
        return htmlFontSize * (332 / 192);
      }
      return 332;
    },
    ...mapGetters({
      getCollectJudge: "map/getCollectJudge",
      getClickObj: "map/getClickObj",
    }),
    firstItem() {
      let item = this.list[0];
      return item || {};
    },
    currentDevice() {
      let obj = {
        1: {
          type: "camera",
          requestUrl: getDeviceDetailByDeviceId,
          picUrl: "wifi",
          name: "视频监控",
          path: "",
          collectionType: 4,
          defaultImg: require("@/assets/img/default-img/device_default.png"),
        },
        2: {
          type: "vehicle",
          requestUrl: getDeviceDetailByDeviceId,
          picUrl: "wifi",
          name: "车辆识别",
          path: "vehicleContent",
          collectionType: 6,
          defaultImg: require("@/assets/img/default-img/vehicle_default.png"),
        },
        11: {
          type: "face",
          requestUrl: getDeviceDetailByDeviceId,
          picUrl: "wifi",
          name: "人员识别",
          path: "faceContent",
          collectionType: 5,
          defaultImg: require("@/assets/img/default-img/device_default.png"),
        },
        3: {
          type: "wifi",
          requestUrl: getWifiDetailByDeviceId,
          picUrl: "wifi",
          name: "WIFI采集设备",
          path: "wifiContent",
          collectionType: 7,
          defaultImg: require("@/assets/img/default-img/image_error.png"),
        },
        4: {
          type: "electric",
          requestUrl: getBaseStationDetailByDeviceId,
          picUrl: "ZM-dianwei",
          name: "电子围栏",
          path: "electricContent",
          collectionType: 9,
          defaultImg: require("@/assets/img/default-img/image_error.png"),
        },
        5: {
          type: "rfid",
          requestUrl: getRfdiDetailByDeviceId,
          picUrl: "RFID",
          name: "RFID采集设备",
          path: "RFIDContent",
          collectionType: 8,
          defaultImg: require("@/assets/img/default-img/image_error.png"),
        },
      };
      let defaultObj = {
        type: "other",
        requestUrl: getDeviceDetailByDeviceId,
        picUrl: "wifi",
        name: "其他设备",
        path: "wifiContent",
        collectionType: 4,
        defaultImg: require("@/assets/img/default-img/image_error.png"),
      };
      let item = obj[this.deviceInfo.deviceType] || defaultObj;
      return item;
    },
    isSimpleDevice() {
      return !(
        this.currentDevice.type == "camera" ||
        this.currentDevice.type == "vehicle" ||
        this.currentDevice.type == "face"
      );
    },
  },
  watch: {
    getCollectJudge: {
      handler(val) {
        if (this.deviceInfo.id == this.getClickObj.id) {
          let collect = val % 2 == 0 ? 2 : 1;
          this.$set(this.deviceInfo, "myFavorite", collect);
        }
      },
      immediate: true,
    },
    detailVisible: {
      handler(val) {
        this.$emit("isModelDetail", val);
      },
    },
  },
  data() {
    return {
      loadingText: "加载中",
      collectionList: [{ count: 3 }, { count: 4 }, { count: 5 }, { count: 6 }],
      currentCollectionIndex: -1,
      collectType: "",
      deviceInfo: {
        geoPoint: {
          lat: "",
          lon: "",
        },
        deviceType: "0",
      },
      list: [],
      total: 0,
      deviceGbId: "",
      pageInfo: {
        pageNumber: 1,
        pageSize: 1,
      },
      imageUrls: "",
      loading: false,
      deviceObj: {},
      detailVisible: false,
    };
  },
  methods: {
    ...mapMutations({
      setCollectJudge: "map/setCollectJudge",
      setClickObj: "map/setClickObj",
    }),
    init({ deviceGbId = "" }, pointItem, name, icon, isFirst = true) {
      this.detailVisible = false;
      this.deviceGbId = deviceGbId === "" ? pointItem.deviceId : deviceGbId;
      this.list = [];
      this.handleImageUrl(pointItem);
      this.deviceInfo = pointItem;
      // 从视频地图定位入口进入 获取设备类型
      if (pointItem.type) {
        this.deviceInfo.deviceType = pointItem.type;
      }
      this.deviceInfo.myFavorite = this.getClickObj.myFavorite;
      if (isFirst) {
        this.loading = true;
        this.pageInfo.pageNumber = 1;
      }
      if (this.currentDevice.type == "camera") {
        this.collectType = "";
        return this.playVideo();
      }
      if (this.currentDevice.type == "vehicle") {
        this.collectType = 6;
        return this.vehicleContent();
      }
      if (this.currentDevice.type == "face") {
        this.collectType = 5;
        return this.faceContent();
      }

      this.collectType =
        this.currentDevice.type == "wifi"
          ? 7
          : this.currentDevice.type == "rfid"
          ? 8
          : 9;
      return this.currentDevice
        .requestUrl({ ...this.pageInfo, deviceId: this.deviceGbId })
        .then((res) => {
          const {
            data: { entities = [], total = 0 },
          } = res;
          this.list = this.list.concat(...entities);
          this.total = total;
          return res;
        })
        .catch((err) => console.log(err))
        .finally(() => {
          this.loading = false;
        });
    },
    // 摄像机，（人像抓拍）
    faceContent() {
      let { startDate, endDate } = this.currentDate();
      let params = {
        dataSource: 2,
        deviceIds: [this.deviceGbId],
        ...this.pageInfo,
        portraitType: "",
        similarity: 0.85,
        timeSlotArr: [],
        total: 0,
        startDate,
        endDate,
      };
      return queryFaceRecordSearch(params)
        .then((res) => {
          const { total, entities } = res.data;
          this.total = total;
          this.list = this.list.concat(...entities);
          return res;
        })
        .catch((err) => console.log(err))
        .finally(() => {
          this.loading = false;
        });
    },
    // 摄像机，（车辆抓拍）
    vehicleContent() {
      let { startDate, endDate } = this.currentDate();
      let params = {
        devices: [this.deviceGbId],
        features: [],
        ...this.pageInfo,
        plateBrands: [],
        selectDeviceList: [],
        similarity: 0.66,
        timeSlot: "",
        total: 0,
        urlList: ["", "", "", "", ""],
        startDate,
        endDate,
      };
      return vehicleRecordSearchEx(params)
        .then((res) => {
          const { total, entities } = res.data;
          this.total = total;
          this.list = this.list.concat(...entities);
          return res;
        })
        .catch((err) => console.log(err))
        .finally(() => {
          this.loading = false;
        });
    },
    handleImageUrl(pointItem) {
      let filedName = "imageUrls";
      this.imageUrls = "";
      "picUrl" in pointItem && !!pointItem.picUrl
        ? (filedName = "picUrl")
        : null;
      if (!!pointItem[filedName]) {
        this.imageUrls = pointItem[filedName].split(",")[0];
      }
    },
    playVideo() {
      const { deviceGbId, deviceName, geoPoint, ptzType, orgCode } =
        this.deviceInfo;
      return new Promise((resolve, reject) => {
        this.deviceObj = {
          deviceGbId,
          deviceName,
          geoPoint,
          ptzType,
          orgCode,
          devicetype: liveType,
          playType: "live",
        };
        this.loading = false;
        this.queryLog({
          muen: "时空分析",
          name: "选择设备",
          type: "4",
          remark: `查看【${deviceName}】实时视频`,
        });
        resolve({ data: true });
      });
    },
    stopVideo() {
      this.deviceObj = {};
    },
    seeMoreHandler() {
      const { path } = this.currentDevice;
      this.$util.common.openNewWindow({
        name: "search-center",
        query: {
          sectionName: path,
          deviceInfo: JSON.stringify(this.deviceInfo),
          noMenu: 1,
          noSearch: 1,
        },
      });
    },
    handleReachEdge(dir) {
      let totalPage = Math.floor(this.total / this.pageInfo.pageSize);
      // 大于0是向上滚动
      this.loadingText = "加载中";
      if (dir > 0) {
        if (this.pageInfo.pageNumber <= 1) {
          this.loadingText = "已经是第一页了";
          return;
        }
        this.pageInfo.pageNumber = this.pageInfo.pageNumber - 1;
      } else {
        if (this.pageInfo.pageNumber >= totalPage) {
          this.loadingText = "已经是最后一页了";
          return;
        }
        this.pageInfo.pageNumber = this.pageInfo.pageNumber + 1;
      }
      const { deviceGbId } = this;
      this.init({ deviceGbId }, { ...this.deviceInfo }, false);
    },
    // 收藏/取消收藏
    collection($event, item) {
      item.myFavorite = $event;
    },
    collectionListFunc($event, item) {
      item.myFavorite = $event;
    },
    collectionTab(flag) {
      let val = this.currentDevice.collectionType;
      let params = {
        favoriteObjectId: this.deviceInfo.deviceGbId,
        favoriteObjectType: val,
      };
      if (flag == 1) {
        //进行收藏
        addCollection(params).then((res) => {
          this.$Message.success("收藏成功");
          this.deviceInfo.myFavorite = flag;
          if (flag == this.getCollectJudge) {
            let changedata = flag + 2;
            this.setCollectJudge(changedata);
          } else {
            this.setCollectJudge(flag);
          }
        });
      } else {
        //取消收藏
        deleteMyFavorite(params).then((res) => {
          this.$Message.success("取消收藏成功");
          this.deviceInfo.myFavorite = flag;
          if (flag == this.getCollectJudge) {
            let changedata = flag + 2;
            this.setCollectJudge(changedata);
          } else {
            this.setCollectJudge(flag);
          }
        });
      }
    },
    // 时间
    currentDate() {
      let time = this.$dayjs().format("YYYY-MM-DD");
      let endDate = this.$dayjs().format("YYYY-MM-DD HH:mm:ss");
      let startDate = time + " 00:00:00";
      return { startDate, endDate };
    },
  },
};
</script>
<style lang="less" scoped>
.dom {
  width: 100%;
  background: #ffffff;
  box-shadow: 0px 3px 5px 0px rgba(0, 0, 0, 0.3);
  position: relative;
  &-content {
    height: 372px;
    width: 100%;
    display: flex;
    font-size: 14px;
    &-left {
      flex: 1;
      position: relative;
      max-width: 100%;
      &-left.dom-content-left-showInfo {
        max-width: calc(~"200% - 220px");
      }
      .toggle {
        position: absolute;
        top: calc(~"50% - 14px");
        right: 0;
        border-top-left-radius: 4px;
        border-bottom-left-radius: 4px;
        cursor: pointer;
        width: 16px;
        height: 28px;
        background: #2c86f8;
        color: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
        // z-index: 99;
        i {
          transform: rotate(180deg);
        }
        &.hide {
          i {
            transform: rotate(0deg);
          }
        }
      }
    }
    &-right {
      width: 650px;
    }
    .playVideo {
      height: 100%;
      width: 100%;
      padding: 20px;
    }

    .device-info {
      padding: 10px 10px 0 10px;
      position: relative;
      width: 100%;
      .content-one {
        height: 100%;
        width: 100%;
        /deep/.ivu-scroll-container {
          padding-top: 20px;
          overflow-y: auto !important;
        }
        /deep/ .ivu-scroll-content {
          height: 100%;
        }
        .content-one-box {
          height: 100%;
          width: 100%;
          margin-top: -20px;
          .operate {
            height: 30px;
            background: #ebedf1;
            display: flex;
            align-items: center;
            justify-content: space-between;
          }
          .wrap {
            max-width: 300px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
          > img {
            margin: 0 auto;
            display: block;
            height: calc(~"100% - 30px");
            max-width: 100%;
          }
        }
      }
      .content {
        display: flex;
        padding: 6px;
        height: 90px;
        background: #f9f9f9;
        margin-top: 10px;
        &-left {
          width: 78px;
          height: 78px;
          border: 1px solid #d3d7de;
          display: flex;
          align-items: center;
          justify-content: center;
          overflow: hidden; //img如果超出这个div会隐藏超出部分

          > img {
            width: 78px;
          }
        }

        &-right {
          margin-left: 10px;
          display: flex;
          flex-direction: column;
          font-size: 12px;
          position: relative;
          width: 230px;

          .name {
            font-size: 14px;
            font-weight: bold;
            color: #2c86f8;
          }
          .camersVid {
            font-size: 14px;
            font-weight: bold;
            color: #f29f4c;
          }
          .iconfont {
            margin-right: 10px;
          }
          .wrap {
            display: flex;
            align-items: center;
            // overflow: hidden;
            // text-overflow: ellipsis;
            // white-space: nowrap;
            span {
              width: 200px;
              display: inline-block;
            }
          }
          .operate-bar {
            position: absolute;
            right: -6px;
            bottom: 0;
            top: 64px;
          }
          /deep/ .ivu-tooltip-rel {
            margin-top: 3px;
          }
        }
      }
    }
    .dom-content-detail {
      display: flex;
      flex-wrap: wrap;
      .dom-content-p {
        width: 33.3%;
        line-height: 34px;
        display: flex;
        border: 1px solid #d3d7de;
        border-top: none;
        &:nth-child(3n + 2),
        &:nth-child(3n + 3) {
          border-left: none;
        }
        &:nth-child(1),
        &:nth-child(2),
        &:nth-child(3) {
          border-top: 1px solid #d3d7de;
        }
        .label {
          border-right: 1px solid #d3d7de;
          font-size: 12px;
          display: inline-block;
          font-weight: bold;
          color: rgba(0, 0, 0, 0.9);
          white-space: nowrap;
          width: 92px;
          background: #f9f9f9;
          padding: 0 5px;
        }

        .message {
          padding: 0 5px;
          font-size: 12px;
          width: 140px;
          display: inline-block;
          color: rgba(0, 0, 0, 0.9);
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
      }
    }
  }
  .otherDevice {
    padding: 10px 10px 0 10px;
    .otherDevice-detail {
      display: flex;
      flex-wrap: wrap;
      .otherDevice-detail-p {
        width: 50%;
        margin-bottom: 10px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        &:nth-child(2n + 1) {
          padding-right: 10px;
        }
      }
    }
  }
}

.title {
  font-size: 14px;
  // font-weight: bold;
  color: rgba(0, 0, 0, 0.9);
  padding-left: 9px;
  position: relative;
  height: 30px;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;

  > span {
    font-weight: bold;
  }

  .num {
    font-weight: normal;

    > span {
      font-weight: normal;
      color: #2c86f8;
    }
  }

  .more {
    cursor: pointer;
    color: rgba(0, 0, 0, 0.35);
    margin-left: 30px;
  }
}

.title:before {
  content: "";
  position: absolute;
  width: 3px;
  height: 16px;
  top: 50%;
  transform: translateY(-50%);
  left: 0;
  background: #2c86f8;
}

.content-wrapper {
  display: flex;
  flex-wrap: wrap;
}

.cover {
  pointer-events: none;
}

.point-detail {
  width: 100%;
  height: 100%;
  padding: 10px 10px 10px 0;

  .pictures {
    width: 200px;
    height: 200px;
    border: 1px solid #d3d7de;
    position: relative;
    margin-bottom: 10px;

    > img {
      width: 100%;
      height: 100%;
    }

    > div {
      position: absolute;
      width: 100%;
      height: 36px;
      line-height: 36px;
      background: rgba(0, 0, 0, 0.5);
      bottom: 0;
      color: #fff;
      display: flex;
      align-items: center;
      justify-content: center;

      > span {
        padding-left: 12px;
      }
    }
  }
}

.collection-icon {
  /deep/ .iconfont {
    font-size: 14px;
    color: #fff;
  }
  /deep/ .icon-shoucang {
    color: #888888 !important;
    text-shadow: 0px 1px 0px #e1e1e1;
  }
  /deep/ .icon-yishoucang {
    color: #f29f4c !important;
  }
}
</style>
