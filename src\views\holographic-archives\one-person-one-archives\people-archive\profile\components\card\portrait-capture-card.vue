<template>
  <div class="portrait-capture-card card-box">
    <div class="protrait-img card-border-color">
      <!-- <span class="num" v-show='type === "people" && data.quality'>{{ data.quality | filterNum }}%</span> -->
      <ui-image
        :type="type"
        :src="data.traitImg || data.sceneImg"
        alt="动态库"
        @click.native="faceDetailFn"
        v-bind="$attrs"
      />
      <!-- <ui-image viewer :type="type" :src="data.traitImg"/> -->
      <template v-if="type === 'vehiclePlate'">
        <div class="pic-tag-warning-small">主驾</div>
        <div class="license-plate">
          <div class="license-plate-small">苏A 29999</div>
        </div>
      </template>
    </div>
    <div class="portrait-capture-card-content">
      <!-- <div
        v-for="(item, $index) in options[type].info"
        :key="$index"
        class="content-li"
      >
        <div :class="options[type].icons[$index]" class="iconfont"></div>
        <div class="content-li-text ellipsis">{{ data[$index] || "--" }}</div>
      </div> -->
      <div class="content-li">
        <div class="iconfont icon-time"></div>
        <div class="content-li-text ellipsis">{{ data.absTime }}</div>
      </div>
      <div class="content-li">
        <div class="iconfont icon-location"></div>
        <div class="content-li-text ellipsis">
          {{ data.deviceName || data.captureAddress || "--" }}
        </div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    type: {
      type: String,
      default: "people",
    },
    data: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      faceShow: false,
      vehicleShow: false,
      nonMotorVehicleShow: false,
      options: {
        people: {
          traitImg: "抓拍照片",
          icons: {
            absTime: "icon-time",
            deviceName: "icon-location",
          },
          info: {
            absTime: "抓拍时间",
            deviceName: "icon-location",
          },
        },
        vehicle: {
          imgUrl: require("@/assets/img/default-img/vehicle_default.png"),
          icons: {
            absTime: "icon-time",
            deviceName: "icon-location",
          },
          info: {
            absTime: "icon-time",
            deviceName: "icon-location",
          },
        },
        "non-motor-archive": {
          imgUrl: require("@/assets/img/default-img/vehicle_default.png"),
          icons: {
            absTime: "icon-time",
            captureAddress: "icon-location",
          },
          info: {
            absTime: "icon-time",
            captureAddress: "icon-location",
          },
        },
        vehiclePlate: {
          imgUrl: require("@/assets/img/default-img/vehicle_default.png"),
          icons: {
            captureTime: "icon-time",
            captureAddress: "icon-location",
          },
          info: {
            time: "icon-time",
            address: "icon-location",
          },
        },
      },
    };
  },
  methods: {
    faceDetailFn() {
      this.$emit("detail");
    },
    handleClose(event, val) {
      event.stopPropagation();
      switch (val) {
        case "video":
          this.videoShow = false;
          break;
        case "vehicle":
          this.vehicleShow = false;
          break;
        case "nonMotorVehicleShow":
          this.nonMotorVehicleShow = false;
          break;
      }
    },
  },
};
</script>
<style lang="less" scoped>
.portrait-capture-card {
  width: 187px;
  height: 240px;
  padding: 10px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  border-radius: 4px;
  .protrait-img {
    width: 100%;
    height: 168px;
    border: 1px solid #fff;
    position: relative;
    box-sizing: border-box;
    .license-plate {
      position: absolute;
      left: 0;
      bottom: 0;
      width: 100%;
      height: 26px;
      background: rgba(0, 0, 0, 0.7);
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .num {
      position: absolute;
      z-index: 2;
      font-size: 12px;
      padding: 2px 5px;
      border-radius: 4px;
      color: #fff;
      background: linear-gradient(180deg, #5ba3ff 0%, #2c86f8 100%);
    }
  }
  .portrait-capture-card-content {
    .content-li {
      display: flex;
      margin-top: 8px;
      align-items: center;
      .iconfont {
        color: #888;
        font-size: 14px;
        margin-right: 6px;
        line-height: 14px;
      }
      .content-li-text {
        font-size: 12px;
        color: rgba(0, 0, 0, 0.8);
        line-height: 18px;
      }
    }
  }
}

.faceDetail {
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  background: rgba(0, 0, 0, 0.2);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 6;
}
.vehicleDetail {
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  background: rgba(0, 0, 0, 0.2);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 6;
}
</style>
