<template>
  <div class="map-search-box">
    <div class="map-search-input-box" :class="{ 'width-260': isSearch }">
      <Input
        placeholder="请输入场所名称"
        v-model="keyWords"
        clearable
        @on-focus="isSearch = true"
        @on-blur="isSearch = keyWords.length != 0"
        @on-clear="clearSearch"
        @on-enter="searchCommunity"
      >
        <template #append>
          <Button icon="ios-search" @click.native="searchCommunity"></Button>
        </template>
      </Input>
    </div>
    <div class="search-result" v-if="showList">
      <div
        class="search-item"
        v-for="(item, index) in resultList"
        :key="index"
        :class="{ 'active-item': selectActive == index }"
        @click="locationMapHandler(item, index)"
      >
        <div class="community-title" :title="item.name">
          {{ item.name }}
        </div>
        <div class="community-label" :title="item.secondLevelName">
          {{ item.secondLevelName }}
        </div>
      </div>
      <ui-empty v-if="resultList.length == 0 && !loading"></ui-empty>
      <ui-loading v-if="loading" />
    </div>
  </div>
</template>

<script>
import { getPlaceArchivesListAPI } from "@/api/placeArchive";
export default {
  name: "MapSearchPlace",
  data() {
    return {
      keyWords: "",
      resultList: [],
      selectActive: -1,
      showList: false,
      loading: false,
      isSearch: false,
    };
  },
  watch() {},
  methods: {
    async searchCommunity() {
      this.showList = true;
      this.loading = true;
      try {
        const res = await getPlaceArchivesListAPI({ name: this.keyWords });
        this.resultList = res?.data?.entities || [];
      } catch (e) {
        console.log(e);
      } finally {
        this.loading = false;
      }
    },
    locationMapHandler(item, index) {
      this.selectActive = index;
      this.$emit("locationMap", { ...item });
    },
    clearSearch() {
      this.showList = false;
      this.isSearch = false;
    },
  },
};
</script>

<style lang="less" scoped>
.map-search-box {
  .map-search-input-box {
    width: 180px;
    transition: width 0.3s;
    box-shadow: 0px 3px 5px 0px rgba(0, 0, 0, 0.2987);
    /deep/ .ivu-btn {
      padding-left: 0;
      padding-right: 0;
      background: white;
      border: 1px solid rgb(211, 215, 222);
      border-top-right-radius: 4px;
      border-bottom-right-radius: 4px;
      border-left: none;
      color: #808695;

      .ivu-icon {
        color: #808695;
      }
    }

    /deep/ .ivu-input {
      border-right: none;
    }

    &:hover {
      /deep/ .ivu-btn {
        border: 1px solid #248afc;
        border-left: none;
      }

      /deep/ .ivu-input {
        border: 1px solid #248afc;
        border-right: none;
      }
    }

    &:focus-within {
      /deep/ .ivu-btn {
        border: 1px solid #248afc;
        border-left: none;
      }
    }
  }
  .width-260 {
    width: 260px;
  }
  .search-result {
    position: relative;
    margin-top: 4px;
    width: 260px;
    padding: 10px 0;
    background: #ffffff;
    box-shadow: 0px 2px 4px 0px rgba(0, 21, 41, 0.12);
    border-radius: 4px 4px 4px 4px;
    height: 326px;
    overflow: auto;
    .search-item {
      width: 100%;
      padding: 7px 10px;
      display: flex;
      justify-content: space-between;
      &:hover {
        background: rgba(44, 134, 248, 0.1028);
      }

      .community-title {
        max-width: 120px;
        font-size: 14px;
        color: rgba(0, 0, 0, 0.8);
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      .community-label {
        max-width: 100px;
        background: rgba(44, 134, 248, 0.1028);
        border-radius: 4px 4px 4px 4px;
        border: 1px solid #2c86f8;
        padding: 0 6px;
        font-size: 12px;
        color: #2c86f8;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
    .active-item {
      background: rgba(44, 134, 248, 0.1028);
    }
  }
}
</style>
