<template>
  <div :class="['edit-form', !isEdit ? 'edit-form-padding' : '']" id="EditFormBubble">
    <div :class="['edit-form-header', !isEdit ? 'check-header' : '']">
      <i v-if="isEdit" class="icon-font icon-querenbianjixinxi"></i>
      <span v-if="isEdit" class="ml-5px">请确认编辑信息</span>
      <Button v-if="isEdit" type="default" class="mr-sm btn-width" @click="handleReset">取 消</Button>
      <Button v-if="isEdit" type="primary" class="btn-width" @click="handleSave">保 存</Button>
      <i v-if="!isEdit" class="icon-font icon-bianji" @click="edit"></i>
      <!--      <slot name="closeBubble"></slot>-->
      <i class="icon-font icon-guanbi close bb-modal-header-close" @click="handleReset"></i>
    </div>
    <div class="edit-form-content">
      <Form
        ref="formValidate"
        class="form-list"
        :model="formValidate"
        :rules="isEdit ? ruleValidate : {}"
        :show-message="false"
        :label-width="100"
      >
        <FormItem label="设备编码">
          <p class="devcodetext ellipsis" :title="formValidate.deviceId">
            {{ formValidate.deviceId }}
          </p>
        </FormItem>
        <FormItem label="设备名称" prop="deviceName" required>
          <Input
            v-if="isEdit"
            v-model="formValidate.deviceName"
            class="select-width"
            placeholder="请输入设备名称"
          ></Input>
          <p v-else class="devcodetext ellipsis" :title="formValidate.deviceName">
            {{ formValidate.deviceName }}
          </p>
        </FormItem>
        <FormItem label="所属单位" prop="orgCode" required>
          <api-organization-tree
            v-if="isEdit"
            class="select-width"
            :select-tree="selectOrgTree"
            @selectedTree="selectOrgCode"
            :needClearable="false"
            placeholder="请选择组织机构"
          >
          </api-organization-tree>
          <p v-else class="devcodetext ellipsis" :title="formValidate.orgName">
            {{ formValidate.orgName }}
          </p>
        </FormItem>
        <FormItem label="行政区划" prop="civilCode" required>
          <api-area-tree
            v-if="isEdit"
            class="area-tree select-width"
            :select-tree="selectAreaTree"
            @selectedTree="selectedArea"
            placeholder="请选择行政区划"
          ></api-area-tree>
          <p v-else class="devcodetext ellipsis" :title="formValidate.civilName">
            {{ formValidate.civilName }}
          </p>
        </FormItem>
        <FormItem label="经度" prop="longitude" required>
          <Input v-if="isEdit" number v-model="formValidate.longitude" class="select-width" placeholder="请输入经度">
            <i
              v-if="!locationActive && !locationLayerShow"
              slot="suffix"
              class="icon-font icon-dingwei f14"
              @click="handleLocation"
            ></i>
          </Input>
          <p v-else class="devcodetext ellipsis" :title="formValidate.longitude">
            {{ formValidate.longitude }}
          </p>
        </FormItem>
        <FormItem label="纬度" prop="latitude" required>
          <Input v-if="isEdit" v-model="formValidate.latitude" class="select-width" placeholder="请输入纬度">
            <i
              v-if="!locationActive && !locationLayerShow"
              slot="suffix"
              class="icon-font icon-dingwei f14"
              @click="handleLocation"
            ></i>
          </Input>
          <p v-else class="devcodetext ellipsis" :title="formValidate.latitude">
            {{ formValidate.latitude }}
          </p>
        </FormItem>
        <FormItem label="安装地址" prop="address">
          <Input v-if="isEdit" v-model="formValidate.address" class="select-width" placeholder="请输入安装地址"></Input>
          <p v-else class="devcodetext ellipsis" :title="formValidate.address">
            {{ formValidate.address }}
          </p>
        </FormItem>
        <FormItem label="采集区域">
          <Button v-if="isEdit" type="dashed" class="area-btn" @click="clickArea">
            {{ checkedTreeData.length ? `已选择 ${checkedTreeData.length}个` : '请选择采集区域类型' }}</Button
          >
          <p v-else class="devcodetext ellipsis" :title="sbcjqyText">
            {{ sbcjqyText }}
          </p>
        </FormItem>
        <FormItem label="安装位置" prop="roomType">
          <Select
            v-if="isEdit"
            v-model="formValidate.roomType"
            class="select-width"
            clearable
            placeholder="请选择安装位置"
          >
            <Option
              v-for="(item, index) in locationList"
              :key="index"
              :label="item.dataValue"
              :value="`${item.dataKey}`"
            >
            </Option>
          </Select>
          <p v-else class="devcodetext ellipsis">
            {{ locationList.find((item) => item.dataKey == formValidate.roomType)?.dataValue }}
          </p>
        </FormItem>
        <FormItem label="监控方位">
          <!--          <Input v-if="isEdit" v-model="formValidate.directionType" class="select-width" placeholder="请输入监控方位"></Input>-->
          <Select
            v-if="isEdit"
            v-model="formValidate.directionType"
            class="select-width"
            transfer
            filterable
            clearable
            placeholder="请选择监视方位"
          >
            <Option
              v-for="(item, index) in deviceDirectionList"
              :key="index"
              :label="item.dataValue"
              :value="`${item.dataKey}`"
            >
            </Option>
          </Select>
          <span v-else class="devcodetext">{{
            deviceDirectionList.find((item) => item.dataKey == formValidate.directionType)?.dataValue
          }}</span>
        </FormItem>
        <FormItem label="安装水平高度">
          <Input
            v-if="isEdit"
            v-model="formValidate.horizontalHeight"
            class="select-width"
            placeholder="请输入安装水平高度"
          ></Input>
          <span class="devcodetext" v-else>{{ formValidate.horizontalHeight }}</span>
        </FormItem>
        <FormItem label="空间信息异常">
          <p v-if="formValidate.errorMessage" class="font-red ellipsis" :title="formValidate.errorMessage">
            {{ formValidate.errorMessage }}
          </p>
          <!--          <Tooltip v-if="formValidate.errorMessage" :content="formValidate.errorMessage">-->
          <!--            <div class="ellipsis font-red">{{ formValidate.errorMessage }}</div>-->
          <!--          </Tooltip>-->
        </FormItem>
        <FormItem label="设备标签">
          <tags-more
            :tagList="formValidate.tagList"
            :defaultTags="5"
            placement="left-start"
            bgColor="#2B84E2"
          ></tags-more>
        </FormItem>
      </Form>
      <ul class="img-box">
        <li v-for="(item, index) in formValidate.imageUrls" :key="index" @click="lookImg(index)">
          <ui-image :key="index" :src="item" />
        </li>
      </ul>
    </div>
    <look-scene v-model="visibleScence" :img-list="formValidate.imageUrls" :view-index="viewIndex"></look-scene>
    <area-select
      v-model="areaSelectModalVisible"
      @confirm="confirmArea"
      :checkedTreeDataList="checkedTreeData"
    ></area-select>
  </div>
</template>

<script>
import BaseBubble from './BaseBubble.vue';

export default {
  extends: BaseBubble,
  data() {
    return {};
  },
};
</script>

<style lang="less" scoped>
[data-theme='dark'] {
  .edit-form-header {
    color: #29c9fb;
  }
  .devcodetext {
    color: #b4ceef;
  }
}
.edit-form-header {
  color: var(--color-primary);
}
.flex {
  display: flex;
  justify-content: center;
  align-items: center;
}
.edit-form {
  //position: relative;
  //padding-bottom: 10px;
  max-height: 520px;
  overflow-y: auto;
  &-padding {
    padding: 10px 0;
  }
  &-header {
    max-width: 360px;
    display: flex;
    align-items: center;
    padding: 15px 15px 5px;

    i {
      font-size: 16px;
    }

    span {
      display: inline-block;
      flex: 1;
      font-size: 14px;
      font-weight: 400;
    }

    .icon-zu874.close {
      font-size: 24px !important;
      color: #768192 !important;
    }
  }

  .check-header {
    display: flex;
    justify-content: flex-end;
    padding: 5px 10px !important;
  }

  &-content {
    padding: 0 5px;
    max-width: 360px;

    .form-list {
      padding: 0 5px;
    }

    .img-box {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      width: 100%;

      li {
        width: 106px;
        height: 106px;
        margin: 0 5px 10px;

        &:last-child {
          margin-right: auto;
        }
      }
    }
  }

  .btn-width {
    .flex;
    width: 48px !important;
    padding: 0 !important;
    font-size: 12px !important;
  }

  .ml-5px {
    margin-left: 5px;
  }

  .icon-dingwei {
    color: var(--color-primary) !important;
  }
}

.devcodetext {
  color: var(--color-content);
}

.font-red {
  color: var(--color-failed);
}

.area-btn {
  width: 100%;
}
.bb-modal-header-close {
  position: absolute;
  right: -20px;
  top: -20px;
  //&:before{
  //  color: #768192 !important;
  //}
}

@{_deep} .ivu-form-item {
  margin-bottom: 0px !important;
}

@{_deep} .select-width {
  width: 100% !important;

  .ivu-dropdown {
    width: 100%;
  }
}

@{_deep} .ivu-tooltip {
  width: 100% !important;
}
</style>
