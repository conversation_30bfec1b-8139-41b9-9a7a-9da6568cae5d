import evaluationoverview from '@/config/api/evaluationoverview';
import detectionResult from '@/config/api/detectionResult';
import downLoadTips from '@/mixins/download-tips';
import { superiorinjectfunc } from '@/views/disposalfeedback/cascadelist/util/proxyInterface.js';

// 级联清单mixin
export default {
  mixins: [downLoadTips],
  data() {
    return {
      exportLoading: false,
      tableLoading: false,
    };
  },
  methods: {
    //获取不合格原因下拉列表
    async mixinGetQualificationList(data) {
      let params = {
        indexId: data.indexId,
        batchId: data.batchId,
        access: 'TASK_RESULT',
        displayType: data.statisticType,
        customParameters: data.customParameters,
        orgRegionCode: data.statisticType === 'REGION' ? data.regionCode : data.orgCode,
      };
      try {
        let res = await superiorinjectfunc(
          this,
          detectionResult.getVideoUnqualifiedInfo,
          params,
          'post',
          data.cascadeId,
          {},
        );
        let options = res.data.data || [];
        return options.map((item) => {
          return { value: item.code, label: item.reason };
        });
      } catch (err) {
        console.log(err);
      }
    },

    // 获取统计
    async mixinGetStatInfo(data) {
      let params = {
        indexId: data.indexId,
        batchId: data.batchId,
        access: 'TASK_RESULT',
        displayType: data.statisticType,
        isImportant: data.isImportant,
        orgRegionCode: data.statisticType === 'REGION' ? data.regionCode : data.orgCode,
      };
      try {
        return await superiorinjectfunc(this, evaluationoverview.getStatInfo, params, 'post', data.cascadeId, {});
      } catch (err) {
        console.log(err);
      }
    },
    // 设备模式列表数据
    async mixinGetFirstModeData(data) {
      try {
        this.tableLoading = true;
        let params = {
          indexId: data.indexId,
          batchId: data.batchId,
          access: 'TASK_RESULT',
          displayType: data.statisticType,
          customParameters: data.customParameters,
          orgRegionCode: data.statisticType === 'REGION' ? data.regionCode : data.orgCode,
          pageNumber: data.pageNum,
          pageSize: data.pageSize,
        };
        return await superiorinjectfunc(this, evaluationoverview.getDetailData, params, 'post', data.cascadeId, {});
      } catch (error) {
        console.log(error);
      } finally {
        this.tableLoading = false;
      }
    },
    // 导出设备列表
    async mixinGetExport(data) {
      this.exportLoading = true;
      let params = {
        indexId: data.indexId,
        batchId: data.batchId,
        displayType: data.statisticType,
        customParameters: data.customParameters,
        ...data,
      };
      try {
        this.$_openDownloadTip();
        let res = await superiorinjectfunc(
          this,
          evaluationoverview.exportDeviceDetailData,
          params,
          'post',
          data.cascadeId,
          {},
        );
        await this.$util.common.transformBlob(res.data.data);
        this.exportLoading = false;
      } catch (err) {
        this.exportLoading = false;
      }
    },
  },
  computed: {},
};
