<template functional>
  <div class="scheme_header">
    <div class="scheme_line"></div>
    <div class="scheme_title">
      <span>{{ props.titleName }}</span>
      <slot name="content"> </slot>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    titleName: {
      type: String,
      default: '检测结果统计',
    },
  },
};
</script>
<style lang="less" scoped>
.scheme_header {
  //   height: 30px;
  line-height: 30px;
  width: 100%;
  display: inline-block;
  margin-top: 10px;
  .scheme_line {
    width: 8px;
    height: 30px;
    vertical-align: middle;
    display: inline-block;
    background: #239df9;
    opacity: 1;
  }
  .scheme_title {
    width: calc(100% - 18px);
    height: 30px;
    line-height: 30px;
    vertical-align: middle;
    display: inline-block;
    margin-left: 10px;
    padding-left: 10px;
    color: #fff;
    background: linear-gradient(90deg, #0a4f8d 0%, rgba(9, 40, 77, 0) 100%);
  }
}
</style>
