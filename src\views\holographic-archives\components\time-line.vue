<template>
  <div class="timeline-wrapper" :style="style">
    <div
      class="scroll-wrapper"
      v-infinite-scroll="handleReachBottom"
      infinite-scroll-distance="1"
    >
      <div
        class="timeline-item"
        v-for="(e, i) in timelineList"
        :key="i"
        @click="chooseTimeline(e, i)"
      >
        <div :class="['timeline-box', { chooseItem: i === currentClickIndex }]">
          <div
            :class="[
              'out-circle',
              {
                'first-circle': i === 0,
                'last-circle': +i + 1 === timelineList.length,
              },
            ]"
          >
            <div class="in-circle">
              <ui-icon
                v-if="e.dataType === 0"
                type="renlianheyan"
                :color="'#fff'"
              ></ui-icon>
              <ui-icon
                v-if="e.dataType == 1"
                type="cheliang"
                :color="'#fff'"
              ></ui-icon>
              <ui-icon
                v-if="e.dataType == 2"
                type="feijidongche1"
                :color="'#fff'"
              ></ui-icon>
              <ui-icon
                v-if="e.dataType == 3"
                type="diandongche"
                :color="'#fff'"
              ></ui-icon>
            </div>
          </div>
        </div>
        <div :class="['timeline-content', { active: i === currentClickIndex }]">
          <img v-if="e.dataType == 0" v-lazy="e.traitImg" alt="" />
          <ui-image
            v-if="e.dataType == 1"
            type="car"
            :src="e.sceneImg || e.traitImg"
            alt=""
          />
          <img
            v-if="e.dataType == 2"
            src="~@/assets/img/vehicle-1.png"
            alt=""
          />
          <img v-if="e.dataType == 3" v-lazy="e.traitImg" alt="" />
          <div>
            <div class="timeline-title" v-if="e.dataType === 1">
              {{ e.plateNo }}
            </div>
            <div class="timeline-title" v-if="e.dataType === 2">
              {{ e.deviceId }}
            </div>
            <div class="timeline-date" v-if="type == 'vehicle'">
              {{ e.absTime }}
            </div>
            <div class="timeline-date" v-else>{{ e.captureTime }}</div>
            <div class="timeline-address ellipsis">
              {{ e.captureAddress || "--" }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <ui-loading v-if="loading" />
    <ui-empty v-if="!loading && !timelineList.length" />
  </div>
</template>

<script>
import getImageCropper from "@/mixins/mixinVehicleCrop";
export default {
  name: "Timeline",
  mixins: [getImageCropper],
  props: {
    // 感知列表数据
    timelineList: {
      type: Array,
      default: [],
    },
    // 组件动态高度
    height: {
      type: Number,
      default: 0,
    },
    // 加载状态
    loading: {
      type: Boolean,
      default: false,
    },
    // 当前点击的index
    currentClickIndex: {
      type: Number,
      default: -1,
    },
    type: {
      type: String,
      default: "people",
    },
  },
  computed: {
    style() {
      let style = {};
      style.height = parseFloat(this.height) / 192 + "rem";
      style.MaxHeight = parseFloat(this.height) / 192 + "rem";
      return style;
    },
  },

  methods: {
    chooseTimeline(e, i) {
      this.$emit("chooseTimeline", i);
    },
    handleReachBottom() {
      this.$emit("handleReachBottom");
    },
  },
};
</script>

<style lang="less" scoped>
.timeline-wrapper {
  margin-top: 20px;
  padding-top: 2px;
  display: flex;
  min-height: 300px;
  flex-direction: column;
  // overflow: hidden;
  // overflow-y: auto;
  position: relative;
  .scroll-wrapper {
    height: 100%;
    overflow-y: auto;
  }
}
/deep/.ivu-scroll-wrapper {
  width: 100%;
}
.timeline-item {
  width: 100%;
  padding-right: 20px;
  display: flex;
  align-items: center;
  height: 70px;
  margin-bottom: 10px;
  cursor: pointer;
  .timeline-box {
    text-align: center;
    width: 25px;
    margin-left: 0px;
    position: relative;
    .out-circle {
      width: 22px;
      height: 22px;
      border-radius: 50%;
      background: #ea4a36;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .first-circle,
    .last-circle {
      position: relative;
    }
    .first-circle::before {
      top: -28px;
      border-bottom: 8px solid #d3d7de;
    }
    .last-circle:before {
      bottom: -45px;
      border-top: 8px solid #d3d7de;
    }
    .first-circle::before,
    .last-circle:before {
      content: "";
      width: 0;
      height: 0;
      position: absolute;
      left: 7px;
      border-left: 4px solid transparent;
      border-right: 4px solid transparent;
    }
  }
  .chooseItem {
    .out-circle {
      width: 22px;
      height: 22px;
      background: #2c86f8;
      position: relative;
      .in-circle {
        color: #2c86f8;
        left: 0px;
      }
    }
  }

  .timeline-box:before {
    content: "";
    width: 2px;
    height: 23px;
    background: #d3d7de;
    position: absolute;
    left: 10px;
    top: -23px;
  }
  .timeline-box:after {
    content: "";
    width: 2px;
    height: 46px;
    background: #d3d7de;
    position: absolute;
    left: 10px;
    top: 22px;
  }
  .timeline-content {
    box-sizing: border-box;
    height: 70px;
    width: 100%;
    text-align: left;
    margin-left: 14px;
    background: #f9f9f9;
    border: 1px solid #d3d7de;
    display: flex;
    align-items: center;
    flex-direction: row;
    > img {
      width: 70px;
      height: 100%;
    }
    /deep/ .ui-image {
      width: 70px;
      height: 100%;
    }
    > div {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      margin-left: 10px;
      max-width: 190px;
    }
    .timeline-title {
      font-size: 14px;
      color: #2c86f8;
      font-weight: 600;
      flex: 1;
    }

    .timeline-date {
      font-size: 12px;
      flex: 1;
      color: rgba(0, 0, 0, 0.35);
    }
    .timeline-desc {
      font-size: 12px;
      flex: 1;
      color: #999999;
    }
  }
  .active {
    background: rgba(44, 134, 248, 0.1);
    border: 1px solid #2c86f8;
  }
}
</style>
