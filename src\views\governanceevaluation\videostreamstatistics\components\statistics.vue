<template>
  <div class="statistics-container auto-fill">
    <div class="wrapper auto-fill" v-if="!componentName">
      <div class="title base-text-color t-center">
        <slot name="title">实时视频流离线情况统计</slot>
      </div>
      <div class="search mb-md mt-md">
        <ui-switch :data="switchData" v-model="searchData.isImportant" @on-change="onChangeSwitch"></ui-switch>
        <ui-label label="统计时间" :width="70" class="inline fr">
          <DatePicker
            :value="monthPart"
            type="month"
            format="yyyy-MM"
            placeholder="请选择统计时间"
            class="width-sm"
            @on-change="onChangeDate"
          ></DatePicker>
        </ui-label>
      </div>
      <div class="auto-fill">
        <ui-table
          class="ui-table auto-fill"
          :table-columns="tableColumns"
          :table-data="tableData"
          :loading="loading"
          full-border
        >
        </ui-table>
      </div>
    </div>
    <keep-alive v-else>
      <component :is="componentName" :columns="columns">
        <template #day="{ searchData }">
          <slot name="day" :searchData="searchData"> </slot>
        </template>
      </component>
    </keep-alive>
    <create-tabs
      ref="createTabsRef"
      :componentName="themData.componentName"
      :tabs-text="themData.text"
      @selectModule="selectModule"
      :tabs-query="activeTabsQuery"
    >
    </create-tabs>
  </div>
</template>

<script>
import dealWatch from '@/mixins/deal-watch';
import governanceevaluation from '@/config/api/governanceevaluation';
export default {
  name: 'statistics',
  mixins: [dealWatch],
  components: {
    UiTable: require('@/components/ui-table').default,
    UiSwitch: require('../components/ui-switch').default,
    DeviceDetail: require('@/views/governanceevaluation/videostreamstatistics/components/device-detail.vue').default,
    CreateTabs: require('@/components/create-tabs/create-tabs.vue').default,
  },
  props: {
    indexId: {
      required: true,
    },
    switchData: {
      required: true,
    },
    columns: {},
  },
  data() {
    return {
      monthPart: new Date(),
      searchData: {
        monthPart: '',
        isImportant: 0,
      },
      loading: false,
      tableData: [],
      tableColumns: [
        { title: '序号', type: 'index', width: 70, align: 'center' },
        { title: '行政区划', key: 'civilName', minWidth: 100, align: 'center' },
        { title: '区划编码', key: 'civilCode', minWidth: 100, align: 'center' },
        {
          title: '本月累计调阅失败天数',
          align: 'center',
          children: [
            {
              title: '1-5(天)',
              key: 'oneOffline',
              align: 'center',
              startCount: 1,
              endCount: 5,
              render: this.renderColumns,
            },
            {
              title: '6-10(天)',
              key: 'sixOffline',
              align: 'center',
              startCount: 6,
              endCount: 10,
              render: this.renderColumns,
            },
            {
              title: '11-15(天)',
              key: 'elevenOffline',
              align: 'center',
              startCount: 11,
              endCount: 15,
              render: this.renderColumns,
            },
            {
              title: '16-20(天)',
              key: 'sixteenOffline',
              align: 'center',
              startCount: 16,
              endCount: 20,
              render: this.renderColumns,
            },
            {
              title: '21-25(天)',
              key: 'twentyOneOffline',
              align: 'center',
              startCount: 21,
              endCount: 25,
              render: this.renderColumns,
            },
            {
              title: '>=26(天)',
              key: 'twentySixOffline',
              align: 'center',
              startCount: 26,
              endCount: 0,
              render: this.renderColumns,
            },
          ],
        },
      ],
      activeTabsQuery: {},
      activeTabsName: '设备详情',
      componentName: '', //如果有组件名称则显示组件，否则显示路由本身dom
      themData: {
        componentName: 'DeviceDetail', // 需要跳转的组件名
        text: '设备详情', // 跳转页面标题
      },
    };
  },
  computed: {},
  created() {
    this.setDefaultParams();
    this.initList();
  },
  activated() {
    this.startWatch(
      '$route',
      () => {
        this.getParams();
      },
      { immediate: true },
    );
  },
  methods: {
    setDefaultParams() {
      let date = new Date();
      let year = date.getFullYear();
      let month = date.getMonth() + 1;
      if (month < 10) {
        month = `0${month}`;
      }
      this.searchData.monthPart = `${year}${month}`;
    },
    getParams() {
      // 根据路由地址参数来判断加载组件或者路由本身
      this.selectModule(this.$route.query.componentName);
    },
    selectModule(name) {
      if (this.$route.query.componentName) {
        let components = name.split('-');
        this.componentName = components[0];
      } else {
        this.componentName = null;
      }
    },
    onChangeDate(val) {
      this.searchData.monthPart = val.replace('-', '');
      this.initList();
    },
    renderColumns(h, { row, column, index }) {
      return (
        <span>
          <span class={{ link: true, 'dis-link': !row.clickable }} onClick={() => this.handleClick(row, column, index)}>
            {row[column['key']]}
          </span>
        </span>
      );
    },
    handleClick(row, column) {
      if (!row.clickable) {
        return this.$Message.error('您没有此组织机构权限');
      }
      this.activeTabsQuery = {
        indexId: this.indexId,
        isImportant: this.searchData.isImportant,
        monthPart: this.searchData.monthPart,
        civilCode: row.civilCode,
        startCount: column.startCount,
        endCount: column.endCount,
      };
      this.$nextTick(() => {
        this.$refs.createTabsRef.create();
      });
    },
    onChangeSwitch() {
      this.initList();
    },
    async initList() {
      try {
        this.loading = true;
        let params = {
          indexId: Number.parseInt(this.indexId),
          isImportant: this.searchData.isImportant,
          monthPart: this.searchData.monthPart,
        };
        let {
          data: { data },
        } = await this.$http.post(governanceevaluation.getEvaluationDeviceOnlineStatisticsDayStatistics, params);
        this.tableData = data;
      } catch (e) {
        console.log(e);
      } finally {
        this.loading = false;
      }
    },
  },
};
</script>

<style lang="less" scoped>
.statistics-container {
  width: 100%;
  height: 100%;
  .wrapper {
    .title {
      font-size: 24px;
      background: var(--bg-title-card);
      padding: 8px 0;
    }
    .search {
      padding-bottom: 10px;
      border-bottom: 1px solid var(--border-color);
    }
  }

  .ui-table {
    @{_deep} .link {
      color: var(--color-primary);
      cursor: pointer;
      text-decoration: underline;
    }

    @{_deep} .dis-link {
      text-decoration: none;
    }
  }
}
</style>
