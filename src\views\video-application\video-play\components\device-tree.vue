<template>
  <div class="device-tree">
    <ui-loading v-if="loading" />
    <div class="search-top">
      <Input
        placeholder="请输入"
        v-model="searchInput"
        clearable
        @keydown.enter.native="searchName(true)"
        @on-clear="searchName(true)"
      >
        <template #append>
          <Button icon="ios-search" @click.prevent="searchName(true)"></Button>
        </template>
      </Input>
    </div>
    <div class="tree-operators-bar-header">
      <div class="tree-operators-title">资源列表：</div>
      &nbsp;
      <div
        class="tree-operators-icon-wrap"
        @mouseenter="showTreeFilter"
        @mouseleave="hideTreeFilter"
      >
        <i class="iconfont color-bule icon-loudou"></i>
        <div class="tree-filter-wrap" v-if="isTreeFilter">
          <div class="triangle"></div>
          <div class="tree-filter-wrap-content">
            <div class="tree-filter-wrap-title">
              <span>状态</span>
            </div>
            <div class="tree-filter-check-wrap">
              <Checkbox v-model="stuVAll" @on-change="changeStatusAll"
                >全选</Checkbox
              >
              <CheckboxGroup v-model="stuV" @on-change="changeStatus">
                <Checkbox label="在线"></Checkbox>
                <Checkbox label="离线"></Checkbox>
              </CheckboxGroup>
            </div>
          </div>
        </div>
      </div>
    </div>
    <xn-tree
      class="deviceTree"
      :ref="'tree'"
      :option="option"
      :label="labelFn"
      :fileOpe="!isOnlyTree ? fileOpe : []"
      :videoOpe="videoOpe"
      @dblclickNode="dblclickNode"
      @startInspect="startInspect"
      @parseInspect="parseInspect"
      @stopInspect="stopInspect"
      @clickVideoTrack="clickVideoTrack"
    ></xn-tree>

    <div class="general-search-footer" v-if="searchInput">
      <ui-page
        :simple="true"
        :show-elevator="false"
        :show-sizer="false"
        :total="total"
        countTotal
        :current="pageInfo.pageNumber"
        :page-size="pageInfo.pageSize"
        @pageChange="pageChange"
        size="small"
        show-total
      >
      </ui-page>
    </div>

    <!-- 轮询 -->
    <inspectModal
      ref="inspectModal"
      :checkedIndexs="checkedIndexs"
      @inspectStart="inspectStart"
    />

    <!-- 加入分组 -->
    <add-to-group ref="addToGroup"></add-to-group>
  </div>
</template>
<script>
import { mapGetters } from "vuex";
import {
  queryDeviceOrgTree,
  getTreeAncestors,
  queryPvgChannelList,
} from "@/api/player";
import { copyText } from "@/util/modules/common";
import inspectModal from "./inspect-modal.vue";
import inspectMixin from "../mixins/inspect-mixin";
import xnTree from "@/components/xn-tree/index.vue";
import addToGroup from "./add-to-group.vue";

export default {
  components: {
    inspectModal,
    xnTree,
    addToGroup,
  },
  data() {
    return {
      searchInput: "",
      loading: false,
      total: 0,
      pageInfo: {
        pageNumber: 1,
        pageSize: 20,
      },
      isTreeFilter: false,
      stuVAll: true,
      stuV: ["在线", "离线"],
      treeData: [],
      option: {
        id: "id",
        pId: false,
        hideIcon: true,
        selectType: null, //radio,null,checkbox
        canMove: false,
        lazyLoad: true,
        expandOnClick: true,
        autoOpen: function (d, level) {
          return level <= 2;
        },
        on: {
          loadData: async (node) => {
            this.loading = true;
            let roleParam = {
              roleId: this.userInfo.roleVoList[0].id,
              filter:
                this.userInfo.roleVoList[0].initFlag == "1" ? false : true,
            };
            return new Promise((resolve) => {
              queryDeviceOrgTree({
                orgCode: node.orgCode,
                deviceType: 1,
                isOnlineStatus: this.isOnlineStatus,
                ...roleParam,
              }).then((res) => {
                this.loading = false;
                resolve(this._formatDeviceOrgList(res));
              });
            });
          },
        },
      },
      fileOpe: [
        {
          label: "查看视频",
          show: this.$_has(["video-realTime"]),
          clickFn: (item) => {
            this.dblclickNode(item);
          },
        },
        {
          label: "历史录像",
          show: this.$_has(["video-history"]),
          clickFn: (item) => {
            this.dblclickNode(item, "vod");
          },
        },
        {
          label: "查看档案",
          show: true,
          clickFn: (item) => {
            const { href } = this.$router.resolve({
              name: "device-archive",
              query: { archiveNo: item.deviceGbId },
            });
            window.open(href, "_blank");
          },
        },
        {
          label: "加入分组",
          show: true,
          clickFn: (item) => {
            this.$refs.addToGroup.show(item.deviceId);
          },
        },
        {
          label: "查看目录",
          show: true,
          clickFn: (item) => {
            getTreeAncestors(item.deviceId).then((res) => {
              if (res.code == 200) {
                let list = res.data.data.deviceOrgList;
                let text = list.map((v) => v.orgName).join("/");
                this.$Message.info({ content: text, duration: 5 });
              } else {
                this.$Message.error(res.msg);
              }
            });
          },
        },
        {
          label: "复制名称",
          show: true,
          clickFn: (item) => {
            copyText(item.deviceName);
          },
        },
        // ,{
        // 	label: '发送工单',
        // 	show: true,
        // 	clickFn: (item) => {

        // 	}
        // },{
        // 	label: '查询工单',
        // 	show: true,
        // 	clickFn: (item) => {

        // 	}
        // }
      ],
      videoOpe: [],
    };
  },
  mixins: [inspectMixin],
  props: {
    playingDeviceIds: {
      type: Array,
      default: () => {
        return [];
      },
    },
    // 仅保留树结构供其他页面使用
    isOnlyTree: {
      type: Boolean,
      defalut: false,
    },
    // 视频追踪
    showVideoTrack: {
      type: Boolean,
      defalut: false,
    },
  },
  computed: {
    ...mapGetters({
      userInfo: "userInfo",
      ignoreDevices: "player/getIgnoreDevices",
    }),
    isOnlineStatus() {
      if (this.stuVAll) {
        return [];
      } else {
        return this.stuV.map((v) => {
          if (v == "在线") {
            return 0;
          } else {
            return 1;
          }
        });
      }
    },
    onlyOffLine() {
      return this.stuV.length == 1 && this.stuV[0] == "离线" ? true : false;
    },
  },
  watch: {
    playingDeviceIds: {
      handler(val) {
        if (this.$refs.tree.xnTree) this.$refs.tree.xnTree.refreshDom();
      },
    },
    isOnlineStatus: {
      handler(val) {
        this.searchName(true);
      },
    },
  },
  mounted() {
    this.getParentData();
    // this.getVideoOpe();
  },
  methods: {
    labelFn(data) {
      let titleClass = data.deviceId && data.isOnline == "1" ? "offline" : "";
      let playingClass =
        data.deviceId && this.playingDeviceIds.includes(data.deviceId)
          ? "playing"
          : "";
      let iconClass =
        data.deviceId && this.playingDeviceIds.includes(data.deviceId)
          ? "playing-icon"
          : !data.deviceId
          ? "icon-fenju"
          : this.getDeviceIcon(data);
      let iconColor = this.getDeviceIconColor(data);
      let ignoreClass = this.ignoreDevices.includes(data.deviceId)
        ? "ignore"
        : "";
      let onLine = `<span class="color-bule">${data.onlineTotal || 0}</span>`;
      let offLine = `<span class="color-grey">${data.offlineTotal || 0}</span>`;
      let statistics = !data.deviceId
        ? `(${this.onlyOffLine ? offLine : onLine}/${data.allTotal || 0})`
        : "";
      let operate = "";
      if (this.$_has(["video-realTime"]) && !data.isLeaf && !this.isOnlyTree) {
        if (!this.insoectNodes.find((v) => v.node.id == data.id))
          operate += `<i class="iconfont operate startInspect icon-lunxunkaishi" title="轮巡播放"></i>`;
        if (
          this.insoectNodes.find((v) => v.node.id == data.id) &&
          this.insoectNodes.find((v) => v.node.id == data.id)["parse"]
        )
          operate += `<i class="iconfont operate parseInspect icon-lunxunkaishi" title="轮巡继续"></i>`;
        if (
          this.insoectNodes.find((v) => v.node.id == data.id) &&
          !this.insoectNodes.find((v) => v.node.id == data.id)["parse"]
        )
          operate += `<i class="iconfont operate parseInspect icon-lunxunzanting" title="轮巡暂停"></i>`;
        if (this.insoectNodes.find((v) => v.node.id == data.id))
          operate += `<i class="iconfont operate stopInspect icon-lunxuntingzhi" title="轮巡停止"></i>`;
      }
      if (data.isLeaf && this.showVideoTrack) {
        operate += `<i class="iconfont operate videoTrack icon-leixing1" title="视频追踪"></i>`;
      }
      let html = `<div class="node-title ${titleClass} ${playingClass} ${ignoreClass}"">
						<i class="iconfont color-bule ${iconClass}" style="color: ${iconColor}"></i>
						<span class="label">${data.label}</span>
						<span class="statistics">${statistics}</span>
					</div>
					<div class="operateBtns">${operate}</div>
					`;
      return html;
    },
    getParentData() {
      let roleParam = {
        roleId: this.userInfo.roleVoList[0].id,
        filter: this.userInfo.roleVoList[0].initFlag == "1" ? false : true,
      };
      this.loading = true;
      let params = {
        searchKey: this.searchInput,
        deviceType: 1,
        isOnlineStatus: this.isOnlineStatus,
        ...roleParam,
      };
      if (this.searchInput) params = { ...params, ...this.pageInfo };
      queryDeviceOrgTree(params).then((res) => {
        this.total = res.data.total || 0;
        this.loading = false;
        this.treeData = this._formatDeviceOrgList(res);
        this.$refs.tree.initTree(this.treeData);
      });
    },
    getVideoOpe() {
      queryPvgChannelList().then((res) => {
        let list = res.data.sort((a, b) => {
          return b.isMaster - a.isMaster;
        });
        this.videoOpe = list.map((v) => {
          let item = {
            label: `${v.isMaster ? "主" : "备"}通道(${v.alias})`,
            clickFn: (item) => {
              let parentNode = item.$pId
                ? this.$refs.tree.xnTree.getNodeById(item.$pId)
                : {};
              let { orgCode, orgName } = { ...parentNode };
              this.$emit(
                "handlePlayByAlias",
                { ...item, orgCode, orgName, playType: "live" },
                v.alias
              );
            },
          };
          return item;
        });
      });
    },
    dblclickNode(nodeData, playType = "live") {
      if (nodeData.deviceId) {
        let parentNode = nodeData.$pId
          ? this.$refs.tree.xnTree.getNodeById(nodeData.$pId)
          : {};
        let obj = { ...nodeData };
        let { orgCode, orgName } = { ...parentNode };
        //   this.$emit('handleClick', { ...obj, orgCode, orgName, devicetype: liveType, playType })
        this.$emit("handleClick", { ...obj, devicetype: liveType, playType });
        if (playType == "live") {
          this.queryLog({
            muen: "视频中心",
            name: "视频资源",
            type: "4",
            remark: `查看【${nodeData.deviceName}】实时视频`,
          });
        }
      }
    },
    getDeviceIcon(item) {
      return Toolkits.getDeviceIconType(item);
    },
    getDeviceIconColor(item) {
      return Toolkits.getDeviceIconColor(item);
    },
    // 格式化设备、组织、统计数据
    _formatDeviceOrgList(deviceOrgResult) {
      let deviceList = deviceOrgResult.data.deviceList
        ? deviceOrgResult.data.deviceList.map((v) => {
            v.id = v.deviceId;
            v.label = v.deviceName;
            (v.isLeaf = true),
              (v.ptzType = v.deviceChildType ? v.deviceChildType : v.ptzType); // 基线视频应用这的枪机球机根据ptzType判断，蚌埠根据deviceChildType
            return v;
          })
        : [];
      let deviceOrgList = deviceOrgResult.data.deviceOrgList
        ? deviceOrgResult.data.deviceOrgList.map((v) => {
            v.label = v.orgName;
            return v;
          })
        : [];
      deviceOrgList = deviceOrgList.filter((v) => v.allTotal > 0);
      return [...deviceOrgList, ...deviceList];
    },
    searchName(isReset) {
      if (this.loading) return;
      if (isReset) this.pageInfo.pageNumber = 1;
      this.getParentData();
    },
    pageChange(pageNumber) {
      this.pageInfo.pageNumber = pageNumber;
      this.searchName();
    },
    // 展示筛选列表
    showTreeFilter() {
      this.isTreeFilter = true;
    },
    // 隐藏筛选列表
    hideTreeFilter() {
      this.isTreeFilter = false;
    },
    changeStatusAll() {
      if (this.stuVAll) {
        this.stuV = ["在线", "离线"];
      } else {
        this.stuV = [];
      }
    },
    changeStatus(data) {
      if (data.length == 2) {
        this.stuVAll = true;
      } else {
        this.stuVAll = false;
      }
    },
    clickVideoTrack(item) {
      this.$emit("clickVideoTrack", item);
    },
  },
};
</script>
<style lang="less" scoped>
.device-tree {
  @import "./style/index";
  height: 100%;
  position: relative;
  display: flex;
  flex-direction: column;
  .search-top {
    margin: 15px;
    margin-top: 0;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    /deep/ .ivu-btn {
      padding-left: 0;
      padding-right: 0;
      background: white;
      border: 1px solid rgb(211, 215, 222);
      border-top-right-radius: 4px;
      border-bottom-right-radius: 4px;
      border-left: none;
      color: #808695;

      .ivu-icon {
        color: #808695;
      }
    }

    /deep/ .ivu-input {
      border-right: none;
    }

    &:hover {
      /deep/ .ivu-btn {
        border: 1px solid #248afc;
        border-left: none;
      }

      /deep/ .ivu-input {
        border: 1px solid #248afc;
        border-right: none;
      }
    }

    &:focus-within {
      /deep/ .ivu-btn {
        border: 1px solid #248afc;
        border-left: none;
      }
    }
  }
  .group-list {
    max-height: 300px;
    overflow-y: auto;
  }
  /deep/.el-tree.is-dragging.is-drop-not-allow .el-tree-node__content {
    cursor: pointer;
  }
  .tree-operators-bar-header {
    display: flex;
    align-items: center;
    padding-left: 15px;
    /deep/ .ivu-checkbox-wrapper {
      color: #fff;
    }
    .tree-operators-icon-wrap {
      position: relative;
      .tree-filter-wrap {
        position: absolute;
        top: 28px;
        left: -80px;
        z-index: 9;
        width: 325px;
        background: rgba(44, 48, 51, 0.9);
        border-radius: 3px;
        color: var(--font-white-color);
        padding: 5px 10px;
        .triangle {
          width: 0;
          height: 0;
          overflow: hidden;
          font-size: 0;
          line-height: 0;
          border-width: 5px;
          border-style: solid;
          border-color: transparent transparent rgba(44, 48, 51, 0.9)
            transparent;
          position: absolute;
          left: 82px;
          top: -8px;
        }
        .tree-filter-wrap-content {
          .tree-filter-wrap-title {
            color: #fff;
            height: 22px;
            font-size: 14px;
            line-height: 25px;
          }
          .tree-filter-check-wrap {
            display: flex;
          }
          .xui-checkbox-group {
            display: flex;
            flex-wrap: wrap;
            .xui-checkbox {
              margin: 0 15px 0 0 !important;
              height: 25px;
              line-height: 22px;
            }
          }
        }
      }
    }
  }
  .deviceTree {
    padding: 0 10px;
    height: calc(~"100% - 100px");
  }
}
</style>
