<template>
    <div class="point-content-wrapper" :class="{'isClose': !isOpen}">
        <div class="point-content-wrapper-header">
            <div class="label">
                <div class="label-text">资源列表：</div>
            </div>
            <div class="operator" @click="()=>{$emit('update:isOpen', !isOpen)}">{{isOpen ? '收起面板' : '点击展开'}}</div>
        </div>
        <div class="point-content">
            <div v-for="(item, i) in dataList" :key="i">
                <div class="point-item" :class="{ active: current.id == item.id }" @click="chooseMapItem(item)">
                    <div class="content">
                        <i class="iconfont icon-jingli"/>
                        <span>{{ item.name }}</span>
                    </div>
                </div>
            </div>
            <ui-loading v-if="loading && isOpen" />
            <ui-empty v-if="!loading && !dataList.length" />
        </div>
        <div class="general-search-footer">
            <ui-page :simple="true" :show-elevator="false" countTotal :show-sizer="false" :total="total" :current="pageInfo.pageNumber" :page-size="pageInfo.pageSize" size="small" show-total @pageChange="handleCurrentChange"> </ui-page>
        </div>
    </div>
</template>

<script>
import { regionalScopeList } from '@/api/config'

export default {
    props: {
        //搜索条件
        searchPrams: {
            type: Object,
            default: () => { }
        },
        isOpen: {
            type: Boolean,
            default: () => false
        }
    },
    data() {
        return {
            dataList: [],
            total: 0,
            pageInfo: {
                pageNumber: 1,
                pageSize: 20
            },
            loading: false,
            current: {}
        }
    },
    created() {
        this.init()
    },
    methods: {
        init() {
            if (this.loading) return
            this.pageInfo.pageNumber = 1
            this.querySearch()
        },
        searchName() {
            if (this.loading) return
            this.pageInfo.pageNumber = 1
            this.querySearch()
        },
        querySearch() {
            this.loading = true;
            let params = {
                ...this.pageInfo,
                name: this.searchPrams.keyWords,
            }
            this.dataList = []
            regionalScopeList(params)
            .then(res => {
                if (res.code === 200) {
                    const { data: { entities = [], total = 0 } } = res
                    this.dataList = entities || [];
                    this.total = total;
                }
            })
            .catch(() => {
                this.dataList = []
            })
            .finally(() => {
                this.loading = false
            })
        },
        handleCurrentChange(val){
            if(this.pageInfo.pageNumber == val) return;
            this.pageInfo.pageNumber = val
            this.querySearch()
        },
        chooseMapItem(item) {
            this.current = item
            this.$parent.frameSelectionResultName = 'AOIFrameSelectionResult'
            this.$nextTick(() => {
                this.$parent.$refs['mapBase'] && this.$parent.$refs['mapBase'].showSelectDraw(item, false)
            })
            
        }
    }
}
</script>

<style lang="less" scoped>
.point-content-wrapper{
    height: 100%;
    position: relative;
    padding: 10px;
    padding-top: 10px!important;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    &.isClose {
        height: 40px!important;
    }
    &-header {
        height: 22px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10px;
        .label {
            font-size: 14px;
            color: #333333;
            display: flex;
            align-items: center;
        .label-text {
            font-weight: 600;
        }
        }
        .operator {
            cursor: pointer;
            &:hover {
                color: #2c86f8;
            }
        }
    }
}
.point-content {
  position: relative;
  overflow: hidden;
  overflow-y: auto;
  flex: 1;
  .point-item {
    height: 30px;
    line-height: 30px;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    .content {
      flex: 1;
      display: flex;
      padding: 0 10px;
      i {
        color: #2c86f8;
        margin-right: 5px;
      }
    }
    .operate {
        opacity: 0;
        i {
            color: #2c86f8;
            margin-right: 5px;
        }
    }
    &:hover {
        background: #f5f7fa;
        .operate {
            opacity: 1;
        }
    }
  }
  .active {
    background: rgba(44, 134, 248, 0.2);
  }
}
</style>
