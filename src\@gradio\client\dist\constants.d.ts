export declare const HOST_URL = "host";
export declare const API_URL = "predict/";
export declare const SSE_URL_V0 = "queue/join";
export declare const SSE_DATA_URL_V0 = "queue/data";
export declare const SSE_URL = "queue/data";
export declare const SSE_DATA_URL = "queue/join";
export declare const UPLOAD_URL = "upload";
export declare const LOGIN_URL = "login";
export declare const CONFIG_URL = "config";
export declare const API_INFO_URL = "info";
export declare const RUNTIME_URL = "runtime";
export declare const SLEEPTIME_URL = "sleeptime";
export declare const HEARTBEAT_URL = "heartbeat";
export declare const COMPONENT_SERVER_URL = "component_server";
export declare const RESET_URL = "reset";
export declare const CANCEL_URL = "cancel";
export declare const RAW_API_INFO_URL = "info?serialize=False";
export declare const SPACE_FETCHER_URL = "https://gradio-space-api-fetcher-v2.hf.space/api";
export declare const SPACE_URL = "https://hf.space/{}";
export declare const QUEUE_FULL_MSG = "This application is currently busy. Please try again. ";
export declare const BROKEN_CONNECTION_MSG = "Connection errored out. ";
export declare const CONFIG_ERROR_MSG = "Could not resolve app config. ";
export declare const SPACE_STATUS_ERROR_MSG = "Could not get space status. ";
export declare const API_INFO_ERROR_MSG = "Could not get API info. ";
export declare const SPACE_METADATA_ERROR_MSG = "Space metadata could not be loaded. ";
export declare const INVALID_URL_MSG = "Invalid URL. A full URL path is required.";
export declare const UNAUTHORIZED_MSG = "Not authorized to access this space. ";
export declare const INVALID_CREDENTIALS_MSG = "Invalid credentials. Could not login. ";
export declare const MISSING_CREDENTIALS_MSG = "Login credentials are required to access this space.";
export declare const NODEJS_FS_ERROR_MSG = "File system access is only available in Node.js environments";
export declare const ROOT_URL_ERROR_MSG = "Root URL not found in client config";
export declare const FILE_PROCESSING_ERROR_MSG = "Error uploading file";
//# sourceMappingURL=constants.d.ts.map