<template>
  <div class="statistic">
    共查询到 {{ total }} 条信息
    <span v-if="total > 9">,仅展示前10条</span>。 可查看
    <span class="more" @click="more">更多</span> 信息。
    <div v-for="item in list">
    <span class="more" @click="deviceArchives(item)">{{item.deviceName}}</span>
  </div>
  </div>

</template>
<script>
export default {
  props: {
    list: {
      type: Array,
      required: true,
    },
    total: {
      type: Number,
      required: true,
    },
  },
  data() {
    return {
      tableData: [],
      tableColumns: [
        { type: 'selection', width: 40, fixed: 'left', align: 'center' },
        {
          title: '序号',
          width: 50,
          align: 'center',
          fixed: 'left',
          slot: 'Index',
        },
        {
          width: 180,
          title: `${this.global.filedEnum.deviceId}`,
          key: 'deviceId',
          slot: 'deviceId',
          align: 'left',
          fixed: 'left',
          tree: true,
        },
        {
          width: 190,
          title: `${this.global.filedEnum.deviceName}`,
          key: 'deviceName',
          align: 'left',
          tooltip: true,
        }
      ],
    }
  },
  methods: {
    more() {
      this.$emit('openDeviceList')
    },
    deviceArchives(item) {
      let routeData = this.$router.resolve({
        name: 'archives',
        query: { id: item.id },
      });
      window.open(routeData.href, '_blank');
    }
  },
  components: {
  }
};
</script>
<style scoped lang="less">
.statistic {
  margin-bottom: 10px;
  .more {
    color: #2c86f8;
    cursor: pointer;
  }
}
</style>
