<template>
  <div class="statistic">
    共查询到 {{ answer.total }} 条信息
    <span v-if="answer.total > 3">,仅展示前3条</span>。 可查看
    <span class="more" @click="more">更多</span> 信息。
    <div v-if="answer.hasOpen" class="more" @click="openVideo">打开摄像头</div>
  </div>
</template>
<script>
const pageCardTypes = ["tajectory"];
export default {
  props: {
    answer: {
      type: Object,
      required: true,
    },
  },
  methods: {
    more() {
      console.log("---more", this.answer);
      let name = "";
      let param = {
        ...this.answer.param,
      };
      if (pageCardTypes.includes(this.answer.cardType))
        return this.$emit("openPage", param);
      if (this.answer.cardType === "people") name = "real-name-file";
      if (this.answer.cardType === "vehicle") name = "one-vehicle-one-archives";
      if (this.answer.cardType === "device") name = "one-plane-one-archives";
      if (this.answer.cardType === "place") name = "place-archives";
      this.$router.push({
        name: name,
        query: param,
      });
    },
    openVideo() {
      this.$emit("openVideo", this.answer.list);
    },
  },
};
</script>
<style scoped>
.statistic {
  margin-bottom: 10px;
  .more {
    color: #2c86f8;
    cursor: pointer;
  }
}
</style>
