<template>
  <div class="add-plan-dialog" v-if="isShow">
    <header class="add-plan-header">
      <div style="display: flex">
        <div class="add-plan-header-back" @click="close">
          <i class="iconfont icon-return"></i>
        </div>
        <div class="add-plan-header-info">
          <div
            class="add-plan-header-text"
            :class="{ aphiCheck: step === 1 }"
            v-if="step === 1 || step === 2 || step === 3"
          >
            分组设置
          </div>
          <div
            class="add-plan-header-text"
            :class="{ aphiCheck: step === 2 }"
            v-if="step === 2 || step === 3"
          >
            &nbsp;&gt;&nbsp;分屏设置
          </div>
          <div
            class="add-plan-header-text"
            :class="{ aphiCheck: step === 3 }"
            v-if="step === 3"
          >
            &nbsp;&gt;&nbsp;自启动设置
          </div>
        </div>
      </div>
      <div
        class="add-plan-header-toggle-wrap mop-header-foo"
        @click="toggleLy('show')"
        v-show="step === 2"
      >
        <div
          class="mop-layout-label mop-header-layout layout-value"
          :data="selectedLayout.value"
          style="display: inline-block"
        >
          <i class="iconfont" :class="selectedLayout.icon"></i>
        </div>
        <span
          class="mop-layout-text mop-header-layout"
          :title="selectedLayout.text"
          >{{ selectedLayout.text }}</span
        >
        <ul class="mop-layouts" v-show="selectedLayoutShow">
          <li class="innerbox">
            <div
              class="mop-layout"
              :title="item.text"
              :class="{ active: selectedLayout.value === item.value }"
              v-for="(item, index) in baseScreenLayoutList"
              :key="index"
              @click.stop="toggleLy(item)"
            >
              <i class="iconfont" :class="item.icon"></i>&nbsp;&nbsp;
              {{ item.text }}
            </div>
            <!-- <div style="border-top: 1px dashed #cccccc;">
                            <div class="mop-layout" :title="item.text" :class="{'active': selectedLayout.value === item.value}" v-for="(item, index) in screenLayoutListSet" :key="index" @click.stop="toggleLy(item)">
                                <i class="iconfont" :class="item.icon"></i>&nbsp;&nbsp;
                                {{ item.text }}
                            </div>
                        </div> -->
          </li>
        </ul>
        <i
          class="iconfont icon-jiantou"
          :class="{ up: selectedLayoutShow, down: !selectedLayoutShow }"
          style="margin-left: 5px; font-size: 10px; color: #a5b0b6"
        ></i>
      </div>
    </header>
    <div class="content">
      <div class="add-plan-type-wrap" v-if="step === 1">
        <div class="add-plan-type">
          <div class="add-plan-type-name">
            <div class="add-plan-type-info">
              <span style="color: red">*</span>
              <span>预案名称：</span>
            </div>
            <Input v-model="planName" placeholder="请输入预案名称"></Input>
          </div>
          <div class="add-plan-type-choose">
            <div class="add-plan-type-info">
              <span style="color: red">*</span>
              <span>预案类型：</span>
            </div>
            <RadioGroup v-model="planType" @on-change="changeHandle">
              <Radio label="1">我的预案</Radio>
              <Radio label="2">公共预案</Radio>
            </RadioGroup>
          </div>
        </div>
      </div>
      <div class="add-plan-content-wrap" v-if="step === 1">
        <div class="add-plan-content">
          <div class="add-plan-left">
            <div class="group-toggle">
              <div
                class="group-toggle-btn"
                :class="{ 'group-toggle-active': !groupType }"
                @click="toggleGroup(0)"
              >
                我的分组
              </div>
              <div
                class="group-toggle-btn"
                :class="{ 'group-toggle-active': groupType }"
                @click="toggleGroup(1)"
              >
                共享分组
              </div>
            </div>
            <Input
              class="xui-input-style-search"
              v-model="searchGroup"
              placeholder="请输入分组名称"
            ></Input>
            <div style="margin: 10px 0">
              共<span style="color: #2c86f8; font-size: 16px">{{
                childrenConf.records.length
              }}</span
              >条数据
            </div>
            <div class="right-tree" ref="rightTree">
              <div v-if="childrenConf.records.length">
                <Checkbox
                  v-model="isAll"
                  @on-change="changeAll"
                  class="right-tree-checkbox"
                ></Checkbox>
                <span>全选</span>
              </div>
              <div v-else>暂无数据</div>
              <div class="child-tree" v-scroll>
                <div
                  class="users-item"
                  v-for="(item, index) in childrenConf.records"
                  :key="index"
                >
                  <Checkbox
                    v-model="item.checked"
                    @on-change="selectChildren(item)"
                  ></Checkbox>
                  <i class="iconfont nui-iconfont i-f_z_ry"></i>
                  <span :data-id="item.id" :title="item.name">{{
                    item.name
                  }}</span>
                </div>
              </div>
            </div>
          </div>
          <div class="add-plan-right">
            <div class="choose-header">
              <div class="choose-header-text">
                <span>已选择：</span>
                <span class="choose-header-num">{{
                  selectedConf.records.length
                }}</span>
                <span>个分组</span>
              </div>
              <div style="display: flex">
                <div class="add-plan-right-btn" @click="addPatchGroup">
                  <i class="iconfont icon-tianjia1"></i>&nbsp;
                  <span>新建临时分组</span>
                </div>
                <div
                  class="add-plan-right-btn"
                  style="margin-left: 15px"
                  @click="deleteAll"
                >
                  删除全部
                </div>
              </div>
            </div>
            <div class="choose-viewport-wrap" ref="chooseViewport">
              <div v-scroll style="height: 100%">
                <div
                  class="plan-child-group"
                  v-for="(group, index2) in selectedConf.records"
                  :key="index2"
                >
                  <div
                    class="plan-child-group-header"
                    :class="{ 'plan-child-group-select': group.spread }"
                  >
                    <div
                      class="plan-child-group-info"
                      @click.stop="selectGroup(group)"
                    >
                      <div
                        class="sanjiao"
                        style="position: relative; top: 7px"
                        :class="{ select: group.spread }"
                      ></div>
                      <div
                        class="plan-child-group-info-name"
                        style="line-height: 25px"
                        :title="group.name"
                      >
                        {{ group.name }}
                      </div>
                    </div>
                    <div style="display: flex">
                      <!-- <div class="plan-screen-set" title="点击切换高标清" @click="toggleDefinitionType(group)"  v-if="group.spread">
                                                {{group.definitionType == 1 ? '标清': (group.definitionType == 2 ? '高清' : '超清')}}
                                            </div> -->
                      <!-- <div class="plan-screen-line"  v-if="group.spread"></div> -->
                      <div
                        class="plan-child-group-time"
                        style="display: flex"
                        v-if="group.spread"
                      >
                        <div style="line-height: 25px">间隔&nbsp;</div>
                        <div>
                          <input
                            type="text"
                            :disabled="disabledInput && itGroup.id !== group.id"
                            @input="inputHandler(group.stopTime, group)"
                            v-model="group.stopTime"
                          />
                        </div>
                        <div style="line-height: 25px">&nbsp;秒</div>
                      </div>
                      <div
                        style="
                          height: 25px;
                          line-height: 24px;
                          margin: 0 11px;
                          cursor: pointer;
                          font-weight: 100;
                        "
                        @click="delGroup(group)"
                      >
                        <i
                          style="font-size: 15px"
                          class="iconfont i-m_gjl_delect icon-shanchu"
                          title="删除分组"
                        ></i>
                      </div>
                    </div>
                  </div>
                  <transition name="fadedown">
                    <div class="plan-child-device-wrap" v-if="group.spread">
                      <div
                        class="plan-child-device"
                        v-for="(device, index3) in group.resourceData"
                        :key="index3"
                      >
                        <div style="display: flex">
                          <div class="plan-child-device-icon">
                            <i
                              class="iconfont"
                              :class="getIconType(device)"
                            ></i>
                          </div>
                          <div
                            class="plan-child-device-name"
                            :title="device.deviceName || device.name"
                          >
                            {{ device.deviceName || device.name }}
                          </div>
                        </div>
                        <div>{{ device.orgName || device.regionName }}</div>
                        <div>
                          <i
                            class="iconfont i-m_gjl_delect icon-shanchu"
                            @click="handleDeleteSelected(group, device)"
                          ></i>
                        </div>
                      </div>
                    </div>
                  </transition>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <add-device
        ref="addDevice"
        patch="true"
        modalName="临时分组"
        @refreshDataList="refreshDataList"
        @patchGroup="patchGroup"
      ></add-device>
      <div class="add-plan-screen-wrap" v-show="step === 2">
        <div class="add-plan-screen-left">
          <plan-list
            ref="planList"
            :planList="saveGroup"
            @updateOrder="updateOrder"
            @handleDeleteSelected="handleDeleteSelected"
            @getSecondExtend="getSecondExtend"
            isEdit="true"
          >
          </plan-list>
        </div>
        <div class="add-plan-screen-right">
          <ul
            style="width: 100%; height: 100%; position: relative"
            id="wrap"
            ref="wrap"
          >
            <li
              v-for="(item, index) in screenValue"
              :key="index"
              class="screen-item-layout"
              :style="{
                width: item.width,
                height: item.height,
                top: item.top,
                left: item.left,
                position: 'absolute',
              }"
            >
              <Select
                class="select-style"
                clearable
                v-model="item.groupId"
                placeholder="选择分组"
                :disabled="groupList.length > screenValue.length"
                @on-change="changeSelectGroup(item)"
              >
                <Option
                  :value="item.value"
                  v-for="(item, index) in selectOptions"
                  :key="index"
                  >{{ item.text }}</Option
                >
              </Select>
            </li>
          </ul>
        </div>
      </div>
      <div class="add-plan-auto-wrap" v-show="step === 3">
        <auto-start-set ref="autostartset"></auto-start-set>
      </div>
    </div>
    <div class="footer">
      <div
        class="footer-btn btn-tog"
        @click="back"
        v-if="step > 1 && !isTimeEdit"
      >
        上一步
      </div>
      <div class="footer-btn btn-tog" @click="next" v-if="step < 3">下一步</div>
      <div class="footer-btn btn-tog" @click="save" v-if="step === 3">完成</div>
      <div class="footer-btn btn-sta" @click="close">取消</div>
    </div>
  </div>
</template>

<script>
import addDevice from "../../video-play/components/add-modals.vue";
import planList from "./planList.vue";
import autoStartSet from "./autostartset.vue";
// import extendScreen from "viuaPath/base/common/extend-screen.js";
import { getPlanName, addPlan, getScreenList } from "@/api/inspectionTour.js";
import { queryMyVideoGroupList, queryVideoDeviceGroupList } from "@/api/player";
import { mapGetters } from "vuex";
export default {
  //name: "addPlan",
  data() {
    return {
      isShow: false,
      planName: "", //预案名称
      planType: "1", //预案类型
      groupType: 0, // 0 我的分组 1共享的分组
      searchGroup: "", //搜索分组名称
      isAll: false, //分组是否全选
      step: 1, //当前设置步骤 1 2 3
      childrenConf: {
        //分组数据
        containerHeight() {
          return 500; //容器高度
        },
        recordHeight: 28, //条目高度
        records: [],
      },
      selectedConf: {
        containerHeight() {
          return 665; //容器高度
        },
        recordHeight: 50, //条目高度
        records: [],
      },
      selectOptions: [],
      ownGroup: [], //我的分组
      shareGroup: [], //共享的分组
      initShareGroup: [],
      initOwnGroup: [],
      patchGroupList: [], // 临时分组
      baseScreenLayoutList: [
        {
          val: "1*2",
          text: "三分屏",
          value: 2,
          type: "A",
          icon: "icon-sanfenping1",
          row: 2,
          layout: [
            { x: 0, y: 0, w: 2, h: 1 },
            { x: 0, y: 1, w: 1, h: 1 },
            { x: 1, y: 1, w: 1, h: 1 },
          ],
        },
        {
          val: "2*1",
          text: "三分屏",
          value: 3,
          type: "A",
          icon: "icon-sanfenping2",
          row: 2,
          layout: [
            { x: 0, y: 0, w: 1, h: 2 },
            { x: 1, y: 0, w: 1, h: 1 },
            { x: 1, y: 1, w: 1, h: 1 },
          ],
        },
        {
          val: "2*2",
          text: "四分屏",
          value: 4,
          icon: "icon-sifenping",
          layout: [
            { x: 0, y: 0, w: 1, h: 1 },
            { x: 1, y: 0, w: 1, h: 1 },
            { x: 0, y: 1, w: 1, h: 1 },
            { x: 1, y: 1, w: 1, h: 1 },
          ],
          row: 2,
        },
        {
          val: "1*5",
          text: "六分屏",
          value: 6,
          type: "A",
          icon: "icon-liufenping",
          layout: [
            { x: 0, y: 0, w: 2, h: 2 },
            { x: 2, y: 0, w: 1, h: 1 },
            { x: 2, y: 1, w: 1, h: 1 },
            { x: 0, y: 2, w: 1, h: 1 },
            { x: 1, y: 2, w: 1, h: 1 },
            { x: 2, y: 2, w: 1, h: 1 },
          ],
          row: 3,
        },
        {
          val: "1*7",
          text: "八分屏",
          value: 8,
          type: "A",
          icon: "icon-bafenping",
          layout: [
            { x: 0, y: 0, w: 3, h: 3 },
            { x: 3, y: 0, w: 1, h: 1 },
            { x: 3, y: 1, w: 1, h: 1 },
            { x: 3, y: 2, w: 1, h: 1 },
            { x: 0, y: 3, w: 1, h: 1 },
            { x: 1, y: 3, w: 1, h: 1 },
            { x: 2, y: 3, w: 1, h: 1 },
            { x: 3, y: 3, w: 1, h: 1 },
          ],
          row: 4,
        },
        {
          val: "3*3",
          text: "九分屏",
          value: 9,
          icon: "icon-jiufenping",
          layout: [
            { x: 0, y: 0, w: 1, h: 1, i: "0", moved: false },
            { x: 1, y: 0, w: 1, h: 1, i: "3", moved: false },
            { x: 2, y: 0, w: 1, h: 1, i: "6", moved: false },
            { x: 0, y: 1, w: 1, h: 1, i: "1", moved: false },
            { x: 1, y: 1, w: 1, h: 1, i: "4", moved: false },
            { x: 2, y: 1, w: 1, h: 1, i: "7", moved: false },
            { x: 0, y: 2, w: 1, h: 1, i: "2", moved: false },
            { x: 1, y: 2, w: 1, h: 1, i: "5", moved: false },
            { x: 2, y: 2, w: 1, h: 1, i: "8", moved: false },
          ],
          row: 3,
        },
        {
          val: "4*4",
          text: "十六分屏",
          value: 16,
          icon: "icon-shiliufenping",
          layout: [
            { x: 0, y: 0, w: 1, h: 1, i: "0", moved: false },
            { x: 1, y: 0, w: 1, h: 1, i: "4", moved: false },
            { x: 2, y: 0, w: 1, h: 1, i: "8", moved: false },
            { x: 3, y: 0, w: 1, h: 1, i: "12", moved: false },
            { x: 0, y: 1, w: 1, h: 1, i: "1", moved: false },
            { x: 1, y: 1, w: 1, h: 1, i: "5", moved: false },
            { x: 2, y: 1, w: 1, h: 1, i: "9", moved: false },
            { x: 3, y: 1, w: 1, h: 1, i: "13", moved: false },
            { x: 0, y: 2, w: 1, h: 1, i: "2", moved: false },
            { x: 1, y: 2, w: 1, h: 1, i: "6", moved: false },
            { x: 2, y: 2, w: 1, h: 1, i: "10", moved: false },
            { x: 3, y: 2, w: 1, h: 1, i: "14", moved: false },
            { x: 0, y: 3, w: 1, h: 1, i: "3", moved: false },
            { x: 1, y: 3, w: 1, h: 1, i: "7", moved: false },
            { x: 2, y: 3, w: 1, h: 1, i: "11", moved: false },
            { x: 3, y: 3, w: 1, h: 1, i: "15", moved: false },
          ],
          row: 4,
        },
      ],
      screenLayoutList: [],
      screenLayoutListSet: [],
      screenValue: [],
      selectedLayoutShow: false,
      setScreenValue: 4,
      baseSelectedLayout: {
        val: "2*2",
        text: "四分屏",
        value: 4,
        icon: "i-4s",
        layout: [
          { x: 0, y: 0, w: 1, h: 1 },
          { x: 0, y: 1, w: 1, h: 1 },
          { x: 1, y: 0, w: 1, h: 1 },
          { x: 1, y: 1, w: 1, h: 1 },
        ],
        row: 2,
      },
      selectedLayout: {
        val: "2*2",
        text: "四分屏",
        value: 4,
        icon: "i-4s",
        layout: [
          { x: 0, y: 0, w: 1, h: 1 },
          { x: 0, y: 1, w: 1, h: 1 },
          { x: 1, y: 0, w: 1, h: 1 },
          { x: 1, y: 1, w: 1, h: 1 },
        ],
        row: 2,
      },
      saveGroup: [], //保存的预案
      selectCount: false,
      groupList: [], //用来判断分组的数量
      currentPlan: { id: "" },
      toggleLyCount: false,
      it: {},
      disabledInput: false,
      itGroup: { id: "" }, //间隔秒数操作的分组
      isTimeEdit: false, // 是否是修改时间
      isSecond: false, // 是否进入过第二部，如果进入过，第二次进入的时候不触发重设分屏
      operateLayoutList: {},
    };
  },
  computed: {
    ...mapGetters({
      userInfo: "userInfo",
    }),
  },
  methods: {
    open(plan, type = false) {
      this.isShow = true;
      if (plan.name && plan.name.indexOf("-") >= 0) {
        //当创建者编辑公共预案时进入
        //因为预案列表接口原因，前端会以用户输入的预案名+当前用户真实姓名作为最终预案名称存入后端，但对用户透明
        plan.name = plan.name.split("-")[0];
      }
      this.currentPlan = IX.clone(plan);
      this.isTimeEdit = type;
      if (plan.id) {
        this.planName = plan.name;
        this.step = 1;
        this.planType = plan.sharedState == 0 ? "1" : "2";
        this.selectedConf.records = IX.clone(
          this.switchGroupList(plan.groupList)
        );
        this.setInitGroup(IX.clone(this.switchGroupList(plan.groupList)));
        this.switchChecked(this.selectedConf.records);
        this.setScreenValue = plan.childType * 1 ? plan.childType * 1 : 4;
        this.screenLayoutList.map((item) => {
          if (plan.childType == item.value) {
            this.operateLayoutList = IX.clone(item);
            this.toggleLy(item);
          }
        });
      } else {
        this.planType = plan.planType == "own" ? "1" : "2";
      }
    },
    close() {
      this.planName = "";
      this.planType = "1";
      this.searchGroup = "";
      this.isAll = false;
      this.step = 1;
      this.ownGroup = [];
      this.shareGroup = [];
      this.patchGroupList = [];
      this.selectedConf.records = [];
      this.selectedLayoutShow = false;
      this.setScreenValue = 4;
      this.selectCount = false;
      this.toggleLyCount = false;
      this.isSecond = false;
      this.saveGroup = [];
      this.groupList = [];
      this.screenValue.map((item) => {
        delete item.groupId;
      });
      this.childrenConf.records.map((item) => {
        this.$set(item, "checked", false);
      });
      this.selectedLayout = IX.clone(this.baseSelectedLayout);
      this.isShow = false;
    },
    // 改变预案类型
    changeHandle(v) {
      console.log(v);
    },
    //切换分组
    toggleGroup(v) {
      this.groupType = v;
      this.getGroup(this.searchGroup, v);
    },
    // 全选分组
    changeAll(val) {
      if (val) {
        //this.selectedConf.records = [];
        this.childrenConf.records.map((item) => {
          this.$set(item, "checked", true);
        });
        const tar = IX.clone(this.childrenConf.records);
        tar.map(async (item) => {
          const res = await queryVideoDeviceGroupList({ groupId: item.id });
          item.resourceData = res.data.map((v) => {
            v.id = v.deviceId;
            return v;
          });
          item.stopTime = 10; //间隔时间
          item.definitionType = "2"; //高标清
          item = { spread: true, ...item };
          if (this.groupType) {
            this.shareGroup.push(item); //存放到共享的分组
          } else {
            this.ownGroup.push(item); // 存放到我的分组
          }
          if (!this.selectedConf.records.find((val) => val.id === item.id)) {
            this.selectedConf.records.push(item);
          }
        });
      } else {
        if (this.groupType) {
          this.shareGroup = [];
          const obj1 = IX.clone(this.ownGroup);
          const obj2 = IX.clone(this.patchGroupList);
          this.selectedConf.records = [...obj1, ...obj2];
        } else {
          this.ownGroup = [];
          const obj1 = IX.clone(this.shareGroup);
          const obj2 = IX.clone(this.patchGroupList);
          this.selectedConf.records = [...obj1, ...obj2];
        }
        this.childrenConf.records.map((item) => {
          this.$set(item, "checked", false);
        });
      }
    },
    // 选择单个分组
    selectChildren(item) {
      if (item) {
        if (!this.selectedConf.records.find((val) => val.id === item.id)) {
          queryVideoDeviceGroupList({ groupId: item.id }).then((res) => {
            item.resourceData = res.data.map((v) => {
              v.id = v.deviceId;
              return v;
            });
            item.stopTime = 10; //间隔时间
            item.definitionType = "2"; //高标清
            item = { spread: true, ...item };
            if (this.groupType) {
              this.shareGroup.push(item); //存放到共享的分组
            } else {
              this.ownGroup.push(item); // 存放到我的分组
            }
            this.selectedConf.records.push(item);
          });
        } else {
          this.deleteSelectedItem(item);
        }
      }
      let count = 0;
      this.childrenConf.records.map((item) => {
        item.checked && count++;
      });
      if (count == this.childrenConf.records.length) {
        this.isAll = true;
      } else {
        this.isAll = false;
      }
    },
    // 删除单个选中
    deleteSelectedItem(item) {
      this.selectedConf.records.map((val, index) => {
        if (val.id === item.id) {
          this.selectedConf.records.splice(index, 1);
        }
      });
      if (this.groupType) {
        this.shareGroup.map((val, index) => {
          if (val.id === item.id) {
            this.shareGroup.splice(index, 1);
          }
        });
      } else {
        this.ownGroup.map((val, index) => {
          if (val.id === item.id) {
            this.ownGroup.splice(index, 1);
          }
        });
      }
    },
    //删除全部
    deleteAll() {
      this.selectedConf.records = [];
      this.shareGroup = [];
      this.ownGroup = [];
      this.patchGroupList = [];
      this.childrenConf.records.map((item) => {
        this.$set(item, "checked", false);
      });
      this.isAll = false;
    },
    //删除单个设备
    handleDeleteSelected(group, item) {
      this.selectedConf.records.map((val) => {
        if (group.id === val.id) {
          val.resourceData.map((res, index) => {
            if (item.id === res.id) {
              val.resourceData.splice(index, 1);
            }
          });
        }
      });
      if (this.saveGroup.length && this.step === 2) {
        this.saveGroup[0].groupList.map((val) => {
          if (val.id === group.id) {
            val.resourceData.map((res, index) => {
              if (item.id === res.id) {
                val.resourceData.splice(index, 1);
              }
            });
          }
        });
      }
    },
    // 删除单个分组
    delGroup(group) {
      del(this.selectedConf.records);
      del(this.shareGroup);
      del(this.ownGroup);
      del(this.patchGroupList);
      this.childrenConf.records.map((item) => {
        if (item.id === group.id) {
          this.$set(item, "checked", false);
          this.isAll = false;
        }
      });
      function del(arr) {
        arr.map((item, index) => {
          if (item.id === group.id) {
            arr.splice(index, 1);
          }
        });
      }
    },
    // 选中后的分组点击
    selectGroup(item) {
      this.$set(item, "spread", !item.spread);
    },
    // 切换高标清
    toggleDefinitionType(group) {
      let definitionType = group.definitionType;
      if (definitionType == 1) {
        definitionType = "2";
      } else if (definitionType == 2) {
        definitionType = "3";
      } else if (definitionType == 3) {
        definitionType = "1";
      }
      this.$set(group, "definitionType", definitionType);
    },
    // 高标清值检查
    inputHandler(v, group) {
      this.itGroup = IX.clone(group);
      this.disabledInput = true;
      if (this.it.g) {
        clearTimeout(this.it.g);
      }
      this.it.g = setTimeout(() => {
        if (v < 10) {
          this.$set(group, "stopTime", 10);
          return this.$Message.warning("最小值为10");
        } else if (v > 300) {
          this.$set(group, "stopTime", 300);
          return this.$Message.warning("最大值为300");
        } else if (!/^[0-9]+$/.test(v + "")) {
          this.$set(group, "stopTime", 10);
          return this.$Message.warning("只能输入数字！");
        }
      }, 1000);
      setTimeout((_) => {
        this.disabledInput = false;
      }, 1100);
    },
    // 改变分屏值
    changeSelectGroup(item) {
      const source = IX.clone(this.screenValue);
      this.screenValue = [];
      source.map((val) => {
        if (val.groupId === undefined) {
          delete val.groupId;
        }
        this.screenValue.push(val);
      });
    },
    //上一步
    back() {
      this.step--;
      if (this.step === 1) {
        this.getGroup(this.searchGroup, this.groupType);
      }
      if (this.step === 2) {
        //this.setLayout();  没有必要在进行重新设置
        //this.getSecondSaveList();
      }
      console.log(this.step, "back");
    },
    // 下一步
    async next() {
      if (this.step === 1) {
        if (!this.planName) {
          return this.$Message.warning("预案名称不能为空");
        }
        if (!this.termName(this.planName)) {
          return this.$Message.warning("预案名称不能包含特殊符号");
        }
        if (this.planName.length > 20) {
          return this.$Message.warning("预案名称最大为20");
        }
        if (!this.selectedConf.records.length) {
          return this.$Message.warning("预案无分组");
        }
        if (!this.currentPlan.id || this.currentPlan.name !== this.planName) {
          try {
            let checkName = this.planName;
            if (this.planType == "2") {
              /**
               * 后端获取预案列表时无法返回用户姓名
               * 公共预案需要创建者真实姓名作为后缀以便区分
               * 因此前端会以用户输入的预案名+当前用户真实姓名作为最终预案名称存入后端
               */
              checkName = `${this.planName}-${this.userInfo.name}`;
            }
            const data = await getPlanName({
              name: `${this.planName}-`,
              id: this.currentPlan.id,
            });
            if (!data) {
              return this.$Message.warning("预案名称重复");
            }
          } catch (e) {
            console.warn(e);
          }
        }
        if (!this.currentPlan.id) {
          if (
            this.selectedConf.records &&
            this.selectedConf.records.length > this.setScreenValue
          ) {
            this.$Message.warning(
              "分组数大于分屏数，请切换分屏数大于分组数的分屏！"
            );
          }
        } else {
          let tar =
            this.operateLayoutList.layout.length > this.setScreenValue
              ? this.operateLayoutList.layout.length
              : this.setScreenValue;
          if (
            this.selectedConf.records &&
            this.selectedConf.records.length > tar
          ) {
            this.$Message.warning(
              "分组数大于分屏数，请切换分屏数大于分组数的分屏！"
            );
          }
        }
        let isGroupDevice = true;
        this.selectedConf.records.map((item) => {
          if (!item.resourceData || !item.resourceData.length) {
            isGroupDevice = false;
          }
        });
        if (!isGroupDevice) {
          return this.$Message.warning("存在没有设备的分组，请重新设置");
        }
      }
      if (this.step === 2 && !this.getThird()) {
        return this.$Message.warning("每个分组至少需要选择一次！");
      }
      this.step++;
      console.log(this.step, "next");
      if (this.step === 2) {
        if (!this.isSecond) {
          this.setLayout();
          this.isSecond = true;
        }
        setTimeout((_) => {
          this.getSecondSaveList();
        }, 100);
      }
      this.$nextTick(() => {
        if (this.step === 3 && this.currentPlan.id) {
          this.$refs.autostartset.setData(IX.clone(this.currentPlan));
        } else if (this.step === 3 && !this.currentPlan.id) {
          this.$refs.autostartset.setDefaultStatus();
        }
      });
    },
    // 新建临时分组
    addPatchGroup() {
      // this.$refs.addDevice.open({planNameList: this.selectedConf.records.map(item => item.name)});
      this.$refs.addDevice.show();
    },
    refreshDataList() {
      this.getGroup(this.searchGroup, this.groupType);
    },
    // 获取临时分组数据
    patchGroup(obj) {
      obj.stopTime = 10; //间隔时间
      obj.definitionType = "2"; //高标清
      obj.resourceData = obj.deviceList.map((v) => {
        v.id = v.deviceId;
        return v;
      });
      obj.name = obj.groupName;
      obj = { checked: true, spread: true, ...obj };
      this.patchGroupList.push(obj);
      this.selectedConf.records.push(obj);
    },
    // 获取分组 0 自己的 1别人分享的  2 自己创建 + 别人分享的
    getGroup(key, type) {
      let count = 0;
      queryMyVideoGroupList({ userId: this.userInfo.id, searchKey: key }).then(
        (res) => {
          let data = type
            ? res.data.grouplist.filter((v) => v.permission != "write")
            : res.data.grouplist.filter((v) => v.permission == "write");
          data.map((item) => {
            item.name = item.groupName;
            this.selectedConf.records.map((val) => {
              if (item.id === val.id) {
                count++;
                this.$set(item, "checked", true);
              }
            });
          });
          if (count == data.length) {
            this.isAll = true;
          } else {
            this.isAll = false;
          }
          this.childrenConf.records = data;
        }
      );
    },
    // 初始获取自己的分组和共享分组
    getInitGroup(type) {
      queryMyVideoGroupList({ userId: this.userInfo.id, searchKey: "" }).then(
        (res) => {
          if (type) {
            this.initShareGroup = IX.clone(
              res.data.grouplist
                .filter((v) => v.permission != "write")
                .map((item) => item.id)
            );
          } else {
            this.initOwnGroup = IX.clone(
              res.data.grouplist
                .filter((v) => v.permission == "write")
                .map((item) => item.id)
            );
          }
        }
      );
    },
    // 设置初始的分组类型
    setInitGroup(group = []) {
      group.map((item) => {
        if (this.initShareGroup.includes(item.id)) {
          this.shareGroup.push(item);
        } else if (this.initOwnGroup.includes(item.id)) {
          this.ownGroup.push(item);
        } else {
          this.patchGroupList.push(item);
        }
      });
    },
    // 切换分屏
    toggleLy(data) {
      this.toggleLyCount = true;
      if (typeof data === "string") {
        //展开下拉菜单
        this.selectedLayoutShow = !this.selectedLayoutShow;
      } else if (typeof data.value === "number") {
        this.selectedLayoutShow = false;
        this.setScreenValue = data.value;
        this.selectedLayout = IX.clone(data);
        this.setLayout();
      }
    },
    // 设置分屏样式
    setLayout() {
      this.screenValue = [];
      this.$nextTick((__) => {
        const baseHeight = this.$refs.wrap.clientHeight;
        const baseWidth = this.$refs.wrap.clientWidth;
        for (let i = 0; i < this.screenLayoutList.length; i++) {
          const item = this.screenLayoutList[i];
          if (item.value == this.setScreenValue) {
            for (let j = 0; j < item.layout.length; j++) {
              const val = item.layout[j];
              val.width =
                (val.w * baseWidth) / (item.row ? item.row : val.row) + "px";
              val.height =
                (val.h * baseHeight) / (item.row ? item.row : val.row) + "px";
              val.top =
                (val.y * baseHeight) / (item.row ? item.row : val.row) + "px";
              val.left =
                (val.x * baseWidth) / (item.row ? item.row : val.row) + "px";
              val.groupId = undefined;
              if (this.currentPlan.id && !this.toggleLyCount) {
                this.selectedConf.records.map((res) => {
                  if (res.screenIndex && res.screenIndex.includes(j + "")) {
                    val.groupId = res.id;
                  } else if (!res.screenIndex) {
                    delete val.groupId;
                  }
                });
              } else {
                if (val.groupId) {
                  delete val.groupId;
                }
              }
              this.screenValue.push(val);
            }
          }
        }
      });
    },
    // 获取自定义分屏数据
    async getLayout() {
      let arr = IX.clone(this.baseScreenLayoutList);
      let arr2 = [];
      const res = await getScreenList();
      const isOcx = this.isOcx; //屏蔽isOcx不支持的分屏样式
      if (!isOcx) {
        res.data.map((item, index) => {
          item.value = item.row * 10 + index;
          item.val = item.row * 1 + "*" + (item.col + "");
          item.own = true;
          item.text = item.name;
          item.icon = "i-zidingyi";
          item.type = "user";
          item.layout = JSON.parse(item.layout);
          arr.push(item);
          arr2.push(item);
        });
      } else {
        arr.map((item, index) => {
          if (item.value === 2 || item.value === 3) {
            delete arr[index];
          }
        });
        arr = arr.filter((item) => item != undefined);
      }
      this.screenLayoutList = arr;
      this.screenLayoutListSet = arr2;
    },
    // 第二步获取保存的数据
    getSecondSaveList() {
      console.log("第二步获取保存的数据");
      const id = 1;
      const name = this.planName; //预案名称
      const sharedState = this.planType == 1 ? 0 : 1; //预案类型
      const groupList = IX.clone(this.selectedConf.records); //预案分组列表
      const arr1 = [{ id, name, sharedState, groupList: IX.clone(groupList) }];
      const arr2 = [];
      this.groupList = IX.clone(groupList);
      groupList.map((item) => {
        arr2.push({ text: item.name, value: item.id });
      });
      this.selectOptions = IX.clone(arr2);
      this.saveGroup = IX.clone(arr1);
      if (!this.selectCount) {
        this.$refs.planList.clearSelect(); //展开操作
        this.$refs.planList.selectPlan(arr1[0]); //展开操作
        this.selectCount = true;
      }
      this.selectedConf.records.map((item) => {
        const screenIndex = item.screenIndex
          ? item.screenIndex.split(",").filter((value) => value)
          : [];
        screenIndex.map((value) => {
          this.screenValue[value * 1] = {
            ...this.screenValue[value * 1],
            groupId: item.id,
          };
        });
      });
    },
    // 第二步发送到扩展屏预览
    getSecondExtend() {
      const id = 0;
      const name = this.planName;
      const layout = this.selectedLayout.screen || ""; // 布局
      const layoutType = this.selectedLayout.type; // 布局类型
      const value = this.selectedLayout.value; // 布局代码
      const specification = this.selectedLayout.val; //布局规格
      const autoStart = 1;
      const groupList = IX.clone(this.selectedConf.records); //预案分组列表
      let isScreen = false;
      groupList.map((item) => {
        item.cameras = IX.clone(item.resourceData);
        item.screenIndex = this.switchScreen(item.id);
        if (item.screenIndex.split(",")[0]) {
          isScreen = true;
        }
        delete item.cameraData;
      });
      if (!isScreen) {
        return this.$Message.warning("请至少关联一个分屏");
      }
      const tourTime = [];
      const obj = {
        id,
        name,
        layout,
        layoutType,
        specification,
        autoStart,
        groupList,
        tourTime,
        value,
      };
      extendScreen.exMonitorGroupScreen(obj, () => {});
    },
    // 第三步判断第二步的数据合理性
    getThird() {
      let arr = [];
      let flag = true;
      if (this.groupList.length <= this.screenValue.length) {
        this.screenValue.map((item) => {
          if (!arr.includes(item.groupId) && item.groupId) {
            arr.push(item.groupId);
          }
        });
        arr = arr.filter((item) => item != undefined);
        if (arr.length < this.groupList.length) {
          flag = false;
        }
      }
      return flag;
    },
    // 上移下移位置获取并删除 type 1上移 0下移
    updateOrder(group, device, type) {
      let idx;
      let value;
      if (this.step === 2 && this.saveGroup.length) {
        const groupList = this.saveGroup[0].groupList;
        groupList.map((item) => {
          if (item.id === group.id) {
            item.resourceData.map((val, index) => {
              if (val.id === device.id) {
                idx = index;
                value = IX.clone(val);
                item.resourceData.splice(index, 1);
              }
            });
            if (type) {
              item.resourceData.splice(idx - 1, 0, value);
            } else {
              item.resourceData.splice(idx + 1, 0, value);
            }
          }
        });
        this.selectedConf.records = IX.clone(groupList);
      }
    },
    //保存数据
    save() {
      const id = this.currentPlan.id;
      const groupList = IX.clone(this.selectedConf.records); //预案分组列表
      if (this.planType == "2") {
        /**
         * 后端获取预案列表时无法返回用户姓名
         * 公共预案需要创建者真实姓名作为后缀以便区分
         * 因此前端会以用户输入的预案名+当前用户真实姓名作为最终预案名称存入后端
         */
        this.planName = this.planName
          ? `${this.planName.split("-")[0]}-${this.userInfo.name}`
          : "";
      }
      const name = this.planName; //预案名称
      const layout = this.selectedLayout.screen || ""; // 布局
      const layoutType = this.selectedLayout.type; // 布局类型
      const specification = this.selectedLayout.val; //布局规格
      const sharedState = this.planType == 1 ? 0 : 1; //预案类型
      const childType = this.setScreenValue; // TODO  布局ID
      const tourTime = IX.clone(this.$refs.autostartset.getAutoStartTimeData());
      const validateTimeHandler = this.$refs.autostartset.validateTimeHandler();
      if (!validateTimeHandler) {
        return;
      }
      if (
        tourTime &&
        tourTime.length &&
        tourTime[0].startTime &&
        tourTime[0].endTime
      ) {
        tourTime.map((item) => {
          //item.week = this.switchDate(item.week);
          item.startTime = item.startTime == "" ? "" : item.startTime;
          item.endTime = item.endTime == "" ? "" : item.endTime;
          item.week = item.week.length ? item.week.join(",") : "";
        });
      } else {
        tourTime.map((item) => {
          item.startTime = item.startTime ? item.startTime : "";
          item.endTime = item.endTime ? item.endTime : "";
          item.week = item.week.length ? item.week.join(",") : "";
        });
      }
      groupList.map((item, index) => {
        item.resourceData = this.switchDevice(item.resourceData);
        item.screenIndex = this.isTimeEdit
          ? this.currentPlan.groupList[index].screenIndex
          : this.switchScreen(item.id);
        delete item.cameraData;
      });
      addPlan({
        id,
        groupList,
        name,
        layout,
        layoutType,
        specification,
        sharedState,
        childType,
        tourTime,
      }).then((res) => {
        this.queryLog({
          muen: "视频中心",
          name: "视频预案",
          type: id ? "2" : "1",
          remark: `${id ? "修改" : "新增"}预案${name}`,
        });
        this.$Message.success(`${id ? "修改" : "新增"}预案 ${name} 成功`);
        if (id) {
          this.$emit("changeAutoStart", this.currentPlan, false);
        }
        setTimeout(() => {
          this.close();
          this.$emit("refreshList");
        }, 1000);
      });
    },
    // 新增数据格式处理
    switchGroupList(arr) {
      const tar = [];
      arr.map((item) => {
        item.resourceData = IX.clone(item.cameraData);
        tar.push(item);
      });
      return tar;
    },
    // 新增默认选中
    switchChecked(arr) {
      let count = 0;
      arr.map((item) => {
        this.childrenConf.records.map((val) => {
          if (item.id === val.id) {
            count++;
            this.$set(val, "checked", true);
          }
        });
      });
      if (count == this.childrenConf.records.length) {
        this.isAll = true;
      }
    },
    //转换日期汉字为数字
    switchDate(arr) {
      let tar = [];
      arr.map((item) => {
        if (item == "周一") {
          tar.push(1);
        }
        if (item == "周二") {
          tar.push(2);
        }
        if (item == "周三") {
          tar.push(3);
        }
        if (item == "周四") {
          tar.push(4);
        }
        if (item == "周五") {
          tar.push(5);
        }
        if (item == "周六") {
          tar.push(6);
        }
        if (item == "周日") {
          tar.push(0);
        }
      });
      return tar.join(",") || "";
    },
    // 设备格式处理
    switchDevice(arr = []) {
      const tar = [];
      if (arr.length) {
        arr.map((item, index) => {
          tar.push({ resourceId: item.id, order: index });
        });
      }
      return tar;
    },
    // 设备播放分屏处理
    switchScreen(id) {
      const arr = [];
      this.screenValue.map((item, index) => {
        if (item.groupId === id) {
          arr.push(index);
        }
      });
      return arr.join(",") + ",";
    },
    // 判断名称由只能汉字，数字，字母组成
    termName(str) {
      const regu = "^[0-9a-zA-Z\u4e00-\u9fa5]+$";
      const re = new RegExp(regu);
      let result = true;
      if (!re.test(str + "")) {
        result = false;
      }
      return result;
    },
    // 图标
    getIconType(camera) {
      return Toolkits.getDeviceIconType(camera);
    },
  },
  mounted() {
    this.getLayout();
    this.getInitGroup(1);
    this.getInitGroup(0);
    this.$nextTick((_) => {
      // 组件赋值
      this.getGroup(this.searchGroup, this.groupType);
    });
  },
  watch: {
    searchGroup(val) {
      this.getGroup(val, this.groupType);
    },
  },
  components: { addDevice, planList, autoStartSet },
};
</script>

<style lang="less">
@blueColor: #2c86f8;
.epl(@width) {
  width: @width;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.add-plan-dialog {
  font-size: 14px;
  width: 100%;
  height: 100%;
  background: white;
  position: absolute;
  top: 0;
  left: 0;
  border-radius: 5px;
  z-index: 999;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  .content {
    flex: 1;
    padding: 10px;
    overflow: hidden;
  }
  .add-plan-header {
    display: flex;
    justify-content: space-between;
    width: 100%;
    height: 40px;
    border-bottom: 1px solid #d3d7de;
    background: #ffffff;
    box-sizing: border-box;
    user-select: none;
    position: relative;
    .add-plan-header-back {
      cursor: pointer;
      width: 70px;
      height: 100%;
      background-color: #2c86f8;
      color: white;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      font-size: 14px;
    }
    .add-plan-header-info {
      display: flex;
      align-items: center;
      margin-left: 20px;
    }
    .aphiCheck {
      font-weight: bold;
    }
    .add-plan-header-step {
      width: 50%;
      display: flex;
      height: 50px;
      justify-items: center;
      font-size: 14px;
      margin-right: 25%;
      .add-plan-header-step-num {
        height: 32px;
        width: 32px;
        border-radius: 50%;
        border: 1px solid rgba(165, 176, 182, 0.7);
        color: #a5b0b6;
        margin: 9px 5px 0;
        text-align: center;
        line-height: 32px;
      }
      .add-plan-header-step-name {
        color: #a5b0b6;
        margin: 0 5px;
        height: 50px;
        line-height: 50px;
        white-space: nowrap;
      }
      .add-plan-header-step-line {
        width: 25%;
        height: 1px;
        background: #f6f7f8;
        margin-top: 25px;
      }
      .stepNum {
        color: #fff;
        border: 1px solid rgba(165, 176, 182, 0.7);
      }
      .stepName {
        color: #fff;
      }
      .stepLine {
        background: #ffffff;
      }
    }
    .add-plan-header-toggle-wrap {
      position: absolute;
      right: 10px;
      & > i {
        position: relative;
        bottom: 15px;
      }
    }
    .mop-header-foo {
      float: right;
      display: inline-block;
      height: 31px;
      line-height: 31px;
      color: #666666;
      margin: 4px 2px;
      cursor: pointer;
      border-left: 1px solid rgba(165, 176, 182, 0.7);
      border-right: 1px solid rgba(165, 176, 182, 0.7);
      margin-left: 3px;
      padding: 0 5px;
      background: #f1f7fc;
      border-radius: 5px;
      & > .mop-layout-label {
        border-color: @blueColor;
      }
      &:hover {
        background-color: white;
        border-left: 1px solid rgba(165, 176, 182, 0.7);
        border-right: 1px solid rgba(165, 176, 182, 0.7);
        color: @blueColor;
      }
    }
    .mop-layouts {
      position: absolute;
      top: 40px;
      left: 0;
      width: 113px;
      z-index: 11;
      .own-layout {
        color: @blueColor !important;
        background: #f1f7fc;
        height: 36px;
        line-height: 36px;
        display: flex;
        justify-content: center;
      }
      .innerbox {
        overflow-y: auto;
        overflow-x: hidden;
        height: 255px;
      }
      .innerbox::-webkit-scrollbar {
        width: 4px;
      }
      .innerbox::-webkit-scrollbar-thumb {
        border-radius: 10px;
        -webkit-box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
        background: #ffffff;
        filter: alpha(opacity=10);
        -moz-opacity: 0.1;
        -khtml-opacity: 0.1;
        opacity: 0.1;
      }
      .innerbox::-webkit-scrollbar-track {
        -webkit-box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
        background: #ffffff;
        filter: alpha(opacity=10);
        -moz-opacity: 0.1;
        -khtml-opacity: 0.1;
        opacity: 0.1;
      }
    }
    .mop-layouts {
      background: #ffffff;
      border: 1px solid rgba(165, 176, 182, 0.7);
      box-shadow: 5px 5px 0 rgba(34, 34, 34, 0.1);
    }
    .mop-layout {
      height: 36px;
      color: #666666;
      box-sizing: border-box;
      padding: 0 10px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      line-height: 36px;
      i {
        vertical-align: middle;
      }
      .mop-layout-label {
        margin: 7px 5px 0 7px;
      }
      &:hover,
      &.active {
        color: @blueColor;
        .mop-layout-label {
          color: @blueColor;
          border-color: @blueColor;
        }
      }
    }
    .mop-layout-label {
      display: inline;
      float: left;
      width: 15px;
      height: 15px;
      margin: 12px 5px 0 5px;
      line-height: 15px;
      text-align: center;
      font-family: monospace;
    }
    .mop-layout-text {
      line-height: 36px;
      display: inline-block;
      height: 36px;
      .epl(56px);
    }
    .icon-jiantou {
      &.up {
        transform: rotate(180);
      }
    }
  }
  .header-bottom {
    width: 100%;
    height: 5px;
    background: #44484c;
  }
  .add-plan-type-wrap {
    height: 65px;
    border-bottom: 1px dashed rgba(165, 176, 182, 0.7);
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    .add-plan-type {
      width: 40%;
      display: flex;
      justify-content: space-between;
      .add-plan-type-info {
        height: 30px;
        line-height: 30px;
        white-space: nowrap;
      }
      .add-plan-type-name {
        display: flex;
      }
      .add-plan-type-choose {
        display: flex;
        .xui-radio-group {
          display: inline-flex;
          align-items: center;
        }
      }
    }
  }
  .add-plan-content-wrap {
    width: 100%;
    height: calc(~"100% - 65px");
    display: flex;
    justify-content: center;
    box-sizing: border-box;
    padding-top: 15px;
    .add-plan-content {
      display: flex;
    }
    .add-plan-left {
      height: 100%;
      margin-right: 20px;
      .xui-input-style-search {
        width: 480px;
        .input-clear-icon {
          right: 25px;
        }
      }
      .group-toggle {
        display: flex;
        cursor: pointer;
        user-select: none;
        margin-bottom: 10px;
        .group-toggle-btn {
          width: 240px;
          height: 30px;
          box-sizing: border-box;
          border: 1px solid @blueColor;
          text-align: center;
          line-height: 28px;
          color: @blueColor;
        }
        .group-toggle-active {
          background: #2c86f8;
          color: #fff;
        }
      }
      .right-tree {
        height: calc(~"100% - 140px");
        width: 480px;
        padding: 10px 15px;
        box-sizing: border-box;
        .right-tree-checkbox {
          // margin-left: 6px;
          margin-bottom: 5px;
        }
        .child-tree {
          padding-left: 10px;
        }
        .xui-scroll {
          height: calc(~"100% - 15px") !important;
          .users-item {
            padding: 6px;
            span {
              text-overflow: ellipsis;
              max-width: 387px;
              overflow: hidden;
              white-space: nowrap;
              display: inline-block;
            }
          }
        }
        .xui-virtualized {
        }
      }
    }
    .add-plan-right {
      width: 600px;
      background: #f6f7f8;
      box-sizing: border-box;
      padding: 0 15px;
      .choose-header {
        width: 100%;
        display: flex;
        justify-content: space-between;
        border-bottom: 1px solid rgba(165, 176, 182, 0.7);
        padding: 10px 0;
        box-sizing: border-box;
        .iconfont {
          font-size: 15px;
        }
        .choose-header-text {
          font-size: 14px;
          font-weight: bold;
          line-height: 28px;
        }
        .choose-header-num {
          color: @blueColor;
        }
        .add-plan-right-btn {
          color: @blueColor;
          border: 1px solid @blueColor;
          border-radius: 3px;
          background: #f1f7fc;
          padding: 5px 10px;
          line-height: 16px;
          cursor: pointer;
          user-select: none;
        }
      }
      .choose-viewport-wrap {
        height: calc(~"100% - 90px");
        .xui-scroll {
          height: 100%;
        }
        .plan-child-group {
          user-select: none;
          padding: 10px 3px;
          .plan-child-group-header {
            display: flex;
            justify-content: space-between;
            .plan-child-group-info {
              display: flex;
              .sanjiao {
                width: 0;
                height: 0;
                transform-origin: 10% 50%;
                overflow: hidden;
                font-size: 0; /*是因为, 虽然宽高度为0, 但在IE6下会具有默认的 */
                line-height: 0; /* 字体大小和行高, 导致盒子呈现被撑开的长矩形 */
                border-width: 5px;
                border-style: solid; /*ie6下会出现不透明的兼容问题*/
                border-color: transparent transparent transparent #44484c;
              }
              .select {
                transform: rotateZ(90deg);
              }
              .plan-child-group-info-name {
                .epl(160px);
              }
            }
            .plan-child-group-time {
              color: #a5b0b6;
              input {
                width: 50px;
                height: 25px;
                border: 1px solid #2c86f8;
                text-align: center;
                color: @blueColor;
                border-radius: 3px;
              }
            }
            .plan-screen-set {
              height: 25px;
              line-height: 23px;
              border: 1px solid @blueColor;
              text-align: center;
              color: @blueColor;
              border-radius: 3px;
              padding: 0 7px;
              cursor: pointer;
            }
            .plan-screen-line {
              height: 15px;
              width: 1px;
              background: #a5b0b6;
              margin: 5px 15px 0;
            }
          }
          .plan-child-group-select {
            font-weight: bold;
          }
          .plan-child-device-wrap {
            margin-top: 5px;
            .plan-child-device {
              padding: 8px 3px;
              display: flex;
              align-items: center;
              justify-content: space-between;
              div {
                margin-right: 8px;
              }
              .plan-child-device-count {
                padding: 1px 8px;
                border: 1px solid @blueColor;
                border-radius: 8px;
                color: @blueColor;
                font-size: 12px;
                font-weight: 400;
              }
              .plan-child-device-icon {
                color: @blueColor;
              }
              .plan-child-device-name {
                .epl(200px);
              }
            }
            .plan-child-device:hover {
              background: rgba(70, 121, 250, 0.1);
              .i-m_gjl_delect {
                color: @blueColor;
              }
            }
          }
          .fadedown-enter-active,
          .fadedown-leave-active {
            transition: opacity 0.5s;
          }
          .fadedown-enter,
          .fadedown-leave-to {
            opacity: 0;
          }
        }
        .xui-viewport-records {
          width: 95%;
          .xui-viewport-empty {
            position: relative;
            margin-top: 50%;
            margin-left: 35%;
          }
        }
        .selected-device {
          height: 28px;
          line-height: 28px;
          font-size: 0;
          .xui-icon-empty {
            cursor: pointer;
            visibility: hidden;
          }
          & > span,
          & > i {
            display: inline-block;
            vertical-align: top;
          }
          .iconfont {
            color: @blueColor;
          }
          span {
            display: inline-block;
            width: calc(~"100% - 32px");
            font-size: 12px;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            text-indent: 5px;
          }
          &:hover {
            background-color: #ffffff;
            span {
              width: calc(~"100% - 32px");
            }
            .xui-icon-empty {
              visibility: visible;
            }
          }
        }
        .xui-scroll .scroll-bar {
          margin-right: 0;
        }
        .no-select-device {
          position: absolute;
          top: 40%;
          left: 33%;
          font-size: 12px;
          color: #a5b0b6;
        }
        .xui-virtualized {
          width: 100% !important;
          .scroll-content {
            width: 100%;
            .selected-users-item {
              width: 100%;
              .delete {
                margin-right: 5px;
              }
            }
          }
        }
        .selected-users {
          width: 250px;
          margin: 0 auto;
        }
        .selected-users-item {
          width: 250px;
          line-height: 30px;
          padding-right: 10px;
        }
        .delete {
          float: right;
          cursor: pointer;
          font-size: 15px;
        }
        .select-count {
          color: @blueColor;
        }
      }
    }
  }
  .add-plan-screen-wrap {
    display: flex;
    height: 100%;
    .add-plan-screen-left {
      width: 310px;
      height: 100%;
      box-sizing: border-box;
      .plan-list-content {
        height: 100%;
        .xui-scroll {
          height: 100%;
        }
      }
    }
    .add-plan-screen-right {
      width: calc(~"100% - 310px");
      height: 100%;
      box-sizing: border-box;
      .screen-item-layout {
        box-sizing: border-box;
        border: 1px solid rgba(165, 176, 182, 0.7);
        background: url("./bgCenter.png") no-repeat center center;
        background-color: #44484c;
        display: flex;
        justify-content: center;
        align-items: center;
        .icon_check_default {
          position: absolute;
          top: 5px;
          right: 5px;
          height: 19px;
          width: 19px;
          cursor: pointer;
          // .x-pic;
          // .x-pic-icon19-check-default;
          & > .checked {
            background-position: -619px -27px;
          }
        }
        .checked {
          background-position: -619px -27px;
        }
      }
      .screen-item {
        position: relative;
        // background: url("~src/assets/images/monitor/normal_480.png") no-repeat;
        background-size: 100% 99%;
        margin: 1px 0 0 1px;
        .icon_check_default {
          position: absolute;
          top: 5px;
          right: 5px;
          height: 19px;
          width: 19px;
          cursor: pointer;
          // .x-pic;
          // .x-pic-icon19-check-default;
          // &.checked {
          //     .x-pic-icon19-checked;
          // }
        }
        &.four-screen-item {
          width: 49.7%;
          height: 49.7%;
        }
        &.nine-screen-item {
          width: 33%;
          height: 33%;
        }
        &.sixting-screen-item {
          width: 24.7%;
          height: 24.7%;
        }
      }
      .xui-select {
        width: 105px;
        background: #ffffff;
      }
      .select-style {
        width: 105px;
      }
    }
  }
  .add-plan-auto-wrap {
    height: 100%;
    overflow: hidden;
    .xui-scroll {
      height: 100%;
    }
  }
  .footer {
    width: 100%;
    display: flex;
    justify-content: center;
    background: #ffffff;
    padding-bottom: 10px;
    user-select: none;
    height: 50px;
    padding-top: 5px;
    .footer-btn {
      width: 80px;
      height: 30px;
      box-sizing: border-box;
      text-align: center;
      line-height: 30px;
      border-radius: 3px;
      margin-right: 20px;
      cursor: pointer;
    }
    .btn-tog {
      background: @blueColor;
      color: #fff;
    }
    .btn-sta {
      border: 1px solid @blueColor;
      color: @blueColor;
      background: #ffffff;
    }
  }
}
</style>
