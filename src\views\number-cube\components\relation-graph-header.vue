<template>
  <div class="relation-graph-header">
    <!-- 保存 -->
    <div class="relation-graph-header-edit">
      <div>
        <i class="iconfont icon-baocun" title="保存"></i>
        <i class="iconfont icon-lingcun" title="另存"></i>
      </div>
      <div>
        <i class="iconfont icon-chexiao" title="撤销"></i>
        <i class="iconfont icon-huifu" title="恢复"></i>
      </div>
    </div>
    <div class="toll-border"></div>
    <!-- 操作 -->
    <div class="relation-graph-header-tool">
      <div>
        <i class="iconfont icon-lishijilu"></i>
        <div>历史记录</div>
      </div>
      <div class="triangle">
        <i class="iconfont icon-daochutupian"></i>
        <div>导出图片</div>
        <div class="menus">
          <div @click="downloadAsImage('jpg')"><i class="iconfont icon-daochutupian"></i>JPG</div>
          <div @click="downloadAsImage('png')"><i class="iconfont icon-daochutupian"></i>PNG</div>
        </div>
      </div>
      <div>
        <i class="iconfont icon-daoru"></i>
        <div>导入实体</div>
      </div>
      <div class="toll-border"></div>
      <div>
        <i class="iconfont icon-tianjia"></i>
        <div>增加实体</div>
      </div>
      <div>
        <i class="iconfont icon-shanchu2"></i>
        <div>删除实体</div>
      </div>
      <div>
        <i class="iconfont icon-guanxi"></i>
        <div>增加关系</div>
      </div>
      <div>
        <i class="iconfont icon-jiechuguanxi"></i>
        <div>删除关系</div>
      </div>
      <div class="toll-border"></div>
      <div>
        <i class="iconfont icon-biaoji"></i>
        <div>标记</div>
      </div>
      <div>
        <i class="iconfont icon-quxiaobiaoji"></i>
        <div>清除标记</div>
      </div>
      <div>
        <i class="iconfont icon-hebingshiti"></i>
        <div>合并实体</div>
      </div>
      <div>
        <i class="iconfont icon-suoding"></i>
        <div>锁定</div>
      </div>
      <div>
        <i class="iconfont icon-jiesuo"></i>
        <div>解锁</div>
      </div>
      <div class="toll-border"></div>
      <div class="triangle">
        <i class="iconfont icon-guanxishaixuan"></i>
        <div>关系筛选</div>
        <div class="menus">
          <div><i class="iconfont icon-daochutupian"></i>JPG</div>
          <div><i class="iconfont icon-daochutupian"></i>PNG</div>
        </div>
      </div>
      <div class="triangle">
        <i class="iconfont icon-shitishaixuan"></i>
        <div>实体筛选</div>
        <div class="menus">
          <div><i class="iconfont icon-daochutupian"></i>JPG</div>
          <div><i class="iconfont icon-daochutupian"></i>PNG</div>
        </div>
      </div>
      <div class="triangle">
        <i class="iconfont icon-shijianshaixuan"></i>
        <div>时间筛选</div>
        <div class="menus">
          <div><i class="iconfont icon-daochutupian"></i>JPG</div>
          <div><i class="iconfont icon-daochutupian"></i>PNG</div>
        </div>
      </div>
      <div class="toll-border"></div>
      <div>
        <i class="iconfont icon-lujingtuiyan"></i>
        <div>路径推演</div>
      </div>
      <div>
        <i class="iconfont icon-lianjiefenxi"></i>
        <div>连接分析</div>
      </div>
      <div>
        <i class="iconfont icon-shequnfenxi"></i>
        <div>社群分析</div>
      </div>
      <div class="triangle">
        <i class="iconfont icon-shenjing"></i>
        <div>{{ layoutSelect }}</div>
        <div class="menus">
          <div v-for="item in layoutList" :key="item.name" @click="layoutClick(item)" :class="{ 'layout-checked': item.name === layoutSelect }"><i class="iconfont" :class="item.icon"></i>{{ item.name }}</div>
        </div>
      </div>
    </div>
    <!-- 检索/切换 -->
    <div class="relation-graph-header-search">
      <Input class="input-width" suffix="ios-search" placeholder="查找实体" />
      <RadioGroup v-model="value" type="button" button-style="solid">
        <Radio label="0">对象视图</Radio>
        <Radio label="1">关系视图</Radio>
      </RadioGroup>
    </div>
  </div>
</template>
<script>
import { format } from 'echarts'
export default {
  components: {},
  props: {},
  data() {
    return {
      value: '0',
      layoutSelect: '网络布局',
      layoutList: [
        { name: '网络布局', icon: 'icon-shenjing' },
        { name: '层次布局', icon: 'icon-buju' },
        { name: '环形布局', icon: 'icon-huanxingbuju' },
        { name: '辐射布局', icon: 'icon-ico_fushebuju' },
        { name: '同心圆布局', icon: 'icon-ico_tongxinyuanbuju' },
        { name: '格子布局', icon: 'icon-gezi' }
      ]
    }
  },
  computed: {},
  watch: {},
  filter: {},
  created() {},
  mounted() {},
  methods: {
    // 导出图片
    downloadAsImage(format) {
      this.$emit('downloadAsImage', format)
    },
    // 布局
    layoutClick(item) {
      this.layoutSelect = item.name
    }
  }
}
</script>
<style lang="less" scoped>
.relation-graph-header {
  height: 60px;
  background: #ebedf1;
  border-radius: 4px 4px 0 0;
  display: flex;
  flex-wrap: nowrap;
  padding-top: 4px;
  padding-bottom: 4px;
  .toll-border {
    border-left: 1px solid #d3d7de;
    height: 48px;
    padding: 0px !important;
  }
  .relation-graph-header-edit {
    margin-left: 8px;
    margin-right: 4px;
    margin-top: 6px;
    i {
      margin-right: 4px;
      color: #333333;
      font-size: 14px;
      cursor: pointer;
      padding: 4px;
      &:hover {
        color: #2c86f8 !important;
        background: rgba(44, 134, 248, 0.1);
        border-radius: 4px;
      }
    }
  }
  .relation-graph-header-tool {
    display: flex;
    flex-wrap: nowrap;
    margin-left: 8px;
    & > div {
      text-align: center;
      margin-right: 8px;
      font-size: 12px;
      color: rgba(0, 0, 0, 0.6);
      padding: 4px;
      cursor: pointer;
      i {
        color: #333333;
        font-size: 18px;
      }
    }
    & > .triangle {
      word-break: keep-all;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      width: auto;
      display: block;
      background: linear-gradient(-38deg, #888888 4%, transparent 4%);
      .menus {
        position: absolute;
        padding: 10px 0;
        width: 120px;
        background: #ffffff;
        box-shadow: 0px 3px 5px 0px rgba(0, 0, 0, 0.3);
        border-radius: 4px;
        z-index: 99;
        margin-top: 4px;
        display: none;
        & > div {
          height: 30px;
          padding-left: 16px;
          line-height: 30px;
          cursor: pointer;
          font-size: 14px;
          text-align: left;
          color: rgba(0, 0, 0, 0.9);
          i > {
            margin-right: 10px;
          }
          &:hover {
            background: #2c86f8;
            color: #fff;
            i {
              color: #fff;
            }
          }
        }
      }
    }
    & > .triangle:hover {
      word-break: keep-all;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      width: auto;
      display: block;
      border-bottom-right-radius: 0px;
      background: linear-gradient(-38deg, #2c86f8 4%, rgba(44, 134, 248, 0.1) 4%);
      .menus {
        display: block;
        &:hover {
          display: block;
        }
      }
    }
    & > div:hover {
      background: rgba(44, 134, 248, 0.1);
      border-radius: 4px;
      color: #2c86f8 !important;
      & > i {
        color: #2c86f8 !important;
      }
    }
  }
  .layout-checked {
    background: #2c86f8;
    color: #fff !important;
    i {
      color: #fff !important;
    }
  }
  .relation-graph-header-search {
    margin-top: 10px;
    position: absolute;
    right: 20px;
    .input-width {
      width: 250px;
      border-radius: 4px;
      margin-right: 20px;
    }
    /deep/ .ivu-radio-group-item {
      color: #2d8cf0;
    }
    /deep/ .ivu-radio-wrapper-checked {
      color: #fff;
    }
    /deep/ .ivu-radio {
      margin: 0;
    }
  }
}
</style>
