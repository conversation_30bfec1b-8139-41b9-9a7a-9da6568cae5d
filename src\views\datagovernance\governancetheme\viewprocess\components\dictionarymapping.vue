<template>
  <div class="dictionarymapping">
    <ui-modal v-model="visible" title="字典映射" width="81.25rem" :footerHide="true" @onCancel="onCancel">
      <div class="dictionarymapping-wrap">
        <modal-left @newMapping="newMapping">
          <operate-tree
            :loading="treeLoading"
            :isEdit="isEdit"
            :mappedData="mappedData"
            :distData="distData"
            @getDictList="getDictList"
            @setData="setData"
            @updatamapData="getMappedList"
            @delete="deleteDict"
          ></operate-tree>
        </modal-left>
        <dictionary-left
          v-if="isHandle"
          :accessData="mappedData"
          :mapdictdData="mapdictdData"
          :dictTypeData="dictTypeData"
          :editData="editData"
          :isEdit="isEdit"
          :kafkaValue="kafkaValue"
          :fields="fields"
          :topicId="topicId"
          :leftLoading="leftLoading"
          @getfieldData="getfieldData"
          @close="close"
          style="flex: 1"
        ></dictionary-left>
        <div v-if="!isHandle" class="empty-wrap">
          <div class="no-data">
            <i class="no-data-img icon-font icon-zanwushuju1"></i>
            <div class="null-data-text">暂无数据</div>
          </div>
        </div>
      </div>
    </ui-modal>
  </div>
</template>
<script>
import DictionaryLeft from '../../components/dictionary.vue';
import ModalLeft from '../../components/modal-left.vue';
import OperateTree from '../../components/operate-tree.vue';
import governancetheme from '@/config/api/governancetheme';
import user from '@/config/api/user';
export default {
  props: {
    topicType: {
      type: String,
      default: '',
    },
    topicId: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      visible: false,
      title: '字段映射',
      tablenames: [{ tablename: 'table1' }, { tablename: 'table2' }],
      active: '',
      isHandle: false,
      mappedData: [],
      accessData: [],
      distData: [],
      mapdictdData: [],
      isEdit: false,
      dictTypeData: [],
      editData: [],
      kafkaValue: '', // 编辑时接入数据表名
      fields: {}, // 编辑时选中的字段数据
      leftLoading: false,
      treeLoading: false,
      dictval: '',
    };
  },
  created() {},
  methods: {
    init() {
      this.visible = true;
      this.isHandle = false;
      this.getMappedList();
      this.getDictTypeData();
    },
    // 新建映射
    newMapping() {
      this.isHandle = true;
      this.isEdit = false;
      this.accessData = [];
      this.mapdictdData = [];
      this.editData = [];
      this.kafkaValue = '';
      this.fields = {};
      this.getMappedList();
    },
    // 获取已映射列表
    async getMappedList() {
      try {
        let params = {
          topicId: this.topicId, // 主题ID
          isMapping: '1', // 是否映射
        };
        let res = await this.$http.get(governancetheme.getTopicTransferByMapping, { params });
        this.mappedData = res.data.data;
      } catch (error) {
        console.log(error);
      }
    },
    // 获取已映射数据表里面的字段 || id: 接入数据表ID
    async getDictList(val) {
      // this.treeLoading = true;
      this.dictval = val;
      let arr = val.split(',');
      this.kafkaValue = arr[0] + ',' + arr[1];
      this.getKafkaPropertyList(arr[0]);
      try {
        let params = {
          topicTransferId: arr[0],
        };
        let res = await this.$http.get(governancetheme.queryTransferDictList, {
          params,
        });
        this.distData = res.data.data;
        // this.treeLoading = false;
      } catch (error) {
        console.log(error);
      }
    },
    // 查询字典映射数据列表
    async getDictData(id) {
      try {
        let params = {
          transferDictId: id, // 	字典映射id
        };
        let res = await this.$http.get(governancetheme.queryTransferDictDataList, { params });
        this.editData = res.data.data;
        this.leftLoading = false;
      } catch (error) {
        console.log(error);
      }
    },
    setData(val) {
      this.isHandle = true;
      this.isEdit = true;
      this.leftLoading = true;
      this.getDictData(val.id);
      this.fields = val;
    },
    getfieldData(val) {
      const selectVal = val.split(',');
      this.getKafkaPropertyList(selectVal[0]);
    },
    // 获取字典类型
    async getDictTypeData() {
      try {
        let res = await this.$http.get(user.getDictType);
        this.dictTypeData = res.data.data;
      } catch (error) {
        console.log(error);
      }
    },
    close() {
      this.isEdit = false;
      this.isHandle = false;
      this.accessData = [];
      this.mapdictdData = [];
      this.editData = [];
      this.kafkaValue = '';
      this.fields = {};
    },
    async getKafkaPropertyList(topic) {
      try {
        let params = {
          topicTransferId: topic,
        };
        let res = await this.$http.post(governancetheme.queryKafkaPropertyList, params);
        this.mapdictdData = res.data.data;
      } catch (error) {
        console.log(error);
      }
    },
    onCancel() {
      this.close();
      this.$emit('render');
    },
    async deleteDict(dictItem) {
      try {
        let config = {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        };
        let form = new FormData();
        form.append('transferDictId', dictItem.id);
        await this.$http.post(governancetheme.deleteTransferDict, form, config);
        this.$Message.success('该字典删除成功！');
        this.getDictList(this.dictval);
        this.isHandle = false;
      } catch (error) {
        console.log(error);
      }
    },
  },
  watch: {},
  components: { ModalLeft, OperateTree, DictionaryLeft },
};
</script>
<style lang="less" scoped>
.align-flex {
  display: flex;
  align-items: center;
}
.dictionarymapping {
  &-wrap {
    width: 100%;
    height: 630px;
    display: flex;
  }
  ul {
    width: 100%;
    height: 100%;
    // overflow-y: auto;
    li {
      height: 33px;
      padding: 0 20px;
      .align-flex;
      justify-content: space-between;
      &:hover,
      &:visited {
        background: #184f8d;
      }
      .table-name {
        flex: 1;
        // .align-flex;
        color: #ffffff;
        font-size: 14px;
      }
      .edit-icon {
        width: 13px;
        font-size: 12px;
        color: #ffffff;
      }
    }
    .active {
      background: #184f8d;
    }
  }
}
.empty-wrap {
  flex: 1;
  height: 100%;
  position: relative;
  .no-data {
    .null-data-text {
      color: var(--color-primary);
      font-size: 16px;
      line-height: 1.5;
      text-align: center;
    }
  }
}
@{_deep} .ivu-modal-body {
  min-height: 630px !important;
  padding: 0 !important;
  // padding: 10px 20px !important;
}
@{_deep} .ivu-modal-header {
  margin-bottom: 0 !important;
  padding: 0 !important;
}
// @{_deep} .ivu-modal-content{
//   height: 630px;
// }
</style>
