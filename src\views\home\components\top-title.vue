<template>
  <div class="container">
    <span class="time f-14 time-color">{{ curtime }}</span>
    <div class="nav-logo mr-sm mb-sm">
      <img class="logo" src="@/assets/img/login/nav-title.png" />
    </div>
    <span class="title mb-sm">视图数据治理系统(iVDG)</span>
  </div>
</template>

<script>
export default {
  name: 'top-title',
  components: {},
  props: {},
  data() {
    return {
      curtime: '',
    };
  },
  computed: {},
  watch: {},
  filter: {},
  created() {
    this.currentTime();
    setInterval(() => {
      this.currentTime();
    }, 1000);
  },
  methods: {
    currentTime() {
      let date = new Date();
      let curYear = date.getFullYear();
      let curMonth = this.zeroFill(date.getMonth() + 1);
      let curDate = this.zeroFill(date.getDate());
      let curHours = this.zeroFill(date.getHours());
      let curMinutes = this.zeroFill(date.getMinutes());
      let curSeconds = this.zeroFill(date.getSeconds());
      let curtime = `${curYear} 年 ${curMonth} 月 ${curDate} 日 ${curHours} : ${curMinutes} : ${curSeconds} ${this.getWeek()}`;
      this.curtime = curtime;
    },
    zeroFill(i) {
      if (i >= 0 && i <= 9) {
        return '0' + i;
      } else {
        return i;
      }
    },
    getWeek() {
      let week = new Date().getDay();
      let weekArr = ['日', '一', '二', '三', '四', '五', '六'];
      return '星期' + weekArr[week];
    },
  },
};
</script>

<style lang="less" scoped>
.container {
  position: relative;
  height: 79px;
  background: url('~@/assets/img/home/<USER>') no-repeat;
  background-size: cover;
  display: flex;
  justify-content: center;
  align-items: center;

  .title {
    font-size: 30px;
    font-weight: bold;
    color: #fff;
  }
  .time {
    position: absolute;
    left: 1%;
    top: 10px;
  }
  .time-color {
    color: #bee2fb;
  }
}

.nav-logo {
  width: 36px;
  height: 36px;
  .logo {
    height: 100%;
  }
}
</style>
