<template>
  <map-window-container
    :title="title"
    :close="close"
    class="map-alarm-window-container"
  >
    <div class="map-alarm-window">
      <div
        class="body-item"
        v-for="item in fields"
        :key="item.key"
        :style="item.entireRow ? { width: '100%' } : {}"
      >
        <div class="label">{{ item.title }}:</div>
        <div class="value text-ellipsis" v-if="item.key === 'ajlb'">
          {{ deatils[item.key] | commonFiltering(caseTypeList) }}
        </div>
        <div class="value text-ellipsis" v-else-if="item.key === 'jjlx'">
          {{ deatils[item.key] | commonFiltering(partiTypeList) }}
        </div>
        <div class="value" v-else>
          <Tooltip
            :content="deatils[item.key]"
            placement="bottom"
            :max-width="200"
            transfer
            :style="{ maxWidth: '100%' }"
            class="map-alarm-window-tooltip"
          >
            <p>{{ deatils[item.key] }}</p>
          </Tooltip>
        </div>
      </div>
    </div>
  </map-window-container>
</template>

<script>
import { mapGetters } from "vuex";
import mapWindowContainer from "./map-window-container.vue";
import {
  caseColumns,
  alarmColumns,
} from "@/components/select-modal/case-alarm-columns";

export default {
  components: {
    mapWindowContainer,
  },
  props: {
    data: {
      type: Object,
      default: () => ({}),
    },
    close: {
      type: Function,
      default: () => {},
    },
  },
  data() {
    return {
      title: "警情信息",
      deatils: {},
      fields: [],
    };
  },
  computed: {
    ...mapGetters({
      caseTypeList: "dictionary/getCaseTypeList", //案件类型
      partiTypeList: "dictionary/getPartiTypeList", // 警情类型
    }),
  },
  created() {
    this.deatils = this.data.detail || {};
    const type = this.data.policeDataType;
    this.title = type == "1" ? "警情信息" : "案件信息";
    this.fields = type == "1" ? alarmColumns : caseColumns;
  },
  methods: {},
};
</script>

<style lang="less" scoped>
.map-alarm-window-container {
  width: 450px;
}

.map-alarm-window {
  padding: 10px 10px;
  overflow: hidden;

  .body-item {
    width: 50%;
    display: flex;
    float: left;
    padding: 0 5px;
    font-weight: 400;
    font-size: 12px;
    line-height: 18px;
    margin-bottom: 10px;
    overflow: hidden;

    .label {
      width: 70px;
      color: rgba(0, 0, 0, 0.6);
      text-align: right;
      margin-right: 8px;
    }

    .value {
      flex: 1;
      color: rgba(0, 0, 0, 0.9);
      overflow: hidden;
    }

    .text-ellipsis {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}

/deep/ .map-alarm-window-tooltip {
  .ivu-tooltip-rel {
    width: 100%;
    p {
      width: 100%;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}
</style>
