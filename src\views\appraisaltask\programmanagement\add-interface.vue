<template>
  <div>
    <!-- 编辑弹窗 -->
    <ui-modal v-model="visible" :title="moduleAction.title" :styles="styles">
      <div class="left-city">
        <ul class="ul">
          <li
            :class="{ active: current == index }"
            v-for="(item, index) in citys"
            :key="index"
            @click="cityClick(item, index)"
          >
            <div class="til">
              <Tooltip :content="item.name" :disabled="item.name.length < 8">
                {{ item.name }}
              </Tooltip>
            </div>
            <i-switch class="switch" v-model="item.type" @on-change.native="change" @click.native.stop="stop()" />
          </li>
        </ul>
      </div>
      <Form ref="formData" class="form-content edit-form" :model="formData" :rules="ruleCustom">
        <div class="orgCode" v-if="citys[current]">
          在待检测平台中的组织机构编码：
          <i-input v-model="citys[current].orgCode" placeholder="Enter something..." style="width: 300px" />
        </div>
        <div class="topUl">
          <ul>
            <li>
              <div class="item">接口名称</div>
              <div class="item">每轮检测调用次数(次)</div>
              <div class="item">调用超时阈值(秒)</div>
              <div class="item">相似度%</div>
            </li>
            <!-- <li>接口名称</li>
            <li>每轮检测调用次数(次)</li>
            <li>调用超时阈值(秒)</li>
            <li>相似度%</li> -->
          </ul>
        </div>
        <div style="margin-top: 30px" v-if="citys[current]">
          <ul>
            <li v-for="(item, index) in citys[current].apiModel" :key="index">
              <div class="item">{{ item.name }}</div>
              <div class="item">
                <FormItem label="" prop="apiTimes" :label-width="0">
                  <Input placeholder="次数" v-model.number="item.apiTimes"></Input>
                </FormItem>
              </div>
              <div class="item">
                <FormItem label="" prop="apiDura" :label-width="0">
                  <Input placeholder="阈值" v-model.number="item.apiDura"></Input>
                </FormItem>
              </div>
              <div class="item">
                <FormItem label="" prop="apiRate" :label-width="0">
                  <Input placeholder="相似度" v-model.number="item.apiRate"></Input>
                </FormItem>
              </div>
            </li>
            <div class="clear"></div>
          </ul>
        </div>
      </Form>
      <template slot="footer">
        <!-- <Button  @click="visible = false" class="plr-33">取 消</Button> -->
        <Button type="primary" @click="query" class="plr-33">保 存</Button>
      </template>
    </ui-modal>
  </div>
</template>

<script>
import governanceevaluation from '@/config/api/governanceevaluation';
export default {
  props: {
    value: {
      required: true,
      type: Boolean,
    },
    moduleAction: {
      default: () => {
        return {
          title: '选择算法',
          action: 'arithmetic',
        };
      },
    },
    modalData: {},
    currentItem: {}, // 当前table对象
  },
  data() {
    return {
      current: 0,
      switch1: true,
      visible: false,
      styles: {
        width: '6rem',
      },
      formData: {
        algVendors: [],
        algProcess: '',
      },
      arithmeticList: [],
      ruleCustom: {
        algVendors: [{ required: true, type: 'array', min: 1, message: '请选择算法厂商', trigger: 'change' }],
        dataType: [{ required: true, message: '请选择关联设备方式', trigger: 'blur' }],
      },
      citys: [],
      interfacesObj: null,
    };
  },
  created() {},
  mounted() {},
  methods: {
    async init() {
      let res = await this.$http.get(governanceevaluation.getSchemeIndexById + '?id=' + this.currentItem.id, {});
      this.interfacesObj = res.data.data;
      var obj = JSON.parse(res.data.data.extensionData);
      // console.log('obj', obj)
      this.citys = obj.apiListModel;
      // this.citys = obj.apiModel.extensionData
      // console.log('citys', this.citys)
    },
    async query() {
      try {
        for (let i of this.arithmeticList) {
          i.apiRate = i.apiRate / 100;
        }
        let validate = await this.$refs.formData.validate();
        if (!validate) return;
        this.visible = false;

        // this.interfacesObj.apiListModel = this.citys
        var obj = JSON.parse(this.interfacesObj.extensionData);
        obj.apiListModel = this.citys;
        this.interfacesObj.extensionData = obj;
        // this.interfacesObj.extensionData = this.citys
        this.$emit('getFormData', this.interfacesObj.extensionData);
        // this.$emit('getFormData', { apiModel: this.interfacesObj })
      } catch (err) {
        console.log(err);
      }
    },
    async getOptionAlgVendors() {
      try {
        let res = await this.$http.get(governanceevaluation.sourceAll, {
          // params: { indexId: this.modalData.indexId },
        });
        this.arithmeticList = res.data.data;
        this.$emit('arithmeticList', this.arithmeticList);
      } catch (err) {
        console.log(err);
      }
    },
    resetForm() {
      this.$refs.formData.resetFields();
      this.formData = {
        algVendors: this.modalData.algVendors || [],
        algProcess: this.modalData.algProcess,
      };
    },
    getTableList() {
      var obj = JSON.parse(this.modalData.extensionData);
      this.arithmeticList = obj.apiModel;
      for (let i of this.arithmeticList) {
        i.apiRate = i.apiRate * 100;
      }
    },
    cityClick(item, index) {
      this.current = index;
    },
    change() {},
    stop() {},
  },
  watch: {
    visible(val) {
      this.$emit('input', val);
      if (val) {
        // this.interfacesObj = this.currentItem
        // var obj = JSON.parse(this.currentItem.extensionData)
        // this.citys = obj.apiListModel

        this.init();
        // this.getTableList()
      }
      // this.getOptionAlgVendors(this.modalData);
      // this.resetForm();
    },
    value(val) {
      this.visible = val;
    },
  },
  computed: {
    //单选算法
    isArithmetic() {
      return this.moduleAction.action === 'arithmetic';
    },
    //
  },
  components: {},
};
</script>

<style lang="less" scoped>
.left-city {
  float: left;
  width: 200px;
  min-height: 300px;
  color: var(--color-content);
  border: 1px solid var(--border-color);
  padding: 10px;
  margin-bottom: 20px;
  border-radius: 5px;
}
.edit-form {
  margin-left: 220px;
}

.topUl {
  height: 30px;
  border-bottom: 1px solid var(--border-color);
}
ul {
  color: #fff;
  margin-top: 30px;
  li {
    .item {
      float: left;
      width: 25%;
      // margin-top: 12px;
    }

    .item::after {
      clear: both;
      content: '';
    }

    /deep/ .ivu-input {
      width: 80%;
    }
  }
}

.orgCode {
  color: #fff;
  margin-top: 20px;
}

.clear {
  clear: both;
}

@{_deep} .ivu-modal-body {
  // max-height: 220px !important;
  // height: 220px !important;
  // overflow-y: auto;
  padding: 0 40px 0 40px !important;
}

.ul {
  margin-top: 0;

  li {
    position: relative;
    width: 120px;
    padding: 0 10px;
    height: 40px;
    line-height: 40px;

    .til {
      width: 120px;
      white-space: nowrap;
      overflow:hidden;
      text-overflow:ellipsis;
      padding-right: 10px;
    }
  }
  // li.active, li:hover {
  //   cursor: pointer;
  //   background: #184f8d;
  // }
  li:hover {
    cursor: pointer;
  }
  li.active {
    cursor: pointer;
    background: #184f8d;
  }

  .switch {
    position: absolute;
    top: 8px;
    right:-58px
    // float:right;
    // margin-top: 8px;
  }
}

/deep/ .ivu-tooltip-rel {
  width: 120px;
  white-space: nowrap;
  overflow:hidden;
  text-overflow:ellipsis;
  padding-right: 10px;
}
</style>
