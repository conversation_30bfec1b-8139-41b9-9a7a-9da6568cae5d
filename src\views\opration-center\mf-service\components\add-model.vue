<template>
  <ui-modal
    v-model="visible"
    :title="type === 'add' ? '新增主机' : '编辑主机'"
    :r-width="500"
    @onOk="comfirmHandle"
  >
    <Form ref="form" :model="form" :rules="ruleForm" class="form">
      <FormItem label="主机名称" :label-width="80" prop="serverName">
        <Input v-model="form.serverName" placeholder="请输入" />
      </FormItem>
      <FormItem label="主机ip" :label-width="80" prop="ip">
        <Input v-model="form.ip" placeholder="请输入" />
      </FormItem>
      <FormItem label="端口" :label-width="80" prop="port">
        <Input v-model="form.port" placeholder="请输入" />
      </FormItem>
      <FormItem label="用户名" :label-width="80" prop="userName">
        <Input v-model="form.userName" :maxlength="20" placeholder="请输入" />
      </FormItem>
      <FormItem label="密码" :label-width="80" prop="passWord">
        <Input
          type="password"
          passWord
          v-model="form.passWord"
          :maxlength="20"
          placeholder="请输入"
        />
      </FormItem>
      <FormItem label="备注" :label-width="80" prop="remark">
        <Input type="textarea" v-model="form.remark" placeholder="请输入" />
      </FormItem>
      <FormItem label=" " :label-width="80">
        <a @click="testLinkClick"> <Icon type="ios-link" /> 测试连接 </a>
      </FormItem>
    </Form>
  </ui-modal>
</template>
<script>
import { add, update, testLink } from "@/api/opration-center";

export default {
  data() {
    return {
      type: "add", //新增/编辑
      visible: false,
      form: {
        serverName: "",
        ip: "",
        port: "",
        userName: "",
        passWord: "",
        remark: "",
      },
      ruleForm: {
        serverName: [{ required: true, message: "请输入", trigger: "blur" }],
        ip: [{ required: true, message: "请输入", trigger: "blur" }],
        port: [{ required: true, message: "请输入", trigger: "blur" }],
        userName: [{ required: true, message: "请输入", trigger: "blur" }],
        passWord: [{ required: true, message: "请输入", trigger: "blur" }],
      },
    };
  },
  methods: {
    // 初始化
    show(item) {
      this.type = item ? "edit" : "add";
      this.visible = true;
      this.$nextTick(() => {
        this.$refs.form.resetFields();
        if (this.type === "edit") {
          this.form = {
            id: item.id,
            serverName: item.serverName,
            ip: item.ip,
            port: item.port,
            userName: item.userName,
            passWord: item.passWord,
            remark: item.remark,
          };
        } else {
          this.form = {
            serverName: "",
            ip: "",
            port: "",
            userName: "",
            passWord: "",
            remark: "",
          };
        }
      });
    },
    // 确认提交
    comfirmHandle() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          // 新增
          if (this.type === "add") {
            add({
              ...this.form,
            }).then((res) => {
              this.visible = false;
              if (res.code == 200) {
                this.$Message.success(res.msg);
                this.$emit("refreshList");
              } else {
                this.$Message.error(res.msg);
              }
            });
          } else {
            //编辑
            update({
              ...this.form,
            }).then((res) => {
              this.visible = false;
              if (res.code == 200) {
                this.$Message.success(res.msg);
                this.$emit("refreshList");
              } else {
                this.$Message.error(res.msg);
              }
            });
          }
        }
      });
    },
    //测试连接
    testLinkClick() {
      let params = {
        ip: this.form.ip,
        port: this.form.port,
        userName: this.form.userName,
        passWord: this.form.passWord,
      };
      testLink(params).then((res) => {
        if (res.code == 200) {
          this.$Message.success(res.data);
        }
      });
    },
  },
};
</script>
<style lang="less" scoped>
.dialog-input {
  width: 330px;
}
// .form {
//   height: 200px;
// }
</style>
