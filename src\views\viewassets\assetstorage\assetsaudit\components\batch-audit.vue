<template>
  <ui-modal
    v-model="visible"
    :title="modalTitle"
    :styles="{
      width: searchParams.isStorage === '1' ? '6.25rem' : '3rem',
    }"
    @cancel="handleCancel"
  >
    <div :class="searchParams.isStorage === '1' ? 'batch-audit-content' : ''">
      <div class="custorm-content">
        <section class="section-box">
          <p class="mb-md base-text-color">
            <span v-if="searchParams.isStorage === '1'">1、</span>共选择<span class="num-color ml-xs mr-xs">{{
              chooseDataLen
            }}</span
            >条设备<span v-if="chooseAbnormalDataIds.length"
              >，其中<span class="font-red ml-xs mr-xs">{{ chooseAbnormalDataIds.length }}</span
              >条基础数据检测异常</span
            >，请确定审核结果：
          </p>
          <RadioGroup class="ml-md" v-model="searchParams.isStorage">
            <Radio label="1">审核通过</Radio>
            <Radio label="2">审核不通过</Radio>
          </RadioGroup>
        </section>
        <section v-if="searchParams.isStorage === '1'" class="section-box mt-md">
          <span class="base-text-color">2、请确定入库策略：</span>
          <p class="mt-md mb-md ml-sp strategy">
            <span class="base-text-color">设备的字段为空时：</span>
            <RadioGroup class="ml-md" v-model="searchParams.fieldNull">
              <Radio label="0">覆盖资产库值</Radio>
              <Radio label="1">保留资产库值</Radio>
            </RadioGroup>
          </p>
          <p class="mb-md ml-sp strategy">
            <span class="base-text-color">设备的字段不为空时：</span>
            <RadioGroup v-model="searchParams.fieldNotNull">
              <Radio label="0">覆盖资产库值</Radio>
              <Radio label="1">保留资产库值</Radio>
            </RadioGroup>
          </p>
        </section>
      </div>
      <div v-if="searchParams.isStorage === '1'">
        <instorage-config ref="InstorageConfig" :property-list="propertyList"> </instorage-config>
      </div>
      <div class="desc-content">
        <Form ref="formValidate" :model="formValidate" :rules="ruleValidate" :label-width="90">
          <FormItem label="审核说明" prop="remark">
            <Input
              v-model="formValidate.remark"
              type="textarea"
              :autosize="{ minRows: 3, maxRows: 3 }"
              placeholder="请输入审核说明"
            ></Input>
          </FormItem>
        </Form>
      </div>
    </div>
    <template #footer>
      <Button class="plr-30" type="primary" :loading="btnLoading" @click="handleSubmit">确定</Button>
    </template>
  </ui-modal>
</template>

<script>
import taganalysis from '@/config/api/taganalysis';
import assetsexamine from '@/config/api/assetsexamine';
import assetsSync from '@/config/api/assetsSync';
export default {
  name: 'batch-audit',
  props: {
    value: {
      type: Boolean,
      default: false,
    },
    chooseAuditDataIds: {
      type: Array,
      default: () => [],
    },
    chooseAbnormalDataIds: {
      type: Array,
      default: () => [],
    },
    chooseDataLen: {
      type: Number,
      default: 0,
    },
    checkAll: {
      type: Boolean,
      default: false,
    },
    searchData: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      visible: false,
      modalTitle: '批量审核',
      searchParams: {
        isStorage: '1',
        fieldNull: '1',
        fieldNotNull: '0',
      },
      propertyList: [],
      defaultId: '',
      defaultStorageParam: [],
      propertyFields: Object.freeze([
        { label: '未检测设备', value: '1' },
        { label: '检测通过设备', value: '2' },
        { label: '检测不通过设备', value: '3' },
      ]),
      configType: '1',
      formValidate: {
        remark: '',
      },
      ruleValidate: {},
      btnLoading: false,
      defaultSelectKey: {
        deviceId: {
          addType: 1, // 覆盖
          _checked: true, // 选中复选框
          disabled: true, // 入库策略-下拉框禁用
          _disabled: true, // 复选框不可取消选中
        },
        sourceId: {
          addType: 2, // 追加
          _checked: true,
          disabled: false,
          _disabled: false,
        },
      },
    };
  },
  methods: {
    // 查询全部字段
    async getPropertyList() {
      try {
        let { data } = await this.$http.post(taganalysis.queryPropertySearchList, {
          propertyType: '1', // 视图
        });
        this.defaultSelectedList = [];
        this.propertyList = data.data.filter((item) => {
          if (item.propertyName === 'id') {
            return false;
          }
          if (Object.keys(this.defaultSelectKey).includes(item.propertyName)) {
            item.addType = this.defaultSelectKey[item.propertyName].addType;
            item._checked = this.defaultSelectKey[item.propertyName]._checked;
            item.disabled = this.defaultSelectKey[item.propertyName].disabled;
            item._disabled = this.defaultSelectKey[item.propertyName]._disabled;
            return item;
          }
          this.defaultStorageParam.forEach((storageItem) => {
            if (item.propertyName === storageItem.fieldName) {
              item.addType = storageItem.addType;
              item._checked = true;
            }
          });
          return item;
        });
      } catch (err) {
        console.log(err);
      }
    },
    async handleSubmit() {
      try {
        this.btnLoading = true;
        let params = {
          remark: this.formValidate.remark,
          ...this.searchParams,
        };
        if (this.checkAll) {
          params = Object.assign(
            params,
            {
              ids: [],
            },
            this.searchData,
          );
        } else {
          params = Object.assign(params, {
            fillInIds: this.chooseAuditDataIds,
          });
        }
        params.examineStatus = this.searchParams.isStorage;
        if (this.searchParams.isStorage === '1') {
          const targetList = this.$refs.InstorageConfig.targetList;
          params.storageParamList = targetList.map((item) => {
            const obj = {
              addType: item.addType,
              fieldName: item.checkColumnName,
              fieldRemark: item.checkColumnValue,
            };
            return obj;
          });
        }
        await this.$http.post(assetsexamine.doExamineBatch, params);
        this.$Message.success('审核成功！');
        this.$emit('handleUpdateBatchAudit');
      } catch (e) {
        console.log(e);
      } finally {
        this.btnLoading = false;
      }
    },
    handleCancel() {},
    async queryByConfigType() {
      try {
        let {
          data: { data },
        } = await this.$http.get(assetsSync.queryByConfigType, { params: { configType: this.configType } });
        this.searchParams.fieldNull = data.fieldNull;
        this.searchParams.fieldNotNull = data.fieldNotNull;
        this.searchParams.storageCondition = data.storageCondition ? data.storageCondition.split(',') : [];
        this.defaultId = data.id;
        this.defaultStorageParam = data.storageParam ? JSON.parse(data.storageParam) : [];
      } catch (err) {
        console.log(err);
      }
    },
  },
  computed: {
  },
  watch: {
    visible(val) {
      this.$emit('input', val);
    },
    async value(val) {
      if (val) {
        this.formValidate.remark = '';
        await this.queryByConfigType();
        this.getPropertyList();
      }
      this.visible = val;
    },
  },
  components: {
    InstorageConfig: require('./instorage-config.vue').default,
  },
};
</script>

<style lang="less" scoped>
[data-theme='light'] {
  .num-color {
    color: var(--color-primary);
  }
}
.batch-audit-content {
  height: 720px;
  overflow-y: auto;
}
.AbnormalContent {
  padding: 30px 40px;
  color: #ffffff;
}
.num-color {
  color: var(--color-bluish-green-text);
}
.desc-content {
  padding: 30px 20px;
  color: #ffffff;
}
.custorm-content {
  padding: 0 20px;
  .section-box {
    color: #fff;
  }
  .ml-sp {
    margin-left: 22px;
  }
  .strategy {
    position: relative;
    &:before {
      display: inline-block;
      content: '';
      width: 6px;
      height: 6px;
      background: var(--color-primary);
      border-radius: 0px 0px 0px 0px;
    }
  }
}
</style>
