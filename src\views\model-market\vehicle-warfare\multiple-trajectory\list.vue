<template>
  <div class="map-box">
    <!-- 地图 -->
    <mapCustom
      ref="mapBase"
      mapType="multiple-trajectory"
      @initialed="initialed"
      sectionName="vehicle"
    />
    <left-nav :aimParams="resultList"></left-nav>
    <vehicle-monitor
      :value="faceDetailInfo"
      ref="faceDetailWin"
      @closeWin="closeAllWindow"
      @changeMarker="changeMarker"
    ></vehicle-monitor>
  </div>
</template>
<script>
import axios from "axios";
import mapCustom from "@/views/model-market/components/map/index.vue";
import LeftNav from "./components/map/LeftNav";
import vehicleMonitor from "./components/VehicleMonitor.vue";
import { RANGEICONS, POLYLINESCOLOR } from "./params";
import { getVehicleTrajectory } from "@/api/modelMarket";
export default {
  name: "multipleTrajectory",
  components: {
    mapCustom,
    LeftNav,
    vehicleMonitor,
  },
  props: {
    searchParams: {
      type: Object,
      default: () => {},
    },
  },
  // mixins: [subcribeMixin],
  data() {
    return {
      _VIDPOLYLINE: {},
      faceDetailInfo: {},
      options: {
        container: "fusion-trajectory",
        // config: "common/map/mapConfig.json",
        style: "",
        mapOpts: {
          pitch: 60,
          centerPoint: [117.35301760029327, 32.921854944796905], //定位地图中心点(需要在接口中获取到这个参数)
        },
        toolbar: {
          centerBtn: true,
          zoomBtn: true,
        },
        layerOpts: [
          "polylinesLayer0",
          "polylinesLayer1",
          "polylinesLayer2",
          "polylinesLayer3",
          "polylinesLayer4",
          "sortMarkerLayer",
          "sortLapMarkerLayer",
        ],
      },
      resultList: {
        captureList: [],
        coincidentPointList: [],
      },
    };
  },
  mounted() {
    //重绘制轨迹
    $pubsub.subscribe("record-list", (list) => {
      let sortIcons = {
        normal: "TRACK_CAR",
        hover: "TRACK_CAR_HOVER",
      };
      if (list.length > 0) {
        list.map((item, index) => {
          item.index = `${index + 1}`;
        });
        this.bizMap && this.bizMap.removeAllOverlays("sortMarkerLayer");
        //添加序列
        this.addSortMarker(list, sortIcons, "sortMarkerLayer");
        if (list.length > 0) {
          this.recordVid = `${list[0].plateNo}_${list[0].plateColor}`; //缓存轨迹
        }
      }
    });
    //重新绘制重合点位
    $pubsub.subscribe("lapPoints-output", (list) => {
      let sortIcons = {
        normal: "COINCIDE",
        hover: "COINCIDE_HIGHLIGHT",
      };
      if (this.bizMap) {
        this.bizMap.removeAllOverlays("sortLapMarkerLayer");
        if (Array.isArray(list)) {
          this.addSortMarker(list, sortIcons, "sortLapMarkerLayer", false);
        }
      } else {
        setTimeout(() => {
          this.addSortMarker(list, sortIcons, "sortLapMarkerLayer", false);
        }, 2000);
      }
    });

    //重合点位联动
    $pubsub.subscribe("show-lappoints-detail", (index, record, list) => {
      this.curTypeData = list;
      this.getDetails(record, index);
    });
  },
  methods: {
    initialed(bizMap) {
      this.bizMap = bizMap;
      this.loaderTrailResource();
    },
    // 车辆轨迹
    async loaderTrailResource() {
      this._VIDPOLYLINE = {};
      if (this.searchParams.result) {
        const _params = {
          st: this.$dayjs(this.searchParams.startTime).format(
            "YYYY-MM-DD HH:mm:ss"
          ),
          et: this.$dayjs(this.searchParams.endTime).format(
            "YYYY-MM-DD HH:mm:ss"
          ),
          plateList: this.searchParams.result.map(
            ({ plateNo, plateColor }) => ({ plateNo, plateColor })
          ),
          deviceGbIdList: this.searchParams.devices.map((v) => v.deviceGbId),
        };
        let result = await getVehicleTrajectory(_params);
        this.resultList = this.formatResult(result.data);
        this.resultList.captureList.forEach((v, i) => {
          this.renderLine(v, i);
        });
        $pubsub.publish(
          "lapPoints-output",
          this.resultList.coincidentPointList
        );
      }
    },
    formatResult(result) {
      result.captureList.forEach((item) => {
        let current = this.searchParams.result.find(
          (i) => i.plateNo == item.plateNo
        );
        item.originPic = current.originPic;
        item.targetPic = current.targetPic;
        // 过滤掉不正常的经纬度
        item.captureList = item.captureList.filter((v) => {
          if (v.geoPoint && !!v.geoPoint.lon && !!v.geoPoint.lat) {
            let str1 = v.geoPoint.lon.toString().split(".")[1];
            let str2 = v.geoPoint.lat.toString().split(".")[1];
            if (str1.startsWith("000") || str2.startsWith("000")) {
              return false;
            } else {
              return true;
            }
          } else {
            return false;
          }
        });
      });
      return result;
    },
    // 根据点位信息画线
    createPolyline(data, lineStyle) {
      return this.bizMap.drawPolyline(data, lineStyle);
    },
    // 添加序列标注和重合点位
    addSortMarker: function (list, sortIcons, layeName, showText = true) {
      let points = [];
      let defaultLabelStyle = {
        offset: [0, 10],
        color: "#ffffff",
      };
      list.map((item, index) => {
        item.index = showText ? `${index + 1}` : 0;
      });
      showText &&
        list.map((item, index) => {
          item.list = list;
        });
      this.bizMap.initMarkers(
        layeName,
        list,
        {
          label: Object.assign({}, defaultLabelStyle, {
            text: (item) => (showText ? item.index : ""),
          }),
        },
        (item, index) => {
          return {
            ...item,
            images: {
              ...sortIcons,
            },
            longitude: item.geoPoint.lon,
            latitude: item.geoPoint.lat,
          };
        },
        () => {}
      );
    },
    // 得到车辆详情
    getDetails(item, index) {
      this.curSelDeviceIndex = index;
      this.selectDeviceRecord({
        data: item,
        index: index,
      });
    },
    // 为线路添加起点和终点
    addStartAndEndMarker(list, icon, index) {
      if (!Array.isArray(list) || !list.length) {
        return;
      }
      let len = list.length;
      let startPoint = list[0];
      let endPoint = list[len - 1];
      startPoint.icon = icon[0];
      endPoint.icon = icon[1];
      this.bizMap.initMarkers(
        `polylinesLayer${index}`,
        [startPoint, endPoint],
        {},
        (item) => {
          return {
            images: {
              normal: item.icon,
            },
            longitude: item.geoPoint.lon,
            latitude: item.geoPoint.lat,
            data: item,
          };
        },
        () => {}
      );
    },
    // 没有路网画直线
    renderLine(trackResource, i) {
      if (
        trackResource &&
        trackResource.captureList &&
        trackResource.captureList.length > 0
      ) {
        let _trackResource = trackResource.captureList;
        const vid = `${_trackResource[i].plateNo}_${_trackResource[i].plateColor}`;
        let tr = {
          list: _trackResource,
          lineStyle: { color: POLYLINESCOLOR[i] },
          icon: RANGEICONS[i],
          index: i,
        };
        this.getRoadNet(tr, (line, index) => {
          this.bizMap.addOverlays(`polylinesLayer${index}`, [line]);
        });
        this.addStartAndEndMarker(tr.list, tr.icon, i);
        this._VIDPOLYLINE[vid] = `polylinesLayer${i}`; //ViD和轨迹形成映射关系
      }
    },
    // 根据点位查询路网
    getRoadNet(tr, callBack) {
      // 获取设备的经纬度
      let linePoints = tr.list.map((item) => [
        item.geoPoint.lon,
        item.geoPoint.lat,
      ]);
      if (linePoints.length < 1) return;
      let data = new FormData();
      data.append("stops", linePoints.join(";"));
      let lineStyle = tr.lineStyle;
      axios.post("/npgisdataservice/gis/routing", data).then((res) => {
        // 路网画线
        callBack(this.renderLinesByRoadNet(res.data, lineStyle), tr.index);
      });
    },
    // 根据路网创建地图线路
    renderLinesByRoadNet(roatNet, lineStyle) {
      if (!roatNet || !roatNet.length) {
        return;
      }
      let points = [];
      roatNet.forEach((item) => {
        if (!item.expend) return;
        points = points.concat(item.expend.split(";").map((p) => p.split(",")));
      });
      return this.createPolyline(points, lineStyle);
    },
    setMapCenter(item) {
      // this.bizMap && this.bizMap.setCenter(item)
    },
    // 关闭所有弹窗
    closeAllWindow: function () {
      this.bizMap.closeAllInfoWindows();
    },
    // infowindow弹窗前进后退按钮事件
    changeMarker: function (type) {
      let data = null;
      let index = 0;
      if (type === "up") {
        if (this.curSelDeviceIndex - 1 < 0) {
          this.$Message.warning("已经是第一条数据");
          return;
        }
        index = this.curSelDeviceIndex - 1;
        data = this.curTypeData[index];
      }

      if (type === "down") {
        if (this.curSelDeviceIndex + 1 >= this.curTypeData.length) {
          this.$Message.warning("已经是最后一条数据");
          return;
        }
        index = this.curSelDeviceIndex + 1;
        data = this.curTypeData[index];
      }
      $pubsub.publish("reshow-list", index);
      this.selectDeviceRecord({
        data: data,
        index: index,
      });
    },
    selectDeviceRecord: function ({ data, index }) {
      let el = this.$refs.faceDetailWin.$el;
      this.closeAllWindow();
      this.curSelDeviceIndex = index;
      this.faceDetailInfo = {
        ...data,
        isShow: true,
      };
      this.setMapCenter(data);
      this.bizMap.addInfoWindow(
        { lon: data.geoPoint.lon, lat: data.geoPoint.lat },
        el,
        {
          width: 590,
          height: 300,
        },
        ""
      );
    },
  },
};
</script>

<style lang="less" scoped>
.map-box {
  width: 100vw;
  height: 100vh;
}
</style>
