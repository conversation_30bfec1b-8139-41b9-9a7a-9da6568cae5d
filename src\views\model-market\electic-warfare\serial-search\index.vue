<!--
    * @FileDescription: 连环智叟
    * @Author: H
    * @Date: 2024/06/24
 * @LastEditors: liyuhang1 <EMAIL>
 * @LastEditTime: 2024-10-12 15:19:23
 -->
<template>
  <div class="serial-search">
    <mapTrack
      ref="mapBase"
      :idlerWheel="true"
      cutIcon="track"
      :trackPoints="layerManageMap[sectionName]"
      :sectionName="sectionName"
      :disableScroll="false"
      :currentClickIndex.sync="currentClickIndex"
      :trackConnected="trackConnected"
      @chooseMapItem="chooseMapItem($event, true)"
      :mapLayerConfig="{ ...mapLayerConfig }"
      @loaded="mapLoaded"
    />
    <!-- 高级搜索展示的页面位置 -->
    <advancedSearch
      ref="advancedSearch"
      v-if="searchType == 'advanced'"
      menuType="map"
      @updateLayerManager="updateLayerManager"
    />
    <div
      class="advanced-search-back"
      v-if="searchType == 'advanced'"
      :class="searchType == 'advanced' ? 'advanced-search-active' : ''"
      @click="advancedSearchButton"
    >
      <span>返回</span>
      <ui-icon type="return" :size="20" color="#2C86F8"></ui-icon>
    </div>
    <div class="advanced-search" @click="advancedSearchButton" v-else>
      <ui-icon type="gaojisousuo" :size="16"></ui-icon>
      <span>轨迹搜索</span>
    </div>
    <!-- 轨迹上图结果 -->
    <div class="search-result" v-show="isShowTrack">
      <div class="search-result-header" :class="{ 'expand-header': !expand }">
        <div>
          <Checkbox
            v-if="expand"
            :value="trackConnected"
            @click.prevent.native="handleTrackConnect"
            >轨迹连线</Checkbox
          >
          <span
            class="mapLine"
            @click="handleLine(0)"
            v-if="expand && trackConnected && polylineshow"
            >暂停</span
          >
          <span
            class="mapLine"
            @click="handleLine(1)"
            v-if="expand && trackConnected && !polylineshow"
            >继续</span
          >
          <!-- <ui-icon type="close" :size="16" @click.native.stop="closeLayerManager(false)"></ui-icon> -->
        </div>
      </div>
      <Menu
        :active-name="sectionName"
        :width="50 / 192 + 'rem'"
        @on-select="selectItemHandle"
        class="search-menu"
      >
        <MenuItem
          v-for="(item, index) in menuList"
          :key="index"
          :name="item.name"
          v-permission="item.permission"
          :class="!!item.num ? 'active-search' : ''"
        >
          <Tooltip :content="item.label" placement="right" theme="light">
            <i class="iconfont" :class="item.iconName"></i>
          </Tooltip>
        </MenuItem>
      </Menu>
      <!-- 普通搜索展示的页面位置 -->
      <div :class="{ 'expand-content': !expand }">
        <component
          class="search-result-content"
          :ref="sectionName"
          :is="sectionName"
          :updateLayerId="updateLayerId"
          :dataList="layerManageMap[sectionName]"
          :currentClickIndex="currentClickIndex"
          :orderType="menuItem.order"
          @chooseMapItem="chooseMapItem"
          @changeOrder="changeOrder"
          @deleteItem="deleteItem"
        ></component>
      </div>
      <div class="switch" @click="switchHandle">
        <img v-if="!expand" src="@/assets/img/expand.png" alt="" />
        <img v-else src="@/assets/img/stow.png" alt="" />
      </div>
    </div>
  </div>
</template>
<script>
import { mapActions, mapMutations, mapGetters } from "vuex";
import advancedSearch from "./components/advanced-search";
import mapTrack from "@/components/map/map-track.vue";
import face from "./components/face-content.vue";
import vehicle from "./components/vehicle-content.vue";
import humanbody from "./components/humanBody-content.vue";
import nonmotorVehicle from "./components/nonmotorVehicle-content.vue";
import all from "./components/all-content.vue";
export default {
  name: "serial-search",
  components: {
    mapTrack,
    advancedSearch,
    face, //人脸模块
    vehicle, //车辆模块
    humanbody, // 人体
    nonmotorVehicle, // 非机动车
    all,
  },
  data() {
    return {
      searchType: "",
      menuList: [
        {
          label: "全部",
          value: 0,
          iconName: "icon-sifenping",
          name: "all",
          numName: "all",
          order: "desc",
          permission: "yes",
        },
        {
          label: "人像",
          value: 2,
          iconName: "icon-yonghu",
          name: "face",
          numName: "face",
          order: "desc",
          permission: ["track-face"],
        },
        {
          label: "车辆",
          value: 3,
          iconName: "icon-cheliang",
          name: "vehicle",
          numName: "vehicleRecord",
          order: "desc",
          permission: ["track-vehicle"],
        },
        {
          label: "人体",
          value: 9,
          iconName: "icon-renti",
          name: "humanbody",
          numName: "humanRecord",
          order: "desc",
          permission: ["track-humanBody"],
        },
        {
          label: "非机动车",
          value: 10,
          iconName: "icon-feijidongche",
          name: "nonmotorVehicle",
          numName: "nonmotorRecord",
          order: "desc",
          permission: ["track-nonmotorVehicle"],
        },
      ],
      expand: true,
      sectionName: "all",
      updateLayerId: null,
      layerManageMap: {
        face: [],
        vehicle: [],
        humanbody: [],
        nonmotorVehicle: [],
        all: [],
      },
      currentClickIndex: -1, // 当前点击的轨迹节点
      trackConnected: false,
      polylineshow: true,
      isShowTrack: true,
      hasResult: false,
      // 地图配置信息
      mapLayerConfig: {
        mapToolVisible: false, // 底部框选操作栏
        trackResult: true, //搜索结果撒点判断
      },
    };
  },
  computed: {
    ...mapGetters({
      mapObj: "systemParam/mapObj",
      globalObj: "systemParam/globalObj",
      getMapSearching: "map/getMapSearching",
    }),
    menuItem() {
      let menuItem = this.menuList.find((v) => v.name == this.sectionName);
      return menuItem || {};
    },
  },
  async created() {
    this.setLayoutNoPadding(true);
    this.setMapSearching(true);
  },
  destroyed() {
    this.setLayoutNoPadding(false);
    this.setMapSearching(false);
    this.setUpImageData();
  },
  mounted() {
    this.init();
    // this.getMapLayerByTypeSite()
  },
  methods: {
    ...mapMutations("admin/layout", ["setLayoutNoPadding"]),
    ...mapMutations({
      setMapSearching: "map/setMapSearching",
    }),
    ...mapMutations({ setUpImageData: "map/setUpImageData" }),
    init() {
      console.log(this.getMapSearching, "getMapSearching");
    },
    mapLoaded() {
      if (this.$route.query.mapData) {
        let item = this.$route.query.mapData;
        let { type, list, deleIdent } = JSON.parse(item);
        this.updateLayerManager({ type, list, deleIdent });
      }
    },
    // 上图数据更新
    updateLayerManager({ type, list, deleIdent }) {
      this.recordName = this.recordName || "新图层";
      this.selectItemHandle(type);
      this.expand = true;
      this.trackConnected = false;
      this.hasResult = true;
      // this.isShowTrack = true
      // this.searchType = ""
      let position = list.map((e) => {
        return {
          ...e,
          type: type,
          lat: e.geoPoint && e.geoPoint.lat,
          lon: e.geoPoint && e.geoPoint.lon,
        };
      });
      let deleteList = position.filter((e) => !e.isChecked)
      deleteList.forEach((e) => {
        this.deleteItem(e, -1)
      });
      position = position.filter((e) => e.lat && e.lon && e.isChecked);
      if (!this.layerManageMap[type]) {
        this.layerManageMap[type] = [];
      }
      let arr = [...this.layerManageMap[type], ...position];
      arr.sort((a, b) => {
        const timeA = new Date(a.absTime);
        const timeB = new Date(b.absTime);
        return this.menuItem.order == "desc" ? timeB - timeA : timeA - timeB;
      });
      this.layerManageMap[type] = arr;
      // 全部
      let allArr = [...this.layerManageMap["all"], ...position];
      let allItem = this.menuList.find((v) => v.name == "all");
      allArr.sort((a, b) => {
        const timeA = new Date(a.absTime);
        const timeB = new Date(b.absTime);
        return allItem.order == "desc" ? timeB - timeA : timeA - timeB;
      });
      this.layerManageMap["all"] = allArr;
      // 将作战中的已上图数据保存起来，避免在此基础上，继续添加，导致重复
      this.setUpImageData(allArr);
    },
    advancedSearchButton() {
      const { searchType } = this;
      this.searchType =
        searchType === "advanced"
          ? ((this.isShowTrack = !this.isShowTrack), (this.searchType = ""))
          : ((this.isShowTrack = false), (this.searchType = "advanced"));
    },
    handleTrackConnect() {
      this.trackConnected = !this.trackConnected;
    },
    handleLine(index) {
      this.polylineshow = !this.polylineshow;
      if (index == 0) {
        this.$refs["mapBase"].polylinePause();
      } else {
        this.$refs["mapBase"].polylineContinus();
      }
    },
    // 关闭图层
    closeLayerManager(val) {
      if (val) {
        return this.clearLayerData();
      }
      this.$Modal.confirm({
        title: "友情提示",
        width: 450,
        closable: true,
        content: `确定关闭吗？`,
        onOk: () => {
          this.$refs["operationalRecords"].openLayerIndex = -1;
          this.isEditLayer = false;
          this.recordName = "";
          this.clearLayerData();
        },
      });
    },
    selectItemHandle(sectionName) {
      if (this.sectionName != sectionName) {
        this.trackConnected = false;
        this.$refs["mapBase"].resetMarker();
      }
      this.sectionName = sectionName;
      this.currentClickIndex = -1;
    },
    // 左侧选中项 点击普通搜索列表
    chooseMapItem(index, noZoom) {
      this.currentClickIndex = index;
      if (!noZoom) this.$refs.mapBase.zoomInto();
    },
    changeOrder(order) {
      let menuItem = this.menuList.find((v) => v.name == this.sectionName);
      menuItem.order = order;
      this.layerManageMap[this.sectionName].sort((a, b) => {
        const timeA = new Date(a.absTime);
        const timeB = new Date(b.absTime);
        return order == "desc" ? timeB - timeA : timeA - timeB;
      });
    },
    deleteItem(item, index) {
      let typeMap = {
        face: { idKey: "id" },
        vehicle: { idKey: "id" },
        humanbody: { idKey: "recordId" },
        nonmotorVehicle: { idKey: "recordId" },
        wifi: { idKey: "mac" },
        rfid: { idKey: "rfidCode" },
        electric: { idKey: "imsi" },
        gps: { idKey: "recordId" },
      };
      let idKey = typeMap[item.type].idKey;
      if (index === -1) {
        this.layerManageMap[item.type].forEach((e, ind) => {
          if (e[idKey] === item[idKey]) {
            index = ind;
          }
        });
      }
      if (this.sectionName == "all") {
        this.layerManageMap["all"].splice(index, 1);
        this.layerManageMap[item.type] = this.layerManageMap[item.type].filter(
          (v) => v[idKey] != item[idKey]
        );
      } else {
        this.layerManageMap[this.sectionName].splice(index, 1);
        this.layerManageMap["all"] = this.layerManageMap["all"].filter(
          (v) => v[idKey] != item[idKey]
        );
      }
      this.setUpImageData(this.layerManageMap["all"]);
    },
    // 收起-展开
    switchHandle() {
      this.expand = !this.expand;
    },
  },
};
</script>
<style lang="less" scoped>
.serial-search {
  padding: 0;
  position: relative;
  width: 100%;
  height: 100%;
  .advanced-search {
    position: absolute;
    left: 10px;
    top: 10px;
    width: 120px;
    height: 40px;
    line-height: 40px;
    background: #ffffff;
    box-shadow: 0px 3px 5px 0px rgba(0, 0, 0, 0.3);
    border-radius: 4px;
    cursor: pointer;
    text-align: center;
    font-size: 14px;
    color: rgba(0, 0, 0, 0.8);
    .iconfont {
      margin-right: 5px;
    }
  }
  .advanced-search:hover {
    color: #2c86f8;
    .iconfont {
      color: #2c86f8 !important;
    }
  }
  .advanced-search-back {
    position: absolute;
    left: 20px;
    top: 20px;
    width: 100px;
    height: 40px;
    line-height: 40px;
    background: #ffffff;
    box-shadow: 0px 3px 5px 0px rgba(0, 0, 0, 0.3);
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-right: 12px;

    > span {
      font-size: 16px;
      font-weight: bold;
      margin-left: 24px;
      color: rgba(0, 0, 0, 0.9);
      position: relative;
    }

    > span:before {
      content: "";
      width: 4px;
      height: 20px;
      background: #2c86f8;
      position: absolute;
      left: -10px;
      top: 50%;
      transform: translateY(-50%);
    }
  }
  /deep/.search-menu {
    height: 100%;
    z-index: 10;
    .active-search {
      position: relative;
      &::before {
        position: absolute;
        right: 3px;
        top: 5px;
        content: "";
        width: 10px;
        height: 10px;
        border-radius: 5px;
        background: #f8775c;
      }
    }
  }
  .search-result {
    position: absolute;
    left: 10px;
    top: 66px;
    height: calc(~"100% - 120px");
    &-header {
      font-size: 12px;
      color: rgba(0, 0, 0, 0.6);
      width: 410px;
      height: 34px;
      padding: 0 10px;
      background: #ffffff;
      box-shadow: inset 0px -1px 0px 0px #d3d7de;
      border-radius: 4px 4px 0px 0px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .total {
        color: #f29f4c;
        font-weight: bold;
      }
      .mapLine {
        cursor: pointer;
      }
      .search-tool {
        cursor: pointer;
      }
      .iconfont {
        margin-right: 5px;
      }
      /deep/.ivu-dropdown {
        .ivu-select-dropdown {
          padding: 0;
          width: 410px;
          left: 0 !important;
          top: -4px !important;
          .ivu-dropdown-menu {
            .arrow-up {
              height: 36px;
              display: flex;
              align-items: center;
              justify-content: end;
              border-bottom: 1px solid #d3d7de;
              padding-left: 20px;
              padding-right: 10px;
            }
            .ivu-form {
              padding: 15px 20px 0px;
              &-item {
                margin-bottom: 15px;
                .frame-selection {
                  width: 34px;
                  height: 34px;
                  border-radius: 4px;
                  border: 1px solid #d3d7de;
                  display: flex;
                  justify-content: center;
                  align-items: center;
                  margin-left: 10px;
                  cursor: pointer;
                  .iconfont {
                    margin-right: 0;
                  }
                }
                .frame-selection:hover {
                  border: 1px solid #2c86f8;
                  .iconfont {
                    color: #2c86f8 !important;
                  }
                }
                &-content {
                  display: flex;
                  .ivu-date-picker {
                    width: 243px !important;
                  }
                  .ivu-select-dropdown {
                    padding: 5px 0px;
                    width: 432px;
                    left: 0px !important;
                    top: 32px !important;
                  }
                }
              }
              .btn-group {
                float: right;
              }
            }
          }
        }
      }
      &.expand-header {
        width: 50px;
      }
    }
    &-content {
      position: absolute;
      left: 50px;
      top: 34px;
      height: 100%;
      background: rgba(255, 255, 255, 0.9);
      box-shadow: 0px 3px 5px 0px rgba(0, 21, 41, 0.12);
      border-radius: 0px 0px 4px 4px;
    }
    .switch {
      width: 18px;
      height: 90px;
      position: absolute;
      bottom: 10px;
      top: 50%;
      right: -18px;
      transform: translateY(-50%);
      cursor: pointer;
      > img {
        width: 100%;
      }
    }
    .expand-content {
      transform: translateX(-100%);
    }
  }
}
</style>
