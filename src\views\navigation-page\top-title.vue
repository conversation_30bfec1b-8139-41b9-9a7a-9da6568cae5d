<template>
  <div class="container">
    <span class="time f-14 time-color">{{ curtime }}</span>
    <div class="icon-box">
      <i class="icon-font icon icon-home home-icon" title="首页" @click="() => $emit('backHome')"></i>
      <i class="icon-font f-12 icon" :class="getIcon" title="全屏" @click="setScreenFull"></i>
    </div>
  </div>
</template>

<script>
import screenfull from 'screenfull';
import { mapActions, mapGetters } from 'vuex';
export default {
  name: 'top-title',
  components: {},
  props: {},
  data() {
    return {
      curtime: '',
    };
  },
  computed: {
    ...mapGetters({
      getFullscreen: 'home/getFullscreen',
    }),
    getIcon() {
      if (this.getFullscreen) {
        return 'icon-tuichuquanping';
      } else {
        return 'icon-ivdg-quanping';
      }
    },
  },
  watch: {},
  filter: {},
  created() {
    this.currentTime();
    setInterval(() => {
      this.currentTime();
    }, 1000);
  },
  mounted() {
    this.DetectFullscreenChange();
  },
  methods: {
    ...mapActions({
      setFullscreen: 'home/setFullscreen',
    }),
    DetectFullscreenChange() {
      if (screenfull.isEnabled) {
        screenfull.on('change', () => {
          if (screenfull.isFullscreen) {
            this.setFullscreen(true);
          } else {
            this.setFullscreen(false);
          }
        });
      }
    },
    setScreenFull() {
      if (screenfull.isEnabled && screenfull.isFullscreen) {
        screenfull.exit();
        this.setFullscreen(false);
      } else {
        screenfull.toggle(this.$parent.$el);
        this.setFullscreen(true);
      }
    },
    currentTime() {
      let date = new Date();
      let curYear = date.getFullYear();
      let curMonth = this.zeroFill(date.getMonth() + 1);
      let curDate = this.zeroFill(date.getDate());
      let curHours = this.zeroFill(date.getHours());
      let curMinutes = this.zeroFill(date.getMinutes());
      let curSeconds = this.zeroFill(date.getSeconds());
      let curtime = `${curYear} 年 ${curMonth} 月 ${curDate} 日 ${curHours} : ${curMinutes} : ${curSeconds} ${this.getWeek()}`;
      this.curtime = curtime;
    },
    zeroFill(i) {
      if (i >= 0 && i <= 9) {
        return '0' + i;
      } else {
        return i;
      }
    },
    getWeek() {
      let week = new Date().getDay();
      let weekArr = ['日', '一', '二', '三', '四', '五', '六'];
      return '星期' + weekArr[week];
    },
  },
};
</script>

<style lang="less" scoped>
.container {
  position: relative;
  height: 40px;
  background-size: cover;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid var(--devider-line);
  margin-bottom: 10px;

  .title {
    font-size: 30px;
    font-weight: bold;
    color: #fff;
  }
  .icon {
    line-height: 12px;
    color: #1f88a2;
    border: 1px solid #1f88a2;
    padding: 5px;
    &.home-icon {
      padding: 5px 3px;
      margin-right: 10px;
    }
  }
  .time-color {
    color: #bee2fb;
  }
}
</style>
