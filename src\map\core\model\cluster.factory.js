import ConvertModelUtil from "../utils/ConvertModelUtil";

export const ClusterMarkerParamsNameType = {
  ID: "id",
  OBJECT_ID: "objectId",
  TITLE_NAME: "titleName"
};

/**
 * 聚合的点位缓存工厂
 */
export class ClusterMarkerFactory {

  // 临时在这里缓存points数组
  points = new Array;

  mapAdapter;

  constructor(mapAdapter) {
    this.mapAdapter = mapAdapter;
  }

  /**
   * 根据传入的参数找到对应的点位
   * 默认只取最先匹配到的数据
   * @returns {null}
   */
  getSystemPointByID (objectId) {
    if (!this.points && this.points.length <= 0) return null;
    let points = this.points;
    let i, len, result;
    for (i = 0, len = points.length; i < len; i++) {
      if (points[i].ObjectID === objectId) {
        result = points[i];
        break;
      }
    }
    return result;
  }

  /**
   * 初始化新增点位信息
   * 坐标为空的点位信息不增加到缓存中
   * @param points
   */
  setPoints (points) {
    let i, len, _points = this.points,
      temp;
    for (i = 0, len = points.length; i < len; i++) {
      temp = points[i];
      if (temp.deviceId && (temp.longitude != 0 || temp.latitude != 0)) {
        _points.push(temp);
      }
    }
  }

  getPoints () {
    return this.points || [];
  }

  /**
   * 通过判断参数名来移除点位缓存
   * @param paramName
   * @param paramValue
   */
  removePointByParams (paramName, paramValue) {
    if (!this.points && this.points.length <= 0) return;
    let i, len, temp;
    for (i = 0, len = this.points.length; i < len; i++) {
      temp = this.points[i];
      if (!temp) {
        console.log(i, "不存在", this.points);
      }
      if (temp[paramName] != null && temp[paramName] === paramValue) {
        this.points.splice(i, 1);
        i--;
        len--;
      }
    }
  }

  addPoint (point) {
    let index = this.getPointIndexInPoints(point);
    if (index >= 0) {
      this.points[index] = point; // 直接替换
    } else {
      this.points.push(point); // 新增
    }

  }

  /**
   * 创建聚合点位
   * @param lon
   * @param lat
   * @param markType
   * @param model
   * @returns {any}
   */
  createClusterMarker (model) {
    // 已做判空操作
    // 因为现在系统只有一种摄像头所以就直接显示普通摄像头
    let result = this.mapAdapter.createClusterMarker({ lon: model.Lon, lat: model.Lat }, { markType: model.LayerType });
    // 设置一些额外参数
    ConvertModelUtil._convertClusterMarker2ClusterMarkerEx(result, model);
    return result;
  };

  getPointIndexInPoints (point) {
    if (!this.points && this.points.length <= 0 && point) return;
    let i, len, points = this.points,
      result = -1;
    for (i = 0, len = points.length; i < len; i++) {
      if (point.ObjectID === points[i].ObjectID) {
        result = i;
      }
    }
    return result;
  }

  createClusterMarkers (points) {
    let i, len, temp, result = new Array;
    for (i = 0, len = points.length; i < len; i++) {
      temp = points[i];
      if (temp && temp.Lat && temp.Lon) {
        result.push(this.createClusterMarker(temp));
      }
    }
    return result;
  }

  createClusterPoints (points) {
    return new NPMapLib.Symbols.ClusterPoints(points, { threshold: 0 });
  }

  /**
   * 获取聚合点位数据
   * @param points
   * @returns {NPMapLib.Symbols.ClusterPoints}
   */
  getClusterPoints (points) {
    if (points && points.length > 0) {
      return this.createClusterPoints(this.createClusterMarkers(points));
    } else {
      return null;
    }
  }

  /**
   * 根据参数获取聚合点位
   * 只返回最先匹配的值
   * @param clusterPoints
   * @param propertyName
   * @param propertyValue
   */
  getClusterMarkerByProperty (clusterPoints, propertyName, propertyValue) {
    if (!clusterPoints) return;
    let arrs = clusterPoints.getClusterPoints();
    let result = null;
    if (arrs && arrs.length > 0) {
      let i = 0,
        len = arrs.length;
      for (; i < len; i++) {
        if (arrs[i][propertyName] === propertyValue) {
          result = arrs[i];
          break;
        }
      }
    }
    return result;
  }
}