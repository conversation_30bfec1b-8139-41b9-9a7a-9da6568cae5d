<!--
    * @FileDescription: 地图-框选列表
    * @Author: H
    * @Date: 2023/03/21
 * @LastEditors: zhengmingming <EMAIL>
 * @LastEditTime: 2025-02-13 17:51:53
 -->
<template>
  <div class="select-result-dom">
    <header>
      <p>
        <span>框选列表</span>
        <span class="total">
          <!-- <span style="color: #2c86f8">（ {{ selectedTotal }} ）</span> -->
        </span>
      </p>
      <ui-icon
        type="close"
        :size="12"
        @click.native="closeSelectResult"
      ></ui-icon>
    </header>
    <section class="dom-content" v-if="!!Object.keys(selectPointsList).length">
      <div class="dom-content-search">
        <Select v-model="filter.type" transfer @on-change="filterHandle">
          <Option :value="'All'">全部</Option>
          <template v-for="(value, key) in deviceTypeMap">
            <Option
              :value="key"
              :key="key"
              v-if="selectPointsList[key].list.length"
              >{{ value.name }}</Option
            >
          </template>
        </Select>
        <Input
          v-model.trim="filter.value"
          :maxlength="20"
          placeholder="请输入"
          style="width: auto"
        >
          <Icon type="ios-search" slot="suffix" @click="filterHandle" />
        </Input>
      </div>
      <div class="option-show">
        <Checkbox
          :value="allList"
          @click.prevent.native="handleCheckAll"
          :indeterminate="indeterminate"
          >全选</Checkbox
        >
        <div class="optiop-num">
          共 <span>{{ seleTotal }}</span
          >条/已选 <span>{{ selectNum }}</span
          >条
        </div>
      </div>
      <div class="layerList" v-if="!!Object.keys(selectPointsList).length">
        <template v-for="(e, key) in selectPointsList">
          <div
            class="dom-content-type"
            :key="key"
            v-if="
              selectPointsList[key].list.length && !selectPointsList[key].show
            "
          >
            <div class="header" @click.stop="switchHandle(key)">
              <ui-icon
                :class="selectPointsList[key].isShow ? 'arrowrun' : 'arrow'"
                type="caret-right"
                color="#187AE4"
              ></ui-icon>
              <Checkbox
                :value="allList"
                @click.prevent.native="handleCheckAll"
                :indeterminate="indeterminate"
              ></Checkbox>
              <div class="device-type">
                <ui-icon
                  :type="deviceTypeMap[key].icon"
                  :color="deviceTypeMap[key].color"
                ></ui-icon>
                <span>{{ deviceTypeMap[key].name }}</span>
                <span
                  v-if="selectPointsList[key].list.length"
                  class="type-total"
                  >({{
                    selectPointsList[key].list.filter((e) => !e.show).length
                  }})</span
                >
              </div>
              <ui-icon
                type="eye"
                title="隐藏"
                v-if="
                  layerCheckedNames.includes(selectPointsList[key].layerType)
                "
                @click.native.stop="
                  layerChange('close', selectPointsList[key].layerType)
                "
              ></ui-icon>
              <ui-icon
                type="eye-close"
                v-else
                title="显示"
                @click.native.stop="
                  layerChange('open', selectPointsList[key].layerType)
                "
              ></ui-icon>
            </div>
            <div class="dom-content-list" slot="content" v-if="e.isShow">
              <template v-for="(item, i) in e.list">
                <div :key="i" @click="openMapDom(item)" v-if="!item.show">
                  <Checkbox
                    v-model="item.check"
                    @on-change="checkAllGroupChange"
                  ></Checkbox>
                  <ui-icon
                    :type="deviceTypeMap[key].icon"
                    :color="deviceTypeMap[key].color"
                  ></ui-icon>
                  <p>{{ item.deviceName }}</p>
                </div>
              </template>
            </div>
          </div>
        </template>
      </div>
      <div class="action-wrapper">
        <!-- <RadioGroup v-model="isMerge">
                    <Radio :label="0">并集</Radio>
                    <Radio :label="1">替换</Radio>
                </RadioGroup> -->
        <Button class="find" type="primary" @click="goSearch">确认</Button>
      </div>
    </section>
  </div>
</template>
<script>
import uiIcon from "@/components/ui-icon.vue";
export default {
  components: { uiIcon },
  props: {
    selectDeviceList: {
      type: Array,
      default: () => [],
    },
    // 时空碰撞所选择的碰撞类型
    crashType: {
      type: String,
      default: "",
    },
  },
  watch: {
    selectDeviceList: {
      handler(val) {
        this.allList = true;
        this.indeterminate = false;
        const { layerTypeList } = this.$parent;
        // 组装框选数据分组
        let selectPointsMap = {};
        layerTypeList.forEach((e) => {
          selectPointsMap[e] = {
            isShow: false,
            list: [],
          };
        });
        val.forEach((e) => {
          e.check = true;
          selectPointsMap[e.LayerType].list.push(e);
          selectPointsMap[e.LayerType].layerType = e.LayerType;
        });
        this.filter = {
          type: "All", // 下拉框选中项
          value: "", // 过滤设备名称
        };
        this.selectPointsList = {
          ...selectPointsMap,
        };
        this.selectNum = this.selectPointsList[this.crashType]?.list?.length;
      },
      // immediate: true
    },
  },
  computed: {
    layerCheckedNames() {
      const { layerCheckedNames } = this.$parent;
      return layerCheckedNames;
    },
    // 计算总总数
    selectedTotal() {
      const { selectPointsList } = this;
      let length = null;
      for (let key in selectPointsList) {
        length +=
          selectPointsList[key].list && selectPointsList[key].list.length;
      }
      return length;
    },
    seleTotal() {
      const { selectPointsList, crashType } = this;
      let list = selectPointsList[crashType]?.list?.filter((item) => {
        if (!item.show) {
          return item;
        }
      });
      return list.length;
    },
  },

  data() {
    return {
      isMerge: 1,
      selectPointsList: {}, //框选设备
      deviceTypeMap: {
        Camera: { name: "高清视频", icon: "shebeizichan", color: "#187AE4" },
        Camera_QiuJi: { name: "球机", icon: "qiuji", color: "#187AE4" },
        Camera_QiangJi: {
          name: "枪机",
          icon: "shebeizichan",
          color: "#187AE4",
        },
        Camera_Vehicle: { name: "车辆卡口", icon: "qiche", color: "#1faf8a" },
        Camera_Face: { name: "人脸卡口", icon: "yonghu", color: "#48BAFF" },
        Camera_RFID: { name: "RFID设备", icon: "RFID", color: "#8173FF" },
        Camera_Wifi: { name: "Wi-Fi设备", icon: "wifi", color: "#914FFF" },
        Camera_Electric: {
          name: "电围设备",
          icon: "ZM-dianwei",
          color: "#614FFF",
        },
        Place_Hotel: { name: "酒店", icon: "hotel", color: "#EB8A5D" },
        Place_InterBar: { name: "网吧", icon: "diannao", color: "#EB6C6C" },
        Place_Government: {
          name: "政府机关",
          icon: "gejizhengfu",
          color: "#187AE4",
        },
        Place_School: { name: "学校", icon: "xuexiao", color: "#1faf8a" },
        Place_Key: { name: "重点场所", icon: "yishoucang", color: "#EA4A36" },
      },
      filter: {
        type: "All", // 下拉框选中项
        value: "", // 过滤设备名称
      },
      allList: true,
      indeterminate: false,
      selectNum: 0,
    };
  },
  methods: {
    goSearch() {
      let list = this.selectPointsList[this.crashType].list;
      let seleList = list.filter((item) => item.check);
      if (seleList.length == 0) {
        this.$Message.warning("未选择设备！");
        return;
      }
      this.$emit("closeSelectWindowDom");
      this.$emit("goSearch", seleList);
    },
    closeSelectResult() {
      this.$emit("closeSelectWindowDom");
    },
    // 展开-收起
    switchHandle(key) {
      const { isShow } = this.selectPointsList[key];
      this.selectPointsList[key].isShow = !isShow;
    },
    // 更新图层
    layerChange(operateType, layerType) {
      operateType === "close"
        ? this.layerCheckedNames.splice(
            this.layerCheckedNames.indexOf(layerType),
            1
          )
        : this.layerCheckedNames.push(layerType);
      this.$emit("updateLayerCheckedNames", this.layerCheckedNames);
    },
    // 打开地图弹框
    openMapDom(item) {
      return;
      // this.$emit('openMapDom', {
      //     ...item,
      //     lat: item.Lat,
      //     lon: item.Lon
      // })
    },
    // 名称过滤
    filterHandle() {
      this.allList = true;
      this.indeterminate = false;
      const { selectPointsList, filter } = this;
      for (let key in selectPointsList) {
        for (const [key, value] of Object.entries(selectPointsList)) {
          if (filter.type === "All") {
            value.show = false;
          } else {
            filter.type === key ? (value.show = false) : (value.show = true);
          }
          if (filter.value) {
            value.list =
              value.list &&
              value.list.length &&
              value.list.map((e) => {
                return {
                  ...e,
                  show: e.deviceName.includes(filter.value) ? false : true,
                  check: e.deviceName.includes(filter.value) ? true : false,
                };
              });
          } else {
            value.list =
              value.list &&
              value.list.length &&
              value.list.map((e) => {
                return {
                  ...e,
                  show: false,
                  check: true,
                };
              });
          }
        }
      }
      this.selectPointsList = {
        ...selectPointsList,
      };
      let list = selectPointsList[this.crashType].list.filter((item) => {
        if (item.check) {
          return item;
        }
      });
      this.selectNum = list.length;
    },
    // 全选
    handleCheckAll() {
      const { selectPointsList } = this;
      if (this.indeterminate) {
        this.allList = false;
      } else {
        this.allList = !this.allList;
      }
      this.indeterminate = false;
      if (this.allList) {
        for (let keys in selectPointsList) {
          selectPointsList[keys].list &&
            selectPointsList[keys].list.map((item) => {
              item.check = true;
            });
        }
        let list = selectPointsList[this.crashType].list.filter((item) => {
          if (item.check) {
            return item;
          }
        });
        this.selectNum = list.length;
      } else {
        for (let keys in selectPointsList) {
          console.log(keys, selectPointsList[keys], "keys");
          selectPointsList[keys].list &&
            selectPointsList[keys].list.map((item) => {
              item.check = false;
            });
        }
        this.selectNum = 0;
      }
    },
    checkAllGroupChange() {
      let list = this.selectPointsList[this.crashType].list.filter((item) => {
        if (!item.show) {
          return item;
        }
      });
      const halfSelected = list.filter((item) => item.check);
      this.selectNum = halfSelected.length;
      if (halfSelected.length == list.length) {
        this.allList = true;
        this.indeterminate = false;
      } else if (halfSelected.length == 0) {
        this.allList = false;
        this.indeterminate = false;
      } else {
        this.allList = false;
        this.indeterminate = true;
      }
    },
  },
};
</script>
<style lang="less" scoped>
.select-result-dom {
  width: 352px;
  background: #ffffff;
  box-shadow: 0px 2px 4px 0px rgba(0, 21, 41, 0.12);
  border-radius: 4px;
  > header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 14px;
    height: 36px;
    background: rgba(211, 215, 222, 0.29);
    box-shadow: inset 0px -1px 0px 0px #d3d7de;
    border-radius: 4px 4px 0px 0px;
    color: rgba(0, 0, 0, 0.9);
    padding: 0 15px;
    .iconfont {
      cursor: pointer;
    }
    > p {
      font-weight: bold;
      width: 100%;
      .total {
        width: 150px;
        margin-left: 10px;
      }
    }
  }
  .dom-content {
    padding-bottom: 5px;
    &-search {
      padding: 10px 15px;
      display: flex;
      /deep/.ivu-select {
        width: 120px;
        margin-right: 10px;
      }
      /deep/.ivu-input-wrapper {
        width: 190px;
      }
    }
    .option-show {
      display: flex;
      justify-content: space-between;
      padding: 0px 16px 16px;
      .optiop-num {
        font-size: 14px;
        font-weight: 400;
        color: rgba(0, 0, 0, 0.6);
        span {
          color: rgba(44, 134, 248, 1);
        }
      }
    }
    .layerList {
      max-height: 374px;
      height: 100%;
      overflow-y: auto;
    }
    &-type {
      padding: 0 16px;
      .header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        cursor: pointer;
        .device-type {
          width: 260px;
          display: flex;
          align-items: center;
          > span {
            color: rgba(0, 0, 0, 0.9);
            font-weight: bold;
            margin-left: 11px;
          }
          .type-total {
            color: #2c86f8;
          }
        }
        .arrowrun {
          transition: 0.2s;
          transform-origin: center;
          transform: rotateZ(90deg);
        }
        .arrow {
          transition: 0.2s;
          transform-origin: center;
          transform: rotateZ(0deg);
        }
      }
    }
    &-list {
      padding-left: 28px;
      cursor: pointer;
      > div {
        display: flex;
        align-items: center;
        height: 30px;
        width: 100%;
      }
      > div:hover {
        background: rgba(44, 134, 248, 0.1);
        > P {
          font-size: 12px;
          color: rgba(0, 0, 0, 0.8);
        }
      }
      i {
        // margin-left: 15px;
        margin-right: 10px;
      }
      .label {
        display: inline-block;
        color: #ffffff;
        white-space: nowrap;
        width: 54px;
      }
      .message {
        display: inline-block;
        color: #b4ceef;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        margin-left: 18px;
      }
      .font-green {
        color: #19a33e;
      }
      .font-yellow {
        color: #bfa631;
      }
      .font-orange {
        color: #f5852a;
      }
    }
  }
}
/deep/.ivu-input-with-suffix {
  padding-right: 0.16667rem;
}
.action-wrapper {
  padding: 10px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  .find {
    width: 100%;
  }
}
/deep/ .ivu-checkbox-wrapper {
  margin-right: 0;
}
</style>
