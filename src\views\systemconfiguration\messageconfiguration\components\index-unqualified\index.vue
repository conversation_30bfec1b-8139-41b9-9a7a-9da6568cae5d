<template>
  <div class="interface-exception auto-fill">
    <Collapse v-model="collapseValue" simple>
      <Panel name="1" class="mb-sm">
        <span class="title">指标不达标 </span>
        <i-switch
          class="fr switch"
          v-model="formData['0801'].markEnable"
          :disabled="!isEdit"
          :true-value="1"
          :false-value="0"
          @click.native.stop
        />
        <div slot="content">
          <ui-label label="通知条件：">
            <span class="base-text-color f-14">指标不达标</span>
          </ui-label>
          <ui-label label="检测任务：" class="mt-sm">
            <div class="task-list">
              <div v-for="(item, index) in receiveData['0801'].taskIds" :key="index" class="mb-sm">
                <Select
                  :value="item"
                  :disabled="!isNetworkOpen"
                  placeholder="请选择检测任务"
                  class="width-lg"
                  clearable
                  @on-change="(val) => selectTask(val, '0801', index)"
                  @on-clear="clearTask('0801', index, item)"
                >
                  <Option
                    v-for="(item, index) in listTaskSchemes"
                    :key="index"
                    :label="item.taskName"
                    :value="item.id"
                    :disabled="getTaskIdsDisabled(item.id, '0801', 'receiveData')"
                    >{{ item.taskName }}</Option
                  >
                </Select>
                <span class="mr-md ml-md">——</span>
                <Button
                  :disabled="!isNetworkOpen || !item"
                  type="default"
                  class="task-index-item"
                  :class="{ 'error-border': errorTip[`0801-${item}`] }"
                  @click="selectIndex('0801', item, index)"
                >
                  {{ getSelectLength('0801', item) }}
                </Button>
                <i
                  v-if="isNetworkOpen"
                  class="icon-font icon-tree-add ml-sm font-active-color"
                  @click="addTask('0801')"
                ></i>
                <i
                  v-if="index !== 0 && isNetworkOpen"
                  class="icon-font icon-shanchu1 ml-sm font-active-color"
                  @click="deleteTask('0801', index, item)"
                ></i>
              </div>
            </div>
          </ui-label>
          <div class="base-text-color f-14 mb-sm option-box">
            <span>通知接收人：</span>
            <div>
              <i class="icon-font icon-fuzhi" title="复制" @click="copyConfig('0801')"></i>
              <i
                :class="['icon-font icon-paste-full ml-sm', copyReceiveConfig ? null : 'not-copy']"
                title="粘贴"
                @click="paste('0801')"
              ></i>
            </div>
          </div>
          <div class="table-module auto-fill">
            <ui-table
              class="ui-table auto-fill"
              :table-columns="setTableColumns('0801')"
              :table-data="receiveData['0801'].receiveConfig"
            >
              <template #people="{ row }">
                <span
                  :class="['font-active-color', 'pointer', isNetworkOpen ? '' : 'not-allowed']"
                  @click="() => isNetworkOpen && selectNotification('0801', row)"
                >
                  {{ row.peopleList && row.peopleList.length ? '已配置' : '未配置' }}
                </span>
              </template>
              <template #option="{ index }">
                <ui-btn-tip
                  v-if="isNetworkOpen"
                  icon="icon-yichu1"
                  content="移除"
                  @handleClick="deleteRow('0801', index)"
                ></ui-btn-tip>
              </template>
            </ui-table>
          </div>
          <div class="mt-sm">
            <ui-label label="通知方式：">
              <CheckboxGroup class="notification-method" v-model="formData['0801'].template">
                <Checkbox label="system" :disabled="true">
                  <span>系统消息</span>
                  <span class="message-content" ref="0801systemTemplateRef">
                    【<span>#组织机构#</span>】的【<span>#任务名称#</span>-<span>#指标名称#</span>】检测结果不达标，请及时处理！（时间：
                    <span>#time@yyyy-MM-dd HH:mm:ss#</span>）
                  </span>
                </Checkbox>
                <Checkbox label="dialog" :disabled="!isNetworkOpen">
                  <span>系统弹框</span>
                  <span class="message-content" ref="0801dialogTemplateRef">
                    【<span>#组织机构#</span>】的【<span>#任务名称#</span>-<span>#指标名称#</span>】检测结果不达标，请及时处理！（时间：
                    <span>#time@yyyy-MM-dd HH:mm:ss#</span>）
                  </span>
                </Checkbox>
                <Checkbox label="home" :disabled="!isNetworkOpen">
                  <span>首页推送</span>
                  <span class="message-content" ref="0801homeTemplateRef">
                    【<span>#组织机构#</span>】的【<span>#任务名称#</span>-<span>#指标名称#</span>】检测结果不达标，请及时处理！（时间：
                    <span>#time@yyyy-MM-dd HH:mm:ss#</span>）
                  </span>
                </Checkbox>
                <Checkbox label="sms" :disabled="!isNetworkOpen">
                  <span>短信通知</span>
                  <span class="message-content" ref="0801smsTemplateRef">
                    【<span>#组织机构#</span>】的【<span>#任务名称#</span>-<span>#指标名称#</span>】检测结果不达标，请及时处理！（时间：
                    <span>#time@yyyy-MM-dd HH:mm:ss#</span>）
                  </span>
                </Checkbox>
              </CheckboxGroup>
            </ui-label>
          </div>
        </div>
      </Panel>
    </Collapse>
    <people-select-module
      v-model="peopleSelectShow"
      title="选择通知对象"
      :default-people-list="defaultPeopleList"
      @pushPeople="pushPeople"
    ></people-select-module>
    <IndexSelectModule
      v-model="indexSelectShow"
      :tree-data="treeData"
      :default-select-keys="defaultSelectKeys"
      :tree-loading="treeLoading"
      @onSubmit="onIndexSelectSubmit"
    ></IndexSelectModule>
  </div>
</template>
<script>
import { mapActions, mapGetters } from 'vuex';
import evaluationoverview from '@/config/api/evaluationoverview';
import governanceevaluation from '@/config/api/governanceevaluation';
import async from '../../mixins/async';
export default {
  mixins: [async],
  props: {
    // 判断编辑状态
    action: {
      type: String,
    },
  },
  data() {
    return {
      collapseValue: [1],
      listTaskSchemes: [], //检测任务
      peopleSelectShow: false,
      tableColumns: [
        {
          title: '序号',
          width: 50,
          align: 'center',
          fixed: 'left',
          type: 'index',
        },
        {
          type: 'expand',
          width: 50,
        },
        {
          title: '所属组织机构',
          minWidth: 300,
          key: 'orgName',
        },
        {
          title: '配置通知接收人',
          minWidth: 300,
          slot: 'people',
        },
        {
          width: 300,
          title: '操作',
          slot: 'option',
        },
      ],
      formData: {
        '0801': {
          markEnable: 0,
          template: ['system'], //通知方式
          taskIds: [], //通知任务来源
          receiveConfig: [], //通知接收人
          systemTemplate: '',
          dialogTemplate: '',
          homeTemplate: '',
          smsTemplate: '',
        },
      },
      // 由于后端返回的数据不能兼容前端页面所以这里要单独处理接收人、检测任务的数据
      receiveData: {
        '0801': {
          receiveConfig: [],
          taskIds: [],
          indexIds: {}, // { 检测任务id：'指标id1_指标id2_指标id3...', ... }
        },
      },
      defaultPeopleList: [],
      receiveConfigData: null, //点击配置接收人时记录
      // 选择指标
      treeLoading: false,
      indexSelectShow: false,
      indexConfigData: {}, // 对应检测任务 的 指标列表
      treeData: [],
      defaultSelectKeys: [],
      indexSelectObj: {},
      errorTip: {},
    };
  },
  async created() {
    await this.getListTaskSchemes();
    /**
     * 0000：通用参数
     * 0101：外部对接异常
     * 0201：系统运维异常
     * 0301：接口异常-人脸相关接口
     * 0302：接口异常-车辆相关接口
     * 0401：平台离线-联网平台离线
     * 0402：平台离线-人脸视图库离线
     * 0403：平台离线-车辆视图库离线
     * 0501：设备离线-视频监控设备离线
     * 0502：设备离线-人脸卡口设备离线
     * 0503：设备离线-车辆卡口设备离线
     * 0601： 资产审核不通过
     * 0701： 工单处理
     * 0801: 指标不达标
     */
    await this.initMx(['0801']);
    this.dealReceiveCofingEchoMx();
    this.dealTaskIdsMx();
  },
  methods: {
    ...mapActions({
      setCopyReceiveConfig: 'systemconfiguration/setCopyReceiveConfig',
    }),
    selectNotification(type, org) {
      this.receiveConfigData = {
        type,
        org,
      };
      this.defaultPeopleList = this.getDefaultPeopleMx(org.peopleList);
      this.peopleSelectShow = true;
    },
    pushPeople(list) {
      // 如果已经保存的数据中的联系电话已经有，则以已保存的电话为填写值
      const peopleList = list.map((row) => {
        const people = this.defaultPeopleList.find((people) => people.id === row.id);
        if (people && people.phone) {
          this.$set(row, 'phone', people.phone);
        } else {
          this.$set(row, 'phone', row.phoneNumber);
        }
        return row;
      });
      // 根据点击selectNotification时的组织机构赋值
      const receiveOrg = this.receiveData[this.receiveConfigData.type].receiveConfig.find(
        (row) => row.orgCode === this.receiveConfigData.org.orgCode,
      );
      receiveOrg.peopleList = peopleList.map(({ id, orgCode, orgName, name, username, phone }) => {
        return {
          id,
          name,
          orgCode,
          orgName,
          username,
          phone,
          orgCodeByAttach: receiveOrg.orgCode,
          orgCodeByAttachName: receiveOrg.orgName,
        };
      });
    },
    reset() {
      this.receiveData = this.$util.common.deepCopy(this.initializedReceiveDataMx);
      this.resetMx();
    },
    // 保存前的一些校验
    validateFn() {
      let flag = [];
      this.receiveData['0801'].taskIds.forEach((item) => {
        let valid = item && !this.receiveData['0801'].indexIds[item];
        this.$set(this.errorTip, `0801-${item}`, valid);
        flag.push(valid);
      });
      let vaild0801 = flag.some((item) => item);
      if (vaild0801) {
        this.$Message.error('【指标不达标-检测任务】中必须选择指标！');
        return vaild0801;
      }
      return false;
    },
    async save() {
      try {
        this.dealReceiveCofingSaveMx();
        this.dealTaskIdsSaveMx();
        this.formData['0801'].systemTemplate = this.$refs['0801systemTemplateRef'].innerText;
        this.formData['0801'].dialogTemplate = this.$refs['0801dialogTemplateRef'].innerText;
        this.formData['0801'].homeTemplate = this.$refs['0801homeTemplateRef'].innerText;
        this.formData['0801'].smsTemplate = this.$refs['0801smsTemplateRef'].innerText;
        await this.saveMx(['0801']);
      } catch (err) {
        console.log(err);
      }
    },
    async getListTaskSchemes() {
      try {
        this.listTaskSchemes = [];
        let {
          data: { data },
        } = await this.$http.post(evaluationoverview.getListTaskSchemes, {
          isDel: 0,
        });
        this.listTaskSchemes = data || [];
      } catch (e) {
        console.log(e);
      }
    },
    addTask(type) {
      this.receiveData[type].taskIds.push('');
    },
    deleteTask(type, index, item) {
      this.setTaskIdsInfoMx(type, this.receiveData[type].taskIds, index);
      this.receiveData[type].taskIds.splice(index, 1);
      this.delReceiveOrgMx(type, this.receiveData);
      this.receiveData[type].indexIds[item] = '';
    },
    async selectTask(val, type, index) {
      if (!val) return;
      this.setTaskIdsInfoMx(type, this.receiveData[type].taskIds, index);
      this.$set(this.receiveData[type].taskIds, index, val);
      this.$set(this.receiveData[type].indexIds, val, '');
      await this.setReceiveOrgMx(val, type);
      this.delReceiveOrgMx(type, this.receiveData);
    },
    clearTask(type, index, item) {
      this.setTaskIdsInfoMx(type, this.receiveData[type].taskIds, index);
      this.receiveData[type].taskIds[index] = '';
      this.delReceiveOrgMx(type, this.receiveData);
      this.receiveData[type].indexIds[item] = '';
    },
    deleteRow(type, index) {
      this.receiveData[type].receiveConfig.splice(index, 1);
    },
    copyConfig(type) {
      this.setCopyReceiveConfig(this.$util.common.deepCopy(this.receiveData[type].receiveConfig));
      this.$Message.success('复制成功');
    },
    paste(type) {
      if (!this.copyReceiveConfig) return;
      this.receiveData[type].receiveConfig = this.$util.common.deepCopy(this.copyReceiveConfig);
    },
    async selectIndex(key, taskSchemeId, index) {
      this.indexSelectShow = true;
      this.treeData = [];
      if (
        !this.indexConfigData[taskSchemeId] ||
        (this.indexConfigData[taskSchemeId] && this.indexConfigData[taskSchemeId].length === 0)
      ) {
        await this.getTaskIndexGeneralConfig(taskSchemeId);
      }
      this.indexSelectObj = { key: key, index: index, taskSchemeId: taskSchemeId };
      let keys = this.receiveData[key].indexIds[taskSchemeId]
        ? this.receiveData[key].indexIds[taskSchemeId].split('_')
        : [];
      this.defaultSelectKeys = keys.map((item) => Number(item));
      let tree = [];
      this.global.indexTypeList.forEach((item) => {
        let children = this.indexConfigData[taskSchemeId].filter((indexItem) => indexItem.indexModule == item.id);
        if (children.length > 0) {
          tree.push({
            ...item,
            indexId: `indexModule-${item.id}`, // 自定义，保存到后端的数据，是不需要这一层的id的
            indexName: item.title,
            children: children,
          });
        }
      });
      this.treeData = tree;
    },
    // 检测任务 已配置 的指标列表
    async getTaskIndexGeneralConfig(taskSchemeId) {
      try {
        this.$set(this.indexConfigData, taskSchemeId, []);
        this.treeLoading = true;
        let params = {
          taskSchemeId: taskSchemeId,
        };
        let {
          data: { data },
        } = await this.$http.post(governanceevaluation.getTaskIndexGeneralConfig, params);
        this.$set(this.indexConfigData, taskSchemeId, data.indexConfigData || []);
      } catch (err) {
        console.log(err);
      } finally {
        this.treeLoading = false;
      }
    },
    /**
     * 已选择的指标列表 --》 indexIds：{ 检测任务id：'指标id1_指标id2_指标id3...' }
     */
    onIndexSelectSubmit(checkedKeys) {
      let { key, taskSchemeId } = this.indexSelectObj;
      this.receiveData[key].indexIds[taskSchemeId] = checkedKeys.join('_');
      this.$set(this.errorTip, `${key}-${taskSchemeId}`, checkedKeys.length === 0 ? true : false);
    },
    getSelectLength(key, item) {
      let selectKeys = this.receiveData[key].indexIds[item] ? this.receiveData[key].indexIds[item].split('_') : [];
      return selectKeys.length > 0 ? `已选择${selectKeys.length}个指标` : '请选择指标';
    },
  },
  watch: {},
  computed: {
    ...mapGetters({
      copyReceiveConfig: 'systemconfiguration/getCopyReceiveConfig',
    }),
    isEdit() {
      return this.action === 'edit';
    },
    isNetworkOpen() {
      return this.formData['0801'].markEnable === 1 && this.action === 'edit';
    },
  },
  components: {
    UiTable: require('@/components/ui-table.vue').default,
    PeopleSelectModule: require('@/components/people-select-module.vue').default,
    IndexSelectModule: require('./index-select-mpdule.vue').default,
  },
};
</script>
<style lang="less" scoped>
.interface-exception {
  overflow-y: auto;
  .task-list {
    width: 580px;
    max-height: 300px;
    overflow-y: auto;
    @{_deep}.task-index-item {
      width: 230px;
      height: 32px;
      line-height: 32px;
      color: var(--color-content) !important;
      &[disabled] {
        color: var(--color-input-disabled) !important;
        &:hover {
          background: var(--bg-btn-default);
          border-color: var(--border-btn-primary-disabled);
          color: var(--color-content) !important;
        }
      }
      &.error-border {
        border-color: #cf3939;
      }
    }
  }
  .table-module {
    clear: both;
    height: 260px;
  }
  .notification-method {
    label {
      display: block;
      margin-bottom: 5px;
    }
    &:nth-child(n + 2) {
      margin-left: 80px;
    }
  }
  .not-allowed {
    cursor: not-allowed;
  }
  .message-content {
    margin-left: 10px;
    display: inline-block;
    border: 1px solid var(--border-input);
    padding: 0 10px;
    background-color: var(--bg-input);
    width: 760px;
    vertical-align: text-top;
    border-radius: 4px;
    span {
      color: var(--color-title);
    }
  }
  .option-box {
    display: flex;
    align-items: center;
    justify-content: space-between;
    i {
      color: var(--color-primary);
    }
    .not-copy {
      color: var(--color-btn-primary-disabled);
      cursor: not-allowed;
    }
  }
  @{_deep} .ivu-collapse {
    background: transparent;
    border: none;
    .ivu-collapse-item {
      border: none;
      .ivu-collapse-header {
        border: none;
        color: var(--color-navigation-title);
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        background-color: var(--bg-collapse-item);
        height: 50px;
        line-height: 50px;
        margin: 0 20px;
        .title {
          font-size: 16px;
          font-weight: 900;
        }
        .switch {
          top: 50%;
          transform: translateY(-50%);
          margin-right: 20px;
        }
        i {
          float: right;
          color: #fff;
          line-height: 50px;
          font-size: 20px;
        }
      }
      .ivu-collapse-content {
        padding: 0;
        background: var(--bg-content);
        .ivu-collapse-content-box {
          padding: 10px 40px;
        }
      }
      &.ivu-collapse-item-active {
        background: var(--bg-content);
      }
    }
  }
}
</style>
