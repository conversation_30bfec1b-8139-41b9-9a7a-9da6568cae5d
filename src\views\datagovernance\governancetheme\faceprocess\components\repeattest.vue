<template>
  <div class="repeattest">
    <ui-modal v-model="visible" title="短时大量抓拍检测" width="40rem">
      <div class="repeattest-content">
        <div class="repeattest-content-tips">图像重复：重复图片或者同一点位极短时间连续多次抓拍同一目标。</div>
        <Form ref="formValidate" :model="formValidate" :label-width="80">
          <FormItem v-for="(item, index) in formValidate.items" :key="index" label="">
            <div class="repeattest-content-list">
              <span>{{ item.label1 }}</span>
              <FormItem
                :prop="'items.' + index + '.value'"
                :rules="[
                  {
                    required: true,
                    message: '请输入数字',
                    trigger: 'blur',
                    type: 'number',
                  },
                ]"
              >
                <Input v-model="item.value" number style="width: 118px" />
              </FormItem>
              <span>{{ item.label2 }}</span>
            </div>
          </FormItem>
        </Form>
      </div>
      <template slot="footer">
        <Button type="primary" @click="save">保&nbsp;存</Button>
      </template>
    </ui-modal>
  </div>
</template>
<script>
import governancetheme from '@/config/api/governancetheme';
export default {
  props: {
    repeatForm: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      visible: false,
      formValidate: {},
    };
  },
  mounted() {
    this.formValidate = JSON.parse(JSON.stringify(this.repeatForm));
  },
  methods: {
    init(processOptions) {
      this.topicComponentId = processOptions.topicComponentId;
      this.visible = true;
      this.getUploadTime();
    },
    async getUploadTime() {
      try {
        let params = {
          topicComponentId: this.topicComponentId,
        };
        let res = await this.$http.get(governancetheme.queryTopicComponentOfUploadTime, { params });
        const datas = res.data.data;
        if (datas.extraParam) {
          let extraParam = JSON.parse(datas.extraParam);
          if (!this.formValidate.items.length) {
            return false;
          }
          this.formValidate.items = this.formValidate.items.map((item) => {
            item.value = extraParam[item.key];
            return item;
          });
        }
      } catch (error) {
        console.log(error);
      }
    },
    save() {
      this.$refs['formValidate'].validate((valid) => {
        if (valid) {
          let extraParam = {};
          this.formValidate.items.forEach((item) => {
            extraParam[item.key] = item.value;
          });
          extraParam = JSON.stringify(extraParam);
          this.updateUploadTime(extraParam);
        } else {
          this.$Message.error('请将信息填写完整！');
        }
      });
    },
    async updateUploadTime(extraParam) {
      try {
        let params = {
          topicComponentId: this.topicComponentId,
          extraParam: extraParam,
        };
        await this.$http.post(governancetheme.updateTopicComponentOfUploadTime, params);
        this.reset();
        this.$emit('render');
        this.$Message.success('图像重复检测成功！');
      } catch (error) {
        console.log(error);
      }
    },
    reset() {
      this.visible = false;
      this.formValidate = this.repeatForm;
    },
  },
  watch: {},
  components: {},
};
</script>
<style lang="less" scoped>
.repeattest {
  &-content {
    width: 100%;
    &-tips {
      margin-bottom: 28px;
      font-size: 14px;
      color: var(--color-primary);
      text-align: center;
    }
    &-list {
      width: 100%;
      margin-bottom: 19px;
      display: flex;
      align-items: center;
      font-size: 14px;
      color: #ffffff;
      span {
        display: inline-block;
        flex: 1;
        &:first-child {
          margin-right: 10px;
          text-align: right;
        }
        &:last-child {
          margin-left: 10px;
          text-align: left;
        }
      }
    }
  }
}
@{_deep} .ivu-modal-body {
  padding: 9px 51px 37px;
}
@{_deep} .ivu-form-item {
  margin-bottom: 3px;
}
// @{_deep} .ivu-input-wrapper {
//   margin: 0 10px;
// }
</style>
