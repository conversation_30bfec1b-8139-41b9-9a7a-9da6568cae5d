<template>
  <div class="people-archive-container">
    <div class="content" id="content930">
      <!-- 基础信息 -->
      <BasicInformation :labelType="2" :baseInfo="baseInfo" />
      <div class="main-information">
        <!-- 通联方式 -->
        <Communication
          :id="'communication'"
          class="m-b10"
          :list="communicationList"
          :loading="communicationLoading"
        ></Communication>
        <!-- 涉案/警记录 -->
        <Involve
          :id="'involve'"
          class="m-b10"
          :list="involveList"
          :loading="involveLoading"
        ></Involve>
        <!-- 人像抓拍 -->
        <PortraitCapture
          :id="'portrait_capture' + routeParams"
          title="人像抓拍"
          :baseInfo="baseInfo"
          :archiveNo="archiveNo"
          :list="portraitCaptureList"
          :loading="portraitCaptureLoading"
          class="m-b10"
        />
        <!-- 人车同拍 -->
        <PeopleCarCapture
          :id="'people_car_capture' + routeParams"
          title="人车同拍"
          :list="peopleCarCaptureList"
          :loading="peopleCarCaptureLoading"
          class="m-b10"
        />
        <!-- 位置信息 -->
        <PositionInformation
          :id="'position_information' + routeParams"
          title="位置信息"
          :list="latestLocationList"
          :latestLocationLoading="latestLocationLoading"
          :oftenGoList="oftenGoList"
          :oftenGoLoading="oftenGoLoading"
          :positionPoints="positionPoints"
          :heatData="heatData"
          class="m-b10"
          @on-change="frequentedList"
        />
      </div>
      <div class="anchor-point-infomation">
        <!-- <Button type="primary" class="export-btn">导出</Button> -->
        <!-- 锚点 -->
        <UiAnchor :anchorLinkList="sumAnchorLinkList" />
      </div>
    </div>
  </div>
</template>
<script>
import BasicInformation from "@/views/holographic-archives/one-person-one-archives/people-archive/profile/components/basic-information.vue";
import Communication from "./components/communication.vue";
import Involve from "./components/involve.vue";
import PortraitCapture from "@/views/holographic-archives/one-person-one-archives/people-archive/profile/components/portrait-capture";
import PeopleCarCapture from "@/views/holographic-archives/one-person-one-archives/people-archive/profile/components/people-car-capture";
import PositionInformation from "@/views/holographic-archives/one-person-one-archives/people-archive/profile/components/position-information";
import Alarm from "./components/alarm.vue";
import UiAnchor from "@/components/ui-anchor.vue";
import {
  getPortraitCaptureList,
  getPeopleCarCaptureList,
  getLatestLocationList,
  getFrequentedList,
  drivingVehicle,
} from "@/api/realNameFile";
import {
  getPersonCommunication,
  getPersonCase,
  getPersonIncidents,
} from "@/api/monographic/base.js";
import mixinWidget from "@/mixins/mixinWidget.js";
import { mapGetters } from "vuex";

export default {
  name: "people-archive",
  props: {
    baseInfo: {
      type: Object | String,
      default: {},
    },
  },
  components: {
    BasicInformation,
    Communication,
    Involve,
    PortraitCapture,
    PeopleCarCapture,
    PositionInformation,
    UiAnchor,
    Alarm,
  },
  mixins: [mixinWidget],
  data() {
    return {
      archiveNo: "",
      routeParams: "",
      anchorLinkList: [
        { href: "#communication", title: "通联方式" },
        { href: "#involve", title: "涉案/警记录" },
        { href: "#portrait_capture", title: "人像抓拍" },
        { href: "#people_car_capture", title: "人车同拍" },
        { href: "#position_information", title: "位置信息" },
      ],
      communicationLoading: false,
      communicationList: [],
      involveList: [],
      involveLoading: false,
      assetsUnderNameList: [],
      assetsUnderNameLoading: false,
      portraitCaptureList: [],
      portraitCaptureLoading: false,
      peopleCarCaptureList: [],
      peopleCarCaptureLoading: false,
      latestLocationList: [],
      latestLocationLoading: false,
      oftenGoList: [],
      oftenGoLoading: false,
      latestLocationPoints: [],
      positionPoints: [],
      heatData: [],
    };
  },
  computed: {
    ...mapGetters({
      graphObj: "systemParam/graphObj", // 是否有图谱
    }),
    sumAnchorLinkList() {
      if (this.graphObj) {
        return this.anchorLinkList;
      } else {
        return this.anchorLinkList.filter(
          (e) => e.href !== "#relationship_information"
        );
      }
    },
  },
  async created() {
    let { archiveNo, source, initialArchiveNo } = this.$route.query;
    this.archiveNo = archiveNo;
    this.routeParams = `?archiveNo=${archiveNo}&source=${source}&initialArchiveNo=${initialArchiveNo}`;
    this.sumAnchorLinkList.forEach((v) => {
      v.href = v.href + this.routeParams;
    });
    // 人像抓拍
    this.portraitCaptureLoading = true;
    getPortraitCaptureList({
      archiveNo: this.archiveNo,
      dataSize: 7,
      dataType: 1,
    })
      .then((res) => {
        this.portraitCaptureList = res.data;
      })
      .catch(() => {})
      .finally(() => {
        this.portraitCaptureLoading = false;
      });
    // 人车同拍
    this.peopleCarCaptureLoading = true;
    getPeopleCarCaptureList({
      archiveNo: this.archiveNo,
      dataSize: 5,
      dataType: 1, //1-实名档案，2-路人档案
    })
      .then((res) => {
        this.peopleCarCaptureList = res.data;
      })
      .catch(() => {})
      .finally(() => {
        this.peopleCarCaptureLoading = false;
      });
    // 最新位置
    this.latestLocationLoading = true;
    getLatestLocationList({
      archiveNo: this.archiveNo,
      dataSize: 5,
      dataType: 1,
    })
      .then((res) => {
        this.latestLocationList = res.data;
        this.latestLocationPoints = this.latestLocationList.map((v) => {
          return {
            ...v,
            ...v.geoPoint,
          };
        });
        // 将最新位置点位和常去地点位合并
        this.positionPoints = this.positionPoints.concat(
          this.latestLocationPoints
        );

        this.frequentedList(1);
      })
      .catch(() => {})
      .finally(() => {
        this.latestLocationLoading = false;
      });
  },
  mounted() {
    this.getCommunicationList();
    this.getInvolveList();
  },
  methods: {
    async getOwnVehicleList(source) {
      try {
        if (!this.archiveNo) return;
        this.assetsUnderNameLoading = true;
        let params = {
          archiveNo: this.archiveNo,
          dataSize: 5,
          dataType: source == "zdr" ? 3 : 1,
        };
        //   let res = await getOwnVehicleByIdCard(params);
        let res = await drivingVehicle(params);
        this.assetsUnderNameList = res.data;
      } catch (e) {
        console.log(e);
      } finally {
        this.assetsUnderNameLoading = false;
      }
    },
    frequentedList(val, startDate, endDate) {
      this.oftenGoLoading = true;
      getFrequentedList({
        archiveNo: this.archiveNo,
        dataRange: val,
        dataType: 1,
        startDate,
        endDate,
      })
        .then((res) => {
          let { locationList, heatmapList } = res.data;
          this.oftenGoList = locationList;
          // 常去地点位
          let positionPoints = this.oftenGoList.map((v) => {
            if (v.type === 1) {
              // 人脸抓拍位置
              return {
                ...v.geoPoint,
                ...v,
                type: "face",
                markerIconUrl: require("@/assets/img/archives/marker_face.png"),
              };
            } else if (v.type === 2) {
              // 车辆抓拍位置
              return {
                ...v.geoPoint,
                ...v,
                type: "vehicle",
                markerIconUrl: require("@/assets/img/archives/marker_vehicle.png"),
              };
            } else if (v.type === 3) {
              // IMSI感知数据位置
              return {
                ...v.geoPoint,
                ...v,
                type: "imsi",
                markerIconUrl: require("@/assets/img/archives/marker_imsi.png"),
              };
            }
          });
          // 将最新位置点位和常去地点位合并
          this.positionPoints = [
            ...this.latestLocationPoints,
            ...positionPoints,
          ];
          // 常去地热力图
          this.heatData = heatmapList.map((v) => {
            return {
              ...v.geoPoint,
              numCount: v.times,
            };
          });
        })
        .catch(() => {})
        .finally(() => {
          this.oftenGoLoading = false;
        });
    },
    // 通联信息
    async getCommunicationList() {
      this.communicationLoading = true;
      let { data = [] } = await getPersonCommunication(this.archiveNo);
      this.communicationList = data;
      this.communicationLoading = false;
    },
    // 涉案列表
    async getInvolveList() {
      this.involveLoading = true;
      try {
        let { data } = await getPersonCase(this.archiveNo);
        this.involveList = data;
        // 涉警暂时没信息
        let { dataIncidents } = await getPersonIncidents(this.archiveNo);
        this.involveList.push(...dataIncidents);
      } catch (err) {
        console.log(e);
      } finally {
        this.involveLoading = false;
      }
    },
  },
};
</script>
<style lang="less" scoped>
.m-b10 {
  margin-bottom: 10px;
}
.people-archive-container {
  padding: 16px 10px 0 10px;
  display: flex;
  flex: 1;
  overflow: hidden;
  .content {
    display: flex;
    height: 100%;
    overflow-y: auto;
    overflow-x: hidden;
    position: relative;
    flex: 1;
    .main-information {
      width: 1442px;
      height: min-content;
      padding: 0 10px;
      margin-left: 350px;
      /deep/ .ui-card {
        overflow: unset !important;
        .card-head {
          overflow: hidden;
          border-top-left-radius: 4px;
        }
      }
    }
    .anchor-point-infomation {
      width: 100px;
      position: fixed;
      top: 78px;
      right: 18px;
      z-index: 9;
      .export-btn {
        margin-bottom: 10px;
      }
    }
  }
}
</style>
