<template>
  <ul v-if="!!iconList.length" class="icon-ul">
    <li v-for="(item, index) of iconList" :key="index" :style="item.style" class="icon-li f-14">
      <div class="icon-bg inline mr-sm">
        <i class="icon-font f-12" :class="item.iconName"></i>
      </div>
      <span>{{ item.name }} </span>
      <span :style="item.countStyle"> {{ getCount(item) }} </span>
      <i class="icon-font f-14 vt-middle" v-if="item.tailIcon" :class="item.tailIcon"></i>
      <slot></slot>
    </li>
  </ul>
</template>
<script>
/**
 * 评测结果 检测明细
 */
export default {
  /**
   * {
  name: '视频监控总量:',
  count: '0',
  countStyle: {
    color: '#05FEF5'
  },
  style: {},
  iconName: 'icon-shipinjiankongjianshezongliang',
  fileName: 'asycTimes'
}
   */
  props: {
    iconList: {
      default: () => [],
    },
  },
  data() {
    return {};
  },
  created() {},
  methods: {
    getCount(item) {
      return item.count || (item.type === 'percent' ? '0%' : 0);
    },
  },
  watch: {},
  components: {},
};
</script>
<style lang="less" scoped>
[data-theme='light'],
[data-theme='deepBlue'] {
  .icon-li {
    color: rgba(0, 0, 0, 0.6);
  }
  .icon-font {
    color: #fff;
  }
  .icon-bg {
    background: url('~@/assets/img/async-bg-light.png');
    background-size: cover;
    height: 33px;
  }
}
.icon-ul {
  display: flex;
  align-items: center;
}
.icon-li {
  margin-right: 40px;
  color: #f5f5f5;
  height: 30px;
  line-height: 30px;
  &:last-child {
    margin-right: 0;
  }
}
.icon-font {
  color: #73d2f6;
  margin: 0 auto;
}
.icon-bg {
  background: url('~@/assets/img/async-bg.png') no-repeat no-repeat;
  width: 30px;
  height: 30px;
  align-items: center;
  text-align: center;
}
</style>
