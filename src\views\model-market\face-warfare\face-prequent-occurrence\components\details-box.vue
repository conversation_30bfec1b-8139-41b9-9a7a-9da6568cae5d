<!--
    * @FileDescription: 落脚地分析
    * @Author: H
    * @Date: 2023/01/16
    * @LastEditors: 
    * @LastEditTime: 
 -->
<template>
  <div class="details-box" :class="{ 'details-box-pack': footerUpDown }">
    <div class="box-hint">
      <i class="iconfont icon-jiantou" @click="handleback"></i>
      <span @click="handleback">返回频繁出没结果</span>
    </div>
    <div class="box-content">
      <div class="title">
        <div class="title-left">
          <!-- <p>{{ dateObj.deviceName }}</p> -->
          <p>抓拍详情</p>
        </div>
        <Icon type="ios-close" @click="handleback" />
      </div>
      <ul class="box-ul">
        <li
          class="box-li"
          :class="{
            'box-li-pack': !packUpDown[index],
            'box-li-packend': index == dataList.length - 1 && packUpDown[index],
          }"
          v-for="(item, index) in dataList"
          :key="index"
        >
          <div class="box-li-top">
            <Icon type="md-radio-button-on"></Icon>
          </div>
          <div class="box-li-bottom">
            <div class="time-title" @click="handletimelist(item, index)">
              <p>
                <span class="time-date">{{ item.dateStr }}</span>
                <span class="time-num">{{ item.count }}次</span>
              </p>
              <p
                class="triangle"
                :class="{ 'active-triangle': !packUpDown[index] }"
              ></p>
            </div>
            <div
              class="child_list"
              v-for="(it, ind) in item.children"
              :key="ind"
            >
              <p class="sec-radio"></p>
              <div class="content-top" @click="handleListTrack(it, ind)">
                <div class="content-top-img">
                  <img v-lazy="it.traitImg" alt="" />
                </div>
                <div class="content-top-right">
                  <span class="ellipsis">
                    <ui-icon type="time" :size="14"></ui-icon>
                    <span>{{ it.absTime }}</span>
                  </span>
                  <span class="ellipsis">
                    <ui-icon type="location" :size="14"></ui-icon>
                    <span>{{ it.deviceName }}</span>
                  </span>
                </div>
              </div>
            </div>
          </div>
        </li>
        <ui-empty v-if="dataList.length === 0 && loading == false"></ui-empty>
        <ui-loading v-if="loading"></ui-loading>
      </ul>
    </div>
    <div
      class="footer"
      :class="{ packArrow: footerUpDown }"
      @click="handlePackup"
    >
      <img :src="packUrl" alt="" />
      <p>{{ footerUpDown ? "展开" : "收起" }}</p>
    </div>
  </div>
</template>

<script>
import { countByDate, queryFaceTrajectory } from "@/api/modelMarket";
import { dateTime } from "@/util/modules/common";
export default {
  name: "",
  components: {},
  data() {
    return {
      dataList: [],
      total: 0,
      packUpDown: [],
      footerUpDown: false,
      packUrl: require("@/assets/img/model/icon/arrow.png"),
      loading: false,
      page: {
        pageNumber: 1,
        pageSize: 100,
      },
      dateObj: {},
      detailsList: [],
    };
  },
  watch: {},
  computed: {},
  created() {},
  mounted() {},
  methods: {
    init(item, data) {
      this.searchParams = { ...data, vid: item.vid };
      this.dateObj = item;
      this.handleQueryList(item, data);
    },
    handleQueryList(item, data) {
      this.loading = true;
      let params = {
        vid: item.vid,
        ...data,
        ...this.page,
      };
      countByDate(params)
        .then((res) => {
          res.data.forEach((item) => {
            item.children = [];
          });
          this.dataList = res.data;
        })
        .finally(() => {
          this.loading = false;
        });
    },
    handleTimeList2(item, index) {
      let endDateTime = this.searchParams.endDate.split(" ")[0];
      let startDateTime = this.searchParams.startDate.split(" ")[0];
      let startDate = "",
        endDate = "";
      if (item.dateStr == startDateTime) {
        startDate = this.searchParams.startDate;
      } else {
        startDate = item.dateStr + " 00:00:00";
      }
      if (item.dateStr == endDateTime) {
        endDate = this.searchParams.endDate;
      } else {
        endDate = item.dateStr + " 23:59:59";
      }
      let params = {
        ...this.searchParams,
        endDate: endDate,
        startDate: startDate,
        ...this.page,
      };
      this.loading = true;
      queryFaceTrajectory(params)
        .then((res) => {
          this.dataList[index].children = [...res.data.entities];
          this.total = res.data.total || 0;
          this.dataList[index].children.forEach((item) => {
            item["lat"] = item.geoPoint.lat;
            item["lon"] = item.geoPoint.lon;
          });
          this.detailsList = this.detailsList.concat(res.data.entities);
          this.$emit("oftenHaunt", this.detailsList);
          this.$forceUpdate();
        })
        .finally(() => {
          this.loading = false;
        });
    },
    handleListTrack(item) {
      this.$emit("chooseNormalPoint", item);
    },
    handletimelist(item, index) {
      this.$set(this.packUpDown, index, !this.packUpDown[index]);
      if (this.packUpDown[index] && this.dataList[index].children.length == 0) {
        // this.dataList[index].children = [];
        // this.detailsList = [];
        this.handleTimeList2(item, index);
      }
    },
    handleback() {
      this.$emit("goback");
    },
    // 收缩、展开
    handlePackup() {
      this.footerUpDown = !this.footerUpDown;
    },
  },
};
</script>

<style lang="less" scoped>
@import "../../../style/index";
@import "../../../style/timeLine";
.details-box {
  width: 370px;
  position: absolute;
  right: 10px;
  top: 10px;
  height: calc(~"100% - 30px");
  transition: height 0.2s ease-out;
  .box-hint {
    width: 370px;
    height: 40px;
    background: #ffffff;
    box-shadow: 0px 3px 5px 0px rgba(0, 0, 0, 0.3);
    border-radius: 4px;
    filter: blur(0px);
    color: #2c86f8;
    font-size: 14px;
    line-height: 40px;
    padding-left: 14px;
    .icon-jiantou {
      transform: rotate(90deg);
      display: inline-block;
      cursor: pointer;
    }
    span {
      font-size: 14px;
      cursor: pointer;
      margin-left: 10px;
    }
  }
  .box-content {
    background: #ffffff;
    box-shadow: 0px 3px 5px 0px rgba(0, 0, 0, 0.3);
    margin-top: 10px;
    height: calc(~"100% - 50px");
    // overflow-y: auto;
    .box-ul {
      overflow-y: auto;
    }
    .title {
      .title-left {
        display: flex;
        align-items: center;
        .title-icon {
          position: relative;
          background: url("~@/assets/img/map/trajectory-red.png") no-repeat;
          background-size: 100% 100%;
          width: 20px;
          height: 22px;
          color: #ea4a36;
          > span {
            position: absolute;
            top: -12px;
            width: 20px;
            font-size: 10px;
            color: #ea4a36;
            text-align: center;
          }
        }
      }
    }
  }
  .footer {
    color: #000000;
    position: absolute;
    bottom: -8px;
    left: 50%;
    transform: translate(-50%, 0px);
    background: #fff;
    width: 100%;
    z-index: 30;
  }
}
.details-box-pack {
  height: 120px;
  transition: height 0.2s ease-out;
  overflow: hidden;
}
</style>
