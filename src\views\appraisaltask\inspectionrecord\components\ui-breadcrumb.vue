<template>
  <div class="breadcrumb-container">
    <span class="box vt-middle"></span>
    <span class="ml-sm vt-middle pointer">
      <span
        v-for="(item, index) in dataList"
        :key="index"
        class="address color-white f-14"
        :class="disabled ? 'disabled' : ''"
        @click="handleClick(item, index)"
        >{{ `${item.add} ${index !== dataList.length - 1 ? '>' : ''}` }}</span
      >
    </span>
  </div>
</template>

<script>
export default {
  name: 'ui-breadcrumb',
  props: {
    data: {
      type: Array,
      default: () => {
        return [];
      },
    },
    disabled: {
      default: false,
    },
  },
  data() {
    return {
      dataList: [],
    };
  },
  watch: {
    data: {
      handler: function (val) {
        this.dataList = val;
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {
    handleClick(item, index) {
      if (this.disabled || index === this.dataList.length - 1) return;
      this.dataList.splice(index + 1, this.dataList.length);
      this.$emit('change', item);
    },
  },
};
</script>

<style lang="less" scoped>
[data-theme='light'],
[data-theme='deepBlue'] {
  .address:last-child {
    color: #000000;
  }
}
.address:last-child {
  color: var(--color-primary);
  cursor: pointer;
}
.color-white {
  color: var(--color-content);
}
.active {
  color: var(--color-primary);
}
.box {
  display: inline-block;
  width: 4px;
  height: 14px;
  background: var(--color-primary);
}
.breadcrumb-container {
  position: relative;
  display: inline-block;
}
.disabled {
  cursor: default !important;
}
</style>
