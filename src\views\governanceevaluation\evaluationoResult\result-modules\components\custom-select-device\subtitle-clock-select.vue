<template>
  <basic-select v-bind="getAttrs" v-on="$listeners" @reset="reset">
    <template #select>
      <Select
        v-model="tempErrorCodes.reason"
        placeholder="请选择不合格原因"
        class="width-md"
        multiple
        clearable
        @on-change="onChangeReason"
      >
        <Option v-for="item in errorCodesOptionsReason" :value="item" :key="item">{{ item }}</Option>
      </Select>
      <span class="base-text-color ml-xs mr-xs">-</span>
      <Select
        v-model="tempErrorCodes.desc"
        placeholder="请选择不合格原因"
        class="width-md"
        multiple
        clearable
        :disabled="tempErrorCodes.reason.length === 0"
      >
        <Option v-for="item in errorCodesOptionsDesc" :value="item.code" :key="item.code">{{ item.desc }}</Option>
      </Select>
    </template>
    <!-- 表格插槽 -->
    <template slot="outcome" slot-scope="{ row }">
      <Tag v-if="row.qualified in qualifiedColorConfig" :color="qualifiedColorConfig[row.qualified].color">
        {{ qualifiedColorConfig[row.qualified].dataValue }}
      </Tag>
      <span class="ml-sm" v-if="row.dataMode === '3'"> (人工) </span>
    </template>
    <template #detectionMode="{ row }">
      {{ row.detectionMode == 1 ? 'OCR' : 'SDK' }}
    </template>
    <template #phyStatus="{ row }">
      <span>
        {{ !row.phyStatusText ? '--' : row.phyStatusText }}
      </span>
    </template>
    <template #reason="{ row }">
      <Tooltip :content="row.reason" transfer max-width="150">
        {{ row.reason }}
      </Tooltip>
    </template>
    <template #tagNames="{ row }">
      <tags-more :tag-list="row.tagList || []"></tags-more>
    </template>
  </basic-select>
</template>
<script>
import dealWatch from '@/mixins/deal-watch';
import errorCodesMixin from '@/views/governanceevaluation/evaluationoResult/util/Mixins/errorCodesMixin.js';
export default {
  mixins: [errorCodesMixin, dealWatch],
  props: {
    qualifiedColorConfig: {},
  },
  data() {
    return {
      formData: {
        deviceId: '',
        deviceName: '',
        outcome: '',
        errorCodes: null,
      },
    };
  },
  created() {
    // errorCodesMixin.js
    this.getQualificationList(this.$attrs.moduleData);
  },
  methods: {},
  watch: {},
  computed: {
    getAttrs() {
      return {
        searchData: this.formData,
        ...this.$attrs,
      };
    },
  },
  components: {
    BasicSelect: require('./basic-select.vue').default,
    TagsMore: require('@/components/tags-more.vue').default,
  },
};
</script>
<style lang="less" scoped></style>
