<template>
  <div class="layout">
    <tabsPage :list="alarmList" :selectLi="selectLi" @change="tabChange"/>
    <component :ref="isAndRef" :is="isAndRef" :compareType="selectLi" :key="componentKey"/>
  </div>
</template>
<script>
  import tabsPage from './components/tabs.vue'
  import peoplePage from './components/people.vue'
  import vehiclePage from './components/vehicle.vue'
  import sensoryPage from './components/sensory.vue'
  export default {
    name: 'my-control-task',
    components: { 
      tabsPage, peoplePage, vehiclePage, sensoryPage
    },
    data() {
      return {
        isAndRef: 'peoplePage',
        alarmList: [
          { name: '人员布控', value: 1},
          { name: '车辆布控', value: 2},
          { name: 'WIFI布控', value: 3},
          { name: 'RFID布控', value: 5},
          { name: '电围布控', value: 4},
          { name: 'ETC布控', value: 6},
        ],
        selectLi: 1,
        componentKey: 1
      }
    },
    computed: {},
    watch: {
      '$route.query'(val) {
        if (val.page) {
          this.isAndRef = val.page
          if (val.compareType) this.selectLi = Number(val.compareType)
          this.$nextTick(() => {
            this.$refs[this.isAndRef].init();
          })
        }
      }
    },
    mounted() {
      this.init();
    },
    methods: {
      init() {
        var name = this.isAndRef;
        this.$refs[name].init();
      },
      tabChange(row) {
        if (row.value == 1) {
          this.isAndRef = 'peoplePage'
        } else if (row.value == 2) {
          this.isAndRef = 'vehiclePage'
        } else {
          this.isAndRef = 'sensoryPage'
          this.componentKey++
        }
        this.selectLi = row.value
        this.$nextTick(() => {
          this.$refs[this.isAndRef].init();
        })

      }
    }
  }
</script>
<style lang="less" scoped>
.layout {
  width: 100%;
//   height: 100%;
  height: inherit;
  display: flex;
  flex-direction: column;
  .tabs {
    height: 100px;
  }
  .container {
    flex: 1;
    display: flex;
    flex-direction: column;
  
   
  }
}
</style>