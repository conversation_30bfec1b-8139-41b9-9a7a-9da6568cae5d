<!--
    * @FileDescription: etc-搜索
    * @Author: H
    * @Date: 2024/04/22
 * @LastEditors: liyuhang1 <EMAIL>
 * @LastEditTime: 2025-02-07 14:00:19
 -->
<template>
  <div class="search card-border-color">
    <Form :inline="true">
      <div class="general-search">
        <div class="input-content">
          <div class="input-content-row">
            <FormItem label="ETC类型:">
              <Select v-model="formData.type" :disabled="mapOnData">
                <Option value="0">ETC高速出入轨迹</Option>
                <Option value="1">ETC高速出行轨迹</Option>
                <Option value="2">ETC通行卡过车信息</Option>
                <Option value="3">ETC卡主身份信息</Option>
              </Select>
            </FormItem>
            <FormItem label="ETC编号:">
              <Input v-model="formData.obuId" placeholder="请输入ETC号"></Input>
            </FormItem>
            <FormItem label="设备资源:">
              <div class="select-tag-button" @click="selectDevice()">
                选择设备/已选（{{ selectDeviceList.length }}）
              </div>
            </FormItem>
            <FormItem label="抓拍时段:">
              <ui-quick-date
                ref="quickDateRef"
                v-model="dateType"
                @change="changeTime"
              ></ui-quick-date>
              <!-- <hl-timerange
                ref="timerange"
                @onceChange="handleTimeChange"
                @change="handleTimeChange"
                :reflectValue="formData.timeSlot"
                :reflectTime="{
                  startDate: formData.startDate,
                  endDate: formData.endDate,
                }"
              >
              </hl-timerange> -->
            </FormItem>
          </div>
        </div>
        <div class="btn-group">
          <Button type="primary" @click="search">查询</Button>
          <Button type="default" @click="reset">重置</Button>
        </div>
      </div>
    </Form>
    <!-- 选择设备 -->
    <select-device
      :deviceType="2"
      ref="selectDevice"
      @selectData="selectData"
    />
  </div>
</template>
<script>
import { commonMixins } from "@/mixins/app.js";
import dayjs from "dayjs";
export default {
  mixins: [commonMixins], //全局的mixin
  props: {
    mapOnData: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      dateType: 1, // 时间类型
      formData: {
        type: "0",
        obuId: "",
        startDate: dayjs().format("YYYY-MM-DD 00:00:00"), // 抓拍时段 - 开始时间
        endDate: dayjs().format("YYYY-MM-DD 23:59:59"), // 抓拍时段 - 结束时间
        devices: [], // 选中的设备id，取deviceGbId
      },
      selectDeviceList: [], // 选中的设备全量
    };
  },
  async mounted() {
    //#region
    let query = this.$route.query;
    /**
     * 走到了mounted生命周期中，并满足该条件，说明是点击左侧菜单触发的，此时不需要处理路由携带的参数
     * 否则，则表示直接从别的页面跳到该页面，并直接定位到当前组件
     */
    if (query.sectionName !== "etcContent") {
      this.$emit("search");
      return;
    }
    //#endregion
    // 设备档案 数据采集记录更多的跳转
    if (query.archiveDeviceList) {
      let deviceList = JSON.parse(query.archiveDeviceList);
      let list = deviceList.map((item) => {
        return { ...item, deviceGbId: item.deviceId, select: true };
      });
      this.selectData(list);
    }
    if (query.keyWords) {
      this.formData.searchValue = query.keyWords;
    }
    if (query.dateType) {
      this.dateType = Number(query.dateType);
      if ([2, 3].includes(this.dateType)) {
        this.$nextTick(() => {
          this.changeTime(this.$refs.quickDateRef.getDate());
        });
      }
      // 自定义时间，当为自定义时间时，query必定携带dateRange起止时间
      if (this.dateType === 4) {
        let { startDate, endDate } = query;
        this.changeTime({ startDate, endDate });
        this.$nextTick(() => {
          this.$refs.quickDateRef.setDefaultDate([startDate, endDate]);
        });
      }
    }
    if (query.deviceInfo) {
      let deviceInfo = JSON.parse(query.deviceInfo);
      this.selectData([{ ...deviceInfo, select: true }]);
      this.formData.devices = [deviceInfo.deviceId];
    }
    // 选中的设备
    if (query.deviceList) {
      let deviceList = JSON.parse(query.deviceList);
      let list = deviceList.map((item) => {
        return { ...item, deviceId: item.deviceGbId, select: true };
      });
      this.formData.selectDeviceList = list;
      this.selectData(list);
    }
    this.$nextTick(() => {
      // 需要等时间更新后发起查询
      this.$emit("search");
    });
  },
  methods: {
    /**
     * @description: 修改抓拍时段起始时间
     * @param {object} val 起始时间
     */
    changeTime(val) {
      this.formData.startDate = val.startDate;
      this.formData.endDate = val.endDate;
    },

    /**
     * @description: 查询，手动触发，需要清空来自全景智搜的keyWords，如果有的话，重置视为主动触发查询
     */
    search() {
      delete this.formData.searchValue;
      this.$emit("search");
    },

    /**
     * @description: 重置
     */
    reset() {
      // 清空选中的设备
      this.selectDeviceList = [];
      // 清空搜索参数
      this.formData.type = "0";
      this.formData.obuId = "";
      this.formData.devices = [];
      //#region 重置时间
      // 如果是自定义被重置，则需要清空已选择的时间
      if (this.dateType == 4) {
        this.$refs.quickDateRef.setDefaultDate();
      }
      this.dateType = 1;
      this.$nextTick(() => {
        this.changeTime(this.$refs.quickDateRef.getDate());
        this.search();
      });
      //#endregion
    },

    /**
     * 选择设备
     */
    selectDevice() {
      let keyWords = this.$route.query.keyWords
        ? JSON.parse(this.$route.query.keyWords)
        : "";
      this.$refs.selectDevice.show(
        this.formData.selectDeviceList,
        keyWords,
        "16"
      );
    },

    /**
     * @description: 选中设备
     * @param {array} list 选中的设备
     */
    selectData(list) {
      this.selectDeviceList = list;
      this.formData.selectDeviceList = list;
      this.formData.devices = list.map((item) => item.deviceGbId);
    },

    /**
     * @description: 获取查询参数，供父组件使用
     * @return {object}
     */
    getQueryParams() {
      return this.formData;
    },
  },
};
</script>
<style lang="less" scoped>
.btn-group {
  display: flex;
  align-items: flex-end;
  margin-bottom: 16px;
  justify-content: flex-end;
  flex: 1;
}
.search {
  padding: 16px 20px 0;
  border-bottom: 1px solid #ffff;
  display: flex;
  width: 100%;
  justify-content: space-between;
  position: relative;
  .ivu-form-inline {
    width: 100%;
  }
  .ivu-form-item {
    margin-bottom: 16px;
    margin-right: 30px;
    display: flex;
    align-items: center;
    /deep/ .ivu-form-item-label {
      white-space: nowrap;
      width: 85px;
    }
    /deep/ .ivu-form-item-content {
      display: flex;
    }
  }
  .general-search {
    display: flex;
    width: 100%;
    .input-content {
      display: flex;
      flex-wrap: wrap;
      .input-content-row {
        display: flex;
      }
    }
  }
}
</style>
