<template>
  <div>
    <ui-modal :title="moduleAction.title" v-model="visible" :styles="styles" :footer-hide="true">
      <Form class="form-content" :model="formData">
        <FormItem label="指标名称：" class="right-item mb-sm" :label-width="100">
          <span class="item-desc">{{ formData.indexName }}</span>
        </FormItem>
        <FormItem label="达标值：" class="right-item mb-sm" :label-width="100">
          <span class="item-desc">{{ formData.dataTargetValue }} {{ formData.dataTargetValue && '%' }} </span>
        </FormItem>
        <FormItem label="计算公式：" class="right-item mb-sm" :label-width="100">
          <span class="item-desc">{{ formData.indexDefinition }}</span>
        </FormItem>
        <FormItem label="评价标准：" class="right-item mb-0" :label-width="100">
          <span class="item-desc">{{ formData.evaluationCriterion }}</span>
        </FormItem>
      </Form>
      <!-- <template slot="footer">
                <Button @click="visible = false">取 消</Button>
            </template> -->
    </ui-modal>
  </div>
</template>
<style lang="less" scoped>
.mb-0 {
  margin-bottom: 0;
}
.right-item {
  @{_deep} .ivu-form-item-label {
    color: var(--color-form-label) !important;
  }
}
@{_deep} .ivu-modal-body {
  max-height: 500px;
  overflow-y: auto;
  padding: 20px 50px 50px;
}

.item-desc {
  font-size: 14px;
  line-height: 14px;
  color: var(--color-content);
}
</style>
<script>
export default {
  props: {
    value: {
      required: true,
      type: Boolean,
    },
    indexDetail: {},
    moduleAction: {},
  },
  data() {
    return {
      visible: false,
      styles: {
        width: '3rem',
      },
      formData: {},
    };
  },
  created() {},
  mounted() {},
  methods: {},
  watch: {
    visible(val) {
      this.$emit('input', val);
    },
    value(val) {
      this.visible = val;
    },
    indexDetail(val) {
      this.formData = val;
    },
  },
  computed: {},
  components: {},
};
</script>
