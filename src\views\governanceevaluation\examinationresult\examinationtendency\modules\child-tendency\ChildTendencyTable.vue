<template>
  <div class="child-tendency-table-box">
    <div class="table-head-buttons flex-row mt-md mb-md">
      <div class="open-box" v-if="showTableData?.length" @click="handleOpen">
        <i :class="['icon-font', 'f-12', isOpen ? 'icon-zhankai' : 'icon-xingzhuang']"></i>
        <span v-if="!isOpen" class="vt-middle">展开考核内容</span>
        <span v-else>收缩考核内容</span>
      </div>
      <div class="btn-box">
        <slot name="tableButtons" :isOpen="isOpen"></slot>
      </div>
    </div>
    <div class="table-box">
      <ui-table
        ref="childTendencyTable"
        class="ui-table auto-fill"
        :table-columns="showTableColumns"
        :table-data="showTableData"
        :loading="tableLoading"
        full-border
      >
        <template #month="{ row }">
          <span class="span-btn" @click="onClickMonth(row)"> {{ row.month }}月 </span>
        </template>
      </ui-table>
    </div>
  </div>
</template>
<script>
export default {
  name: 'childTendencyTable',
  props: {
    tableLoading: {
      type: Boolean,
      default: false,
    },
    headerList: {
      type: Array,
      default: () => [],
    },
    bodyList: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      showTableColumns: [],
      showTableData: [],
      isOpen: false,
      defaultColConfig: {
        minWidth: 100,
        align: 'center',
        tooltip: true,
        className: 'header-table',
      },
      needSlotColumnCodes: ['month'], //需要插槽的列
      needStaticColumns: {
        //key为bodylist的code, 值为属性
        month: {
          width: 120,
        },
      },
    };
  },
  watch: {
    headerList: {
      handler() {
        this.setShowTableColumns();
      },
      deep: true,
    },
    bodyList: {
      handler() {
        this.setShowTableBodyList();
      },
      deep: true,
    },
  },
  methods: {
    // 设置列
    setShowTableColumns() {
      let val = this.headerList;
      this.showTableColumns = this.isOpen ? this.getOpenedTableColumns(val) : this.getClosedTableColumns(val);
    },
    // 获取展开的表格列
    getOpenedTableColumns(headerList) {
      const notNeedSortColumns = ['month'];
      return headerList?.map((firstHead) => {
        let firstColItem = {
          ...this.defaultColConfig,
          key: firstHead.code,
          title: firstHead.name,
        };
        //若有需要插槽的列
        if (this.needSlotColumnCodes.includes(firstHead.code)) {
          firstColItem.slot = firstHead.code;
        }
        //若有需要固定属性的列
        if (Object.keys(this.needStaticColumns).includes(firstHead.code)) {
          Object.assign(firstColItem, this.needStaticColumns[firstHead.code]);
        }
        //若有子集, 递归设置属性
        if (firstHead.childList?.length) {
          firstColItem.children = this.getOpenedTableColumns(firstHead.childList);
        }
        //不需要递归并且没有子级了，设置排序以及无值显示
        if (!notNeedSortColumns.includes(firstHead.code) && !firstHead.childList?.length) {
          firstColItem.sortable = true;
          firstColItem.render = (h, { row, column }) => {
            return <span>{row[column.key] || row[column.key] === 0 ? row[column.key] : '-'}</span>;
          };
        }
        return firstColItem;
      });
    },
    // 获取收缩的表格列
    getClosedTableColumns(headerList) {
      const notNeedSortColumns = ['month'];
      return headerList?.map((firstHead) => {
        let colItem = {
          ...this.defaultColConfig,
          key: firstHead.code,
          title: firstHead.name,
        };
        //若有需要插槽的列
        if (this.needSlotColumnCodes.includes(firstHead.code)) {
          colItem.slot = firstHead.code;
        }
        // 不需要排序
        if (!notNeedSortColumns.includes(firstHead.code)) {
          colItem.sortable = true;
          colItem.render = (h, { row, column }) => {
            return <span>{row[column.key] || row[column.key] === 0 ? row[column.key] : '-'}</span>;
          };
        }
        return colItem;
      });
    },
    // 设置展示的数据
    setShowTableBodyList() {
      this.showTableData = this.$util.common.deepCopy(this.bodyList);
    },
    // 点击展开|收缩
    handleOpen() {
      this.isOpen = !this.isOpen;
      this.setShowTableColumns();
      this.setShowTableBodyList();
    },
    onClickMonth(row) {
      this.$emit('onClickMonth', row);
    },
  },
  mounted() {},
  components: {
    UiTable: require('@/components/ui-table.vue').default,
  },
};
</script>
<style lang="less" scoped>
.child-tendency-table-box {
  height: 100%;
  .table-head-buttons {
    .open-box {
      width: 150px;
      height: 30px;
      line-height: 30px;
      cursor: pointer;
      color: var(--color-primary);
      font-size: 14px;
      i {
        margin-right: 5px;
      }
    }
  }
  .table-box {
    min-height: 500px;
    position: relative;
    .span-btn {
      cursor: pointer;
      color: var(--color-primary);
      text-decoration: underline;
    }
    @{_deep} .ui-table {
      height: 600px;
    }
  }
}
</style>
