<template>
  <div class="base-search">
    <ui-label class="fl" label="抓拍时间" :width="70">
      <div class="date-picker-box">
        <DatePicker
          class="input-width mb-md"
          v-model="searchData.startTime"
          type="datetime"
          placeholder="请选择开始时间"
          :options="startTimeOption"
          confirm
        ></DatePicker>
        <span class="ml-sm mr-sm">--</span>
        <DatePicker
          class="input-width"
          v-model="searchData.endTime"
          type="datetime"
          placeholder="请选择结束时间"
          :options="endTimeOption"
          confirm
        ></DatePicker>
      </div>
    </ui-label>
    <!--    <ui-label class="fl  ml-lg" label="组织机构" :width="70" v-if="!determineResult">-->
    <!--      <ApiOrganizationTree-->
    <!--        style="width: auto!important;"-->
    <!--        :treeData="treeData"-->
    <!--        :taskObj="taskObj"-->
    <!--        :selectTree="searchData"-->
    <!--        placeholder="请选择组织机构"-->
    <!--      >-->
    <!--      </ApiOrganizationTree>-->
    <!--    </ui-label>-->
    <ui-label v-if="determineResult" class="fl ml-lg" label="判定结果" :width="70">
      <Select v-model="searchData.synthesisResult" style="width: 200px">
        <Option value="">全部</Option>
        <Option value="1">{{ modular == 1 ? '抓拍正确' : '上传及时' }}</Option>
        <Option value="0">{{ modular == 1 ? '准确性存疑' : '上传超时' }}</Option>
      </Select>
    </ui-label>

    <ui-label class="fl ml-lg" label="抓拍设备" :width="70">
      <select-camera class @pushCamera="pushCamera" :device-ids="searchData.deviceIds"></select-camera>
    </ui-label>
    <ui-label :width="30" class="fl" label=" ">
      <Button type="primary" class="mr-sm" @click="startSearch">查询</Button>
      <Button type="default" class="mr-lg" @click="resetSearchDataMx1(searchData, startSearch)">重置</Button>
    </ui-label>
  </div>
</template>
<script>
export default {
  props: {
    determineResult: {
      default: () => false,
    },
    modular: {
      default: 1,
    },
    taskObj: {
      type: Object,
      default() {
        return {};
      },
    },
    treeData: {
      type: Array,
      default() {},
    },
  },
  data() {
    return {
      searchData: {
        deviceIds: [],
        startTime: '',
        endTime: '',
        synthesisResult: '',
        // orgCode: '',
      },
      startTimeOption: {
        disabledDate: (date) => {
          let endTime = new Date(this.searchData.endTime);
          if (endTime) {
            return date > endTime;
          }
          return false;
        },
      },
      endTimeOption: {
        disabledDate: (date) => {
          let startTime = new Date(this.searchData.startTime);
          if (startTime) {
            return date < startTime;
          }
          return false;
        },
      },
    };
  },
  created() {
    // this.copySearchDataMx(this.searchData)
  },
  mounted() {},
  methods: {
    pushCamera(list) {
      this.searchData.deviceIds = list.map((row) => {
        return row.deviceId;
      });
    },
    startSearch() {
      this.searchData.endTime = this.searchData.endTime ? this.formateDate(this.searchData.endTime) : null;
      this.searchData.startTime = this.searchData.startTime ? this.formateDate(this.searchData.startTime) : null;
      this.$emit('startSearch', this.searchData);
    },
    formateDate(chinaStandard) {
      var date = new Date(chinaStandard);
      var y = date.getFullYear();
      var m = date.getMonth() + 1;
      m = m < 10 ? '0' + m : m;
      var d = date.getDate();
      d = d < 10 ? '0' + d : d;
      var h = date.getHours() < 10 ? '0' + date.getHours() : date.getHours();
      var minute = date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes();
      // var s = date.getSeconds();
      let time = y + '-' + m + '-' + d + ' ' + h + ':' + minute + ':00';
      return time;
    },
    resetClick() {
      this.resetSearchDataMx(this.searchData);
    },
    resetSearchDataMx1() {
      this.searchData = {
        deviceIds: [],
        startTime: '',
        endTime: '',
        synthesisResult: '',
        orgCode: '',
      };
      this.$emit('startSearch', this.searchData);
    },
  },
  watch: {},
  components: {
    SelectCamera: require('@/components/select-camera.vue').default,
    // ApiOrganizationTree: require('../../components/api-tree.vue').default,
  },
};
</script>
<style lang="less" scoped>
.base-search {
  overflow: hidden;
  padding-bottom: 0;
  .date-picker-box {
    display: flex;
  }
  .input-width {
    width: 200px;
  }
  .ui-label {
    // line-height: 40px;
  }
}
.mt4 {
  margin-top: 4px;
}
.exportBtn {
  float: right;
  margin-top: 3px;

  .icon-daochu {
    font-size: 10px;
    margin-right: 5px;
    margin-top: -2px;
  }
}
</style>
