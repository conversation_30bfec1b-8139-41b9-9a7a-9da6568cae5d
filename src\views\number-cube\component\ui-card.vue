<template>
  <div class="list-card card-box">
    <div class="list-card-head">
      <div class="head-left">
        <div
          class="header-icon"
          :class="currentEntity.bg"
          v-if="currentEntity.label"
        >
          <div class="icon-text">{{ currentEntity.label }}</div>
        </div>
        <div class="head-con cursor-p" @click="archivesDetailHandle">
          <div class="text ellipsis" :class="currentEntity.textClass">
            {{ cardData.ext.properties[currentEntity.archiveno] }}
          </div>
        </div>
      </div>
    </div>
    <div class="list-card-content">
      <div class="list-card-content-body">
        <div class="content-home-img card-border-color">
          <ui-image
            :src="cardData.ext[imgKey]"
            :default-icon="cardData.ext.icon"
            alt="图片"
            viewer
          />
        </div>
        <div class="content-info">
          <div
            class="info-li"
            v-for="(item, index) in sortPropertyList.filter(
              (item) => ![currentEntity.archiveno, imgKey].includes(item.key)
            )"
            :key="index"
          >
            <span class="info-name" :title="item.value"
              >{{ item.label }}：</span
            >
            <span class="info-value">{{
              item.value | commonFiltering(transDicData(item.propertyDicType))
            }}</span>
          </div>
        </div>
      </div>
    </div>
    <div class="list-card-background">
      <img :src="currentEntity.cardBg" />
    </div>
  </div>
</template>
<script>
import { queryDataByKeyTypes } from "@/api/user";
import { mapGetters } from "vuex";

export default {
  props: {
    cardData: {
      type: Object,
      default: () => {},
    },
    propertyList: {},
  },
  data() {
    return {
      imgKey: "propertyIcon",
      selectOptionData: {},
      archiveGraphConfig: {
        realNameGraphInfo: {
          label: "身",
          bg: "bg-primary",
          textClass: "primary",
          cardBg: "idcard",
          pathName: "people-archive",
        },
        videoGraphInfo: {
          label: "视",
          bg: "bg-warning",
          textClass: "color-green",
          cardBg: "video",
          pathName: "video-archive",
        },
        vehicleGraphInfo: {
          label: "",
          bg: "bg-warning",
          textClass: "license-plate-small",
          cardBg: "car",
          pathName: "vehicle-archive",
          pathType: "car",
        },
        placeGraphInfo: {
          label: "场",
          bg: "bg-warning",
          textClass: "color-green",
          cardBg: "home",
          pathName: "place-archive",
        },
        deviceGraphInfo: {
          label: "设",
          bg: "bg-warning",
          textClass: "color-green",
          cardBg: "idcard",
          pathName: "device-archive",
        },
      },
    };
  },
  computed: {
    ...mapGetters({
      archiveObj: "systemParam/archiveObj",
      relationEntityCard: "systemParam/relationEntityCard",
    }),
    currentEntity() {
      const label = this.cardData.ext.label;
      let obj = {
        label: "",
        bg: "",
        textClass: "primary_other",
        cardBg: "idcard",
        archiveno: "",
      };
      Object.keys(this.archiveGraphConfig).forEach((key) => {
        const { name, primaryKeys } = this.archiveObj[key]?.entityInfo || {};
        if (name === label) {
          obj = {
            ...this.archiveGraphConfig[key],
            name,
            archiveno: primaryKeys?.[0],
          };
        }
      });
      return {
        ...obj,
        cardBg: require(`@/assets/img/card-bg/${obj.cardBg}.png`),
      };
    },
    sortPropertyList() {
      const { metadata, label } = this.cardData.ext || {};
      const filterPropertyList =
        this.relationEntityCard?.[metadata.qsdi_graph_instance_id]?.[label] ||
        [];
      return filterPropertyList?.length > 0
        ? filterPropertyList.map((key) =>
            this.propertyList.find((item) => item.key === key)
          )
        : this.propertyList;
    },
  },
  watch: {
    propertyList(val) {
      this.getPropertyDicTypes();
    },
  },
  mounted() {
    this.getPropertyDicTypes();
  },
  methods: {
    transDicData(labelKey) {
      return this.selectOptionData?.[labelKey] || [];
    },
    getPropertyDicTypes(val = []) {
      const propertyDicTypes = this.propertyList
        .filter((el) => el.propertyDicType)
        .map((el) => el.propertyDicType);
      this.queryDataByKeyTypesData(propertyDicTypes);
    },
    async queryDataByKeyTypesData(propertyDicTypes) {
      // 避免同样字典重复请求
      const newPropertyDicTypes = propertyDicTypes?.filter(
        (item) => !this.selectOptionData[item]
      );
      if (newPropertyDicTypes?.length === 0) return;
      const resp = await queryDataByKeyTypes([...new Set(newPropertyDicTypes)]);
      const selectTypeData = {};
      resp.data?.forEach((item) => {
        const propertyDicType = Object.keys(item)[0];
        selectTypeData[propertyDicType] = item[propertyDicType];
      });
      // 触发翻译字段中心渲染
      this.selectOptionData = {
        ...this.selectOptionData,
        ...selectTypeData,
      };
    },
    // 详情跳转
    archivesDetailHandle() {
      const { pathName, archiveno } = this.currentEntity;
      if (!pathName) return;

      let type = this.currentEntity.pathType || pathName.split("-")[0];
      let archiveNo = this.cardData.ext.properties[archiveno];
      let plateNo = null;

      if (pathName === "vehicle-archive") {
        archiveNo = JSON.stringify(archiveNo);
        plateNo = JSON.stringify(archiveNo?.split("_")[0]);
      }

      const { href } = this.$router.resolve({
        name: pathName,
        query: {
          archiveNo: archiveNo,
          source: type,
          initialArchiveNo: archiveNo,
          plateNo: plateNo,
        },
      });
      window.open(href, "_blank");
    },
  },
};
</script>
<style lang="less" scoped>
.list-card {
  width: 316px;
  position: relative;
  overflow: hidden;
  border-radius: 4px;
  .list-card-head {
    height: 35px;
    border-bottom: 1px solid #fff;
    display: flex;
    align-items: center;
    padding: 0 10px;
    box-sizing: border-box;
    justify-content: space-between;
    overflow: hidden;
    border-top-right-radius: 4px;
    .head-left {
      display: flex;
      align-items: center;
      .header-icon {
        padding: 4px;
        border-radius: 2px;
        margin-right: 10px;
        display: flex;
        justify-content: center;
        align-items: center;
        .iconfont {
          font-size: 14px;
          line-height: 20px;
        }
        .icon-text {
          font-size: 12px;
          line-height: 12px;
          img {
            width: 100%;
            height: 100%;
          }
        }
      }
      .head-con {
        display: flex;
        flex: 1;
        align-items: center;
      }
      .text {
        font-size: 16px;
        font-family: "MicrosoftYaHei-Bold";
        font-weight: bold;
        line-height: 22px;
      }
    }
    .change-btn {
      width: 40px;
      height: 100%;
      margin-right: -18px;
      transform: skewX(-18deg);
      display: flex;
      align-items: center;
      padding-left: 8px;
      box-sizing: border-box;
      cursor: pointer;
      .iconfont {
        font-size: 16px;
      }
    }
  }
  .list-card-content {
    padding: 10px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    .list-card-content-body {
      display: flex;
      .content-img,
      .content-home-img {
        width: 116px;
        height: 116px;
        border: 1px solid #fff;
        position: relative;
        & > img {
          width: 100%;
          height: 100%;
          object-fit: contain;
          cursor: pointer;
        }
        .video-icon {
          position: absolute;
          top: -3px;
          right: 0;
          .video-icon-top {
            width: 24px;
            height: 4px;
            background: #ffbd7a;
            transform: skewX(-42deg);
            transform-origin: left top;
          }
          .video-icon-top::after {
            content: "";
            position: absolute;
            top: 0.5px;
            right: -1px;
            width: 0;
            height: 0;
            border-bottom: 2px solid #cf7820;
            border-left: 2px solid transparent;
            transform: rotate(-50deg);
          }
          .video-icon-bottom {
            color: #fff;
            font-size: 12px;
            line-height: 16px;
            background: #f29f4c;
            width: 24px;
            height: 16px;
            text-align: center;
            position: absolute;
            left: -3px;
            top: 3px;
          }
          .video-icon-bottom::before {
            content: "";
            width: 0;
            height: 0;
            border-top: 6px solid #f29f4c;
            border-right: 12px solid transparent;
            position: absolute;
            left: 0;
            top: 16px;
          }
          .video-icon-bottom::after {
            content: "";
            width: 0;
            height: 0;
            border-top: 6px solid #f29f4c;
            border-left: 12px solid transparent;
            position: absolute;
            right: 0;
            top: 16px;
          }
        }
      }
      .content-home-img {
        border: none;
      }
      .content-info {
        width: 170px;
        max-height: 127px;
        padding-left: 10px;
        flex: 1;
        box-sizing: border-box;
        overflow-y: auto;
        .info-li {
          display: flex;
          margin-bottom: 5px;
          .info-name {
            font-size: 12px;
            line-height: 18px;
            color: #181818;
            font-family: "MicrosoftYaHei-Bold";
            font-weight: bold;
            white-space: nowrap;
          }
          .info-value {
            font-size: 14px;
            line-height: 20px;
            color: #484847;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
        }
      }
    }
  }
  .list-card-background {
    position: absolute;
    bottom: -4px;
    right: 0;
    pointer-events: none;
  }
  .primary_other {
    color: #7054d1;
  }
}
</style>
