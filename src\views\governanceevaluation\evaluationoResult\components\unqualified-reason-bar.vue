<template>
  <div class="unqualified-reason">
    <div class="ranking-title f-14">
      <span class="icon-font icon-yuekaohepaihang-01"></span>
      <span>不合格原因分布统计</span>
    </div>
    <draw-echarts
      v-if="echartData.length != 0"
      :echart-option="determinantEchart"
      :echart-style="ringStyle"
      ref="attributeChart"
      class="charts"
    ></draw-echarts>
    <span class="next-echart" v-if="echartData.length > comprehensiveConfig.basicNum">
      <i
        class="icon-font icon-youjiantou1 f-12"
        @click="scrollRight('attributeChart', echartData, [], comprehensiveConfig.basicNum)"
      ></i>
    </span>
  </div>
</template>
<script>
import evaluationoverview from '@/config/api/evaluationoverview';
import dealWatch from '@/mixins/deal-watch';

export default {
  name: 'unqualified-reason',
  data() {
    return {
      propertyEchart: {},
      determinantEchart: {},
      echartsLoading: false,
      echartList: [],
      series: [],
      echartData: [],
    };
  },
  props: {},
  mixins: [dealWatch],
  async mounted() {
    this.startWatch(
      '$route.query',
      () => {
        this.getGraphsInfo();
      },
      { deep: true, immediate: true },
    );
  },
  methods: {
    async getGraphsInfo() {
      const { regionCode, orgCode, statisticType, access, indexId, batchId } = this.$route.query;
      this.echartLoading = true;
      let data = {
        indexId: indexId,
        batchId: batchId,
        access: access || 'TASK_RESULT',
        displayType: statisticType,
        orgRegionCode: statisticType === 'REGION' ? regionCode : orgCode,
      };
      try {
        let res = await this.$http.post(evaluationoverview.getGraphsInfo, data);
        this.echartData = res.data.data || [];
        this.initRing(this.echartData);
        this.echartLoading = false;
      } catch (err) {
        console.log(err);
        this.echartLoading = false;
      }
    },
    initRing(val) {
      this.barData = val.map((row) => {
        return {
          propertyColumn: row.propertyName,
          value: row.count,
          deviceRate: row.proportion || 0,
        };
      });
      let opts = {
        xAxis: val.map((row) => row.propertyName),
        data: this.barData,
      };
      this.determinantEchart = this.$util.doEcharts.overviewColumn(opts);
      setTimeout(() => {
        this.setDataZoom('attributeChart', [], this.comprehensiveConfig.basicNum);
      });
    },
  },
  computed: {},
  components: {
    DrawEcharts: require('@/components/draw-echarts').default,
  },
};
</script>
<style lang="less" scoped>
.unqualified-reason {
  position: relative;
  width: 488px;
  height: 215px;
  background: var(--bg-sub-echarts-content);
  box-shadow: var(--shadow-sub-echarts-content);
  .ranking-title {
    padding: 10px;
    color: var(--color-title-echarts);
    background: var(--bg-sub-echarts-title);
    .icon-font {
      color: var(--color-icon-echarts);
    }
  }
  .echarts-box {
    width: 100%;
    height: calc(100% - 44.5px) !important;
    .charts {
      width: 100%;
      height: 100% !important;
    }
  }
}
</style>
