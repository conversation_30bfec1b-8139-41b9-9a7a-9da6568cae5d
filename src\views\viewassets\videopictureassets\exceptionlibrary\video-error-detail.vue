<template>
  <ui-modal v-model="visible" title="检测不合格原因" :styles="styles" footer-hide>
    <div>
      <ui-table
        class="ui-table mt-lg"
        :table-columns="tableColumns"
        :table-data="tableData"
        :minus-height="minusTable"
        :loading="loading"
      >
        <template #deviceOsd="{ row }">
          <span v-if="row.deviceOsd">{{ row.deviceOsd }}</span>
          <span v-else>--</span>
        </template>
        <template #address="{ row }">
          <span v-if="row.deviceOsd">{{ row.address }}</span>
          <span v-else>--</span>
        </template>
        <!-- <template #errorMessage="{ row, index }">
                    <span class="font-red">{{ row.errorMessage }}</span>
                </template>
                <template #option="{ row, index }">
                    <Button type="text"
                            class="mr-lg"
                            @click="recordModalShow(row)">
                            编辑
                    </Button>
                </template> -->
      </ui-table>
    </div>
    <ui-page
      class="page menu-content-background"
      :page-data="pageData"
      @changePage="changePage"
      @changePageSize="changePageSize"
    >
    </ui-page>
  </ui-modal>
</template>
<style lang="less" scoped>
.success {
  background: @color-success;
}
.error {
  background: @color-failed;
}
</style>
<script>
import api from '@/config/api/vedio-threm.js';
export default {
  data() {
    return {
      visible: false,
      styles: {
        width: '6.5rem',
        top: '0.3rem',
      },
      searchData: {
        reasonTypes: [],
        pageNumber: 1,
        pageSize: 20,
      },
      pageData: {
        pageNum: 1,
        pageSize: 20,
        totalCount: 0,
      },
      loading: false,
      minusTable: 400,
      // tableColumns: [
      //     { type: "index", width: 70, title: "序号" },
      //     { title: "不合格原因", slot: "errorMessage" },
      //     { title: "检测规则名称", key: "componentName" },
      //     { title: "不合格字段", key: "propertyName" },
      //     { title: "实际结果", key: "propertyValue" },
      //     // { title: "操作", slot: "option" },
      // ],
      tableData: [],
    };
  },
  created() {},
  mounted() {},
  methods: {
    async init() {
      this.tableData = [];
      try {
        this.loading = true;
        this.copySearchDataMx(this.searchData);
        // if (this.searchData.reasonTypes.length == 0) {
        //     this.searchData.reasonTypes = [6,7,8]
        // }
        let res = await this.$http.post(api.queryVideoResultBydeviceIds, this.searchData);

        if (res.data.data.entities.length > 0) {
          this.tableData = res.data.data.entities;
          this.pageData.totalCount = res.data.data.total;
        }
      } catch (err) {
        console.log(err);
      } finally {
        this.loading = false;
      }
    },
    search() {
      this.searchData.pageNum = 1;
      this.init();
    },
    clear() {
      this.resetSearchDataMx(this.searchData, this.search);
      this.search();
    },
    checkColor(row) {
      switch (row.checkStatus) {
        case 2:
          return 'success';
        case 3:
          return 'error';
      }
    },
    changePage(val) {
      this.searchData.pageNumber = val;
      this.init();
    },
    changePageSize(val) {
      this.searchData.pageSize = val;
      this.search();
    },
    recordModalShow(row) {
      this.$emit('recordModalShow', this.viewData, row.propertyName);
    },
    setSearchData(item) {
      this.searchData.reasonTypes = item.reasonTypes;
    },
  },
  watch: {
    value(val) {
      if (val) {
        this.searchData.deviceIds = [`${this.viewData.deviceId}`];
        this.searchData.deviceInfoId = this.viewData.id;
        // this.copySearch = this.$util.common.deepCopy(this.searchData);
        this.init();
      }
      this.visible = val;
    },
    visible(val) {
      this.$emit('input', val);
    },
  },
  computed: {
    tableColumns() {
      let columns = [];
      columns.push(
        { type: 'index', width: 50, title: '序号', align: 'center' },
        { title: '异常类型', key: 'reasonType', tooltip: true },
        { title: '不合格原因', key: 'errorMessage', tooltip: true },
        { title: '设备OSD信息', slot: 'deviceOsd', tooltip: true },
        { title: '设备库OSD信息', slot: 'address', tooltip: true },
        // { title: "不合格原因", slot: "errorMessage" },
        // { title: "检测规则名称", key: "componentName" },
        // { title: "不合格字段", key: "propertyName" },
        // { title: "实际结果", key: "propertyValue" }
      );
      return columns;
    },
  },
  props: {
    value: {
      required: true,
      type: Boolean,
    },
    viewData: {
      required: true,
      type: Object,
    },
    needOption: {
      type: Boolean,
      default: false,
    },
  },
  components: {
    UiTable: require('@/components/ui-table.vue').default,
  },
};
</script>
