<template>
  <div class="repeat-empty">
    <ui-modal v-model="visible" title="数据格式校验检测" width="80%" @onCancel="reset" @query="handleSave">
      <p class="base-text-color mb-xs">1、请确定待检测字段：</p>
      <transfer-table
        @onLeftToRight="selectionChange"
        title1="字段拾取"
        title2="格式校验：根据配置的值域类型、长度、精度、是否检验字典检测数据格式"
        :left-loading="leftLoading"
        :right-loading="rightLoading"
        :left-table-columns="columns1"
        :right-table-columns="columns2"
        :left-table-data="propertyList"
        :right-table-data="targetList"
      >
        <template #left-title>
          <span class="base-text-color">字段拾取</span>
        </template>
        <template #right-title>
          <span class="base-text-color">格式校验：根据配置的值域类型、长度、精度、是否检验字典检测数据格式</span>
        </template>
      </transfer-table>
    </ui-modal>
    <area-select v-model="areaSelectVisible" :checkedTreeDataList="[]" :showOnly="true"> </area-select>
  </div>
</template>
<script>
import governancetheme from '@/config/api/governancetheme';
import governanceevaluation from '@/config/api/governanceevaluation';
import user from '@/config/api/user';
export default {
  props: {
    topicType: {
      type: String,
      default: '1',
    },
  },
  data() {
    return {
      visible: false,
      columns1: [
        { title: '字段名', key: 'propertyName' },
        { title: '注释', key: 'propertyColumn' },
      ],
      propertyList: [],
      valueDomainTypeList: [],
      dicValueList: [],
      columns2: [
        { title: ' ', key: '', width: 14 },
        {
          title: '字段名(注释)',
          key: 'fieldName',
          minWidth: 150,
          render: (h, { row }) => {
            return <span>{`${row.fieldName} (${row.fieldValue})`}</span>;
          },
        },
        {
          title: '值域类型',
          key: 'valueDomainType',
          render: (h, { row, index }) => {
            return (
              <i-select
                value={row.valueDomainType}
                transfer
                placeholder="请选择值域类型"
                vOn:on-change={(row) => {
                  this.onChangeValueDomainType(row, index);
                }}
              >
                {this.valueDomainTypeList.map((item) => {
                  return <i-option value={item.value} label={item.label}></i-option>;
                })}
              </i-select>
            );
          },
        },
        {
          title: '长度',
          key: 'valueLength',
          render: (h, { row, index }) => {
            return (
              <InputNumber
                value={row.valueLength}
                class="width-percent"
                placeholder="请输入长度"
                disabled={['1', '3', '4'].includes(row.valueDomainType)}
                vOn:on-change={(row) => {
                  this.onChangeValueLength(row, index);
                }}
              ></InputNumber>
            );
          },
        },
        {
          title: '精度',
          key: 'precisionLength',
          render: (h, { row, index }) => {
            return (
              <InputNumber
                value={row.precisionLength}
                class="width-percent"
                placeholder="请输入精度"
                disabled={['0', '2', '3', '4'].includes(row.valueDomainType)}
                vOn:on-change={(row) => {
                  this.onChangePrecisionLength(row, index);
                }}
              ></InputNumber>
            );
          },
        },
        {
          title: '校验字典',
          key: 'dictValue',
          render: (h, { row, index }) => {
            if (this.dicLoading) {
              return <span>加载中...</span>;
            } else if (['sbgnlx', 'sbcjqy'].includes(row.fieldName) && ['4'].includes(row.valueDomainType)) {
              return <Input disabled value={row.fieldValue}></Input>;
            } else {
              return (
                <i-select
                  value={row.dictValue}
                  transfer
                  placeholder="请选择校验字典"
                  disabled={['0', '1', '2', '3'].includes(row.valueDomainType)}
                  vOn:on-change={(row) => {
                    this.onChangeDictValue(row, index);
                  }}
                >
                  {row.dictList.map((item) => {
                    return <i-option value={item.value} label={item.label}></i-option>;
                  })}
                </i-select>
              );
            }
          },
        },
        {
          title: ' ',
          key: '',
          width: 45,
          //作为扩展使用
          render: (h, { row }) => {
            // sbgnlx设备功能类型
            if (row.fieldName === 'sbgnlx' && row.valueDomainType == '4') {
              return (
                <Tooltip content="c" transfer placement="right">
                  <i class="icon-font icon-wenhao icon-warning f-14 pointer"></i>
                  <div class="api" slot="content">
                    <h5 class="f-14">功能类型</h5>
                    <div
                      class={'mt-xs mb-xs '}
                      style={{
                        height: '1px',
                        width: 'calc(100% + 20px)',
                        border: '1px solid var(--devider-line)',
                        marginLeft: '-10px',
                        marginRight: '-10px',
                      }}
                    ></div>
                    {row.dictList.map((item, index) => {
                      return (
                        <p class="f-14">
                          {index}-{item.label}
                        </p>
                      );
                    })}
                  </div>
                </Tooltip>
              );
            }
            //sbcjqy 摄像机采集区域
            if (row.fieldName === 'sbcjqy' && row.valueDomainType == '4') {
              return (
                <span
                  class="f-12 link-text-box pointer"
                  onClick={() => {
                    this.handleShowSelectArea(row);
                  }}
                >
                  查看
                </span>
              );
            }
          },
        },
      ],
      targetList: [], // 字段名列表
      checkedList: [],
      checkedIds: [],
      leftLoading: false,
      rightLoading: false,
      dicLoading: false,
      indexConfig: {},
      //
      areaSelectVisible: false,
    };
  },
  methods: {
    async init(processOptions) {
      this.indexConfig = JSON.parse(JSON.stringify(processOptions));
      this.visible = true;
      await this.getValueDomainTypeDictData();
      await this.getPropertyList();
      await this.getDevice();
    },
    async getValueDomainTypeDictData() {
      try {
        const params = ['value_domain_type'];
        let {
          data: { data },
        } = await this.$http.post(user.queryDataByKeyTypes, params);
        let temp = [];
        if (data.length > 0) {
          temp = data[0]['value_domain_type'].map((item) => {
            return {
              label: item.dataDes,
              value: item.dataKey,
            };
          });
        }
        this.valueDomainTypeList = temp;
      } catch (error) {
        console.log(error);
      }
    },
    onChangePrecisionLength(value, index) {
      this.targetList[index]['precisionLength'] = value;
    },
    onChangeValueLength(value, index) {
      this.targetList[index]['valueLength'] = value;
    },
    onChangeValueDomainType(value, index) {
      this.targetList.splice(index, 1, {
        ...this.targetList[index],
        valueDomainType: value,
        valueLength: null,
        precisionLength: null,
        dictValue: '',
      });
    },
    onChangeDictValue(value, index) {
      this.targetList[index]['dictValue'] = value;
    },
    // 获取已勾选的数据
    async getPropertyList() {
      try {
        this.leftLoading = true;
        let { indexRuleId, taskSchemeId, indexType } = this.indexConfig;
        let params = {
          ruleId: indexRuleId,
          taskSchemeId,
          indexType,
        };
        let {
          data: { data },
        } = await this.$http.get(governanceevaluation.inquireIndexRulePropertyList, {
          params,
        });
        let extraParam = JSON.parse(data.extraParam || '{}');
        this.setDefaultValue(extraParam.ruleList || []);
      } catch (error) {
        console.log(error);
      } finally {
        this.leftLoading = false;
      }
    },
    // 字段拾取列表
    async getDevice() {
      try {
        this.rightLoading = true;
        let params = {
          keyWord: '',
          propertyType: this.topicType, // 字段类型，1：视图；2：人脸；3：车辆；4：视频；5：重点人员
        };
        let res = await this.$http.post(governancetheme.standardList, params);
        this.propertyList = res.data.data.map((item) => {
          if (this.checkedIds.length) {
            this.checkedIds.forEach((checkId) => {
              if (item.propertyName == checkId) {
                item._checked = true;
              }
            });
          }
          return item;
        });
        this.targetList = this.checkedList;
      } catch (error) {
        console.log(error);
      } finally {
        this.rightLoading = false;
      }
    },
    selectionChange(selection) {
      let temp = [];
      selection.forEach((item) => {
        let obj = {
          fieldName: item.propertyName,
          fieldValue: item.propertyColumn,
          valueDomainType: item.valueDomainType || '0',
          valueLength: item.valueLength || null,
          precisionLength: item.precisionLength || null,
          dictValue: item.dictValue || (item.dictList && item.dictList.length > 0) ? item.dictList[0]['value'] : '',
          dictList: item.dictList || [],
        };
        this.targetList.forEach((value) => {
          if (item.propertyName === value.fieldName) {
            obj = { ...obj, ...value };
          }
        });
        temp.push(obj);
      });
      this.targetList = temp;
    },
    // 保存
    async handleSave() {
      //值域类型，0整数、1小数、2字符串、3日期、4字典
      let result = this.targetList.every((item) => {
        if (['0', '1', '2'].includes(item.valueDomainType) && !item.valueLength && !item.precisionLength) {
          this.$Message.error(`${item.fieldValue}不能为空`);
          return false;
        }
        return true;
      });
      if (!result) {
        return;
      }
      try {
        let { indexRuleId, taskSchemeId, indexType } = this.indexConfig;
        let params = {
          ruleId: indexRuleId,
          taskSchemeId,
          indexType,
          extraParam: JSON.stringify({ ruleList: this.targetList }),
        };
        let { data } = await this.$http.post(governanceevaluation.editIndexRulePropertyList, params);
        this.$Message.success(data.msg);
        this.reset();
        this.$emit('render');
      } catch (error) {
        console.log(error);
      }
    },
    reset() {
      this.visible = false;
      this.checkedIds = [];
      this.propertyList = [];
      this.targetList = [];
      this.checkedList = [];
    },
    setDefaultValue(val) {
      this.checkedIds = val.map((item) => item.fieldName);
      this.checkedList = val.map((item) => {
        return {
          fieldName: item.fieldName,
          fieldValue: item.fieldValue,
          valueDomainType: item.valueDomainType,
          valueLength: item.valueLength,
          precisionLength: item.precisionLength,
          dictValue: item.dictValue,
          dictList: item.dictList || [],
        };
      });
    },
    //点击查看采集区域
    handleShowSelectArea() {
      this.areaSelectVisible = true;
    },
  },
  watch: {},
  components: {
    TransferTable: require('@/components/transfer-table.vue').default,
    AreaSelect: require('@/components/area-select').default,
  },
};
</script>
<style lang="less" scoped>
@{_deep} .transfer-table {
  padding: 0;
  .ivu-select {
    .ivu-select-selection {
      border: 1px solid var(--border-input) !important;
      background-color: var(--bg-input) !important;
    }
  }
  .transfer-table-left {
    width: 600px;
    flex: 0 1 auto;
  }
}

.repeat-empty {
  @{_deep} .transfer-table-wrapper {
    height: 600px;
    .left-table-wrapper {
      flex: unset;
      width: 600px;
    }

    .left-table,
    .right-table {
      height: calc(~'100% - 35px') !important;
      margin-top: 10px;
    }
  }
}
</style>
