import Vue from 'vue';
import TooltipDom from '@/views/viewassets/assetstatistics/components/tooltip-dom.vue';
/**
 *
 * @param {*} toolTipDom 自定义tooltip的组件传入
 * @param {*} toolTipItem tooltip组件 - 传入的数据
 * @returns
 */
// toolTipItem: {
//   name: '测试',
//   outerSlot: { // 插进组件的插槽，组件必须已经有slot占位和名称
//      supplement: h => [h('span', {}, 'sdlkad')]
//   },
//   list: [
//     {
//       label: 'label1',
//       color: 'red',
//       num: '111',
//       slotName: 'slotA', // [组件没有插槽可不传]组件插槽的名称[必须与slotRender共用]
//       slotRender: h => [h('span', {}, 'sdlkad')] //[组件没有插槽可不传] 组件插槽的vnode
//     }
//   ],
// }
const commonToolTipConfig = (toolTipItem = null, toolTipDom = TooltipDom) => {
  // 劫持插槽: 如果toolTip的组件里面有插槽就传入进来展示
  let tooltip = Vue.extend({
    extends: toolTipDom,
    render: function (h) {
      if (!toolTipItem) return toolTipDom.render.call(this, h);
      // outerSlot是外部插槽，组件内部必须已经定义
      if ('outerSlot' in toolTipItem) {
        Object.keys(toolTipItem.outerSlot).forEach((key) => {
          this.$slots[key] = toolTipItem.outerSlot[key](h);
        });
      }
      // 组件 - list里面的插槽[组件内部未定义]，然后自定义进去
      if ('list' in toolTipItem) {
        toolTipItem.list.map((item) => {
          if ('slotName' in item && 'slotRender' in item) {
            this.$slots[item.slotName] = item.slotRender(h);
          }
        });
        // this.$slots.slotA = [h('span', {}, 'sdlkad')];
      }
      return toolTipDom.render.call(this, h);
    },
  });
  let _this = new tooltip({
    el: document.createElement('div'),
    name: 'getHomeBar',
    data() {
      // 传入组件的数据
      return {
        toolTipItem: toolTipItem,
      };
    },
  });
  return _this.$el.outerHTML;
};
export default commonToolTipConfig;
