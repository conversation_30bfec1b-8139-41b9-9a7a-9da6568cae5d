<template>
<div class="first-step-container">
    <div class="add-ignore-wrap">
        <div class="left-step">
            <div class="step">
                <div class="step-bar">
                    <div class="step-line-highlight">
                        <i class="iconfont icon-circle icon-highlight"></i>
                        <div class="step-bar-gray">
                            <div class="step-bar-arrow"></div>
                            <div class="step-bar-margin"></div>
                        </div>
                        <i class="iconfont icon-circle icon-gray"></i>
                    </div>
                </div>
                <div>
                    <div class="step-text">
                        <i class="iconfont step-one-img"></i>
                        <span>
                            <p>步骤1</p>
                            <p class="large">预案信息</p>
                        </span>
                    </div>
                    <div class="step-text step-two-text">
                        <i class="iconfont step-two-img"></i>
                        <span>
                            <p>步骤2</p>
                            <p class="large">设备选择</p>
                        </span>
                    </div>
                </div>
            </div>
        </div>
        <div class="right-base-info">
            <div class="title">基本信息</div>
            <div class="field-item">
                <div class="ignore-name">
                    <span class="required">预案名称：</span>
                    <Input v-model="name" placeholder="请输入屏蔽预案名称" :options="{maxlength: 30}"></Input>
                </div>
                <div class="expiry-time">
                    <span class="required">有效时间：</span>
                    <DatePicker v-model="timeRange" type="datetimerange" format="yyyy-MM-dd HH:mm:ss" placeholder="请选择时间" style="width: 350px"></DatePicker>
                </div>
            </div>
            <div class="title">选择授权用户</div>
            <user-org-select :showOrganization="true" v-model="selectedUser" ref="userOrg" class="user-org"/>
            <div class="footer-btns">
                <Button size="small" class="mr-20" @click="close">取消</Button>
                <Button size="small" type="primary" :disabled="nextButtonControl" @click="handleSave">下一步</Button>&nbsp;&nbsp;
            </div>
        </div>
    </div>
</div>
</template>

<script>
import userOrgSelect from './userOrgSelect.vue';
export default {
    name: 'firstStep',
    props: {
        setData: {type: Object, default: () => ({})}
    },
    components: {
        userOrgSelect
    },
    data() {
        return {
            name: "",
            timeRange: [],
            selectedUser: [],
            nextButtonControl: true
        }
    },
    methods: {
        handleSave() {
            let [startTime, endTime] = this.timeRange || [];
            if (!this.name) {
                this.$Message.warning("请输入预案名称");
                return;
            }
            if (!startTime || !endTime) {
                this.$Message.warning("请输入开始时间和结束时间");
                return;
            }
            if (startTime > endTime) {
                this.$Message.warning("开始时间不能大于结束时间");
                return;
            }
            if (startTime == endTime) {
                this.$Message.warning("开始时间不能等于结束时间");
                return;
            }
            if(this.selectedUser.length <= 0) {
                this.$Message.warning("请选择用户");
                return;
            }
            this.$emit("stepListener", 'secondStep', {
                name: this.name,
                timeRange: this.timeRange,
                selectedUsers: this.selectedUser
            });
            console.log(this.selectedUser)
        },
        close() {
            this.$emit("close")
        }
    },
    watch: {
        setData({name, timeRange, users}) {
            this.name = name;
            this.timeRange = timeRange;
            this.selectedUser = users.map(v => {
                v.select = true
                return v
            });
        },
        name(val) {
            let [startTime, endTime] = this.timeRange || [];
            if(val && this.timeRange.length && startTime && endTime && startTime < endTime && this.selectedUser.length) {
                this.nextButtonControl = false
            } else {
                this.nextButtonControl = true
            }
        },
        timeRange(val) {
            let [startTime, endTime] = val || [];
            if(val && val.length && startTime && endTime && startTime < endTime && this.name !== '' && this.selectedUser.length) {
                this.nextButtonControl = false
            } else {
                this.nextButtonControl = true
            }
        },
        selectedUser(val) {
            let [startTime, endTime] = this.timeRange || [];
            if(val.length && this.name !== '' && this.timeRange.length && startTime && endTime && startTime < endTime ) {
                this.nextButtonControl = false
            } else {
                this.nextButtonControl = true
            }
        }
    },
}
</script>

<style lang="less">
@blue: #2C86F8;
@border: rgba(167,172,184,0.3);
@bgColor: #f7f8f9;
@textColor: #666666;

.first-step-container {
    .add-ignore-wrap {
        border: 1px solid @border;
        height: 100%;
        display: flex;
        justify-content: start;

        .left-step {
            background: @bgColor;
            height: 100%;
            width: 200px;
            border-right: 1px solid @border;
            overflow: hidden;
            flex-shrink: 0;
        }

        .step {
            display: flex;

            .step-bar {
                border-left: 2px solid @border;
                height: 150px;
                margin: 50px 10px 0 30px;
                width: 30px;
            }

            .step-line-highlight {
                border-left: 2px solid @blue;
                height: 27px;
                margin-left: -2px;
            }

            .icon-highlight {
                color: @blue;
                position: relative;
                left: -9px;
                top: -20px;
            }

            .icon-gray {
                color: @textColor;
                position: relative;
                left: -9.5px;
                top: 3px;
            }

            .step-bar-gray {
                position: relative;
                left: -9px;
                top: 3px;
            }

            .step-bar-arrow {
                width: 15px;
                height: 8px;
                background: @blue;
            }

            .step-bar-margin {
                display: inline-block;
                width: 5.7px;
                height: 5.7px;
                background-color: @blue;
                transform: rotate(45deg);
                margin: 50px;
                position: relative;
                top: -57px;
                left: -37px;
            }

            .step-text {
                display: flex;
                align-items: center;
                height: 40px;
                margin-top: 50px;
                color: @blue;

                p {
                    margin: 10px;
                }
                .large {
                    font-size: 16px;
                    font-weight: 400;
                }
                &.step-two-text {
                    color: @textColor;
                    margin-top: 85px;
                }
                .step-one-img {
                    width: 40px;
                    height: 30px;
                    background: url("~@/assets/img/dispatch/deviceIgnore/ya1.png") no-repeat;
                }
                .step-two-img {
                    width: 40px;
                    height: 30px;
                    background: url("~@/assets/img/dispatch/deviceIgnore/sb.png") no-repeat;
                }
            }
        }

        .right-base-info {
            width: 100%;
            padding: 20px;
            position: relative;
            .title {
                font-weight: bold;
                margin-bottom: 10px;
                padding: 15px 0;
                border-bottom: 1px dotted @border;
            }
            .field-item {
                display: flex;
                align-items: center;
                justify-content: space-between;
                .ignore-name, .expiry-time {
                    display: flex;
                    align-items: center;
                    span{
                        white-space: nowrap;
                    }
                }
                .xui-input {
                    width: 270px;
                }
                .xui-datepicker.xui-datepicker-style .date-input {
                    width: 170px;
                }
            }
            .user-org {
                height: calc(~'100% - 180px');
            }
            .footer-btns {
                position: absolute;
                left: 20px;
                bottom: 5px;
                .xui-btn-9028 {
                    width: 90px;
                    height: 28px;
                }
            }
        }
    }
    .required {
        position: relative;
        &::before {
            content: "*";
            color: #ee6161;
            position: absolute;
            left: -8px;
            top: 2px;
            font-size: 14px;
        }
    }
    .xui-daterange .datePicker-wrap .dib {
        width: auto;
    }
}
</style>
