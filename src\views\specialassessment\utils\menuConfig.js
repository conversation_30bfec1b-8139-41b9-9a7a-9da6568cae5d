/**
 *    name: string,  // 指标简称
 *    type: string, // 指标简称对应的标识
 *    component: string, // 组件名
 *    monthDetailComponent: string, // 月测日明细组件名
 *    width: string, //  检测详情弹框宽度
 *    dayDetailComponent: string, // 检测详情弹框组件名
 *    isGetDetailByapi: boolean， // 检测详情-查看详情（共用弹框）是否需要调接口
 */
const menuConfig = [
  // 资产考核
  {
    name: '资产质量',
    type: 'BASIC_ACCURACY',
    component: 'VideoOnline',
    monthDetailComponent: 'DefaultDetail',
    dayDetailComponent: 'AssetQuality',
  },
  {
    name: '离线统计',
    type: 'FACE_OFFLINE_STAT',
    component: 'OfflineStatistics',
    monthDetailComponent: 'DefaultDetail',
    dayDetailComponent: 'OfflineStatistics',
  },
  {
    name: '离线统计',
    type: 'VEHICLE_OFFLINE_STAT',
    component: 'OfflineStatistics',
    monthDetailComponent: 'DefaultDetail',
    dayDetailComponent: 'OfflineStatistics',
  },
  {
    name: '离线统计',
    type: 'VIDEO_OFFLINE_STAT',
    component: 'OfflineStatistics',
    monthDetailComponent: 'DefaultDetail',
    dayDetailComponent: 'OfflineStatistics',
  },
  // 视频考核
  {
    name: '视频在线',
    type: 'VIDEO_PLAYING_ACCURACY',
    component: 'VideoOnline',
    monthDetailComponent: 'DefaultDetail',
    dayDetailComponent: 'VideoOnline',
    width: '8.8rem',
  },
  {
    name: '录像质量',
    type: 'VIDEO_HISTORY_ACCURACY',
    component: 'VideoOnline',
    monthDetailComponent: 'DefaultDetail',
    dayDetailComponent: 'VideoOnline',
  },
  {
    name: 'OSD标注',
    type: 'VIDEO_OSD',
    component: 'VideoOnline',
    monthDetailComponent: 'DefaultDetail',
    dayDetailComponent: 'VideoOsd',
    width: '9.5rem', // 检测详情-查看详情宽度
    height: '4.4rem',
    isGetDetailByapi: true, // 检测详情-查看详情需要调接口
  },
  {
    name: '时钟质量',
    type: 'VIDEO_CLOCK',
    component: 'VideoOnline',
    monthDetailComponent: 'DefaultDetail',
    dayDetailComponent: 'VideoOsd',
    width: '6rem', // 检测详情-查看详情宽度
    isGetDetailByapi: false, // 检测详情-查看详情不需要调接口
  },
  {
    name: '录像完整',
    type: 'VIDEO_HISTORY_COMPLETE',
    component: 'VideoOnline',
    monthDetailComponent: 'DefaultDetail',
    dayDetailComponent: 'VideoOnline',
  },
  {
    name: '视频质量',
    type: 'VIDEO_PASS',
    component: 'VideoOnline',
    monthDetailComponent: 'DefaultDetail',
    dayDetailComponent: 'VideoPass',
    width: '4rem',
  },
  {
    name: '编码规范',
    type: 'VIDEO_CODE_STANDARD_RATE',
    component: 'VideoOnline',
    monthDetailComponent: 'DefaultDetail',
    dayDetailComponent: 'VideoCodeStandard',
  },
  {
    name: '目录一致',
    type: 'VEHICLE_CATALOGUE_SAME',
    component: 'CatalogueSame',
    monthDetailComponent: 'DefaultDetail',
    dayDetailComponent: 'ImageOnline',
  },
  {
    name: '目录一致',
    type: 'FACE_CATALOGUE_SAME',
    component: 'CatalogueSame',
    monthDetailComponent: 'DefaultDetail',
    dayDetailComponent: 'ImageOnline',
  },
  {
    name: '卡口活跃',
    type: 'VEHICLE_DEVICE_CONNECT_INTERNET',
    component: 'VideoOnline',
    monthDetailComponent: 'DefaultDetail',
    dayDetailComponent: 'ConnectInternet',
  },
  {
    name: '卡口活跃',
    type: 'FACE_DEVICE_CONNECT_INTERNET',
    component: 'VideoOnline',
    monthDetailComponent: 'DefaultDetail',
    dayDetailComponent: 'ConnectInternet',
  },
  // 人像图库考核
  {
    name: '人脸卡口在线',
    type: 'FACE_ONLINE',
    component: 'CheckpointOnline',
    monthDetailComponent: 'DefaultDetail',
    dayDetailComponent: 'ImageOnline',
  },
  {
    name: '人脸时钟质量',
    type: 'FACE_CLOCK_QUALITY',
    component: 'VideoOnline',
    monthDetailComponent: 'DefaultDetail',
    dayDetailComponent: 'ImageQuality',
  },
  {
    name: '人脸数据延时',
    type: 'FACE_DELAY',
    component: 'VideoOnline',
    monthDetailComponent: 'DefaultDetail',
    dayDetailComponent: 'ImageQuality',
  },
  {
    name: '人脸数据质量',
    type: 'FACE_QUALITY',
    component: 'DataQuality',
    monthDetailComponent: 'QualityDetail',
    dayDetailComponent: 'ImageQuality',
  },

  // 车辆图库考核
  {
    name: '车辆卡口在线',
    type: 'VEHICLE_ONLINE',
    component: 'CheckpointOnline',
    monthDetailComponent: 'DefaultDetail',
    dayDetailComponent: 'ImageOnline',
  },
  {
    name: '车辆时钟质量',
    type: 'VEHICLE_CLOCK_QUALITY',
    component: 'VideoOnline',
    monthDetailComponent: 'DefaultDetail',
    dayDetailComponent: 'ImageQuality',
  },
  {
    name: '车辆数据延时',
    type: 'VEHICLE_DELAY',
    component: 'VideoOnline',
    monthDetailComponent: 'DefaultDetail',
    dayDetailComponent: 'ImageQuality',
  },
  {
    name: '车辆数据质量',
    type: 'VEHICLE_QUALITY',
    component: 'DataQuality',
    monthDetailComponent: 'QualityDetail',
    dayDetailComponent: 'ImageQuality',
  },
  {
    name: '人脸卡口资产质量',
    type: 'FACE_ACCURACY',
    component: 'VideoOnline',
    monthDetailComponent: 'DefaultDetail',
    dayDetailComponent: 'AssetQuality',
  },
  {
    name: '车辆卡口资产质量',
    type: 'VEHICLE_ACCURACY',
    component: 'VideoOnline',
    monthDetailComponent: 'DefaultDetail',
    dayDetailComponent: 'AssetQuality',
  },
  {
    name: '视频监控资产质量',
    type: 'VIDEO_ACCURACY',
    component: 'VideoOnline',
    monthDetailComponent: 'DefaultDetail',
    dayDetailComponent: 'AssetQuality',
  },
  // 平台接口考核
  {
    name: '联网平台在线',
    type: 'VIDEO_PLATFORM_ONLINE_RATE',
    component: 'NetworkOnline',
  },
  // 人体视图库考核
  {
    name: '人体卡口在线',
    type: 'BODY_ONLINE_RATE',
    component: 'CheckpointOnline',
    monthDetailComponent: 'DefaultDetail',
    dayDetailComponent: 'ImageOnline',
  },
  {
    name: '人体时钟质量',
    type: 'BODY_CLOCK',
    component: 'VideoOnline',
    monthDetailComponent: 'DefaultDetail',
    dayDetailComponent: 'ImageQuality',
  },
  {
    name: '人体数据延时',
    type: 'BODY_UPLOAD',
    component: 'VideoOnline',
    monthDetailComponent: 'DefaultDetail',
    dayDetailComponent: 'ImageQuality',
  },
  {
    name: '人体卡口活跃',
    type: 'BODY_ACTIVE',
    component: 'VideoOnline',
    monthDetailComponent: 'DefaultDetail',
    dayDetailComponent: 'ConnectInternet',
  },
];
// 统计列表的表格最后一列title
const rateFormatFields = {
  // 资产考核
  BASIC_ACCURACY: '填报准确率',
  FACE_ACCURACY: '填报准确率',
  VEHICLE_ACCURACY: '填报准确率',
  VIDEO_ACCURACY: '填报准确率',
  // 视频考核
  VIDEO_PLAYING_ACCURACY: '视频在线率',
  VIDEO_HISTORY_ACCURACY: '录像合格率',
  VIDEO_OSD: '字幕合格率',
  VIDEO_CLOCK: '时钟合格率',
  VIDEO_PASS: '质量合格率',
  VIDEO_HISTORY_COMPLETE: '录像完整率',
  VIDEO_CODE_STANDARD_RATE: '视频编码规范率',
  // 人像图库考核
  FACE_ONLINE: '卡口在线率',
  FACE_CLOCK_QUALITY: '时钟合格率',
  FACE_DELAY: '上传及时率',
  // 车辆图库考核
  VEHICLE_ONLINE: '卡口在线率',
  VEHICLE_CLOCK_QUALITY: '时钟合格率',
  VEHICLE_DELAY: '上传及时率',
  VEHICLE_CATALOGUE_SAME: '目录一致率',
  FACE_CATALOGUE_SAME: '目录一致率',
  VEHICLE_DEVICE_CONNECT_INTERNET: '卡口活跃率',
  FACE_DEVICE_CONNECT_INTERNET: '卡口活跃率',
  // 人体视图库考核
  BODY_ONLINE_RATE: '卡口在线率',
  BODY_CLOCK: '时钟合格率',
  BODY_UPLOAD: '上传及时率',
  BODY_ACTIVE: '卡口活跃率',
};

const renderHeaderStatistics = (h, { column, params }) => {
  let { statisticsCode } = params;
  if (statisticsCode === '3') {
    column.key = 'name';
    return <span>设备标签</span>;
  } else if (statisticsCode === '1') {
    column.key = 'name';
    return <span>组织机构</span>;
  } else {
    column.key = 'name';
    return <span>行政区划</span>;
  }
};
export { menuConfig, rateFormatFields, renderHeaderStatistics };
