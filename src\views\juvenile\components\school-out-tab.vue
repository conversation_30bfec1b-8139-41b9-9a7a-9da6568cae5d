<template>
  <div class="alarm">
    <div v-if="list.length > 0" class="my-swiper-container">
      <swiper
        ref="mySwiper"
        :options="swiperOption"
        class="my-swiper"
        id="school-swiper"
      >
        <template v-for="(item, index) in list">
          <swiper-slide :key="index">
            <div class="swiper-item" @click="handleDetailFn(item, index)">
              <SchoolOutAlarm :data="item"></SchoolOutAlarm>
            </div>
          </swiper-slide>
        </template>
      </swiper>
      <div>
        <div
          class="swiper-button-prev snap-prev"
          slot="button-prev"
          id="schoolRight"
        >
          <i class="iconfont icon-caret-right"></i>
        </div>
        <div
          class="swiper-button-next snap-next"
          slot="button-next"
          id="schoolLeft"
        >
          <i class="iconfont icon-caret-right"></i>
        </div>
      </div>
    </div>
    <ui-empty v-else></ui-empty>
    <ui-loading v-if="loading" />
    <CaptureDetail
      ref="videoDetail"
      isNoSearch
      :tableList="list"
    ></CaptureDetail>
  </div>
</template>

<script>
import { swiper, swiperSlide } from "vue-awesome-swiper";
import SchoolOutAlarm from "./collect/school-out-alarm.vue";
import CaptureDetail from "./detail/capture-detail.vue";
export default {
  name: "",
  components: {
    swiper,
    swiperSlide,
    SchoolOutAlarm,
    CaptureDetail,
  },
  props: {
    idCardNo: {
      type: String,
      default: null,
    },
    loading: {
      type: Boolean,
      default: false,
    },
    list: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      swiperId: {
        left: Math.random(),
        right: Math.random(),
      },
      swiperOption: {
        effect: "coverflow",
        initialSlide: 1,
        slidesPerView: 1.235,
        centeredSlides: true,
        speed: 1000,
        coverflowEffect: {
          rotate: 0, // slide做3d旋转时Y轴的旋转角度。默认50。
          stretch:
            (((420 / 1920) * window.innerWidth) / 1.235) *
            0.806 *
            (window.innerWidth / 1920), // 每个slide之间的拉伸值，越大slide靠得越紧。
          depth: 150, // slide的位置深度。值越大z轴距离越远，看起来越小。
          modifier: 1, // depth和rotate和stretch的倍率，相当于depth*modifier、rotate*modifier、stretch*modifier，值越大这三个参数的效果越明显。默认1。
          slideShadows: true, // 开启slide阴影。默认 true。
        },
        navigation: {
          nextEl: `#schoolLeft`,
          prevEl: `#schoolRight`,
        },
        observer: true,
        observeParents: true,
      },
    };
  },
  watch: {},
  computed: {},
  async created() {},
  mounted() {},
  methods: {
    // 详情
    handleDetailFn(item, index) {
      this.$refs.videoDetail.showList(index);
    },
  },
};
</script>

<style lang="less" scoped>
.alarm-title {
  font-size: 14px;
  font-weight: 700;
  color: rgba(0, 0, 0, 0.75);
  margin: 10px 0 25px 30px;

  span {
    color: #2c86f8;
  }
}
.alarm {
  width: 100%;
}
.my-swiper-container {
  padding: 0 20px;
  position: relative;
  margin-top: 3px;
  .my-swiper {
    margin: auto;

    .swiper-item {
      width: 100%;
      display: flex;
      justify-content: center;
      cursor: pointer;
    }
  }
}

.swiper-button-prev,
.swiper-button-next {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.5);
  text-align: center;
  line-height: 30px;
  margin-top: -15px;

  .iconfont {
    color: #fff;
    font-size: 18px;
  }

  &:hover {
    background: rgba(0, 0, 0, 0.7);
  }

  &:active {
    background: rgba(0, 0, 0, 1);
  }
}

.swiper-button-prev {
  transform: rotate(180deg);
  left: 20px;
}

.swiper-button-next {
  right: 20px;
}
</style>
