<template>
  <div class="photo-gallery">
    <upload-img
      v-if="!isView"
      :multiple="true"
      :multiple-num="5"
      :default-list="defaultList"
      :required="isRequired('imageUrls')"
      ref="upload"
      @successPut="successPut"
    >
    </upload-img>
    <div class="img-list" v-else>
      <img v-for="(item, index) in imageUrls" :key="index" :src="item" alt="" @click="lookSence(index)" />
    </div>
    <look-scene v-model="visibleScence" :img-list="imgList" :view-index="viewIndex"></look-scene>
  </div>
</template>
<style lang="less" scoped>
.photo-gallery {
  text-align: center;
  height: 516px;
  margin-top: 90px;
  overflow-y: auto;
  .img-list {
    display: flex;
    justify-content: center;
    align-items: center;
    img {
      width: 100px;
      height: 100px;
      margin-right: 10px;
      cursor: pointer;
    }
  }
  @{_deep}.ivu-row {
    justify-content: center;
  }
  @{_deep}.ivu-form-item-label {
    text-align: right;
  }
  @{_deep}.upload-div {
    margin-right: 10px;
    margin-bottom: 10px;
  }
}
</style>
<script>
export default {
  data() {
    return {
      imageUrls: [],
      defaultList: [],
      visibleScence: false,
      imgList: [],
      viewIndex: 0,
    };
  },
  created() {},
  methods: {
    successPut(list) {
      this.imageUrls = list;
    },
    validate() {
      this.$emit('putData', { imageUrls: this.imageUrls });
      return true;
    },
    lookSence(index) {
      this.viewIndex = index;
      this.visibleScence = true;
    },
  },
  watch: {
    defaultForm: {
      handler(val) {
        let length = Object.keys(val).length;
        if (length > 0) {
          Object.keys(val).forEach(() => {
            let imageUrls = val.imageUrls || [];
            Object.assign(this.imageUrls, imageUrls);
            this.defaultList = imageUrls;
            this.imgList = imageUrls;
          });
        } else {
          this.imageUrls = [];
          this.defaultList = [];
        }
      },
      immediate: true,
    },
  },
  computed: {
    isView() {
      return this.modalAction === 'view';
    },
    isAdd() {
      return this.modalAction === 'add';
    },
  },
  props: {
    defaultForm: {
      type: Object,
    },
    modalAction: {
      type: String,
    },
    // 校验必填字段
    isRequired: {
      type: Function,
    },
  },
  components: {
    UploadImg: (resolve) => {
      require(['@/components/upload-img'], resolve);
    },
  },
};
</script>
