<template>
  <div>
    <ui-modal ref="modal" title="不合格详情" :styles="styles" footer-hide>
      <div class="content" v-if="this.obj != '{}'">
        <div class="echarts-title">
          <i></i>
          <div class="titles">位置图片信息</div>
        </div>
        <div class="img">
          <img :src="this.row.areaImage" alt="" />
        </div>
        <div class="echarts-title">
          <i></i>
          <div class="titles">时间图片信息</div>
        </div>
        <div class="img">
          <img :src="this.row.dateImage" alt="" />
        </div>
        <div class="echarts-title">
          <i></i>
          <div class="titles">其他图片信息</div>
        </div>
        <div class="img">
          <img :src="this.row.additionalImage" alt="" />
        </div>
        <div class="echarts-title">
          <i></i>
          <div class="titles">大图信息</div>
        </div>
        <div class="img big">
          <img :src="this.row.imageUrl" alt="" class="big_img" />
        </div>
      </div>
      <div class="content" v-else>
        <div class="no-data">
          <i class="no-data-img icon-font icon-zanwushuju1"></i>
        </div>
      </div>
    </ui-modal>
  </div>
</template>
<script>
export default {
  data() {
    return {
      styles: {
        width: '80%',
      },
      row: {},
      obj: {},
    };
  },
  methods: {
    showModal(row) {
      this.row = JSON.parse(row.remark);
      this.obj = JSON.stringify(this.row);
      this.$refs.modal.modalShow = true;
    },
  },
};
</script>
<style lang="less" scoped>
.content {
  height: 800px;
  overflow-y: auto;
}
.echarts-title {
  width: 100%;
  display: flex;
  margin-top: 5px;
  height: 30px;
  line-height: 30px;
  margin-bottom: 5px;
  i {
    width: 8px;
    height: 100%;
    background: #239df9;
    margin-right: 6px;
  }
  .titles {
    width: 100%;
    padding-left: 5px;
    background-image: linear-gradient(to right, #0a4f8d, #09284d);
  }
}
.img {
  width: 800px;
  img {
    width: 100%;
  }
}
.big {
  width: 100%;
  height: 600px;
  .big_img {
    width: 100%;
    height: 100%;
  }
}
</style>
