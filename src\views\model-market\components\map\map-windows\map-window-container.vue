<template>
    <div class="map-window">
        <div class="map-window-content">
            <div class="map-window-header">
                <div class="line"></div>
                <div class="title">{{ title }}</div>
                <Icon type="ios-close" class="icon" @click="close" />
            </div>
            <div class="map-window-body">
                <slot></slot>
            </div>
        </div>
        <div class="footer"></div>
    </div>
</template>

<script>

export default {
    props: {
        title: {
            type: String,
            default: '标题'
        },
        data: {
            type: Object,
            default: () => ({})
        },
        close: {
            type: Function,
            default: () => { }
        }
    },
    methods: {

    },
};
</script>

<style lang="less" scoped>
.map-window {
    position: relative;
    padding-bottom: 6px;
}

.footer {
    position: absolute;
    left: 50%;
    bottom: 0;
    margin-left: -6px;
    width: 12px;
    height: 12px;
    background: #FFFFFF;
    // box-shadow: 0px 3px 5px 0px rgba(0, 0, 0, 0.2987);
    transform: rotate(45deg);
}

.map-window-content {
    position: relative;
    background: #FFFFFF;
    box-shadow: 0px 3px 5px 0px rgba(0, 0, 0, 0.2987);

    .map-window-header {
        height: 36px;
        display: flex;
        align-items: center;
        column-gap: 10px;
        background-color: #D3D7DE;
        padding: 0 10px;

        .line {
            width: 4px;
            height: 20px;
            background: #2C86F8;
            border-radius: 0px 0px 0px 0px;
        }

        .title {
            flex: 1;
            font-family: Microsoft YaHei, Microsoft YaHei;
            font-weight: 700;
            font-size: 14px;
            color: rgba(0, 0, 0, 0.9);
        }

        .icon {
            font-size: 20px;
            cursor: pointer;
        }
    }
}
</style>
