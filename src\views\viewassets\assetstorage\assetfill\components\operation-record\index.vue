<template>
  <ui-modal v-model="visible" :title="title" :styles="styles" footer-hide class="operation-record">
    <!-- <tag-view
      class="over-flow mb-sm"
      :list="tagList"
      @tagChange="tagChange"
    ></tag-view> -->
    <div class="mb-sm">
      <component class="component" :is="recordType" ref="searchModule"></component>
      <div class="inline">
        <Button type="primary" @click="search">查询</Button>
        <Button class="ml-sm" @click="reset">重置</Button>
      </div>
    </div>
    <ui-table class="ui-table auto-fill" :table-columns="tableColumns" :table-data="tableData" :loading="loading">
    </ui-table>
    <ui-page
      v-if="recordType !== 'audit-records'"
      class="page"
      :page-data="pageData"
      @changePage="changePage"
      @changePageSize="changePageSize"
    >
    </ui-page>
  </ui-modal>
</template>
<script>
export default {
  props: {
    value: {
      type: Boolean,
      required: true,
      default: false,
    },
    title: {
      type: String,
      default: '操作记录',
    },
    recordType: {
      type: String,
      default: 'fill-record',
    },
    tableColumns: {
      type: Array,
      default: () => [],
    },
    tableData: {
      type: Array,
      default: () => [],
    },
    fetchMethod: {
      type: Function,
      default: () => {},
    },
  },
  data() {
    return {
      visible: false,
      loading: false,
      styles: {
        width: '7rem',
      },
      // tagList: [],
      // recordTypeList: [
      //   {
      //     label: '填报记录',
      //     value: 'fill-record',
      //   },
      //   {
      //     label: '审核记录',
      //     value: 'audit-records',
      //   },
      // ],
      pageData: {
        pageNum: 1,
        pageSize: 20,
        totalCount: 0,
      },
      searchData: {
        pageNumber: 1,
        pageSize: 20,
      },
    };
  },
  created() {
    // this.tagList = this.recordTypeList.map((row) => row.label)
  },
  methods: {
    // tagChange(index) {
    //   this.active = this.recordTypeList[index].value
    // },
    async init() {
      try {
        this.loading = true;
        await this.fetchMethod(Object.assign(this.searchData, this.$refs.searchModule.searchData));
      } catch (err) {
        console.log(err);
      } finally {
        this.loading = false;
      }
    },
    search() {
      this.searchData.pageNumber = 1;
      this.pageData.pageNum = 1;
      this.init();
    },
    reset() {
      this.resetSearchDataMx(this.searchData, this.search);
    },
    changePage(val) {
      this.searchData.pageNumber = val;
      this.init();
    },
    changePageSize(val) {
      this.searchData.pageSize = val;
      this.search();
    },
  },
  watch: {
    value(val) {
      this.visible = val;
      if (val) {
        this.init();
      }
    },
    visible(val) {
      this.$emit('input', val);
    },
  },
  components: {
    UiTable: require('@/components/ui-table.vue').default,
    FillRecord: require('./fill-record.vue').default,
    AuditRecords: require('./audit-records.vue').default,
    TagView: require('@/components/tag-view.vue').default,
  },
};
</script>
<style lang="less" scoped>
.operation-record {
  @{_deep} .ivu-modal-body {
    height: 830px;
    display: flex;
    flex-direction: column;
  }
  .component {
    display: inline;
  }
}
</style>
