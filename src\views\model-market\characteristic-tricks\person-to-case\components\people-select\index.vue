<!--
    * @FileDescription: 由人到案-人员选择
-->
<template>
  <div>
    <shrinkageCard
      ref="shrinkageCardRef"
      :index="1"
      title="选择人员"
      :showFooter="peopleList.length > 0"
    >
      <template slot="action">
        <div class="switch_point" v-show="!nextTep">
          <i-switch :value="showArea" @on-change="handleChangeSwitch" />
          <p>轨迹圈刻画</p>
          <i
            class="el-icon-close"
            @click="clearAll"
            style="cursor: pointer"
          ></i>
        </div>
      </template>
      <div class="people-select-box">
        <template v-if="peopleList.length > 0">
          <div class="peopleList">
            <div
              v-for="(item, index) in peopleList"
              :key="index"
              class="peopleList-item"
            >
              <UiListCard
                class="peopleList-item-card"
                type="people"
                :showBar="false"
                :collectIcon="false"
                :data="item"
              />
              <div class="tools primary" v-show="nextTep">
                <i
                  v-for="el in tools"
                  :key="el.type"
                  :title="item.text"
                  :class="el.icon"
                  @click="onAction(el.type, index)"
                ></i>
              </div>
            </div>
          </div>
          <template v-if="nextTep">
            <div class="select-tag-button" @click="selectPeople">
              编辑人员（已选{{ peopleList.length }}）
            </div>
            <Form
              ref="form"
              :rules="ruleValidate"
              :model="formData"
              label-position="top"
            >
              <FormItem label="轨迹类型:" prop="trajectoryTypes">
                <select-group
                  v-model="formData.trajectoryTypes"
                  style="width: 100%"
                >
                </select-group>
              </FormItem>
              <FormItem label="轨迹时间:" prop="timerange">
                <hl-timerange
                  ref="timerange"
                  class="wrapper-select"
                  @onceChange="handleTimeChange"
                  @change="handleTimeChange"
                  :reflectValue="formData.timeSlot"
                  :reflectTime="{
                    startDate: formData.startDate,
                    endDate: formData.endDate,
                  }"
                  :transfer="true"
                >
                </hl-timerange>
              </FormItem>
            </Form>
          </template>
          <div class="details-form" v-else>
            <div class="wrapper-box wrapper-box-float">
              <div class="wrapper-title">轨迹类型:</div>
              <div
                class="wrapper-text text-overflow"
                v-if="formData.trajectoryTypes.length > 0"
                :title="
                  fmtTrajectoryTypes.join('/')
                    | commonFiltering(trajectoryTypeList)
                    | changeWhitespace
                "
              >
                {{
                  fmtTrajectoryTypes.join("/")
                    | commonFiltering(trajectoryTypeList)
                    | changeWhitespace
                }}
              </div>
              <div class="wrapper-text" v-else>全部</div>
            </div>
            <div class="wrapper-box">
              <div class="wrapper-title">轨迹时间:</div>
              <div class="wrapper-text">
                {{ `${formData.startDate}-${formData.endDate}` }}
              </div>
            </div>
          </div>
          <div class="btn-group" v-show="nextTep">
            <Button
              type="primary"
              class="btnwidth"
              @click="handleSearch"
              :loading="loading"
              >下一步</Button
            >
            <Button type="default" @click="handleReset" :loading="loading"
              >重置</Button
            >
          </div>
        </template>
        <template v-else>
          <div class="frist-add-people-btn" @click="selectPeople">
            <i class="el-icon-circle-plus"></i>
            <div>添加人员</div>
          </div>
        </template>
      </div>
    </shrinkageCard>
    <!-- 关联人员 -->
    <associate-people
      v-if="associatePeopleShow"
      v-model="associatePeopleShow"
      :configValue="configValue"
      :checkAssociateItem="checkAssociateItem"
      @onSelectAssociatePeople="onSelectAssociatePeople"
      :limitJudgment="limitJudgment"
    />
    <!-- 编辑人员 -->
    <search-result-input
      v-model="searchResultInputShow"
      :limitJudgment="limitJudgment"
      :maxPeopleNum="maxPeopleNum"
      :selectPeopleList="peopleList"
      ref="searchResultInput"
      @onSelectPeople="onSelectPeople"
    />
  </div>
</template>

<script>
import { cloneDeep } from "lodash";
import { createNamespacedHelpers } from "vuex";
import UiListCard from "@/components/ui-list-card";
import shrinkageCard from "../shrinkage-card";
import selectGroup from "../../../../components/select-group";
import { getPersonClusterTrajectoryList } from "@/api/modelMarket";
import { queryParamDataByKeys } from "@/api/config";
const { mapState, mapActions, mapMutations } =
  createNamespacedHelpers("personToCase");

const defaultFormData = {
  timeSlot: "近一天",
  trajectoryTypes: [],
  // startDate: "",
  // endDate: "",
};
export default {
  props: {
    nextTep: {
      type: Boolean,
      default: true,
    },
  },
  components: {
    shrinkageCard,
    UiListCard,
    selectGroup,
    SearchResultInput: () => import("./../../models/search-result-input.vue"),
    associatePeople: () => import("./../../models/associate-people/index.vue"),
  },
  data() {
    return {
      maxPeopleNum: 5,
      pointSwitch: true,
      searchResultInputShow: false,
      associatePeopleShow: false,
      tools: [
        {
          icon: "el-icon-money",
          text: "关联人员",
          type: "relate",
        },
        {
          icon: "el-icon-delete",
          text: "删除人员",
          type: "del",
        },
      ],
      formData: {
        ...defaultFormData,
      },
      ruleValidate: {},
      queryData: {},
      checkAssociateItem: {},
      peopleList: [],
      loading: false,
      configValue: [],
    };
  },
  watch: {},
  computed: {
    ...mapState({
      showArea: (state) => state.showArea,
    }),
    // 轨迹类型最后一层
    fmtTrajectoryTypes() {
      return this.formData.trajectoryTypes.map((e) => e[e.length - 1]);
    },
    // peopleList() {
    //     return [...this.selectPeopleList, ...this.selectAssociatePeopleList].map(
    //         ({ id, xm, xbdm, archiveNo, jgDzmc, photos, gmsfhm, labels }) => ({
    //             id,
    //             jgDzmc,
    //             xm,
    //             xbdm,
    //             gmsfhm,
    //             archiveNo,
    //             photos,
    //             labels
    //         })
    //     );
    // },
  },
  filters: {
    changeWhitespace(val) {
      return val.replace(/\//g, "、");
    },
  },
  mounted() {
    this.queryParamDataByKeys();
  },
  methods: {
    ...mapActions(["getPeopleTrackList"]),
    ...mapMutations(["setState"]),
    getPeopleList() {
      return cloneDeep(this.peopleList);
    },
    limitJudgment() {
      return this.maxPeopleNum - this.peopleList.length;
    },
    handleChangeSwitch(val) {
      this.setState({
        showArea: val,
      });
    },
    queryParamDataByKeys() {
      var param = ["ICBD_TACTICAL_CONFIG"];
      queryParamDataByKeys(param).then((res) => {
        if (res.data && res.data.length) {
          this.configValue =
            JSON.parse(res.data?.[0]?.paramValue || {})?.personToCase || {};
        }
      });
    },
    // 获取选择人员
    onSelectPeople(selectPeopleList) {
      this.peopleList = selectPeopleList.map((item) => ({
        ...item,
        jgDzmc: item.hjdzDzmc,
      }));
    },
    // 获取关联人员
    onSelectAssociatePeople(associatePeopleList) {
      const archiveNos = this.peopleList.map((item) => item.archiveNo);
      associatePeopleList.forEach((element) => {
        if (!archiveNos.includes(element.archiveNo))
          this.peopleList.push({ ...element });
      });
    },
    selectPeople() {
      this.searchResultInputShow = true;
    },
    onAction(type, index) {
      this[`onActionTo${type}`](index);
    },
    // 关联人员
    onActionTorelate(index) {
      this.associatePeopleShow = true;
      this.checkAssociateItem = this.peopleList[index];
    },
    // 删除人员
    onActionTodel(index) {
      this.peopleList.splice(index, 1);
    },
    clearAll() {
      this.$Modal.confirm({
        title: "友情提示",
        content: "关闭后删选条件将清空，确认关闭吗？",
        onOk: () => {
          this.handleReset();
          this.$emit("onChangeStep", 0);
        },
        onCancel: () => {},
      });
    },
    async handleSearch() {
      const { trajectoryTypes, startDate, endDate } = this.formData;
      if (this.peopleList.length === 0) {
        this.$Message.warning("请选择人员");
        return;
      }
      // if (this.fmtTrajectoryTypes.length === 0) {
      //   this.$Message.warning("请选择轨迹类型");
      //   return;
      // }
      if (this.$dayjs(startDate).isAfter(this.$dayjs(endDate))) {
        this.$Message.warning("开始时间不能大于结束时间");
        return;
      }
      if (
        this.$dayjs(startDate).add(1, "month").isBefore(this.$dayjs(endDate))
      ) {
        this.$Message.warning("时间范围不能超过一个月");
        return;
      }

      this.loading = true;
      const params = {
        dataType: this.fmtTrajectoryTypes,
        startTime: startDate,
        endTime: endDate,
        idCards: this.peopleList.map((item) => item.archiveNo),
      };
      const { data } = await getPersonClusterTrajectoryList(params).finally(
        () => {
          this.loading = false;
        }
      );
      const { personTrajectoryCluster, reqSeq } = data;
      if (!personTrajectoryCluster || !reqSeq) {
        this.$Message.warning("未查询到人员轨迹圈！");
        return;
      }
      this.onFlexible(true);
      this.getPeopleTrackList(personTrajectoryCluster);
      this.$emit("onChangeStep", 1, reqSeq);
    },
    onFlexible(flag) {
      this.$refs.shrinkageCardRef.onFlexible(flag);
    },
    handleReset() {
      // this.peopleList = [];
      this.$refs.timerange && this.$refs.timerang.clearChecked(false);
      this.formData = {
        ...defaultFormData,
      };
    },
    handleTimeChange(obj) {
      this.formData.timeSlot = obj.timeSlot;
      obj.startDate && (this.formData.startDate = obj.startDate);
      obj.endDate && (this.formData.endDate = obj.endDate);
    },
  },
};
</script>

<style lang="less" scoped>
.switch_point {
  font-weight: 400;
  font-size: 14px;
  color: #3d3d3d;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  > p {
    padding: 0 10px;
  }
}

.people-select-box {
  .peopleList {
    display: flex;
    flex-direction: column;
    column-gap: 6px;

    .peopleList-item {
      background: #f9f9f9;
      padding: 8px;
      padding-right: 0;
      display: flex;

      .tools {
        display: flex;
        flex-direction: column;
        align-items: center;
        row-gap: 20px;
        font-size: 18px;
        width: 30px;

        i {
          cursor: pointer;
        }
      }

      .peopleList-item-card {
        flex: 1;
        margin-right: 10px;
        margin-right: 0;
      }

      .peopleList-item-card {
        width: 280px;
        margin-bottom: 0;
      }
    }
  }

  .details-form {
    .wrapper-box {
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      margin-top: 10px;
      column-gap: 6px;
      .wrapper-title {
        font-weight: 400;
        font-size: 14px;
        color: rgba(0, 0, 0, 0.4535);
      }

      .wrapper-text {
        font-weight: 400;
        font-size: 14px;
        color: rgba(0, 0, 0, 0.9);

        .tag-box {
          display: flex;

          .tag-box-li {
            font-weight: 400;
            font-size: 12px;
            color: #1faf8a;
            padding: 2px 6px;
            border: 1px solid;
            margin: 0 2px;
          }
        }
      }

      .wrapper-time {
        width: 100%;
      }
    }
  }

  .select-tag-button {
    width: 100%;
    margin-bottom: 10px;
  }

  .btn-group {
    display: flex;
    column-gap: 10px;

    .btnwidth {
      flex: 1;
    }
  }

  .frist-add-people-btn {
    height: 130px;
    background: url("~@/assets/img/model/people-to-case/add-people-bg.png");
    background-size: cover;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    color: #2c86f8;

    i {
      font-size: 22px;
      margin-bottom: 10px;
    }
  }
}
</style>
