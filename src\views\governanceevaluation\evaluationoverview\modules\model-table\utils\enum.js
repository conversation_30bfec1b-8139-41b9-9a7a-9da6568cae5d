/**
 * 0 默认
 * 1 合格
 * 2 不合格
 */
export const qualityEnum = {
  0: 'default-color',
  1: 'pass-color',
  2: 'fail-color',
};

export let firstLevelTableColumn = [
  {
    title: '序号',
    type: 'index',
    width: 50,
    align: 'center',
    fixed: 'left',
    className: 'header-table',
  },
  {
    title: '行政区划',
    width: 200,
    align: 'center',
    fixed: 'left',
    slot: 'ORG_REGION_CODE',
    className: 'header-table',
    sortable: 'custom',
  },
];
export let secondLevelTableColumn = [
  {
    title: '时间',
    width: 150,
    align: 'center',
    fixed: 'left',
    slot: 'DAY',
    className: 'header-table',
  },
];

// renderTable(h, params) {
//   let row = params.row[params.column.key];
//   if (row.key !== 'ORG_REGION_CODE' && row.clickable) {
//     return h(
//       'create-tabs',
//       {
//         attrs: {
//           class: 'inline',
//           componentName: this.resultData.componentName,
//           'tabs-text': this.resultData.text,
//           'tabs-query': {
//             displayType: 'REGION',
//             indexId: '1001',
//             code: '320100',
//             batchId: 'D%231072%************',
//             uuid: '2ab5f98e602548889a039602ddbf8281',
//           },
//         },
//         on: {
//           selectModule: () => {
//             this.$emit('selectModule');
//           },
//         },
//       },
//       [
//         h(
//           'span',
//           {
//             class: 'can-click',
//             attrs: {},
//           },
//           this.filterKey(row?.title),
//         ),
//         h('span', params.column.title),
//       ],
//     );
//   } else if (row.key === 'ORG_REGION_CODE') {
//     // 行政区划点击进入下一层
//     return h(
//       'span',
//       {
//         attrs: {
//           class: {
//             'can-click': this.searchData.bbb === '1',
//             'region-color': params.column.key === 'ORG_REGION_CODE',
//             'is-parent-color': row.parent,
//           },
//         },
//         on: {
//           click: () => {
//             this.clickOrgRegion(row);
//           },
//         },
//       },
//       row?.title ?? '--',
//     );
//   } else {
//     return h(
//       'span',
//       {
//         attrs: {
//           class: {
//             'region-color': row.key === 'ORG_REGION_CODE',
//           },
//         },
//       },
//       row?.title ?? '--',
//     );
//   }
// },
