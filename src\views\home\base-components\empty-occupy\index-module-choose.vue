<template>
  <IndexModuleSelect
    ref="indexModuleSelectRef"
    v-model="indexModuleSelectData"
    :data="sourceData"
    @change="handleChangeIndexModule"
  ></IndexModuleSelect>
</template>
<script>
import home from '@/config/api/home';
import { mapGetters } from 'vuex';
export default {
  name: 'govern-tendency',

  data() {
    return {
      tabData: [
        { label: '治理趋势', id: 'DrawEcharts' },
        { label: '治理成效', id: 'GovernEffect' },
      ],
      sourceData: {},
      selectData: [],
      governEffectData: [],
      indexModuleSelectData: '1',
      echartOption: {},
      echartsLoading: false,
      echart1: [],

      tableData: [],
      legendList: [],
      colorListGradient: [
        ['rgba(255, 167, 0, 0.3)', 'rgba(255, 167, 0, 0)'],
        ['rgba(25, 129, 245, 0.3)', 'rgba(25, 129, 245, 0)'],
        ['rgba(1, 239, 119, 0.3)', 'rgba(1, 239, 119, 0)'],
        ['rgba(4, 205, 244, 0.3)', 'rgba(4, 205, 244, 0)'],
      ],
      colorList: ['#FFA700', '#1981F5', '#01EF77', 'rgba(4, 205, 244, 1)'],
    };
  },
  computed: {
    ...mapGetters({
      getHomeConfig: 'home/getHomeConfig',
      getFullscreen: 'home/getFullscreen',
    }),
  },
  async created() {
    await this.getAllEvaluationIndex();
  },
  mounted() {
    // 解决全屏screenfull下，一些transfer弹框不展示问题
    let bigParent = this.$refs.indexModuleSelectRef?.$parent?.$parent?.$parent?.$parent?.$el;
    let dropdownTransfer = document.querySelectorAll('.ivu-dropdown-transfer');
    if (!bigParent || !dropdownTransfer) return;
    dropdownTransfer.forEach((dom) => {
      bigParent.appendChild(dom);
    });
  },
  methods: {
    async initList() {
      try {
        let { taskSchemeId, regionCode } = this.getHomeConfig;
        this.echartsLoading = true;
        let params = {
          indexModule: this.indexModuleSelectData,
          taskSchemeId,
          year: this.year,
          civilCode: regionCode,
        };
        let {
          data: { data },
        } = await this.$http.post(home.getHomeGovernanceResultList, params);
        this.governEffectData = data || [];
      } catch (e) {
        console.log(e);
      } finally {
        this.echartsLoading = false;
      }
    },
    async handleChangeIndexModule() {
      this.$emit('handleChangeIndexModule', this.indexModuleSelectData);
    },
    onChangeTitle() {
      if (this.activeValue !== 'DrawEcharts') {
        this.initList();
      }
    },
    async onChangeTab(val) {
      if (val === 'DrawEcharts') {
        await this.initGovernTendency();
        await this.governRin();
      } else {
        await this.initList();
      }
    },
    handleClick() {
      this.open = !this.open;
    },
    handleChange(date) {
      this.year = date;
    },
    async handleOk() {
      this.open = false;
      if (this.activeValue === 'DrawEcharts') {
        await this.initGovernTendency();
        await this.governRin();
      } else {
        await this.initList();
      }
    },
    /**
     * 通过indexType获取IndexName
     * @param indexType
     */
    getIndexNameByIndexType(indexType) {
      let keys = Object.keys(this.sourceData);
      for (let j = 0; j < keys.length; j++) {
        let key = keys[j];
        let data = this.sourceData[key];
        for (let i = 0; i < data.length; i++) {
          let item = data[i];
          if (item.indexType === indexType) {
            return item.indexName;
          }
          if (indexType === 'SCHEME_INDEX_RATE') {
            return '指标达标率';
          }
        }
      }
    },
    /**
     * 随机选3个指标
     * @param
     */
    randomIndex() {
      this.selectData = [];
      if (this.sourceData.hasOwnProperty(1)) {
        let indexTypeList = this.sourceData[1].map((item) => item.indexType);
        this.selectData = indexTypeList.splice(0, 3);
      }
    },
    async getAllEvaluationIndex() {
      let {
        data: { data },
      } = await this.$http.get(home.getAllEvaluationIndex);
      this.sourceData = data || {};
    },
    async initGovernTendency() {
      try {
        this.echartsLoading = true;
        let params = {
          years: this.year,
          schemes: this.selectData,
        };
        let {
          data: { data },
        } = await this.$http.post(home.queryGovernanceTrends, params);
        this.tableData = [];
        this.legendList = [];
        Object.keys(data).map((key) => {
          this.tableData.push(data[key]);
          this.legendList.push(this.getIndexNameByIndexType(key));
        });
      } catch (e) {
        // console.log(e)
      } finally {
        this.echartsLoading = false;
      }
    },
    async handleChangeIndex() {
      await this.initGovernTendency();
      await this.governRin();
    },
    governRin() {
      let series = this.tableData.map((item, index) => {
        return {
          name: this.legendList[index],
          type: 'line',
          data: this.tableData[index],
          showSymbol: false,
          smooth: true,
          lineStyle: {
            width: this.$util.common.fontSize(2),
            color: this.colorList[index % this.colorList.length], //线条颜色
            shadowColor: this.colorList[index % this.colorList.length],
            shadowBlur: 1,
            shadowOffsetY: 1,
          },
          areaStyle: {
            //区域填充样式
            //线性渐变，前4个参数分别是x0,y0,x2,y2(范围0~1);相当于图形包围盒中的百分比。如果最后一个参数是‘true’，则该四个值是绝对像素位置。
            color: new this.$echarts.graphic.LinearGradient(
              0,
              0,
              0,
              1,
              [
                {
                  offset: 0,
                  color: this.colorListGradient[index][0],
                },
                {
                  offset: 1,
                  color: this.colorListGradient[index][1],
                },
              ],
              false,
            ),
            /*shadowColor: 'rgba(10,219,250, 0.5)', //阴影颜色
              shadowBlur: 20 //shadowBlur设图形阴影的模糊大小。配合shadowColor,shadowOffsetX/Y, 设置图形的阴影效果。*/
          },
          itemStyle: {
            color: this.colorList[index % this.colorList.length],
            borderColor: this.colorList[index % this.colorList.length],
            borderWidth: 2,
            shadowColor: this.colorList[index % this.colorList.length],
            shadowBlur: 5,
            shadowOffsetX: 0,
            shadowOffsetXY: 0,
          },
          label: {
            show: false,
            position: 'top',
            color: this.colorList[index % this.colorList.length],
          },
        };
      });
      let opts = {
        data: series,
        dayType: this.dayType,
        year: this.year,
        tooltipFormatter: this.tooltipFormatter,
      };
      this.echartOption = this.$util.doEcharts.baseHomeGovernTendency(opts);
    },
  },

  components: {
    IndexModuleSelect: require('@/views/home/<USER>/index-module-select.vue').default,
  },
};
</script>
<style lang="less" scoped>
@{_deep} .ivu-picker-confirm {
  .ivu-btn-default {
    display: none;
  }
  display: flex;
  justify-content: center;
  align-items: center;
}
.year-color {
  color: #63ccfc;
}
.govern-tendency-container {
  position: relative;
  width: 100%;
  height: 33.5%;
  .body-container {
    position: relative;
    height: calc(100% - 30px);
    overflow-y: auto;
    overflow-x: hidden;
    .echarts {
      height: 100% !important;
      width: 100% !important;
    }

    @{_deep} .govern-effect-container {
      .ui-table {
        height: 100%;
        width: 100%;
        .ivu-table-default {
          background-color: transparent !important;
        }

        .ivu-table-header th {
          background: transparent !important;
          box-shadow: none !important;
          color: #89b9fb !important;
          height: 36px;
          font-size: 12px;
        }
        .ivu-table-body td {
          background: transparent !important;
          color: #ffffff;
          height: 36px;
          font-size: 12px;
        }
      }
      .color-red {
        color: #c60235;
      }
      .color-blue {
        color: #00cdf7;
      }
      .color-green {
        color: #01ef77;
      }
    }
    @{_deep} .ivu-table-tip {
      display: none;
    }
  }
  .tab-title {
    height: 30px;
  }
}
.full-screen-container {
  height: 30.6%;
}
</style>
