<template>
  <div class="container">
    <!-- 查询 -->
    <Search
      ref="searchRef"
      :subTaskType="subTaskType"
      :algorithmList="algorithmList"
      @searchForm="searchForm"
    />
    <div class="table-container">
      <div class="data-above">
        <div class="left-operater">
          <Button size="small" @click="handleAdd">
            <ui-icon type="jia" color="#2C86F8"></ui-icon>
            新增任务
          </Button>
          <Button class="mr" @click="batchStart" size="small">
            <ui-icon type="start" color="#2C86F8"></ui-icon>
            批量开始
          </Button>
          <Button class="mr" @click="batchStop" size="small">
            <ui-icon type="pause2" color="#2C86F8"></ui-icon>
            批量暂停
          </Button>
          <Button size="small" @click="handleDelJobs">
            <ui-icon type="shanchu1" color="#2C86F8"></ui-icon>
            批量删除
          </Button>
        </div>
        <div class="right-operater">
          <div class="flex-center" @click="handleSort">
            <i
              class="iconfont icon-moveup"
              style="color: #2c86f8"
              :class="{
                rotate: params.sortType === 'asc',
              }"
            ></i>
            &nbsp;&nbsp;时间排序
          </div>
        </div>
      </div>
      <div class="table-content">
        <ui-table
          :columns="columns"
          :data="list"
          :loading="loading"
          @on-selection-change="selectChangeHandler"
        >
          <template #effectiveTime="{ row }">
            <div>
              {{ row.effectiveStartTime }} -
              {{ row.effectiveEndTime || "永久" }}
            </div>
          </template>
          <template #workTime="{ row }">
            <span>{{ getWorkTime(row) }}</span>
          </template>
          <template #taskType="{ row }">
            <span>
              {{ taskTypeList[row.taskType] }}
            </span>
          </template>
          <template #algorithmName="{ row }">
            <AlgorithmTagPop
              :data="row.compareAlgorithmNames"
            ></AlgorithmTagPop>
          </template>
          <template #alarmLevel="{ row }">
            <AlarmLevel :alarmLevel="row.taskLevel"></AlarmLevel>
          </template>
          <template #status="{ row }">
            <span>{{
              taskStatusList.find((item) => item.key == row.status).label
            }}</span>
          </template>
          <template #pointNum="{ row }">
            <DevicePop :deviceList="row.taskResourceList"></DevicePop>
          </template>
          <template #action="{ row }">
            <TableAction
              :row="row"
              :subTaskType="subTaskType"
              @taskStatusRealHandler="taskStatusHandler"
              @handleEdit="handleEdit"
              @handleSearch="
                (val) =>
                  toDetailByTask({
                    taskId: val.id,
                    taskType: params.taskParsingType,
                  })
              "
              @mapLoaction="mapLoaction"
              @handleDel="handleDel"
            ></TableAction>
          </template>
        </ui-table>
      </div>
    </div>
    <!-- 分页 -->
    <ui-page
      :current="params.pageNumber"
      :total="total"
      :page-size="params.pageSize"
      @pageChange="pageChange"
      @pageSizeChange="pageSizeChange"
    ></ui-page>
    <!--添加/编辑 start-->
    <addModal
      v-if="isShowAdd"
      ref="addModal"
      :title="subTaskId ? '编辑实时解析任务' : '新增实时解析任务'"
      :subTaskType="subTaskType"
      :subTaskId="subTaskId"
      :subDeviceId="subDeviceId"
      :algorithmList="algorithmList"
      @updated="jobUpdated"
      @close="handlerClose"
    ></addModal>
    <adjustPosition
      v-model="isShowDragDialog"
      :pointsData="pointData"
      :noEdit="true"
    ></adjustPosition>
  </div>
</template>
<script>
import { mapActions } from "vuex";
import TableAction from "../components/table-action.vue";
import Search from "../components/search.vue";
import TaskHandler from "@/views/semantic-placement/mixins/taskHandler.js";
import AlgorithmTagPop from "../components/algorithm-tag-pop.vue";
import AlarmLevel from "../components/alarm-level.vue";
import DevicePop from "@/views/multimodal-analysis/components/device-pop.vue";
import adjustPosition from "@/views/viewanalysis/components/adjustPosition.vue";
import addModal from "../components/add-modal.vue";
import ExpandRow from "@/views/semantic-placement/components/expandRow.vue";
import { getLLMCompareTaskPageList } from "@/api/semantic-placement.js";
export default {
  name: "RealTimeAnalysis",
  components: {
    Search,
    AlgorithmTagPop,
    AlarmLevel,
    DevicePop,
    adjustPosition,
    addModal,
    TableAction,
  },
  mixins: [TaskHandler],
  props: {},
  data() {
    return {
      subTaskType: "real",
      tagList: [],
      list: [],
      childList: [],
      loading: false,
      total: 0,
      params: {
        pageNumber: 1,
        pageSize: 20,
        taskParsingType: 1,
        sortType: "desc",
      },
      timeUpDown: false,
      columns: [
        {
          type: "expand",
          width: 50,
          render: (h, { row, index }) => {
            const deviceInfoList = row?.taskResourceList?.map((item) => {
              const { deviceInfo, ...otherParam } = item;
              return {
                ...deviceInfo,
                ...otherParam,
                deviceId: deviceInfo?.id,
              };
            }) || [{}, {}, {}];
            return h(ExpandRow, {
              props: {
                tableList: deviceInfoList,
                columns: this.childColumns,
                currentJob: { ...row },
                subTaskType: this.subTaskType,
                subTaskStatus: row.status,
                switchLoading: this.switchLoading,
              },
              on: {
                handleEdit: (val) => {
                  this.handleEdit(row, val.deviceId);
                },
                handleSearch: (val) => {
                  this.toDetailByTask({
                    taskId: row.id,
                    taskType: this.params.taskParsingType,
                  });
                },
                handleMap: (val) => {
                  this.childMapLocation(val);
                },
                handleDel: (val) => {
                  this.deleteTasks([val]);
                },
              },
            });
          },
        },
        { type: "selection", align: "center", width: 60 },
        { title: "任务名称", key: "taskName", width: 180, ellipsis: true },
        { title: "有效时间", slot: "effectiveTime" },
        { title: "处理时段", slot: "workTime", width: 180 },
        { title: "处理类型", slot: "taskType", width: 130 },
        { title: "算法名称", slot: "algorithmName", width: 160 },
        { title: "报警级别", slot: "alarmLevel", width: 130 },
        { title: "任务状态", slot: "status", width: 130 },
        { title: "点位数量", slot: "pointNum", width: 130 },
        { title: "创建人", key: "creator", width: 130, ellipsis: true },
        { title: "操作", slot: "action", width: 120 },
      ],
      childColumns: [
        { title: "设备名称", slot: "name", align: "center" },
        { title: "任务状态", slot: "status", align: "center" },
        { title: "操作", slot: "opreate", width: 120 },
      ],
      isShowAdd: false,
      subTaskId: "", //添加编辑页面的任务ID
      subDeviceId: "",
      isShowDragDialog: false,
      pointData: [],
    };
  },
  created() {},
  mounted() {
    this.getLLMCompareAlgorithm();
    this.getList();
  },
  beforeDestroy() {},
  methods: {
    // 排序
    handleSort() {
      this.timeUpDown = !this.timeUpDown;
      this.params.sortType = this.timeUpDown ? "asc" : "desc";
      this.getList();
    },
    // 查询列表
    async getList(otherParam = {}) {
      this.loading = true;
      try {
        let param = {
          ...this.$refs.searchRef.getSearchParam(),
          ...this.params,
          ...otherParam,
        };
        const { data } = await getLLMCompareTaskPageList(param);
        this.list = data?.entities || [];
        this.total = data?.total || 0;
      } catch (e) {
      } finally {
        this.loading = false;
      }
    },
    jobUpdated() {
      this.isShowAdd = false;
      this.params.pageNumber = 1;
      this.getList();
    },
    // 查询
    searchForm() {
      this.params.pageNumber = 1;
      this.getList();
    },
    // 改变页码
    pageChange(pageNumber) {
      this.params.pageNumber = pageNumber;
      this.getList();
    },
    // 改变分页个数
    pageSizeChange(size) {
      this.params.pageSize = size;
      this.params.pageNumber = 1;
      this.getList();
    },
    handleExpand(row, status) {
      let index = this.list.findIndex((v) => v.jobId == row.jobId);
      if (status) {
        this.list[index]._expanded = true;
      } else {
        this.list[index]._expanded = false;
      }
    },
    handleAdd() {
      this.subTaskId = "";
      this.subDeviceId = "";
      this.isShowAdd = true;
      this.$nextTick(() => {
        this.$refs.addModal.init();
      });
    },
    handleEdit(row, subDeviceId = "") {
      if (row.status != 0) {
        return;
      }
      this.subTaskId = row.id;
      this.subDeviceId = subDeviceId;
      this.isShowAdd = true;
      this.$nextTick(() => {
        this.$refs.addModal.init({ ...row });
      });
    },
    async batchStart() {
      if (this.selectedData.length == 0) {
        return this.$Message.warning("请选择任务");
      }
      this.getList();
    },
    async batchStop() {
      if (this.selectedData.length == 0) {
        return this.$Message.warning("请选择任务");
      }
      this.getList();
    },
    // 任务开启/关闭
    async taskStatusHandler(value) {
      value.loading = true;
      // 开启
      if (value.status == 1) {
        await this.stopJobs([value]);
        value.status = 3;
      } else {
        await this.startJobs([value]);
        value.status = 1;
      }
      value.loading = false;
    },
    handleDel(row) {
      this.deleteJobs([row]);
    },
    handleDelJobs() {
      this.deleteJobs(this.selectedData);
    },
    handlerClose() {
      this.isShowAdd = false;
    },
    //定位--父列表 定位地图
    mapLoaction(item) {
      const deviceInfoList = item.taskResourceList.map((item) => {
        return item.deviceInfo;
      });
      var arrayP = [];
      deviceInfoList &&
        deviceInfoList.length > 0 &&
        deviceInfoList.filter((item) => {
          if (
            item.longitude &&
            parseInt(item.longitude) != 0 &&
            item.latitude &&
            parseInt(item.latitude) != 0
          ) {
            item.data = {
              deviceName: !!item["name"] ? item.name : null,
            };
            arrayP.push(item);
          }
        });
      if (arrayP.length < 1) {
        this.$Message.error("该任务无点位信息，无法定位");
        return;
      }
      this.pointData = arrayP;
      this.isShowDragDialog = true;
    },
    // 子列表 定位地图
    childMapLocation(item) {
      if (
        item.longitude &&
        parseInt(item.longitude) != 0 &&
        item.latitude &&
        parseInt(item.latitude) != 0
      ) {
        item.data = {
          deviceName: !!item["name"] ? item.name : null,
        };
        this.pointData = [item];
        this.isShowDragDialog = true;
      } else {
        this.$Message.error("该视频文件无点位信息，无法定位");
      }
    },
    // 处理时段
    getWorkTime(row) {
      if (row?.validTimeRange) {
        const arr = JSON.parse(row?.validTimeRange);
        return arr[0] + "~" + arr[1];
      }
      return "全时段";
    },
  },
};
</script>
<style lang="less" scoped>
@import "../style/index.less";
</style>
