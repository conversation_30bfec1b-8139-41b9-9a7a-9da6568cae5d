<!--
    * @FileDescription: 人体搜索条件
    * @Author: H
    * @Date: 2024/2/26
    * @LastEditors: 
    * @LastEditTime: 
 -->
 <template>
    <div class="search card-border-color">
        <Form :inline="true" :class="formData.visible ? 'advanced-search-show' : ''" @submit.native.prevent>
            
        </Form>
    </div>
</template>
<script>
export default {
    props: {
        formData: {
            default: () => {
                return {}
            }
        }
    },
    data() {
        return {

        }
    },
    mounted() {

    },
    methods: {

    }
}
</script>
<style lang="less" scoped>
@import "style/index";

</style>