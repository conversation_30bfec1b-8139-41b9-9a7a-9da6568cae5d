const defaultTableColumns = [
  {
    title: '序号',
    type: 'index',
    align: 'center',
    width: 50,
  },
  {
    title: '检测时间',
    key: 'examineTime',
    align: 'left',
    tooltip: true,
    minWidth: 120,
  },
  // {
  //     title: '视频监控设备总量',
  //     key: 'total',
  //     align: 'left',
  //     tooltip: true,
  //     sortable: 'custom',
  //     minWidth: 120
  // },
  {
    title: '检测数量',
    key: 'detection',
    slot: 'detection',
    align: 'left',
    tooltip: true,
    sortable: 'custom',
    minWidth: 100,
  },
  {
    title: '合格数量',
    key: 'qualified',
    slot: 'qualified',
    align: 'left',
    tooltip: true,
    sortable: 'custom',
    minWidth: 100,
  },
  {
    title: '不合格数量',
    key: 'unqualified',
    slot: 'unqualified',
    align: 'left',
    tooltip: true,
    sortable: 'custom',
    minWidth: 100,
  },
  {
    title: '视频在线率',
    key: 'rate',
    slot: 'rate',
    align: 'left',
    tooltip: true,
    sortable: 'custom',
    minWidth: 100,
  },
];
const onlineTableColumns = [
  {
    title: '序号',
    type: 'index',
    align: 'center',
    width: 50,
  },
  {
    title: '检测时间',
    key: 'examineTime',
    align: 'left',
    tooltip: true,
    minWidth: 120,
  },
  {
    title: '检测数量',
    key: 'detection',
    slot: 'detection',
    align: 'left',
    tooltip: true,
    sortable: 'custom',
    minWidth: 100,
  },
  {
    title: '在线数量',
    key: 'qualified',
    slot: 'qualified',
    align: 'left',
    tooltip: true,
    sortable: 'custom',
    minWidth: 100,
  },
  {
    title: '离线数量',
    key: 'unqualified',
    slot: 'unqualified',
    align: 'left',
    tooltip: true,
    sortable: 'custom',
    minWidth: 100,
  },
  {
    title: '卡口在线率',
    key: 'rate',
    slot: 'rate',
    align: 'left',
    tooltip: true,
    sortable: 'custom',
    minWidth: 100,
  },
];
const connectInternet = [
  {
    title: '序号',
    type: 'index',
    align: 'center',
    width: 50,
  },
  {
    title: '检测时间',
    key: 'examineTime',
    align: 'left',
    tooltip: true,
    minWidth: 120,
  },
  {
    title: '检测数量',
    key: 'detection',
    slot: 'detection',
    align: 'left',
    tooltip: true,
    sortable: 'custom',
    minWidth: 100,
  },
  {
    title: '活跃数量',
    key: 'qualified',
    slot: 'qualified',
    align: 'left',
    tooltip: true,
    sortable: 'custom',
    minWidth: 100,
  },
  {
    title: '非活跃数量',
    key: 'unqualified',
    slot: 'unqualified',
    align: 'left',
    tooltip: true,
    sortable: 'custom',
    minWidth: 100,
  },
  {
    title: '卡口活跃率',
    key: 'rate',
    slot: 'rate',
    align: 'left',
    tooltip: true,
    sortable: 'custom',
    minWidth: 100,
  },
];
const tableColumns = {
  BASIC_ACCURACY: [
    {
      title: '序号',
      type: 'index',
      align: 'center',
      width: 50,
    },
    {
      title: '检测时间',
      key: 'examineTime',
      align: 'left',
      tooltip: true,
      minWidth: 120,
    },
    {
      title: '检测数量',
      key: 'detection',
      align: 'left',
      tooltip: true,
      sortable: 'custom',
      minWidth: 100,
    },
    {
      title: '合格数量',
      key: 'qualified',
      slot: 'qualified',
      align: 'left',
      tooltip: true,
      sortable: 'custom',
      minWidth: 100,
    },
    {
      title: '不合格数量',
      key: 'unqualified',
      slot: 'unqualified',
      align: 'left',
      tooltip: true,
      sortable: 'custom',
      minWidth: 100,
    },
    {
      title: '视频在线率',
      key: 'rate',
      slot: 'rate',
      align: 'left',
      tooltip: true,
      sortable: 'custom',
      minWidth: 100,
    },
  ],
  FACE_ACCURACY: [
    {
      title: '序号',
      type: 'index',
      align: 'center',
      width: 50,
    },
    {
      title: '检测时间',
      key: 'examineTime',
      align: 'left',
      tooltip: true,
      minWidth: 120,
    },
    {
      title: '检测数量',
      key: 'detection',
      align: 'left',
      tooltip: true,
      sortable: 'custom',
      minWidth: 100,
    },
    {
      title: '合格数量',
      key: 'qualified',
      slot: 'qualified',
      align: 'left',
      tooltip: true,
      sortable: 'custom',
      minWidth: 100,
    },
    {
      title: '不合格数量',
      key: 'unqualified',
      slot: 'unqualified',
      align: 'left',
      tooltip: true,
      sortable: 'custom',
      minWidth: 100,
    },
    {
      title: '视频在线率',
      key: 'rate',
      slot: 'rate',
      align: 'left',
      tooltip: true,
      sortable: 'custom',
      minWidth: 100,
    },
  ],
  VEHICLE_ACCURACY: [
    {
      title: '序号',
      type: 'index',
      align: 'center',
      width: 50,
    },
    {
      title: '检测时间',
      key: 'examineTime',
      align: 'left',
      tooltip: true,
      minWidth: 120,
    },
    {
      title: '检测数量',
      key: 'detection',
      align: 'left',
      tooltip: true,
      sortable: 'custom',
      minWidth: 100,
    },
    {
      title: '合格数量',
      key: 'qualified',
      slot: 'qualified',
      align: 'left',
      tooltip: true,
      sortable: 'custom',
      minWidth: 100,
    },
    {
      title: '不合格数量',
      key: 'unqualified',
      slot: 'unqualified',
      align: 'left',
      tooltip: true,
      sortable: 'custom',
      minWidth: 100,
    },
    {
      title: '视频在线率',
      key: 'rate',
      slot: 'rate',
      align: 'left',
      tooltip: true,
      sortable: 'custom',
      minWidth: 100,
    },
  ],
  VIDEO_ACCURACY: [
    {
      title: '序号',
      type: 'index',
      align: 'center',
      width: 50,
    },
    {
      title: '检测时间',
      key: 'examineTime',
      align: 'left',
      tooltip: true,
      minWidth: 120,
    },
    {
      title: '检测数量',
      key: 'detection',
      align: 'left',
      tooltip: true,
      sortable: 'custom',
      minWidth: 100,
    },
    {
      title: '合格数量',
      key: 'qualified',
      slot: 'qualified',
      align: 'left',
      tooltip: true,
      sortable: 'custom',
      minWidth: 100,
    },
    {
      title: '不合格数量',
      key: 'unqualified',
      slot: 'unqualified',
      align: 'left',
      tooltip: true,
      sortable: 'custom',
      minWidth: 100,
    },
    {
      title: '视频在线率',
      key: 'rate',
      slot: 'rate',
      align: 'left',
      tooltip: true,
      sortable: 'custom',
      minWidth: 100,
    },
  ],
  VIDEO_PLAYING_ACCURACY: [
    {
      title: '序号',
      type: 'index',
      align: 'center',
      width: 50,
    },
    {
      title: '检测时间',
      key: 'examineTime',
      align: 'left',
      tooltip: true,
      minWidth: 120,
    },
    {
      title: '检测数量',
      key: 'detection',
      slot: 'detection',
      align: 'left',
      tooltip: true,
      sortable: 'custom',
      minWidth: 100,
    },
    {
      title: '在线数量',
      key: 'qualified',
      slot: 'qualified',
      align: 'left',
      tooltip: true,
      sortable: 'custom',
      minWidth: 100,
    },
    {
      title: '离线数量',
      key: 'unqualified',
      slot: 'unqualified',
      align: 'left',
      tooltip: true,
      sortable: 'custom',
      minWidth: 100,
    },
    {
      title: '视频在线率',
      key: 'rate',
      slot: 'rate',
      align: 'left',
      tooltip: true,
      sortable: 'custom',
      minWidth: 100,
    },
  ],
  VIDEO_HISTORY_ACCURACY: defaultTableColumns,
  VIDEO_OSD: defaultTableColumns,
  VIDEO_CLOCK: defaultTableColumns,
  VIDEO_PASS: defaultTableColumns,
  VIDEO_HISTORY_COMPLETE: defaultTableColumns,
  FACE_ONLINE: onlineTableColumns,
  FACE_CLOCK_QUALITY: onlineTableColumns,
  FACE_DELAY: onlineTableColumns,
  FACE_QUALITY: [
    {
      title: '序号',
      type: 'index',
      align: 'center',
      width: 50,
    },
    {
      title: '检测时间',
      key: 'examineTime',
      align: 'center',
      tooltip: true,
      minWidth: 150,
    },
    {
      title: '抓拍大图可用性',
      align: 'center',
      indexIds: [2001, 2002],
      colkey: 'usability',
      children: [
        {
          title: '检测数量',
          keyName: 'detection',
          key: '',
          slot: 'detection',
          align: 'center',
          minWidth: 100,
          sortable: 'custom',
        },
        {
          title: 'URL不可用数',
          keyName: 'unqualified',
          key: '',
          slot: 'unqualified',
          align: 'center',
          minWidth: 100,
          sortable: 'custom',
        },
        {
          title: '图片URL可用率',
          keyName: 'rate',
          key: '',
          slot: 'rate',
          align: 'center',
          minWidth: 100,
          sortable: 'custom',
        },
      ],
    },
    {
      title: '抓拍图片合格性',
      align: 'center',
      indexIds: [2003],
      colkey: 'eligibility',
      children: [
        {
          title: '检测数量',
          keyName: 'detection',
          key: '',
          slot: 'detection',
          align: 'center',
          minWidth: 100,
          sortable: 'custom',
        },
        {
          title: '抓拍不合格数',
          keyName: 'unqualified',
          key: '',
          slot: 'unqualified',
          align: 'center',
          minWidth: 100,
          sortable: 'custom',
        },
        {
          title: '合格率',
          keyName: 'rate',
          key: '',
          slot: 'rate',
          align: 'center',
          minWidth: 100,
          sortable: 'custom',
        },
      ],
    },
    {
      title: '抓拍图片评分',
      align: 'center',
      indexIds: [2026],
      colkey: 'score',
      children: [
        {
          title: '检测数量',
          keyName: 'detection',
          key: '',
          slot: 'detection',
          align: 'center',
          minWidth: 100,
          sortable: 'custom',
        },
        {
          title: '低评分数',
          keyName: 'unqualified',
          key: '',
          slot: 'unqualified',
          align: 'center',
          minWidth: 100,
          sortable: 'custom',
        },
        {
          title: '评分率',
          keyName: 'rate',
          key: '',
          slot: 'rate',
          align: 'center',
          minWidth: 100,
          sortable: 'custom',
        },
      ],
    },
  ],
  VEHICLE_ONLINE: onlineTableColumns,
  VEHICLE_CLOCK_QUALITY: onlineTableColumns,
  VEHICLE_DELAY: onlineTableColumns,
  VEHICLE_QUALITY: [
    {
      title: '序号',
      type: 'index',
      align: 'center',
      width: 50,
    },
    {
      title: '检测时间',
      key: 'examineTime',
      align: 'center',
      tooltip: true,
      minWidth: 150,
    },
    {
      title: '抓拍大图可用性',
      align: 'center',
      indexIds: [3010, 3011],
      colkey: 'usability',
      children: [
        {
          title: '检测数量',
          keyName: 'detection',
          key: '',
          slot: 'detection',
          align: 'center',
          minWidth: 100,
          sortable: 'custom',
        },
        {
          title: 'URL不可用数',
          keyName: 'unqualified',
          key: '',
          slot: 'unqualified',
          align: 'center',
          minWidth: 100,
          sortable: 'custom',
        },
        {
          title: '图片URL可用率',
          keyName: 'rate',
          key: '',
          slot: 'rate',
          align: 'center',
          minWidth: 100,
          sortable: 'custom',
        },
      ],
    },
    {
      title: '抓拍数据完整性',
      align: 'center',
      indexIds: [3001, 3002],
      colkey: 'integrity',
      children: [
        {
          title: '检测数量',
          keyName: 'detection',
          key: '',
          slot: 'detection',
          align: 'center',
          minWidth: 100,
          sortable: 'custom',
        },
        {
          title: '图片属性不完整数',
          keyName: 'unqualified',
          key: '',
          slot: 'unqualified',
          align: 'center',
          minWidth: 100,
          sortable: 'custom',
        },
        {
          title: '完整率',
          keyName: 'rate',
          key: '',
          slot: 'rate',
          align: 'center',
          minWidth: 100,
          sortable: 'custom',
        },
      ],
    },
    {
      title: '抓拍数据准确性',
      align: 'center',
      indexIds: [3003, 3004],
      colkey: 'accuracy',
      children: [
        {
          title: '检测数量',
          keyName: 'detection',
          key: '',
          slot: 'detection',
          align: 'center',
          minWidth: 100,
          sortable: 'custom',
        },
        {
          title: '图片属性不准确数',
          keyName: 'unqualified',
          key: '',
          slot: 'unqualified',
          align: 'center',
          minWidth: 100,
          sortable: 'custom',
        },
        {
          title: '准确率',
          keyName: 'rate',
          key: '',
          slot: 'rate',
          align: 'center',
          minWidth: 100,
          sortable: 'custom',
        },
      ],
    },
  ],
  VIDEO_CODE_STANDARD_RATE: [
    {
      title: '序号',
      type: 'index',
      align: 'center',
      width: 50,
    },
    {
      title: '检测时间',
      key: 'examineTime',
      align: 'left',
      tooltip: true,
      minWidth: 200,
    },
    {
      title: '检测数量',
      key: 'detection',
      slot: 'detection',
      align: 'left',
      tooltip: true,
      sortable: 'custom',
      minWidth: 100,
    },
    {
      title: '编码规范数量',
      key: 'qualified',
      slot: 'qualified',
      align: 'left',
      tooltip: true,
      sortable: 'custom',
      minWidth: 100,
    },
    {
      title: '编码不规范数量',
      key: 'unqualified',
      slot: 'unqualified',
      align: 'left',
      tooltip: true,
      sortable: 'custom',
      minWidth: 100,
    },
    {
      title: '视频编码规范率',
      key: 'rate',
      slot: 'rate',
      align: 'left',
      tooltip: true,
      sortable: 'custom',
      minWidth: 100,
    },
  ],
  VEHICLE_DEVICE_CONNECT_INTERNET: connectInternet,
  FACE_DEVICE_CONNECT_INTERNET: connectInternet,
  BODY_UPLOAD: onlineTableColumns,
  BODY_CLOCK: onlineTableColumns,
  BODY_ONLINE_RATE: onlineTableColumns,
  BODY_ACTIVE: connectInternet,
};
export { tableColumns };
