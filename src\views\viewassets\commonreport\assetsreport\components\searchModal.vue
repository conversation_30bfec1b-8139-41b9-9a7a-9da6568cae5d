<template>
  <div class="search-module">
    <div class="search">
      <ui-label class="inline" label="组织机构" :width="65">
        <ApiOrganizationTree :select-tree="selectOrgTree" @selectedTree="selectedOrgTree" placeholder="请选择组织机构">
        </ApiOrganizationTree>
      </ui-label>
      <ui-label class="inline ml-lg" :label="global.filedEnum.deviceId" :width="65">
        <Input v-model="searchData.deviceId" class="width-sm" :placeholder="global.filedEnum.deviceId"></Input>
      </ui-label>
      <ui-label class="inline ml-lg" :label="global.filedEnum.deviceName" :width="65">
        <Input v-model="searchData.deviceName" class="width-sm" :placeholder="global.filedEnum.deviceName"></Input>
      </ui-label>
      <ui-label class="inline ml-lg" label="行政区划" :width="65">
        <ApiAreaTree
          :select-tree="selectTree"
          @selectedTree="selectedArea"
          class="width-sm"
          placeholder="请选择行政区划"
        ></ApiAreaTree>
      </ui-label>
      <ui-label class="inline ml-lg" label="下发时间" :width="65">
        <DatePicker
          class="width-sm"
          v-model="searchData.startTime"
          type="datetime"
          placeholder="请选择开始时间"
          :options="startTimeOption"
          confirm
        ></DatePicker>
        <span class="ml-sm mr-sm">--</span>
        <DatePicker
          class="width-sm"
          v-model="searchData.endTime"
          type="datetime"
          placeholder="请选择结束时间"
          :options="endTimeOption"
          confirm
        ></DatePicker>
      </ui-label>
      <div class="inline ml-lg">
        <Button type="primary" @click="search">查询</Button>
        <Button class="ml-sm" @click="reset">重置</Button>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  components: {
    ApiAreaTree: require('@/api-components/api-area-tree.vue').default,
    ApiOrganizationTree: require('@/api-components/api-organization-tree.vue').default,
  },
  props: {},
  data() {
    return {
      choosedOrg: {},
      allCheck: '',
      searchData: {},
      selectTree: {
        regionCode: '',
      },
      selectOrgTree: {
        orgCode: null,
      },
      startTimeOption: {
        disabledDate: (date) => {
          let endTime = new Date(this.searchData.endTime);
          if (endTime) {
            return date > endTime;
          }
          return false;
        },
      },
      endTimeOption: {
        disabledDate: (date) => {
          let startTime = new Date(this.searchData.startTime);
          if (startTime) {
            return date < startTime;
          }
          return false;
        },
      },
    };
  },
  computed: {},
  watch: {},
  filter: {},
  created() {},
  mounted() {},
  methods: {
    // 批量上报
    testing() {},
    // 查询
    search() {
      let params = JSON.parse(JSON.stringify(this.searchData));
      if (params.startTime) {
        params.startTime = this.$util.common.formatDate(this.searchData.startTime);
      }
      if (params.endTime) {
        params.endTime = this.$util.common.formatDate(this.searchData.endTime);
      }
      this.$emit('startSearch', params, this.choosedOrg);
    },
    // 重置
    reset() {
      this.searchData = {};
      this.selectTree = {
        regionCode: '',
      };
      this.selectOrgTree = {
        orgCode: null,
      };
      this.$emit('startSearch', {}, {});
    },
    // 行政区划
    selectedArea(data) {
      this.searchData.civilCode = data.regionCode;
    },
    selectedOrgTree(val) {
      this.choosedOrg = val;
      this.searchData.orgCode = val.orgCode;
    },
  },
};
</script>
<style lang="less" scoped>
.search-module {
  padding: 10px 0px;
  @{_deep} .select-width {
    width: 160px;
  }
}
.w150 {
  width: 150px;
}
</style>
