<template>
  <ui-modal v-model="trajectoryVisible" :title="title" :footerHide="true" :width="width">
    <!-- <Row>
      <Col span="12">
        <div class="content"></div>
      </Col>
      <Col span="12">
        <div class="content"></div>
      </Col>
    </Row> -->
    <div class="content" v-if="trajectoryVisible">
      <div class="top">
        <div class="left">
          <ui-image class="img" :src="personObj.identityPhoto" alt="" />
          <!-- <img src="@/assets/img/car.jpg" alt="" srcset=""> -->
          <p>姓名：{{ personObj.name }}</p>
          <p>证件号：{{ personObj.idCard }}</p>
        </div>
        <div class="right">
          <ui-image class="img" :src="item.trackImage" alt="" />
          <!-- <img src="@/assets/img/car.jpg" alt="" srcset=""> -->
          <p>{{ item.shotTime }}</p>
          <p>{{ item.catchPlace ? item.catchPlace : '暂无数据' }}</p>
        </div>
        <div class="clear"></div>
      </div>
      <div>
        <ul>
          <li v-for="(item, index) in resultList" :key="index">
            <div>{{ item.name }}</div>
            <div>{{ item.value }}</div>
          </li>
        </ul>
      </div>
    </div>
  </ui-modal>
</template>

<script>
export default {
  name: 'captureDetail',
  props: {},
  data() {
    return {
      width: 1200,
      title: '轨迹准确性存疑原因',
      trajectoryVisible: false,
      resultList: [],
      personObj: null,
      item: null,
    };
  },
  async created() {},
  computed: {},
  methods: {
    init() {
      // this.resultList = [
      //   { name: '检测算法', value: '识别结果'},
      //   // { name: '原始算法', value: this.item.similarity*100+'%'},
      //   { name: '商汤人脸结构化算法名称', value: '90%'},
      //   { name: '海康人脸结构化算法名称', value: '90%'},
      //   { name: '综合判定', value: '不是同一人'},
      // ]
      // var obj = { name: '检测算法', value: '识别结果'};
      var num = (this.item.similarity * 100).toFixed(2);
      this.resultList = [
        { name: '检测算法', value: '识别结果' },
        { name: '原始算法', value: num + '%' },
      ];
      var arr = JSON.parse(this.item.algResult);
      if (arr) {
        arr.forEach((it) => {
          var obj2 = {
            name: it.algorithmType,
            value: it.score + '%',
          };

          this.resultList.push(obj2);
        });
      }

      var obj1 = {
        name: '综合判定',
        value: this.item.synthesisResult == 0 ? '准确性存疑' : '抓拍正常',
      };
      this.resultList.push(obj1);
    },
    show(personObj, item) {
      this.personObj = personObj;
      this.item = item;
      this.init();
      this.trajectoryVisible = true;
    },
  },
  watch: {
    trajectoryVisible(val) {
      this.$emit('input', val);
    },
    value(val) {
      this.trajectoryVisible = val;
    },
  },
  components: {},
};
</script>

<style lang="less" scoped>
.content {
  height: 400px;
  color: #fff;
  padding-left: 80px;
  .top {
    position: relative;
    height: 120px;
    .left,
    .right {
      position: absolute;
      width: 50%;
      height: 100%;
      padding-left: 130px;
      img,
      .img {
        position: absolute;
        width: 120px;
        height: 120px;
        left: 0;
      }
    }
    .left {
      left: 0;
    }
    .right {
      right: 0;
    }
  }

  ul {
    margin-top: 80px;
    font-size: 14px;
    li {
      height: 36px;
      line-height: 36px;
      div {
        float: left;
        width: 50%;
      }
    }
    li:first-child {
      border-bottom: 1px solid #092f69;
    }
  }
}
.clear {
  clear: both;
}
</style>
