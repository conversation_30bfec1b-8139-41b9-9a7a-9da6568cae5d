<template>
  <div class="base-search">
    <ui-label class="fl" label="关键词" :width="52">
      <Input class="input-width" placeholder="请输入设备编码" v-model="searchData.keyWord"></Input>
    </ui-label>
    <ui-label class="fl ml-lg" label="重点人员类型" :width="94">
      <Select v-model="searchData.personType" class="remarks input-width" placeholder="请选择重点场所" clearable>
        <Option v-for="(item, index) in tagTypeList" :key="index" :label="item.tagName" :value="item.tagId"> </Option>
      </Select>
    </ui-label>
    <ui-label :width="30" class="fl" label=" ">
      <Button type="primary" class="mr-sm" @click="startSearch"> 查询 </Button>
      <Button type="default" class="mr-lg" @click="resetClick"> 重置 </Button>
    </ui-label>
    <slot></slot>
  </div>
</template>
<script>
export default {
  props: {
    tagTypeList: {
      default: () => [],
    },
  },
  data() {
    return {
      searchData: {
        personType: '',
        keyWord: '',
        pageNumber: 1,
        pageSize: 20,
      },
    };
  },
  created() {
    this.copySearchDataMx(this.searchData);
  },
  methods: {
    startSearch() {
      this.searchData.pageNumber = 1;
      this.$emit('startSearch', this.searchData);
    },
    resetClick() {
      this.resetSearchDataMx(this.searchData);
      this.startSearch();
    },
  },
  watch: {},
  components: {},
};
</script>
<style lang="less" scoped>
.base-search {
  overflow: hidden;
  .input-width {
    width: 230px;
  }
  .ui-label {
    line-height: 40px;
  }
}
</style>
