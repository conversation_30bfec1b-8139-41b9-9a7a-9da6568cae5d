import { getVehicleArchivesViolation } from '@/api/vehicleArchives';

export default {
  data(){
    return {
      violationLoading: false,
      vehicleViolationList: [],//违章信息列表
    }
  },
  methods: {
    async getVehicleArchivesViolationList() {
      //车辆信息有多个 默认取第一个
      let idcardNo = ""
      if (this.basicInfo && this.basicInfo.length > 0){
        idcardNo = this.basicInfo[0]["idcardNo"]
      }
      if (!idcardNo) return this.$Message.error("查询车辆违章信息失败，车主身份证号码不存在")
      try {
        this.violationLoading = true;
        let params = {
          idCardNo: idcardNo,
          pageNumber: 1,
          pageSize: 100,
        }
        let {data} = await getVehicleArchivesViolation(params)
        this.vehicleViolationList = data.entities
      }catch (e){
        console.log(e);
      }finally {
        this.violationLoading = false;
      }
    },
  }
}
