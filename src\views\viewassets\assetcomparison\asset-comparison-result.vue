<template>
  <div class="asset-comparison-wrap auto-fill">
    <div class="header">
      <div class="header-left">
        <div class="title">
          <span v-if="resultHeaderInfo.left">
            <i class="icon-font icon-shujuyuan1"></i>
            <span> 数据源：{{ resultHeaderInfo.left.tableName }}</span>
          </span>
          <span v-if="resultHeaderInfo.left.funtionTypesNames.length">
            <i class="icon-font icon-jianceshebeishuliang"></i>
            <span
              >摄像机功能类型：
              <span v-for="(e, i) in resultHeaderInfo.left.funtionTypesNames" :key="i">
                {{ e }}{{ resultHeaderInfo.left.funtionTypesNames.length - 1 === i ? '' : '、' }}
              </span>
            </span>
          </span>
        </div>
        <div class="content">
          <template v-for="(e, i) in statisticalQuantityList">
            <div class="content-item" :key="i">
              <span>
                <i class="icon-font" :class="e.icon" :style="{ color: e.color }"></i>
              </span>
              <span>
                <div class="name">{{ e.name }}</div>
                <div class="count" :style="{ color: e.color }">
                  {{ e.leftCount }}
                </div>
              </span>
            </div>
          </template>
        </div>
      </div>
      <div class="header-right">
        <div class="title">
          <span>
            <i class="icon-font icon-shujuyuan1"></i>
            <span v-if="resultHeaderInfo.right"> 数据源：{{ resultHeaderInfo.right.tableName }}</span>
          </span>
          <span v-if="resultHeaderInfo.right.funtionTypesNames.length">
            <i class="icon-font icon-jianceshebeishuliang"></i>
            <span
              >摄像机功能类型：
              <span v-for="(e, i) in resultHeaderInfo.right.funtionTypesNames" :key="i">
                {{ e }}{{ resultHeaderInfo.right.funtionTypesNames.length - 1 === i ? '' : '、' }}
              </span>
            </span>
          </span>
        </div>
        <div class="content">
          <template v-for="(e, i) in statisticalQuantityList">
            <div class="content-item" :key="i">
              <span>
                <i class="icon-font" :class="e.icon" :style="{ color: e.color }"></i>
              </span>
              <span>
                <div class="name">{{ e.name }}</div>
                <div class="count" :style="{ color: e.color }">
                  {{ e.rightCount }}
                </div>
              </span>
            </div>
          </template>
        </div>
      </div>
    </div>
    <div class="tab-line">
      <div class="tab-line-left">
        <div
          v-for="(item, index) in statusList"
          :key="index"
          :class="{ active: item.status === searchInfo.searchType }"
          class="ivu-tabs-tab"
          @click="tabClick(item)"
        >
          <span>{{ item.name }}</span>
        </div>
      </div>
      <div class="tab-line-right">
        <Checkbox v-model="isAll" :disabled="!tableDataRightList.length && !tableDataLeftList.length">全部 </Checkbox>
        <Button type="primary" class="ml-lg button-blue" :loading="exportDataLoading" @click="exportExcel">
          <i class="icon-font icon-daochu delete-icon"></i>
          <span class="inline vt-middle ml-xs">导出</span>
        </Button>
      </div>
    </div>
    <div class="content" v-if="tableColumnsLeftList.length">
      <div class="content-left auto-fill">
        <ui-table
          class="ui-table auto-fill"
          :row-key="'key'"
          :table-columns="tableColumnsLeftList"
          :table-data="tableDataLeftList"
          :loading="loading"
          :minusHeight="160"
          @rowDbClick="checkData"
          @selectTable="selectTableLeft"
        >
          <template v-for="(item, index) in tableColumnsLeftList" :slot="item.slot" slot-scope="{ row }">
            <span :key="index">
              <span
                style="color: var(--color-warning)"
                v-if="row.inconsistentColumns && row.inconsistentColumns.includes(item.slot)"
                >{{ row[item.slot] }}</span
              >
              <span v-else>{{ row[item.slot] }}</span>
            </span>
            <!-- <div v-else>
              <Tooltip placement="bottom-start">
                <span style="color: var(--color-warning)">
                  {{
                    row[item.slot].length > 10
                      ? `${row[item.slot].slice(1, 10) + "..."}`
                      : row[item.slot]
                  }}</span
                >
                <div slot="content">
                  {{ row[item.slot] }}
                </div>
              </Tooltip>
            </div> -->
          </template>
        </ui-table>
      </div>
      <div class="content-right auto-fill">
        <ui-table
          class="ui-table auto-fill"
          :table-columns="tableColumnsRightList"
          :table-data="tableDataRightList"
          :loading="searchInfo.searchType !== '2' ? loading : uniqueRightloading"
          :minusHeight="160"
          @rowDbClick="checkData"
          @selectTable="selectTableRight"
        >
          <template v-for="(item, index) in tableColumnsRightList" :slot="item.slot" slot-scope="{ row }">
            <span :key="index">
              <span
                style="color: var(--color-warning)"
                v-if="row.inconsistentColumns && row.inconsistentColumns.includes(item.slot)"
                >{{ row[item.slot] }}</span
              >
              <span v-else>{{ row[item.slot] }}</span>
            </span>
          </template>
        </ui-table>
      </div>
    </div>
    <div v-if="searchInfo.searchType !== '2'">
      <ui-page
        class="page"
        :page-data="pageData"
        @changePage="changeComparisonListPageNumber"
        @changePageSize="changeComparisonListPageSize"
      >
      </ui-page>
    </div>
    <div class="footer" v-if="searchInfo.searchType === '2'">
      <ui-page
        class="page page-left"
        :page-data="uniquePageDataLeft"
        @changePage="chanheUniqueLeftListPageNumber"
        @changePageSize="chanheUniqueLeftListPageSize"
      >
      </ui-page>
      <ui-page
        class="page page-right"
        :page-data="uniquePageDataRight"
        @changePage="chanheUniqueRightListPageNumber"
        @changePageSize="chanheUniqueRightListPageSize"
      >
      </ui-page>
    </div>
    <equipment-Info-comparison
      ref="info-comparison"
      :title="'设备信息比对'"
      :comparison-fields="comparisonFields"
      :table-data-left="tableDataLeft"
      :table-data-right="tableDatableRight"
      :resultData="resultData"
      @handleResetTableData="getComparisonResultList"
    ></equipment-Info-comparison>
  </div>
</template>

<script>
import assetcomparison from '@/config/api/assetcomparison.js';

export default {
  components: {
    UiTable: require('@/components/ui-table.vue').default,
    EquipmentInfoComparison: require('./components/equipment-info-comparison.vue').default,
  },
  props: {
    resultData: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      statusList: [
        { name: '相同', status: '0' },
        { name: '差异', status: '1' },
        { name: '独有', status: '2' },
      ],
      statisticalQuantityList: [
        {
          name: '设备总量',
          leftCount: 0,
          rightCount: 0,
          color: 'var(--font-card-green)',
          icon: 'icon-jianceshebeishuliang',
          key: 'total',
        },
        {
          name: '信息相同设备',
          leftCount: 0,
          rightCount: 0,
          color: 'var(--color-primary)',
          icon: 'icon-xinxixiangtongshebei',
          key: 'sameCount',
        },
        {
          name: '信息差异设备',
          leftCount: 0,
          rightCount: 0,
          color: 'var(--font-card-light-pink)',
          icon: 'icon-xinxichayishebei',
          key: 'differentCount',
        },
        {
          name: '独有设备',
          leftCount: 0,
          rightCount: 0,
          color: 'var(--font-card-cyan)',
          icon: 'icon-duyoushebei',
          key: 'uniqueCount',
        },
      ],
      tableDataLeftList: [],
      tableDataRightList: [],
      loading: false,
      uniqueRightloading: false,
      tableColumnsLeftList: [],
      tableColumnsRightList: [],
      uniqueLeftPageData: {},
      uniqueRightPageData: {},
      comparisonFields: [],
      pageData: {
        totalCount: 0,
        pageNum: 1,
        pageSize: 20,
      },
      searchInfo: {
        left: {
          tableType: '0',
        },
        right: {
          tableType: '1',
        },
        searchType: '0',
      },
      uniquePageDataLeft: {
        totalCount: 0,
        pageNum: 1,
        pageSize: 20,
      },
      uniquePageDataRight: {
        totalCount: 0,
        pageNum: 1,
        pageSize: 20,
      },
      tableDataLeft: {}, // 修改弹框数据-左
      tableDatableRight: {}, // 修改弹框数据-左
      resultHeaderInfo: {},
      deviceIds: [], // 导出选中的设备id
      isAll: false,
      exportDataLoading: false,
      deviceIdsRight: [], // 独有右边idlist
    };
  },
  watch: {
    isAll(val) {
      this.tableDataRightList.forEach((item) => {
        this.$set(item, '_checked', val);
        this.$set(item, '_disabled', val);
      });
      this.tableDataLeftList.forEach((item) => {
        this.$set(item, '_checked', val);
        this.$set(item, '_disabled', val);
      });
    },
  },
  async created() {
    const { left, right } = this.resultData;
    const { searchType } = this.searchInfo;
    this.resultHeaderInfo = {
      left: {
        ...left,
      },
      right: {
        ...right,
      },
    };
    this.searchInfo = {
      left: {
        ...left,
      },
      right: {
        ...right,
      },
      searchType,
    };
    this.deviceIds = [];
    this.deviceIdsRight = [];
    this.isAll = false;
    await this.getComparisonTableHearder();
    this.getComparisonStatistics();
  },
  async mounted() {
    await this.getComparisonResultList();
  },
  methods: {
    tabClick({ status }) {
      this.searchInfo.searchType = status;
      this.deviceIds = [];
      this.deviceIdsRight = [];
      this.isAll = false;
      if (status === '2') {
        this.uniquePageDataLeft = {
          totalCount: 0,
          pageNum: 1,
          pageSize: 20,
        };
        this.uniquePageDataRight = {
          totalCount: 0,
          pageNum: 1,
          pageSize: 20,
        };
        this.tableColumnsRightList = [
          {
            type: 'selection',
            width: 50,
            align: 'center',
          },
          ...this.tableColumnsRightList,
        ];
        this.queryLeftUniqueDeviceList();
        this.queryRightUniqueDeviceList();
      } else {
        this.pageData = {
          totalCount: 0,
          pageNum: 1,
          pageSize: 20,
        };
        if (this.tableColumnsRightList[0].type === 'selection') {
          this.tableColumnsRightList = [...this.tableColumnsRightList.slice(1, this.tableColumnsRightList.length)];
        }
        this.getComparisonResultList();
      }
    },
    // 差异对比弹框
    checkData(data) {
      if (this.searchInfo.searchType !== '2') {
        const { inconsistentColumns = [], idLeft, idRight } = data;
        this.tableColumnsLeftList.forEach((e) => {
          if (Object.keys(data).includes(e.slot)) {
            this.tableDataLeft[e.slot] = data[e.slot];
          }
        });
        this.tableColumnsRightList.forEach((e) => {
          if (Object.keys(data).includes(e.slot)) {
            this.tableDatableRight[e.slot] = data[e.slot];
          }
        });
        this.tableDataLeft = {
          ...this.tableDataLeft,
          idLeft,
          inconsistentColumns,
        };
        this.tableDatableRight = {
          ...this.tableDatableRight,
          idRight,
          inconsistentColumns,
        };
        setTimeout(() => {
          this.$refs['info-comparison'].init();
        }, 0);
      }
    },
    // 表头
    async getComparisonTableHearder() {
      try {
        let res = await this.$http.post(assetcomparison.getComparisonTableHearder);
        const { data } = res.data;
        const { left: tableHeaderLeftList, right: tableHeaderRightList } = data;
        this.comparisonFields = tableHeaderLeftList;
        const list = ['latitudeLeft', 'longitudeLeft', 'latitudeRight', 'longitudeRight'];
        this.tableColumnsLeftList = tableHeaderLeftList.map((e) => {
          return {
            title: e.name,
            slot: e.key,
            align: 'left',
            minWidth: list.includes(e.key) ? 84 : 180,
            ellipsis: true,
          };
        });
        this.tableColumnsLeftList = [
          {
            type: 'selection',
            width: 50,
            align: 'center',
          },
          {
            title: '序号',
            type: 'index',
            align: 'center',
            width: 50,
          },
          ...this.tableColumnsLeftList,
        ];
        this.tableColumnsRightList = tableHeaderRightList.map((e) => {
          return {
            title: e.name,
            slot: e.key,
            minWidth: list.includes(e.key) ? 84 : 180,
            align: 'left',
            ellipsis: true,
          };
        });
        this.tableColumnsRightList = [
          {
            title: '序号',
            type: 'index',
            align: 'center',
            width: 50,
          },
          ...this.tableColumnsRightList,
        ];
      } catch (err) {
        console.log(err);
      } finally {
        this.loading = false;
      }
    },
    // 相同差异对比结果
    async getComparisonResultList() {
      try {
        this.loading = true;
        let res = await this.$http.post(assetcomparison.getComparisonResultList, {
          ...this.searchInfo,
          ...this.pageData,
          pageNumber: this.pageData.pageNum,
          totalCount: undefined,
          pageNum: undefined,
        });
        const { data } = res.data;
        const { entities, pageNumber, pageSize, total: totalCount } = data;
        this.pageData = {
          totalCount,
          pageNum: pageNumber,
          pageSize,
        };
        this.tableDataLeftList = this.isAll ? this.handleData(entities) : entities;
        this.tableDataRightList = this.isAll ? this.handleData(entities) : entities;
      } finally {
        this.loading = false;
      }
    },
    // 对比结果统计
    async getComparisonStatistics() {
      try {
        let res = await this.$http.post(assetcomparison.getComparisonStatistics, {
          ...this.searchInfo,
          searchType: undefined,
        });
        const { data } = res.data;
        const { leftStatics, rightStatics } = data;
        this.statisticalQuantityList = this.statisticalQuantityList.map((e) => {
          if (Object.keys(leftStatics).includes(e.key) || Object.keys(rightStatics).includes(e.key)) {
            return {
              ...e,
              leftCount: leftStatics[e.key],
              rightCount: rightStatics[e.key],
            };
          }
        });
      } catch (error) {
        console.log(error);
      }
    },
    // 独有-左
    async queryLeftUniqueDeviceList() {
      try {
        this.loading = true;
        let res = await this.$http.post(assetcomparison.queryLeftUniqueDeviceList, {
          ...this.searchInfo,
          ...this.uniquePageDataLeft,
          pageNumber: this.uniquePageDataLeft.pageNum,
          totalCount: undefined,
          pageNum: undefined,
          searchType: undefined,
        });
        const { data } = res.data;
        const { entities, pageNumber, pageSize, total: totalCount } = data;
        this.uniquePageDataLeft = {
          totalCount,
          pageNum: pageNumber,
          pageSize,
        };
        this.tableDataLeftList = entities;
        this.loading = false;
      } finally {
        this.loading = false;
      }
    },
    // 独有-右
    async queryRightUniqueDeviceList() {
      try {
        this.uniqueRightloading = true;
        let res = await this.$http.post(assetcomparison.queryRightUniqueDeviceList, {
          ...this.searchInfo,
          ...this.uniquePageDataRight,
          pageNumber: this.uniquePageDataRight.pageNum,
          totalCount: undefined,
          pageNum: undefined,
          searchType: undefined,
        });
        const { data } = res.data;
        const { entities, pageNumber, pageSize, total: totalCount } = data;
        this.uniquePageDataRight = {
          totalCount,
          pageNum: pageNumber,
          pageSize,
        };
        this.tableDataRightList = entities;
        this.uniqueRightloading = false;
      } finally {
        this.uniqueRightloading = false;
      }
    },
    // 相同差异 分页
    changeComparisonListPageSize(val) {
      this.pageData.pageNum = 1;
      this.pageData.pageSize = val;
      this.getComparisonResultList();
    },

    changeComparisonListPageNumber(val) {
      this.pageData.pageNum = val;
      this.getComparisonResultList();
    },
    // 独有-左分页
    chanheUniqueLeftListPageSize(val) {
      this.uniquePageDataLeft.pageNum = 1;
      this.uniquePageDataLeft.pageSize = val;
      this.queryLeftUniqueDeviceList();
    },

    chanheUniqueLeftListPageNumber(val) {
      this.uniquePageDataLeft.pageNum = val;
      this.queryLeftUniqueDeviceList();
    },
    // 独有-右分页
    chanheUniqueRightListPageSize(val) {
      this.uniquePageDataRight.pageNum = 1;
      this.uniquePageDataRight.pageSize = val;
      this.queryRightUniqueDeviceList();
    },

    chanheUniqueRightListPageNumber(val) {
      this.uniquePageDataRight.pageNum = val;
      this.queryRightUniqueDeviceList();
    },
    selectTableLeft(selection) {
      this.deviceIds = selection.map((e) => {
        return e.deviceIDLeft;
      });
    },
    selectTableRight(selection) {
      this.deviceIdsRight = selection.map((e) => {
        return e.deviceIDRight;
      });
    },
    // 处理全选分页
    handleData(array) {
      array.forEach((item) => {
        this.$set(item, '_checked', true);
        this.$set(item, '_disabled', true);
      });
      return array;
    },
    async exportExcel() {
      const { deviceIds, deviceIdsRight, isAll } = this;
      if (!isAll && !deviceIds.length && !deviceIdsRight.length) {
        return this.$Message.error('请先选择导出数据');
      }
      const { searchType } = this.searchInfo;
      try {
        this.searchInfo.left.deviceIds = isAll ? undefined : deviceIds;
        this.searchInfo.right.deviceIds =
          searchType === '2' ? (isAll ? undefined : deviceIdsRight) : isAll ? undefined : deviceIds;
        let params = {
          ...this.searchInfo,
        };
        this.exportDataLoading = true;
        let res = await this.$http.post(assetcomparison.exportData, params, {
          responseType: 'blob',
        });
        this.$util.common
          .exportfile(res)
          .then()
          .catch((err) => {
            this.$Message.warning(err.msg);
          });
      } catch (err) {
        console.log(err);
      } finally {
        this.exportDataLoading = false;
      }
    },
  },
};
</script>

<style lang="less" scoped>
.asset-comparison-wrap {
  height: 100%;
  padding: 0 20px 20px;
  .header {
    display: flex;
    > div {
      width: 50%;
      padding-bottom: 10px;
      .title {
        height: 53px;
        line-height: 53px;
        font-size: 16px;
        color: var(--color-title);
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        & > span {
          margin-right: 62px;
          i {
            margin-right: 4px;
          }
        }
      }
      .content {
        height: 110px;
        display: flex;
        align-items: center;
        width: 100%;
        background: var(--bg-sub-content);
        .content-item {
          width: 25%;
          display: flex;
          align-items: center;
          position: relative;
          .icon-font {
            font-size: 37px;
            margin-left: 30px;
            margin-right: 10px;
          }
          .name {
            font-size: 14px;
            color: #f5f5f5;
          }
          .count {
            font-size: 18px;
          }
          &:first-child::after {
            content: '';
            width: 0px;
          }
        }
        .content-item::after {
          content: '';
          width: 1px;
          height: 40px;
          position: absolute;
          top: 10px;
          left: 0;
          background: #1568ad;
        }
      }
    }
    .header-left {
      border-right: 1px solid var(--devider-line);
      padding-right: 10px;
    }
    .header-right {
      padding-left: 10px;
    }
  }
  .tab-line {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 48px;
    line-height: 48px;
    background: var(--bg-sub-content);
    padding: 10px;
    margin-bottom: 10px;
    .ivu-tabs-tab {
      float: left;
      height: 36px;
      line-height: 36px;
      font-size: 14px;
      border: 1px solid var(--border-btn-dashed);
      border-right: none;
      padding: 0 22px;
      color: var(--color-btn-dashed);
      &:hover {
        background: var(--color-primary);
        color: #fff;
        cursor: pointer;
      }
      &:last-child {
        border-right: 1px solid var(--border-btn-dashed);
      }
    }
    .active {
      background: var(--color-primary);
      color: #fff;
    }
    .tab-line-right {
      display: flex;
      align-items: center;
    }
  }
  /deep/ .content {
    display: flex;
    .content-left {
      width: 50%;
      border-right: 1px solid var(--devider-line);
      padding-right: 10px;
    }
    .content-right {
      width: 50%;
      padding-left: 10px;
    }
    .content-left,
    .content-right {
      .ui-table {
        .ivu-table-column-left {
          padding-left: 10px;
        }
      }
    }
    .ivu-table-cell-ellipsis {
      .ivu-table-cell-slot {
        > span {
          display: block;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }
  }
  .footer {
    display: flex;
    .page-right,
    .page-left {
      width: 50%;
      padding: 20px 8px;
    }
  }
}
</style>
