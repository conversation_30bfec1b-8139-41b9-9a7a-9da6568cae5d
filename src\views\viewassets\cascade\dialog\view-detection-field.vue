<template>
  <ui-modal v-model="visible" title="检测不合格原因" :styles="styles" footer-hide>
    <div>
      <ui-table
        class="ui-table mt-lg"
        :table-columns="tableColumns"
        :table-data="tableData"
        :minus-height="minusTable"
        :loading="loading"
      >
        <template #errorMessage="{ row }">
          <span class="font-red">{{ row.errorMessage }}</span>
        </template>
      </ui-table>
    </div>
    <ui-page
      class="page menu-content-background"
      :page-data="pageData"
      @changePage="changePage"
      @changePageSize="changePageSize"
    >
    </ui-page>
  </ui-modal>
</template>
<style lang="less" scoped>
.success {
  background: @color-success;
}

.error {
  background: @color-failed;
}
</style>
<script>
import cascade from '@/config/api/cascade';

export default {
  data() {
    return {
      visible: false,
      styles: {
        width: '6.5rem',
      },
      tableData: [],
      searchData: {
        'checkId': '',
        'deviceId': '',
        'params': {
          'pageNumber': 1,
          'pageSize': 10,
        },
      },
      pageData: {
        pageNum: 1,
        pageSize: 10,
        totalCount: 0,
      },
      loading: false,
      minusTable: 400,
      tableColumns: [
        { type: 'index', width: 50, title: '序号', align: 'center' },
        { title: '不合格原因', slot: 'errorMessage', tooltip: true },
        { title: '检测规则名称', key: 'ruleName', tooltip: true },
        { title: '不合格字段', key: 'checkColumnName', tooltip: true },
        { title: '实际结果', key: 'result', tooltip: true },
      ],
    };
  },
  created() {},
  mounted() {},
  methods: {
    init(row) {
      this.visible = true;
      this.searchData.checkId = row.checkId;
      this.searchData.deviceId = row.deviceId;
      this.getDataList();
    },
    async getDataList() {
      try {
        this.loading = true;
        this.copySearchDataMx(this.searchData);
        let res = await this.$http.post(cascade.deviceUnqualifiedList, this.searchData);
        this.tableData = res.data.data.entities;
        this.pageData.totalCount = res.data.data.total;
      } catch (err) {
        console.error(err);
      } finally {
        this.loading = false;
      }
    },
    search() {
      this.searchData.params.pageNumber = 1;
      this.getDataList();
    },
    changePage(val) {
      this.searchData.params.pageNumber = val;
      this.getDataList();
    },
    changePageSize(val) {
      this.searchData.params.pageSize = val;
      this.search();
    },
  },
  components: {
    UiTable: require('@/components/ui-table.vue').default,
  },
};
</script>
