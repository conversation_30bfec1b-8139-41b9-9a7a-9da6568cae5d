<template>
  <div class="auto-fill">
    <Particular
      class="particular"
      :icon-list="iconList"
      :form-data="formData"
      :table-columns="tableColumns"
      :table-data="tableData"
      :total-count="totalCount"
      @handlePageSize="handlePageSize"
      @handlePage="handlePage"
      img-key="scenePath"
    >
      <template #otherButton>
        <div class="other-button mb-sm inline ml-lg">
          <span class="record-text mr-lg" @click="checkReason">历史离线记录</span>
          <Button type="primary" :loading="exportLoading" @click="onExport">
            <i class="icon-font icon-piliangshangbao"></i> 导出
          </Button>
        </div>
      </template>
    </Particular>
    <nonconformance
      ref="nonconformance"
      title="离线记录"
      :tableColumns="reasonTableColumns"
      :tableData="reasonTableData"
      :reasonLoading="reasonLoading"
    ></nonconformance>
  </div>
</template>
<script>
import dealWatch from '@/mixins/deal-watch';
import particularMixin from '@/views/governanceevaluation/evaluationoResult/util/Mixins/particularMixin';
import evaluationoverview from '@/config/api/evaluationoverview';
// 本层配置文件
import { iconStaticsList, tableColumns } from './util/enum/ReviewParticular.js';
export default {
  mixins: [particularMixin, dealWatch],
  data() {
    return {
      iconList: iconStaticsList,
      tableLoading: false,
      formData: {},
      tableColumns: Object.freeze(tableColumns),
      tableData: [],
      pageData: {
        pageNum: 1,
        pageSize: 20,
      },
      totalCount: 0,
      reasonLoading: false,
      reasonPage: { totalCount: 0, pageNum: 1, pageSize: 20, orgCode: '', taskIndexId: null },
      reasonTableColumns: [
        { title: '组织机构', key: 'orgName' },
        { title: '平台类型', key: 'platformType' },
        { title: '离线时间', key: 'unOnlineDate' },
        { title: '检测时间', key: 'checkDate' },
      ],
      reasonTableData: [],
      exportLoading: false,
    };
  },
  created() {
    this.startWatch(
      '$route.query',
      () => {
        this.initAll();
      },
      { deep: true, immediate: true },
    );
  },
  methods: {
    initAll() {
      // 获取列表
      this.getTableData();
      // 获取统计
      this.MixinGetStatInfo().then((data) => {
        // 设备模式统计
        iconStaticsList.forEach((item) => {
          if (item.fileName === 'resultValue') {
            // 配置设备图片地址可用率图标
            this.MixinSpecialIcon(item, data.qualified);
          }
          item.count = data[item.fileName] || 0;
        });
      });
    },
    handlePage(val) {
      Object.assign(this.pageData, val);
      this.getTableData();
    },
    handlePageSize(val) {
      Object.assign(this.pageData, val);
      this.getTableData();
    },
    // 获取列表[mixin的方法]
    getTableData() {
      this.MixinGetTableData().then((data) => {
        this.tableData = data.entities;
        this.totalCount = data.total;
      });
    },
    // 导出
    onExport: function () {
      const params = {
        displayType: this.$route.query.statisticType,
      };
      params.orgRegionCode = params.displayType === 'REGION' ? this.$route.query.regionCode : this.$route.query.orgCode;
      this.MixinGetExport(params);
    },
    async getReason() {
      const paramsList = this.$route.query;
      this.reasonLoading = true;
      let params = {
        indexId: paramsList.indexId,
        batchId: paramsList.batchId,
        access: paramsList.access,
        displayType: paramsList.statisticType,
        orgRegionCode: this.reasonPage.orgCode,
      };
      params.orgRegionCode = params.displayType === 'REGION' ? this.$route.query.regionCode : this.$route.query.orgCode;
      try {
        let res = await this.$http.post(evaluationoverview.getSecondaryPopUpData, params);
        const datas = res.data.data;
        this.reasonTableData = datas.entities || [];
      } catch (error) {
        console.log(error);
      } finally {
        this.reasonLoading = false;
      }
    },
    // 离线记录
    checkReason() {
      this.getReason();
      this.$refs.nonconformance.init();
    },
  },
  components: {
    Particular: require('@/views/governanceevaluation/evaluationoResult/ui-pages/particular.vue').default,
    nonconformance: require('../../components/nonconformance.vue').default,
  },
};
</script>
<style lang="less" scoped>
.particular {
  padding: 0 10px;
}
.monitor-img {
  position: relative;
  width: 56px;
  height: 56px;
  margin: 5px 0;
  cursor: pointer;
}
.other-button {
  align-items: center;
}
.record-text {
  font-size: 16px;
  color: var(--color-primary);
  text-decoration: underline;
  cursor: pointer;
}
</style>
