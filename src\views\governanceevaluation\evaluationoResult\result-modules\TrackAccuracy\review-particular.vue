<!-- 轨迹准确率 -->
<template>
  <div class="review-particular auto-fill">
    <div class="icon-statics-wrapper">
      <icon-statics :icon-list="iconList"></icon-statics>
      <div class="icon-statics-wrapper-right">
        <tag-view :list="tabList" @tagChange="changeMode" ref="tagView" class="tag-view mt-xs"></tag-view>
      </div>
    </div>
    <div class="auto-fill">
      <component
        :is="componentName"
        :form-item-data="formItemData"
        :form-data="formData"
        :table-columns="tableColumns"
        :result-data="resultData"
        :check-picture-params="checkPictureParams"
        :loading="loading"
        @startSearch="startSearch"
      >
        <template #identityPhoto="{ row }">
          <div @click="viewBigPic(row.identityPhoto)" class="ui-images">
            <ui-image :src="row.identityPhoto" />
          </div>
        </template>
        <template #trackImage="{ row }">
          <div @click="viewBigPic(row.trackLargeImage)" class="ui-images pointer">
            <ui-image :src="row.trackImage" />
          </div>
        </template>
        <template #algResult="{ row }">
          <span v-if="row.algResult">
            <div v-for="(item, index) in JSON.parse(row.algResult)" :key="index">
              <span>{{ item.algorithmType }}:</span>
              <span>{{ item.score ? parseFloat(item.score).toFixed(6) : 0 }}</span>
            </div>
          </span>
          <span v-else>--</span>
        </template>
        <template slot="outcome" slot-scope="{ row }">
          <Tag
            v-if="row.synthesisResult in qualifiedColorConfig"
            :color="qualifiedColorConfig[row.synthesisResult].color"
          >
            {{ qualifiedColorConfig[row.synthesisResult].dataValue }}
          </Tag>
          <span class="ml-sm" v-if="row.dataMode === '3'"> (人工) </span>
        </template>
        <template #reason="{ row }">
          <Tooltip :content="row.reason" transfer max-width="150">
            {{ row.reason }}
          </Tooltip>
        </template>
      </component>
    </div>
    <LookScene v-model="bigPictureShow" :img-list="imgList"></LookScene>
  </div>
</template>
<script>
import particularMixin from '@/views/governanceevaluation/evaluationoResult/util/Mixins/particularMixin';
import dealWatch from '@/mixins/deal-watch';
import { mapGetters } from 'vuex';
// 本层配置文件
import { tableColumns, iconStaticsList, listFormData, cardFormData } from './util/enum/ReviewParticular.js';
import evaluationoverview from '@/config/api/evaluationoverview';
import detectionResult from '@/config/api/detectionResult';

export default {
  name: 'reviewParticular',
  mixins: [particularMixin, dealWatch],
  data() {
    return {
      iconList: iconStaticsList,
      activeMode: 'list',
      tabList: ['图片模式', '聚档模式'],
      formData: {
        beginTime: '',
        endTime: '',
        qualified: '',
        causeErrors: [],
        deviceIds: [],
      },
      formItemData: listFormData,
      componentName: 'ListPattern',
      tableLoading: false,
      tableColumns: Object.freeze(tableColumns),
      pageData: {
        pageNum: 1,
        pageSize: 20,
      },
      resultData: {},

      cardList: [],
      statisticShow: false,
      checkPictureParams: {
        indexId: this.$route.query.indexId,
        batchId: this.$route.query.batchId,
        access: 'TASK_RESULT',
        displayType: this.$route.query.statisticType,
        orgRegionCode:
          this.$route.query.statisticType === 'REGION' ? this.$route.query.regionCode : this.$route.query.orgCode,
      },
      bigPictureShow: false,
      imgList: [],
      qualifiedColorConfig: {
        '0': {
          color: 'var(--color-failed)',
          dataValue: '不合格',
        },
        '1': {
          color: 'var(--color-success)',
          dataValue: '合格',
        },
      },
      loading: false,
    };
  },
  created() {
    this.getQualificationList();
    this.startWatch(
      '$route.query',
      () => {
        this.initAll();
      },
      { deep: true, immediate: true },
    );
  },
  methods: {
    initAll() {
      // 获取统计
      this.MixinGetStatInfo().then((data) => {
        // 设备模式统计
        iconStaticsList.forEach((item) => {
          if (item.fileName === 'resultValue') {
            // 配置设备图片地址可用率图标
            this.MixinSpecialIcon(item, data.qualified);
          }
          item.count = data[item.fileName] || 0;
        });
      });
      // 获取列表
      this.selfConfigGetList();
    },
    // 获取不合格原因下拉列表  1 - 合格、2 - 不合格 3 - 无法检测
    async getQualificationList() {
      let params = {
        indexId: this.$route.query.indexId,
        batchId: this.$route.query.batchId,
        access: 'TASK_RESULT',
        displayType: this.$route.query.statisticType,
        customParameters: this.formData,
      };
      params.orgRegionCode = params.displayType === 'REGION' ? this.$route.query.regionCode : this.$route.query.orgCode;
      try {
        let res = await this.$http.post(detectionResult.getVideoUnqualifiedInfo, params);
        let options = res.data.data || [];
        let findErrorCodes = this.formItemData.find((item) => item.key === 'errorCodes');
        if (findErrorCodes) {
          findErrorCodes.options = options.map((item) => {
            return { value: item.code, label: item.reason };
          });
        }
      } catch (err) {
        console.log(err);
      }
    },
    // 获取列表[mixin的方法]
    selfConfigGetList() {
      this.activeMode === 'list' ? this.getTableData() : this.getImageList();
    },
    showModal(row) {
      this.osdDetailVisible = true;
      this.osdDetailData = { ...row };
    },
    handleOsdModalHide() {
      this.$refs.osdModalRef.hide();
    },
    handlePage(val) {
      Object.assign(this.pageData, val);
      this.selfConfigGetList();
    },
    startSearch(params) {
      this.pageData = params.pageData;
      Object.assign(this.formData, params.searchData);
      this.selfConfigGetList();
    },
    handlePageSize(val) {
      Object.assign(this.pageData, val);
      this.selfConfigGetList();
    },
    async getTableData() {
      try {
        this.loading = true;
        const data = await this.MixinGetTableData();
        this.resultData = data;
      } catch (e) {
        console.log(e);
      } finally {
        this.loading = false;
      }
    },
    // 判断更新统计是否显示
    async showRecountBtn() {
      try {
        let res = await this.$http.get(evaluationoverview.showRecountBtn, {
          params: { batchId: this.$route.query.batchId },
        });
        this.statisticShow = res.data.data || false;
      } catch (err) {
        console.log(err);
      }
    },
    // 导出
    exportFunc() {
      this.MixinGetExport();
    },
    viewBigPic(item) {
      if (!item) {
        this.$Message.warning('大图URL缺失');
        return;
      }
      this.imgList = [item];
      this.bigPictureShow = true;
    },
    // 切换模式
    changeMode(type) {
      this.resultData = [];
      this.activeMode = !type ? 'list' : 'cad';
      this.formItemData = this.activeMode === 'list' ? listFormData : cardFormData;
      this.componentName = this.activeMode === 'list' ? 'ListPattern' : 'CardPattern';
      if (type === 'list') {
        this.formData = {
          beginTime: '',
          endTime: '',
          qualified: '',
          causeErrors: [],
          deviceIds: [],
        };
        this.getQualificationList();
      } else {
        // 图片模式获取不合格原因传2
        this.formData = {
          name: '',
          idCard: '',
        };
        this.getQualificationList(2);
      }
      this.selfConfigGetList();
    },
    async getImageList() {
      // 处理图片模式列表数据
      try {
        this.loading = true;
        const data = await this.MixinGetImageList();
        this.resultData = data;
      } catch (e) {
        console.log(e);
      } finally {
        this.loading = false;
      }
    },
  },
  watch: {},
  computed: {
    ...mapGetters({
      deviceTagData: 'governanceevaluation/deviceTagData',
    }),
    // customizedAttrs() {
    //   return {
    //     iconList: this.iconList,
    //     tableColumns: this.tableColumns,
    //     tableData: this.tableData,
    //     formItemData: this.formItemData,
    //     formData: this.formData,
    //     tableLoading: this.tableLoading,
    //     totalCount: this.totalCount,
    //     cardList: this.cardList,
    //   };
    // },
  },
  components: {
    TagView: require('@/components/tag-view.vue').default,
    IconStatics: require('@/components/icon-statics.vue').default,
    DynamicCondition: require('@/views/governanceevaluation/evaluationoResult/components/dynamic-condition.vue')
      .default,
    ListPattern: require('../components/list-pattern').default,
    LookScene: require('@/components/look-scene').default,
    CardPattern: require('./components/card-pattern').default,
  },
};
</script>
<style lang="less" scoped>
.review-particular {
  padding: 0 10px;

  .icon-statics-wrapper {
    display: flex;
    justify-content: space-between;
    height: 50px;
    line-height: 50px;
  }

  .ui-images {
    width: 56px;
    height: 56px;
    margin: 5px 0;

    .ui-image {
      min-height: 56px !important;

      /deep/ .ivu-spin-text {
        img {
          width: 56px;
          height: 56px;
          margin-top: 5px;
        }
      }
    }
  }
}
</style>
