<template>
  <div class="statistics" v-ui-loading="{ loading: loading }">
    <div class="statistics-item" v-for="(item, index) in infoList" :key="index">
      <span class="icon-font f-37 vt-middle mr-sm" :class="item.icon" :style="{ color: item.color }"></span>
      <div class="inline vt-middle line">
        <p>{{ item.name }}</p>
        <p :style="{ color: item.color }">{{ typeModel === 'left' ? item.leftCount : item.rightCount }}</p>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    loading: {
      type: Boolean,
      default: false,
    },
    infoList: [],
    typeModel: {
      type: String,
    },
  },
};
</script>

<style lang="less" scoped>
.statistics {
  height: 110px;
  background: var(--bg-sub-content);
  display: flex;
  align-items: center;
  justify-content: space-evenly;
  .f-37 {
    font-size: 37px;
  }
  .statistics-item {
    position: relative;
    .icon-font {
      margin-left: 30px;
    }
    &:first-child {
      .line::before {
        content: '';
        width: 0;
      }
      .icon-font {
        margin-left: 0;
      }
    }
    .line {
      &::before {
        position: absolute;
        left: 0;
        top: 10px;
        content: '';
        height: 40px;
        width: 1px;
        background: #1568ad;
      }
    }
  }
}
</style>
