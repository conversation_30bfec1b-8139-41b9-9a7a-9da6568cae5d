<template>
  <div
    class="font-icon-bg"
    :style="{
      background: `linear-gradient(to bottom, ${iconInfo.bgColor[0]} 0%, ${iconInfo.bgColor[1]} 100%)`,
    }"
  >
    <i :class="`iconfontconfigure icon-${iconInfo.font_class}`"></i>
  </div>
</template>
<script>
import { getIcon } from "@/components/antv-g6/util/getIcon";
export default {
  props: {
    iconId: {},
  },
  computed: {
    iconInfo() {
      return getIcon(this.iconId) || { bgColor: [], font_class: "" };
    },
  },
  mounted() {},
  methods: {},
};
</script>
<style lang="less" scoped>
.font-icon-bg {
  height: 60px;
  width: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  .iconfontconfigure {
    color: #fff;
    font-size: 30px;
  }
}
</style>
