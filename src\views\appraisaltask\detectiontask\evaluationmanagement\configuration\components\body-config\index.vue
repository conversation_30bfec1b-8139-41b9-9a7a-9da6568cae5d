<template>
  <components :is="componentName" v-bind="$props" ref="components"> </components>
</template>
<script>
export default {
  props: {
    indexType: {
      type: String,
      default: '',
    },
    configInfo: {
      required: true,
      type: Object,
    },
    formModel: {
      required: true,
      type: String,
      default: 'edit',
    },
    moduleAction: {
      type: Object,
      default: () => {},
    },
    // 所有配置详情
    taskIndexConfig: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      formData: {},
    };
  },
  created() {},
  methods: {
    async handleSubmit() {
      const validate = await this.$refs.components.handleSubmit();
      this.formData = this.$refs.components.formData;
      return validate;
    },
  },
  watch: {},
  computed: {
    componentName() {
      switch (this.moduleAction.indexType) {
        // 人体卡口设备在线率
        case 'BODY_ONLINE_RATE':
          return 'BodyOnlineRate';
        // 人体卡口设备活跃率
        case 'BODY_ACTIVE':
          return 'BodyActivityRate';
        // 人体卡口设备时钟准确率
        case 'BODY_CLOCK':
          return 'BodyClock';
        // 人体卡口设备及时上传率
        case 'BODY_UPLOAD':
          return 'BodyUpload';
        default:
          return '';
      }
    },
  },
  components: {
    BodyOnlineRate: require('./components/body-online-rate.vue').default,
    BodyActivityRate: require('./components/body-activity-rate.vue').default,
    BodyClock: require('./components/body-clock.vue').default,
    BodyUpload: require('./components/body-upload.vue').default,
  },
};
</script>
<style lang="less" scoped></style>
