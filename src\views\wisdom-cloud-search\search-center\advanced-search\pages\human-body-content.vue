<!--
    * @FileDescription: 人体
    * @Author: H
    * @Date: 2023/5/15
 * @LastEditors: liyuhang1 <EMAIL>
 * @LastEditTime: 2024-10-12 15:32:52
 * 注释见./face-contents.vue
-->
<template>
  <div class="main-container">
    <div class="search-bar" v-show="!onlyResult">
      <search-human
        ref="searchBar"
        @search="searchHandle"
        @reset="resetHandle"
        @imgChange="imgChange"
      />
    </div>
    <div class="table-container">
      <!-- 没数据时不展示工具栏 -->
      <div
        class="data-export"
        v-if="
          queryParam.algorithmSelect &&
          queryParam.algorithmSelect.length < 2 &&
          dataList.length
        "
      >
        <Checkbox
          v-show="!onlyResult"
          @on-change="checkAllHandler"
          v-model="checkAll"
          >全选</Checkbox
        >
        <div class="export-box" v-if="!mapOnData">
          <Button
            v-show="!onlyResult"
            class="mr"
            @click.stop="exportShow = true"
            size="small"
          >
            <ui-icon type="daoru" color="#2C86F8"></ui-icon>
            导出
          </Button>
          <exportBox
            ref="exportbox"
            v-if="exportShow"
            @confirm="confirm"
            @cancel="hideExportModal"
          ></exportBox>
        </div>
        <Button
          class="mr"
          :type="queryParam.sortField == 'similarity' ? 'primary' : 'default'"
          @click="handleAdvancedSort('similarity')"
          size="small"
          v-if="queryParam.features && queryParam.features.length > 0"
        >
          <Icon type="md-arrow-round-down" v-if="!similUpDown" />
          <Icon type="md-arrow-round-up" v-else />
          相似度排序
        </Button>
        <Button
          class="mr"
          :type="queryParam.sortField == 'absTime' ? 'primary' : 'default'"
          @click="handleAdvancedSort('absTime')"
          size="small"
        >
          <Icon type="md-arrow-round-down" v-if="!timeUpDown" />
          <Icon type="md-arrow-round-up" v-else />
          时间排序
        </Button>
        <Button
          v-show="!onlyResult"
          @click="dataAboveMapHandler"
          size="small"
          style="float: right"
        >
          <ui-icon type="dongtai-shangtudaohang" color="#2C86F8"></ui-icon>
          数据上图
        </Button>
        <Button
          @click="handleFalls"
          size="small"
          style="float: right; margin-right: 10px"
        >
          {{ fallsPage ? "传统翻页版本" : "瀑布流版本" }}
        </Button>
        <slot name="mutilAction"></slot>
      </div>
      <div
        class="table-box"
        v-if="
          queryParam.algorithmSelect && queryParam.algorithmSelect.length == 2
        "
      >
        <geling
          class="table-box-geling"
          ref="geling"
          @collection="collection"
          @checkHandler="checkHandler"
          @archivesPage="archivesPage"
          @openDirectModel="openDirectModel"
          @targetAdd="handleTargetAdd"
          @dataCograph="dataCograph"
          @handleGaitPage="handleGaitPage"
          :mapOnData="mapOnData"
          :queryParam="queryParam"
        ></geling>
        <hk
          class="table-box-hk"
          ref="hk"
          @collection="collection"
          @checkHandler="checkHandler"
          @archivesPage="archivesPage"
          @openDirectModel="openDirectModel"
          @targetAdd="handleTargetAdd"
          @dataCograph="dataCograph"
          @handleGaitPage="handleGaitPage"
          :mapOnData="mapOnData"
          :queryParam="queryParam"
        ></hk>
      </div>
      <div class="table-container-box" v-else>
        <div class="table-content" @scroll="handleScroll">
          <div
            class="list-card box-1"
            v-for="(item, index) in dataList"
            :key="index"
            :class="{ checked: item.isChecked }"
          >
            <div class="collection paddingIcon">
              <div class="bg"></div>
              <ui-btn-tip
                class="collection-icon"
                v-if="item.myFavorite == '1'"
                content="取消收藏"
                icon="icon-yishoucang"
                transfer
                @click.native="collection(item, 2)"
              />
              <ui-btn-tip
                class="collection-icon"
                v-else
                content="收藏"
                icon="icon-shoucang"
                transfer
                @click.native="collection(item, 1)"
              />
            </div>
            <Checkbox
              v-show="!onlyResult"
              class="check-box"
              v-model="item.isChecked"
              @on-change="(e) => checkHandler(e, index)"
            ></Checkbox>
            <p class="img-content">
              <span
                class="num"
                :class="{
                  'gerling-num': queryParam.algorithmVendorType == 'GLST',
                }"
                v-if="item.similarity"
                >{{ item.similarity || "0" }}%</span
              >
              <ui-image
                :src="item.traitImg"
                alt="动态库"
                @click.native="handleDetail(item, index)"
              />
              <!-- <img :src="item.traitImg" alt="" v-viewer /> -->
            </p>
            <div class="bottom-info">
              <time>
                <Tooltip
                  content="抓拍时间"
                  placement="right"
                  transfer
                  theme="light"
                >
                  <i class="iconfont icon-time"></i>
                </Tooltip>
                {{ item.absTime }}
              </time>
              <p>
                <Tooltip
                  content="抓拍地点"
                  placement="right"
                  transfer
                  theme="light"
                >
                  <i class="iconfont icon-location"></i>
                </Tooltip>
                <span class="ellipsis" v-show-tips>{{ item.deviceName }}</span>
              </p>
            </div>
            <div v-show="!onlyResult" class="fast-operation-bar">
              <Poptip trigger="hover" placement="bottom-end">
                <i class="iconfont icon-gengduo"></i>
                <div class="mark-poptip" slot="content">
                  <p @click="archivesPage(item)">
                    <i class="iconfont icon-renti1"></i>以图搜图
                  </p>
                  <p @click="openDirectModel(item)">
                    <i class="iconfont icon-dongtai-shangtudaohang"></i>地图定位
                  </p>
                  <p @click="handleTargetAdd(item)">
                    <Icon type="ios-add-circle-outline" size="14" />搜索目标添加
                  </p>
                  <p @click="handleGaitPage(item)">
                    <i class="iconfont icon-a-lianhe322"></i>人体搜步态
                  </p>
                </div>
              </Poptip>
            </div>
          </div>
          <div
            class="empty-card-1"
            v-for="(item, index) of 9 - (dataList.length % 9)"
            :key="index + 'demo'"
          ></div>
          <div class="loading-box" v-if="fallsPage">
            <ui-loading v-if="scrollLoading"></ui-loading>
          </div>
          <div class="gobacktop" @click="toTop">
            <div class="top-icon">
              <Icon type="md-download" />
            </div>
            <div class="top-text">
              <p>回到</p>
              <p>顶部</p>
            </div>
          </div>
        </div>
        <ui-empty
          v-if="dataList.length === 0 && !listLoading && !fallsPage"
        ></ui-empty>
        <ui-loading v-if="listLoading"></ui-loading>
        <!-- 分页 -->
        <ui-page
          v-if="!fallsPage"
          :current="pageInfo.pageNumber"
          :total="total"
          countTotal
          :page-size="pageInfo.pageSize"
          :page-size-opts="[27, 54, 81, 108]"
          @pageChange="pageChange"
          @pageSizeChange="pageSizeChange"
        >
        </ui-page>
      </div>
    </div>
    <details-modal
      v-show="humanShow"
      ref="humanbody"
      @prePage="prePage"
      @nextPage="nextPage"
      @close="humanShow = false"
    >
    </details-modal>
    <hl-modal
      v-model="modalShow"
      title="提示"
      :r-width="500"
      @onCancel="modalStatus(false, true)"
    >
      <div class="content">
        <p class="tipLoad">数据打包中，请等候......</p>
        <p>大约尚需{{ maybeTime }}秒</p>
      </div>
    </hl-modal>
    <ui-modal
      v-model="warnModalShow"
      title="提示"
      :r-width="500"
      @onCancel="modalStatus(true, false)"
      @onOk="onOk"
    >
      <div class="content">
        <p>当前存在打包任务，请确认是否离开！</p>
      </div>
    </ui-modal>
    <direction-model ref="directionModel"></direction-model>
  </div>
</template>

<script>
import detailsModal from "@/components/detail/details-modal.vue";
import searchHuman from "../components/search-human.vue";
import {
  queryHumanRecordSearchEx,
  humanDownload,
  taskView,
  picturePick,
  getBase64ByImageCoordinateAPI,
} from "@/api/wisdom-cloud-search";
import { myMixins } from "../../components/mixin/index.js";
import { mapMutations, mapGetters, mapActions } from "vuex";
import { addCollection, deleteMyFavorite } from "@/api/user";
import exportBox from "../../components/export/export-box.vue";
import hlModal from "@/components/modal/index.vue";
import directionModel from "../components/direction-model";
import geling from "../../components/human/geling";
import hk from "../../components/human/hk";
import { throttle } from "lodash";
import { deepCopy } from "@/util/modules/common";
export default {
  name: "",
  mixins: [myMixins], //全局的mixin
  props: {
    mapOnData: {
      type: Boolean,
      default: false,
    },
  },
  components: {
    searchHuman,
    detailsModal,
    exportBox,
    hlModal,
    directionModel,
    geling,
    hk,
  },
  data() {
    return {
      dataList: [],
      listLoading: false,
      total: 0,
      pageInfo: {
        pageNumber: 1,
        pageSize: 27,
      },
      humanShow: false,
      queryParam: {
        sortField: "absTime",
        order: "desc",
      },
      checkAll: false, // 全选
      timeUpDown: false, // 时间排序
      similUpDown: false, // 相似度排序
      exportShow: false, // 导出弹框
      modalShow: false, // 导出loading弹框
      warnModalShow: false, // 中断导出提示框
      downTaskId: "", // 下载任务
      loadIntervel: null,
      timeInterval: null,
      maybeTime: 0, // 预计时间
      scrollLoading: false, // 瀑布流请求列表loading
      mayRequest: false, // 无限加载是否可以继续请求
      fallsPage: false, // 是否是瀑布流
    };
  },
  activated() {
    if (this.mapOnData) {
      // 轨迹搜索上图返回加载数据
      this.selectionList = [];
      this.queryList();
    }
  },
  computed: {
    ...mapGetters({
      getMaxLayer: "countCoverage/getMaxLayer",
      getNum: "countCoverage/getNum",
      getNewAddLayer: "countCoverage/getNewAddLayer",
      getListNum: "countCoverage/getListNum",
      upImageData: "map/getUpImageData",
    }),
    // 已上图数据
    alreadyUpImageIds() {
      return this.upImageData.map((e) => e.recordId);
    },
    // 只展示搜索结果，不能全选 | 导出 | 数据上图 | 选中卡片 | 卡片操作
    onlyResult() {
      return this.$route.query.noSearch;
    },
  },
  async mounted() {
    await this.getDictData();
    window.addEventListener("click", this.hideExportModal);
  },
  destroyed() {
    clearInterval(this.loadIntervel);
    clearInterval(this.timeInterval);
    window.removeEventListener("click", this.hideExportModal);
  },
  methods: {
    ...mapActions({
      getDictData: "dictionary/getDictAllData",
    }),

    /**
     * @description: 关闭导出框
     */
    hideExportModal() {
      this.exportShow = false;
    },

    /**
     * @description: 获取翻页列表
     * @param {number} page 标识，1 - 前一页，2 - 后一页
     */
    getDataList(page = 0) {
      this.dataList = [];
      this.listLoading = true;
      let params = {
        ...this.queryParam,
        ...this.pageInfo,
      };
      queryHumanRecordSearchEx(params)
        .then((res) => {
          this.total = res.data.total;
          this.dataList = res.data.entities || [];
          // 勾选
          if (this.dataList.length && this.upImageData.length) {
            this.dataList.forEach((e) => {
              if (this.alreadyUpImageIds.includes(e.recordId)) {
                e.isChecked = true;
              }
            });
          }
          if (page == 1) {
            this.$refs.humanbody.prePage(this.dataList);
          } else if (page == 2) {
            this.$refs.humanbody.nextPage(this.dataList);
          }
          this.logParams(
            params,
            {
              muen: "分类搜索",
              name: "查询分类搜索人体",
              type: "4",
            },
            this.queryParam.selectDeviceList
          );
        })
        .finally(() => {
          this.listLoading = false;
        });
    },
    /**
     * @description: 获取瀑布流列表
     */
    cardScroll() {
      this.mayRequest = false;
      let params = {
        ...this.queryParam,
        ...this.pageInfo,
      };
      queryHumanRecordSearchEx(params).then((res) => {
        if (res.data.entities.length < this.pageInfo.pageSize) {
          this.mayRequest = false;
          this.scrollLoading = false;
        } else {
          this.mayRequest = true;
          // 勾选
          if (res.data.entities.length && this.upImageData.length) {
            res.data.entities.forEach((e) => {
              if (this.alreadyUpImageIds.includes(e.recordId)) {
                e.isChecked = true;
              }
            });
          }
          this.logParams(
            params,
            {
              muen: "分类搜索",
              name: "查询分类搜索人体",
              type: "4",
            },
            this.queryParam.selectDeviceList
          );
          // 请求4页的数据，展示更多
          // 当请求回来的数量小于pageSize，表示没有更多数据了，不需要再发请求了
          if (
            this.pageInfo.pageNumber <= 3 &&
            res.data.entities.length >= this.pageInfo.pageSize
          ) {
            this.pageInfo.pageNumber += 1;
            this.queryList(0, false);
          } else {
            this.scrollLoading = false;
          }
        }
        this.dataList.push(...res.data.entities);
      });
    },

    /**
     * @description: 瀑布 - 翻页切换
     */
    handleFalls() {
      this.fallsPage = !this.fallsPage;
      this.dataList = [];
      if (this.fallsPage) {
        this.pageInfo = {
          pageNumber: 1,
          pageSize: 48,
        };
        this.scrollLoading = true;
        this.queryList(0, false);
      } else {
        this.mayRequest = false;
        this.pageInfo = {
          pageNumber: 1,
          pageSize: 27,
        };
        this.queryList();
      }
    },
    toTop() {
      let scrollTop = document.querySelector(".table-content");
      scrollTop.scrollTo(0, 0);
    },
    handleScroll: throttle(function () {
      if (!this.mayRequest) return; // 避免在未加载完一直滚动发起多余的请求
      // 距离顶部距离
      let scrollTop = document.querySelector(".table-content").scrollTop;
      // 可视区的高度
      let windowHelght = document.querySelector(".table-content").clientHeight;
      // 滚动条的总高度
      let scrollHeight = document.querySelector(".table-content").scrollHeight;
      // 计算底部距离
      let _dis = scrollTop + windowHelght;
      if (_dis + 1000 > scrollHeight && this.mayRequest) {
        this.pageInfo.pageNumber += 1;
        this.queryList(0, false);
      }
    }, 200),

    /**
     * @description: 获取列表
     * @param {number} page 翻页，1 - 前一页，2 - 后一页
     * @param {boolean} noRest 排序
     */
    queryList(page = 0, noRest = false) {
      this.checkAll = false;
      // 查询参数处理
      this.queryParam = {
        sortField: this.queryParam.sortField,
        order: this.queryParam.order,
        ...this.$refs.searchBar.getQueryParams(), // 这个必须在后边，为了让新参数覆盖旧的参数
      };
      this.queryParam.similarity = this.queryParam.similarity / 100;
      if (this.compareTime()) {
        return;
      }
      if (this.queryParam.algorithmSelect.length == 2) {
        this.$nextTick(() => {
          if (!noRest) {
            this.queryParam.order = "desc";
            this.queryParam.sortField = "similarity";
          }
          this.$refs.geling.init();
          this.$refs.hk.init();
        });
      } else {
        if (this.queryParam.features.length > 0 && !noRest) {
          this.queryParam.order = "desc";
          this.queryParam.sortField = "similarity";
        }
        if (this.fallsPage) {
          this.cardScroll();
        } else {
          this.getDataList(page);
        }
      }
    },
    /**
     * @description: 导出提示框
     * @param {boolean} modalShow 导出loading弹框
     * @param {boolean} warnModalShow 中断导出提示框
     */
    modalStatus(modalShow, warnModalShow) {
      this.modalShow = modalShow;
      this.warnModalShow = warnModalShow;
    },

    /**
     * @description: 中断下载
     */
    onOk() {
      this.modalStatus(false, false);
      clearInterval(this.loadIntervel);
      clearInterval(this.timeInterval);
    },
    downdata() {
      this.loadIntervel = setInterval(() => {
        taskView(this.downTaskId)
          .then((res) => {
            if (res.data) {
              this.downStatus = res.data.status;
              if (res.data.status != 0) {
                clearInterval(this.timeInterval);
              }
              if (res.data.status == 1) {
                let filePath = res.data.path;
                let urllength = filePath.split("/");
                let filename = urllength[urllength.length - 1];
                let flieType = filename.indexOf("zip") > 0 ? "zip" : "xlsx";
                let url = "http://" + document.location.host;
                Toolkits.ocxUpDownHttp(
                  "lis",
                  `${flieType}`,
                  `${url}${filePath}`,
                  `${filename}`
                );
                this.onOk();
              } else if (res.data.status == 2) {
                this.onOk();
                this.$Message.warning("打包失败当前任务结束！");
              }
            } else {
              this.onOk();
            }
          })
          .catch(() => {});
      }, 2000);
    },
    confirm(param) {
      let params = {
        ids: [],
        downloadPics: param.downloadPics,
        downloadSize: null,
        ...this.queryParam,
      };
      // type: 1导出选中数据
      if (param.type == "1") {
        this.dataList.forEach((e) => {
          e.isChecked && params.ids.push(e.recordId);
        });
        if (!params.ids.length) {
          this.$Message.warning("请选择需要导出的数据！");
          return;
        }
      } else {
        params.downloadSize = param.downloadSize;
      }
      this.hideExportModal();
      this.modalShow = true;

      humanDownload(params)
        .then((res) => {
          this.downTaskId = res.data.taskId;
          this.maybeTime = res.data.maybeTime;
          this.timeInterval = setInterval(() => {
            if (this.maybeTime == 0) {
              clearInterval(this.timeInterval);
            } else {
              this.maybeTime -= 1;
            }
          }, 1000);
          this.downdata();
        })
        .finally(() => {});
    },
    searchHandle() {
      this.pageInfo.pageNumber = 1;
      this.dataList = []; // 防止瀑布流模式下一直搜索一直添加
      this.queryList();
    },
    // 更新搜索数据
    imgChange() {
      let queryParam = this.$refs.searchBar.queryParam;
      this.queryParam.features = queryParam.features;
    },
    resetHandle() {
      // 目前不做排序的重置，后期产品有需要再说
      this.queryParam = {
        order: this.queryParam.order,
        sortField: this.queryParam.sortField,
      };
      this.pageInfo.pageNumber = 1;
      this.dataList = []; // 防止瀑布流模式下一直搜索一直添加
      this.queryList();
    },
    // 详情
    handleDetail(row, index) {
      this.humanShow = true;
      this.$refs.humanbody.init(
        row,
        this.dataList,
        index,
        1,
        this.pageInfo.pageNumber
      );
    },
    collection(item, flag) {
      var param = {
        favoriteObjectId: item.recordId,
        favoriteObjectType: 16,
      };
      if (flag == 1) {
        addCollection(param).then((res) => {
          this.$set(item, "myFavorite", "1");
          this.$Message.success("收藏成功");
        });
      } else {
        deleteMyFavorite(param).then((res) => {
          this.$set(item, "myFavorite", "2");
          this.$Message.success("取消收藏成功");
        });
      }
    },
    checkAllHandler(val) {
      this.dataList = this.dataList.map((e) => {
        return {
          ...e,
          isChecked: val,
        };
      });
    },
    checkHandler(e, i) {
      this.dataList[i].isChecked = e;
      this.checkAll =
        this.dataList.filter((e) => e.isChecked).length === this.dataList.length
          ? true
          : false;
    },
    ...mapMutations({
      setNum: "countCoverage/setNum",
      setList: "countCoverage/setList",
    }),
    archivesPage(row) {
      const { href } = this.$router.resolve({
        path: "/wisdom-cloud-search/search-center?sectionName=humanBodyContent&noMenu=1",
        query: {
          imgUrl: row.traitImg,
        },
      });
      window.open(href, "_blank");
    },
    pageChange(size) {
      this.pageInfo.pageNumber = size;
      this.queryList(0, true);
    },
    pageSizeChange(size) {
      this.pageInfo.pageNumber = 1;
      this.pageInfo.pageSize = size;
      this.queryList(0, true);
    },
    /**
     * 上一个
     */
    prePage(pageNum) {
      if (pageNum < 1) {
        this.$Message.warning("已经是第一个了");
        return;
      } else {
        this.pageInfo.pageNumber = pageNum;
        if (!this.fallsPage) {
          this.queryList(1, true);
        }
      }
    },
    /**
     * 下一个
     */
    nextPage(pageNum) {
      let num = pageNum - 1; // 这个时候的pageNum已经为下一页的页码了
      let size = this.pageInfo.pageSize;
      if (this.total <= num * size) {
        this.$Message.warning("已经是最后一个了");
      } else {
        if (!this.fallsPage) {
          this.pageInfo.pageNumber = pageNum;
          this.queryList(2, true);
        }
      }
    },
    openDirectModel(row) {
      const { geoPoint, deviceName } = { ...row };
      this.$refs.directionModel.show({ geoPoint, deviceName });
    },
    // 目标添加
    handleTargetAdd(row) {
      let fileData = new FormData();
      fileData.append("algorithmType", 3);
      fileData.append("fileUrl", row.traitImg);
      picturePick(fileData).then(async (res) => {
        if (res.data.length == 0) {
          this.$Message.warning("未识别出人体！");
        } else {
          const response = await this.getBase64ByImageCoordinate(res.data[0]);
          let urlList = {
            fileUrl: "data:image/jpeg;base64," + response.data.imageBase,
            feature: response.data.feature,
            imageBase: response.data.imageBase,
          };

          this.$refs.searchBar.urlImgList([urlList, ""], 2);
        }
      });
    },
    dataCograph(list) {
      this.pointCograph(list);
    },
    dataAboveMapHandler() {
      this.pointCograph(this.dataList);
    },
    pointCograph(dataList) {
      // 已上图的不需要再次上图
      let seleNum = dataList.filter(
        (e) => e.isChecked && !this.alreadyUpImageIds.includes(e.recordId)
      );
      // 取消勾选上图的
      let deletNum = [];
      this.alreadyUpImageIds.forEach((e) => {
        dataList.forEach((item) => {
          if (item.recordId === e && !item.isChecked) {
            deletNum.push(item);
          }
        });
      });
      let isNum = dataList.filter((e) => e.isChecked);
      if (isNum.length === 0) {
        this.$Message.warning("请勾选上图数据");
        return;
      }
      // 合并需要上图的和删除上图的
      seleNum = seleNum.concat(deletNum);
      if (!seleNum.length) {
        this.$Message.warning(
          "请勾选新的上勾选的数据已上图，请勾选新的数据图数据"
        );
        return;
      }
      // 判断是否有坐标信息
      let listHasLocation = seleNum.filter((item) => {
        return item.geoPoint && item.geoPoint.lat && item.geoPoint.lon;
      });
      if (!listHasLocation.length) {
        this.$Message.warning("无经纬度信息，无法上图");
        return;
      }
      if (listHasLocation.length < seleNum.length) {
        this.$Message.warning(
          `已过滤${seleNum.length - listHasLocation.length}条无经纬度信息的数据`
        );
      }
      let newNumLayer = this.getNum.layerNum + this.getNewAddLayer.layer + 1; //图层
      let newNumPoints =
        this.getNum.pointsNum +
        this.getNewAddLayer.pointsInLayer +
        listHasLocation.length; //点位
      if (Number(this.getMaxLayer.maxNumberOfLayer) < newNumLayer) {
        this.$Message.warning("已达到图层最大创建数量");
        return;
      }
      if (Number(this.getMaxLayer.maxNumberOfPointsInLayer) < newNumPoints) {
        this.$Message.warning("已达到上图最大点位总量");
        return;
      }
      let num = this.getListNum;
      this.setList(num++);
      this.setNum({ layerNum: newNumLayer, pointsNum: newNumPoints });
      listHasLocation.map((item) => {
        item.delePoints = true;
        item.deleType = "humanbody";
      });
      this.$emit("dataAboveMapHandler", {
        type: "humanbody",
        list: listHasLocation,
        deleIdent: "humanbody-" + this.getListNum,
      });
    },
    // 根据图片坐标截取图片base64
    getBase64ByImageCoordinate(data) {
      const params = { ...data, type: "human" };
      return getBase64ByImageCoordinateAPI(params);
    },
    // 人体搜步态
    handleGaitPage(row) {
      const { href } = this.$router.resolve({
        path: "/wisdom-cloud-search/search-center?sectionName=gaitContent&noMenu=1",
        query: {
          algorithmType: 6,
          imgUrl: row.traitImg,
        },
      });
      window.open(href, "_blank");
    },
    // 获取选择的数据
    getSelectDataList() {
      const selectList = this.dataList.filter((e) => e.isChecked);
      return deepCopy(selectList);
    },
  },
};
</script>

<style lang="less" scoped>
@import "style/index";
</style>
