<template>
  <ui-modal v-model="visible" title="设备状态检测配置" width="35%" @query="handleSubmit">
    <div class="container">
      <p class="mb-md base-text-color">
        <span class="mr-xs">近</span>
        <InputNumber class="width-sm" v-model="extraParam.day" placeholder="请输入天数"></InputNumber>
        <span class="ml-xs">天</span>
      </p>
      <p class="mb-md base-text-color">有抓拍车辆，设备为可用状态；</p>
      <p class="mb-md base-text-color">有抓拍人脸，设备为可用状态；</p>
      <p class="mb-md base-text-color">有成功调阅实视频流记录，设备为可用状态。</p>
    </div>
  </ui-modal>
</template>
<script>
import governanceevaluation from '@/config/api/governanceevaluation';
export default {
  name: 'device-status',
  components: {},
  props: {},
  data() {
    return {
      indexRuleId: '',
      taskSchemeId: '',
      visible: false,
      extraParam: {
        day: null,
      },
      indexConfig: {},
    };
  },
  computed: {},
  watch: {},
  filter: {},
  mounted() {},
  methods: {
    async handleSubmit() {
      try {
        let { indexRuleId, taskSchemeId, indexType } = this.indexConfig;
        let params = {
          ruleId: indexRuleId,
          taskSchemeId,
          indexType,
          extraParam: JSON.stringify(this.extraParam),
        };
        let { data } = await this.$http.post(governanceevaluation.editIndexRulePropertyList, params);
        this.$Message.success(data.msg);
        this.visible = false;
      } catch (e) {
        console.log(e);
      }
    },
    init(processOptions) {
      this.indexConfig = JSON.parse(JSON.stringify(processOptions));
      this.visible = true;
      this.getDeviceStatus();
    },
    async getDeviceStatus() {
      try {
        let { indexRuleId, taskSchemeId, indexType } = this.indexConfig;
        let params = {
          ruleId: indexRuleId,
          taskSchemeId,
          indexType,
        };
        let {
          data: { data },
        } = await this.$http.get(governanceevaluation.inquireIndexRulePropertyList, { params });
        if (data.rulePropertyList) {
          let { day } = JSON.parse(data.rulePropertyList[0].extraParam || '{}');
          this.extraParam.day = day || null;
        }
      } catch (e) {
        console.log(e);
      }
    },
  },
};
</script>

<style lang="less" scoped>
.color-primary {
  color: var(--color-primary);
}
</style>
