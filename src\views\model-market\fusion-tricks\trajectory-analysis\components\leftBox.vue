<!--
    * @FileDescription: 轨迹分析 搜索、 对象信息
    * @Author: H
    * @Date: 2022/12/20
    * @LastEditors: 
    * @LastEditTime: 
 -->
<template>
  <div class="leftBox">
    <searchBox
      @tabClik="tabClik"
      :title="searchType"
      :selectTabIndex="selectTabIndex"
      modalType="trajectory"
      @searchList="querySearch"
      @packBox="handlePackup"
    ></searchBox>
    <div
      class="object-information"
      v-if="showlist"
      :style="{ height: `calc( 100% - ${top + 10}px )` }"
    >
      <div class="title">
        <p>{{ title }}</p>
        <Icon type="ios-close" @click="handleCancel" />
      </div>
      <div class="box-modal">
        <ul
          class="box-content"
          :class="{ 'overflow-box': objectMsgList.length > 3 }"
          v-infinite-scroll="load"
          infinite-scroll-distance="1"
        >
          <li
            class="box-list"
            :class="{ 'active-box-list': objectIndex == index }"
            v-for="(item, index) in objectMsgList"
            :key="index"
          >
            <div class="content-top">
              <div class="content-top-img">
                <template v-if="item.photos && item.photos.length > 0">
                  <img-list :dataObj="item" :index="index"></img-list>
                </template>
                <ui-image
                  v-else
                  viewer
                  :src="typeIndex.tab == 0 ? item.photo : item.photoUrl"
                />
                <div v-if="item.score" class="similarity">
                  <span>{{ getScore(item.score) }}%</span>
                </div>
              </div>
              <div class="content-top-right">
                <span class="ellipsis">
                  <ui-icon
                    :type="listIcon[typeIndex.tab][0]"
                    :size="14"
                  ></ui-icon>
                  <span class="block">{{
                    item[field[typeIndex.tab][0]] || "--"
                  }}</span>
                </span>
                <span class="ellipsis">
                  <ui-icon
                    :type="listIcon[typeIndex.tab][1]"
                    :size="14"
                  ></ui-icon>
                  <span
                    class="bule"
                    :class="{ block: !item[field[typeIndex.tab][1]] }"
                    >{{ item[field[typeIndex.tab][1]] || "--" }}</span
                  >
                </span>
                <span
                  class="ellipsis"
                  v-if="typeIndex.tab == 0 && typeIndex.secTab == 1"
                >
                  <ui-icon
                    :type="listIcon[typeIndex.tab][2]"
                    :size="14"
                  ></ui-icon>
                  <span
                    class="orange"
                    :class="{ block: !item[field[typeIndex.tab][2]] }"
                    >{{ item[field[typeIndex.tab][2]] || "--" }}</span
                  >
                </span>
              </div>
            </div>
            <div class="content-bottom">
              <div class="iconList">
                <!-- <opera-floor iconSec="icon-dangan2"></opera-floor> -->
              </div>
              <div class="operation">
                <!-- <div class="analyseIcon" @click="handleTrack($event, item, index)">
                                    <i class="iconfont icon-xunjianguiji"></i>
                                    <span>出行轨迹</span>
                                </div> -->
                <div
                  class="analyseIcon"
                  :class="{ activeAnaly: objectIndex === index }"
                  @click="handleObject($event, item, index)"
                >
                  <i class="iconfont icon-wangguan"></i>
                  <span>关联对象</span>
                </div>
              </div>
            </div>
          </li>
          <ui-empty
            v-if="objectMsgList.length === 0 && loading == false"
          ></ui-empty>
          <ui-loading v-if="loading"></ui-loading>
          <p class="loading" v-if="loadingText">加载中...</p>
          <p class="endlist" v-if="noMore && objectMsgList.length !== 0">
            没有更多了
          </p>
        </ul>
        <!-- 关联对象 -->
        <association
          ref="association"
          v-if="objModal"
          :marginTop="removableTop"
          @viewTrack="handleViewTrack"
          @cancel="objModal = false"
        ></association>
      </div>
    </div>
  </div>
</template>

<script>
import { personTargetPageList, queryVehicleList } from "@/api/modelMarket";
import operaFloor from "../../../components/operat-floor/index.vue";
import imgList from "../../components/imgList/index.vue";
import association from "./association-object.vue";
import searchBox from "../../components/search/index.vue";
export default {
  name: "",
  props: {
    title: {
      type: String,
      default: "对象信息",
    },
    // 默认选中的类型
    selectTabIndex: {
      type: Number,
      default: -1,
    },
  },
  components: {
    operaFloor,
    imgList,
    association,
    searchBox,
  },
  data() {
    return {
      searchType: "轨迹分析",
      objectMsgList: [],
      removableTop: -40,
      objModal: false,
      loading: false,
      detailRequest: {
        0: personTargetPageList, // 人脸
        1: queryVehicleList, // 车辆
      },
      listIcon: {
        0: ["xingming", "shenfenzheng", "camera"],
        1: ["chepai", "xingming", "shenfenzheng"],
      },
      field: {
        0: ["xm", "gmsfhm", "archiveNo"],
        1: ["plateNo", "ownerName", "idcardNo"],
      },
      page: {
        pageNumber: 1,
        pageSize: 10,
      },
      typeIndex: {
        tab: 0,
        secTab: 0,
      },
      objectIndex: -1,
      peerObj: {},
      loadingText: false,
      noMore: false,
      total: 0,
      searchInfo: {},
      typaTag: {},
      showlist: false,
      top: 0,
    };
  },
  watch: {},
  computed: {
    // top() {
    //     return document.querySelector('.search_box').scrollHeight + 20
    // },
    scrollHeight() {
      let htmlFontSize = parseFloat(
        window.document.documentElement.style.fontSize
      );
      if (!!htmlFontSize) {
        return htmlFontSize * (450 / 192);
      }
      return 450;
    },
  },
  created() {},
  mounted() {},
  methods: {
    tabClik() {
      this.showlist = false;
    },
    // 高度
    objHeight() {
      setTimeout(() => {
        this.top = document.querySelector(".search_box").scrollHeight + 20;
      }, 210);
    },
    //
    handlePackup() {
      this.objHeight();
    },
    /**
     * tab:0: 人员， 1：车辆， 2：RFID 3：WI-Fi 4: 电围
     * secTab 0：身份证号， 1：人脸照片
     */
    querySearch(item, tab, secTab) {
      this.showlist = true;
      this.$emit("reset");
      this.objHeight();
      this.typaTag = { tab, secTab };
      this.init(item, this.typaTag);
    },
    init(item, tabIndex, isFirst = true) {
      if (isFirst) {
        this.objectMsgList = [];
        this.loading = true;
        this.page.pageNumber = 1;
      } else {
        this.loadingText = true;
      }
      this.objModal = false;
      this.noMore = false;
      this.typeIndex = tabIndex; //用于判断tab类型
      let params = {};
      this.searchInfo = item;
      if (this.typeIndex.tab == 0) {
        this.typeIndex.secTab == 0
          ? (params = {
              searchContent: item.searchContent,
              startDate: item.startDate,
              endDate: item.endDate,
              dateType: item.dateType,
            })
          : (params = {
              features: item.features,
              similarity: item.similarity / 100,
            });
      } else {
        params = {
          plateNo: item.plateNo,
        };
      }
      this.detailRequest[this.typeIndex.tab]({ ...params, ...this.page })
        .then((res) => {
          let list = (res.data && res.data.entities) || [];
          this.objectMsgList = this.objectMsgList.concat(...list);
          this.total = res.data.total;
        })
        .finally(() => {
          this.loading = false;
          this.loadingText = false;
          this.noMore = false;
        });
    },
    // 关闭
    handleCancel() {
      this.showlist = false;
      this.$emit("reset");
    },
    // 出行轨迹
    handleTrack(e, item, index) {
      console.log(item, index, "item, index");
    },
    handleObject(event, item, index) {
      this.objectIndex = index;
      this.objModal = true;
      this.peerObj = item;
      this.$nextTick(() => {
        this.$refs.association.init(item, this.typeIndex);
      });
      this.$emit("cutAnalyse");
    },
    load() {
      let totalPage = Math.ceil(this.total / this.page.pageSize);
      if (this.total <= 10) {
        return;
      }
      if (this.page.pageNumber >= totalPage) {
        this.noMore = true;
        return;
      } else {
        this.noMore = false;
        this.page.pageNumber = this.page.pageNumber + 1;
        this.init(this.searchInfo, this.typeIndex, false);
      }
    },
    // 查看轨迹
    handleViewTrack(val) {
      if (this.typeIndex.tab == 0) {
        //人员
        if (this.typeIndex.secTab == 0) {
          //身份证
          this.$emit(
            "searchTrack",
            {
              ...val,
              archiveNo: this.peerObj.archiveNo,
            },
            this.peerObj,
            this.typaTag,
            this.searchInfo
          );
        } else {
          this.$emit(
            "searchTrack",
            {
              //人脸
              ...val,
              vid: this.peerObj.archiveNo,
            },
            this.peerObj,
            this.typaTag,
            this.searchInfo
          );
        }
      } else if (this.typeIndex.tab == 1) {
        //车辆
        this.$emit(
          "searchTrack",
          {
            ...val,
            plateNo: this.peerObj.plateNo,
          },
          this.peerObj,
          this.typaTag,
          this.searchInfo
        );
      }
      this.objModal = false;
    },
    getScore(val) {
      let a = (val * 100).toFixed(2);
      return a;
    },
  },
};
</script>

<style lang='less' scoped>
@import "../../components/style/index";
@import "../../components/style/boxContent";
.leftBox {
  position: absolute;
  top: 10px;
  left: 10px;
  height: calc(~"100% - 20px");
}
</style>
