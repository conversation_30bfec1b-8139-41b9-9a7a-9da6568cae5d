<template>
	<div class="search  card-border-color">
		<Form :inline="true">
			<div class="general-search">
				<div class="input-content">
					<div class="input-content-row">
						<FormItem label="主机名称:">
							<Input v-model="formData.serverName" placeholder="请输入"></Input>
						</FormItem>
						<FormItem label="主机IP:">
							<Input v-model="formData.ip" placeholder="请输入"></Input>
						</FormItem>
						<FormItem label="运行状态:">
							<div class="app-state" :class="curIndex==index?'active':''" @click="stateChange(item.value,index)" v-for="(item,index) in stateList" :key="item.name">{{ item.name }}</div>
						</FormItem>
						<div class="btn-group">
							<Button type="primary" @click="search">查询</Button>
							<Button type="default" @click="resetForm">重置</Button>
						</div>
					</div>

				</div>

			</div>
		</Form>
	</div>
</template>
<script>

	export default {
		components: {},
		data() {
			return {
				formData: {
					serverName: '',
					ip: '',
					status: '',
				},
				stateList: [
					{ name: '全部', value: '' },
					{ name: '在线', value: 'true' },
					{ name: '离线', value: 'false' },
				],
				curIndex: 0
			}
		},
		created() {

		},
		methods: {
			search() {
				let searchForm = {
					serverName: this.formData.serverName,
					ip: this.formData.ip,
					status: this.formData.status,
				};
				this.$emit('searchInfo', searchForm)
			},
			resetForm() {
				this.formData = {
					serverName: '',
					ip: '',
					status: '',
				};
				this.curIndex = 0
				this.$emit('searchInfo', this.formData)
			},
			stateChange(val, index) {
				this.formData.status = val
				this.curIndex = index
			}
		}
	}
</script>
<style lang="less" scoped>
	.app-state {
		padding: 0 10px;
		height: 24px;
		display: flex;
		align-items: center;
		margin-right: 5px;
		cursor: pointer;
	}
	.active {
		background-color: #2c86f8;
		color: #fff;
	}
	.btn-group {
		display: flex;
		align-items: flex-end;
		margin-bottom: 16px;
		justify-content: flex-end;
		flex: 1;
	}
	.search {
		border-bottom: 1px solid #ffff;
		display: flex;
		width: 100%;
		justify-content: space-between;
		position: relative;
		.ivu-form-inline {
			width: 100%;
		}
		.ivu-form-item {
			margin-bottom: 16px;
			margin-right: 30px;
			display: flex;
			align-items: center;
			/deep/ .ivu-form-item-label {
				white-space: nowrap;
				max-width: 90px;
			}
			/deep/ .ivu-form-item-content {
				display: flex;
			}
		}
		.general-search {
			display: flex;
			width: 100%;
			.input-content {
				width: 85%;
				display: flex;
				flex-wrap: wrap;
				.input-content-row {
					display: flex;
				}
			}
		}
	}
</style>
