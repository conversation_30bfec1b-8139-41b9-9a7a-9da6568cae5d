<template>
  <ul class="tag-view f-14">
    <li
      v-for="(item, index) in list"
      :class="{ active: curTag === index && !noActive, 'li-disabled': item.disabled, [size]: !!size }"
      :key="index"
      @click="tagChange(index, item)"
    >
      <slot :item="item" :index="index">
        {{ typeof item === 'object' ? item.label : item }}
      </slot>
    </li>
  </ul>
</template>
<script>
export default {
  name: 'tag-view',
  props: {
    list: {
      type: Array,
      default: () => {
        return [];
      },
    },
    noActive: {
      default: false,
    },
    size: {
      default: 'default',
    },
    defaultActive: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      curTag: 0,
    };
  },
  created() {},
  methods: {
    tagChange(index, item) {
      if (item.disabled) return;
      this.curTag = index;
      this.$emit('tagChange', index, item);
    },
    reset() {
      this.curTag = this.defaultActive;
    },
  },
  watch: {
    defaultActive: {
      handler(val) {
        this.curTag = val || 0;
      },
      immediate: true,
    },
  },
  components: {},
};
</script>
<style lang="less" scoped>
.tag-view {
  li {
    float: left;
    text-align: center;
    color: var(--color-switch-tag-tab);
    border: 1px solid var(--border-switch-tag-tab);
    margin-right: -1px;
    cursor: pointer;
    &:first-child {
      border-top-left-radius: 4px;
      border-bottom-left-radius: 4px;
      //border-right: none;
    }
    &:last-child {
      border-top-right-radius: 4px;
      border-bottom-right-radius: 4px;
    }
  }
  li:first-of-type:not(:last-of-type),
  li + li:not(:last-of-type) {
    border-right-color: transparent;
  }

  .default {
    padding: 6px 16px;
    height: 34px;
    display: flex;
    align-items: center;
  }
  .mini {
    padding: 0 10px;
    height: 24px;
  }
  .active {
    color: #ffffff;
    background: var(--color-switch-tab-active);
    border: 1px solid var(--color-switch-tab-active);
    cursor: default;
  }
  .li-disabled {
    cursor: no-drop;
  }
}
</style>
