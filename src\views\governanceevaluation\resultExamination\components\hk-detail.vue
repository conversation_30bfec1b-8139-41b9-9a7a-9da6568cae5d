<template>
  <ui-modal v-model="visible" :title="title" :styles="styles" footerHide class="hk-detail">
    <div class="content auto-fill">
      <!-- <div class="over-flow mb-sm">
        <div class="fl">
          <Input
            v-model="searchData.deviceName"
            class="width-md"
            placeholder="请输入设备名称"
          ></Input>
          <Button type="primary" class="ml-sm" @click="search"> 查询 </Button>
        </div>
        <div class="fr">
          <Button type="primary" @click="exportExcel" :loading="exportLoading">
            <i class="icon-font icon-daochu"></i>
            <span class="inline vt-middle ml-xs">导出</span>
          </Button>
        </div>
      </div> -->
      <ui-table
        class="ui-table auto-fill"
        ref="tableContent"
        :table-columns="tableColumns"
        :table-data="tableData"
        :loading="loading"
      >
        <template #EVALUATION_TIME="{ row }">
          <span v-if="systemConfig.distinguishVersion !== '4'">
            {{ row.EVALUATION_TIME }}
          </span>
          <Button type="text" v-else class="span-btn">
            {{ row.EVALUATION_TIME }}
          </Button>
        </template>
      </ui-table>
      <ui-page class="page" :page-data="pageData" @changePage="changePage" @changePageSize="changePageSize"> </ui-page>
    </div>
  </ui-modal>
</template>
<script>
import category from '@/config/api/catalogmanagement';
import governanceevaluation from '@/config/api/governanceevaluation';
export default {
  props: {
    title: {
      type: String,
    },
    value: {
      type: Boolean,
    },
    hkParmas: {},
  },
  data() {
    return {
      visible: false,
      loading: false,
      exportLoading: false,
      styles: {
        width: '1500px',
      },
      searchData: {
        deviceName: '',
        pageNumber: 1,
        pageSize: 20,
      },
      pageData: {
        pageNum: 1,
        pageSize: 20,
        totalCount: 0,
      },
      tableColumns: [
        {
          title: '设备名称',
          key: 'sbmc',
        },
        {
          title: '异常项说明',
          key: 'error_desc',
        },
        {
          title: '复核状态',
          key: 'statusText',
        },
      ],
      tableData: [],
    };
  },
  created() {},
  methods: {
    async exportExcel() {
      try {
        this.exportLoading = true;
        const res = await this.$http.get(category.downloadTemplate);
        await this.$util.common.transformBlob(res.data.data);
      } catch (err) {
        console.log(err);
      } finally {
        this.exportLoading = false;
      }
    },
    async search() {
      try {
        this.loading = true;
        const res = await this.$http.post(governanceevaluation.queryIndexInspectionRecordDetail, this.searchData);
        this.tableData = res.data.data.entities;
        this.pageData.totalCount = res.data.data.total;
      } catch (err) {
        console.log(err);
      } finally {
        this.loading = false;
      }
    },
    changePage(val) {
      this.searchData.pageNumber = val;
      this.search();
    },
    changePageSize(val) {
      this.searchData.pageNumber = 1;
      this.searchData.pageSize = val;
      this.search();
    },
  },
  watch: {
    value(val) {
      this.visible = val;
      if (val) {
        this.searchData.evaluationStatisticsId = this.hkParmas.row[this.hkParmas.column.key].evaluationStatisticsId;
        this.search();
      }
    },
    visible(val) {
      this.$emit('input', val);
    },
  },
  components: {
    UiTable: require('@/components/ui-table').default,
  },
};
</script>
<style lang="less" scoped>
.content {
  height: 500px;
}
</style>
