<template>
  <div class="detail-content auto-fill">
    <Form class="form" ref="form" autocomplete="off" label-position="right" :model="formData" :rules="formRules">
      <FormItem class="form-item" label="检测对象" required>
        <api-area-tree
          class="api-area-tree"
          :disabled="true"
          :select-tree="selectAreaTree"
          placeholder="请选择检测对象"
        ></api-area-tree>
      </FormItem>
      <FormItem class="form-item" label="检测方式" required>
        <Select class="width-lg" v-model="formData.detectMode" placeholder="请选择检测方式" disabled>
          <Option v-for="item in wayList" :key="item.value" :value="item.value">{{ item.label }}</Option>
        </Select>
      </FormItem>
      <FormItem class="form-item" label="检测计划" prop="cronNum" required>
        <TestPlan
          ref="testplan"
          :form-data="formData"
          :form-model="formModel"
          :index-type="moduleAction.indexType"
          @checkTime="checkTime"
        ></TestPlan>
      </FormItem>
      <!-- <FormItem
        class="form-item"
        label="人脸对比算法"
        required
        prop="algVendors"
      >
        <CheckboxGroup v-model="formData.algVendors">
          <Checkbox
            v-for="(item, index) in algorithm"
            :key="index"
            :label="item.algorithmVendorType"
            >{{ item.algorithmLable }}</Checkbox
          >
        </CheckboxGroup>
      </FormItem> -->
      <!-- <FormItem class="form-item" label="相似度阈值" required prop="threshold">
        <Input
          v-model="formData.threshold"
          class="width-lg"
          placeholder="请输入相似度阈值"
        />
        <span class="base-text-color ml-xs">%</span>
      </FormItem> -->

      <FormItem class="form-item" label="布控数据发送时间" required prop="childCronData">
        <span class="inline vt-middle base-text-color">每月</span>
        <Select class="width-mini ml-sm" v-model="startCronData" transfer>
          <Option v-for="(item, index) in planMonth" :key="index" :value="item.value">{{ item.label }}</Option>
        </Select>
        <Select class="width-mini ml-sm" v-model="startTimePoints" transfer>
          <Option v-for="(item, index) in schemeList" :key="index" :value="item.value">{{ item.label }}</Option>
        </Select>
        <span class="ml-sm mr-sm base-text-color">—</span>
        <Select class="width-mini" v-model="endCronData" transfer>
          <Option v-for="item in planMonth" :value="item.value" :key="item.value">{{ item.label }}</Option>
        </Select>
        <Select class="width-mini ml-sm" v-model="endTimePoints" transfer>
          <Option v-for="item in schemeList" :value="item.value" :key="item.value">{{ item.label }}</Option>
        </Select>
        <Tooltip transfer>
          <i class="icon-font icon-wenhao vt-middle icon-warning ml-sm"></i>
          <template slot="content"> 没有31号的月份，取当月最后一天 </template>
        </Tooltip>
      </FormItem>
      <FormItem class="form-item" label="布控策略" required prop="monitorCount">
        <RadioGroup v-model="deploymentStrategy">
          <Radio label="all">
            <span>全部布控</span>
          </Radio>
          <Radio label="randomlyIssued" class="ml-lg">
            <span>随机下发</span>
            <Input
              v-show="deploymentStrategy === 'randomlyIssued'"
              class="ml-sm width-mini"
              v-model="formData.monitorCount"
              placeholder="请输入"
            ></Input>
            <span v-show="deploymentStrategy === 'randomlyIssued'" class="ml-sm">个布控对象</span>
            <Tooltip transfer>
              <i class="icon-font icon-wenhao vt-middle icon-warning ml-sm"></i>
              <template slot="content"> 布控对象数量不够，则全部下发布控 </template>
            </Tooltip>
          </Radio>
        </RadioGroup>
      </FormItem>
    </Form>
    <ui-table class="ui-table auto-fill" :table-columns="tableInfoColumns" :table-data="formData.apis">
      <template #dataList="{ row, index }">
        <template v-if="row.dataList && row.dataList.length">
          <div class="img-box" @click="selectTestData(row, index)" title="点击更改数据">
            <div class="num">{{ row.dataList.length }}</div>
            <ui-image :src="row.dataList[0].url" />
          </div>
        </template>
        <div v-else>
          <Button type="text" @click="selectTestData(row, index)">请选择</Button>
        </div>
      </template>
      <template #action="{ row, index }">
        <Button type="text" :disabled="row.dataList && !row.dataList.length" @click="onClickClear(row, index)"
          >清除</Button
        >
      </template>
    </ui-table>
    <select-data
      ref="select-data"
      v-model="selectDataShow"
      :modal-action="modalAction"
      :search-params="searchTestData"
      :default-checked="defaultChecked"
      @query="selectedData"
      @reset="reset"
    >
      <template #searchModule>
        <RadioGroup class="picture-type" v-model="pictureType" @on-change="changePictureType">
          <Radio label="people">
            <span>实有人口库</span>
          </Radio>
          <Radio label="peoplePicture">
            <span>人员照片库</span>
          </Radio>
        </RadioGroup>
        <div class="inline ml-lg" v-show="pictureType === 'people'">
          <ui-label class="inline" label="姓名">
            <Input class="width-sm" v-model="searchTestData.name" placeholder="请输入姓名"></Input>
          </ui-label>
          <ui-label class="inline ml-lg" label="证件号">
            <Input class="width-sm" v-model="searchTestData.idCard" placeholder="请输入证件号"></Input>
          </ui-label>
          <ui-label class="inline ml-lg" label="所属地">
            <api-area-tree
              :select-tree="selectTree"
              @selectedTree="selectedArea"
              placeholder="请选择行政区划"
            ></api-area-tree>
          </ui-label>
        </div>
        <div class="inline ml-lg" v-show="pictureType === 'peoplePicture'">
          <ui-label class="inline" label="图片名称">
            <Input class="width-sm" v-model="searchTestData.name" placeholder="请输入图片名称"></Input>
          </ui-label>
        </div>
      </template>
      <template #message="{ item }">
        <div v-show="pictureType === 'people'">
          <p class="text">
            <i class="icon-font icon-ren"></i>
            <span class="inline vt-middle base-text-color ml-xs ellipsis" :title="item.name">{{
              item.name || '未知'
            }}</span>
          </p>
          <p class="text mt-xs">
            <i class="icon-font icon-shenfenzheng"></i>
            <span class="inline vt-middle base-text-color ml-xs ellipsis" :title="item.idCard">{{ item.idCard }}</span>
          </p>
          <p class="text mt-xs">
            <i class="icon-font icon-dizhi"></i>
            <span class="inline vt-middle base-text-color ml-xs">{{ item.civilName }}</span>
          </p>
        </div>
        <div v-show="pictureType === 'peoplePicture'">
          <p class="text">
            <span class="inline vt-middle base-text-color ml-xs">{{ item.name }}</span>
          </p>
        </div>
      </template>
    </select-data>
  </div>
</template>
<script>
import fieldData from '../../common-form/field';
import { mapActions, mapGetters } from 'vuex';
import governanceevaluation from '@/config/api/governanceevaluation';
export default {
  props: {
    // 配置回显详情
    configInfo: {
      required: true,
      type: Object,
    },
    //标识 add-新增/edit-编辑
    formModel: {
      required: true,
      type: String,
      default: 'add',
    },
    moduleAction: {
      type: Object,
      default: () => {},
    },
    // 所有配置详情
    taskIndexConfig: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    const validateChildCronData = (rule, value, callback) => {
      if (
        this.startCronData &&
        this.endCronData &&
        (this.startTimePoints || this.startTimePoints === 0) &&
        (this.endTimePoints || this.endTimePoints === 0)
      ) {
        callback();
      } else {
        callback(new Error('请选择布控数据发送时间'));
      }
    };
    const validateCronNum = (rule, value, callback) => {
      if (this.formData.cronType === '1' && this.formData.cronData.length) {
        callback();
      } else if (this.formData.cronData.length && this.formData.timePoints.length) {
        callback();
      } else {
        callback(new Error('请选择检测计划'));
      }
    };
    const validateMonitorCount = (rule, value, callback) => {
      if (this.deploymentStrategy === 'all' || !!this.formData.monitorCount) {
        callback();
      } else {
        callback(new Error('请填写下发次数'));
      }
    };
    return {
      selectAreaTree: {
        regionCode: '',
      },
      algorithm: [],
      planMonth: [],
      schemeList: [],
      formData: {
        detectMode: '3',
        cronData: [],
        timePoints: [],
        cronType: '',
        // algVendors: [],
        // threshold: '',
        startCronData: [],
        endCronData: [],
        startTimePoints: [],
        endTimePoints: [],
        monitorCount: 0,
        apis: [],
      },
      startCronData: '',
      endCronData: '',
      startTimePoints: '',
      endTimePoints: '',
      deploymentStrategy: 'all',
      tableInfoColumns: [
        { type: 'selection', width: 50, fixed: 'left', align: 'center' },
        {
          type: 'index',
          title: '序号',
          width: 50,
          align: 'center',
        },
        {
          title: '组织机构',
          key: 'orgName',
          align: 'center',
        },
        {
          title: '布控对象',
          slot: 'dataList',
          align: 'center',
        },
        {
          title: '操作',
          width: 80,
          align: 'center',
          slot: 'action',
        },
      ],
      formRules: {
        cronNum: [
          {
            validator: validateCronNum,
          },
        ],
        algVendors: [
          {
            required: true,
            type: 'array',
            message: '请选择人脸对比算法',
          },
        ],
        threshold: [
          {
            required: true,
            message: '请填写相似度阈值',
            trigger: 'blur',
          },
        ],
        apis: [
          {
            required: true,
            type: 'array',
            message: '请选择检测接口',
          },
        ],
        childCronData: [
          {
            validator: validateChildCronData,
          },
        ],
        monitorCount: [
          {
            validator: validateMonitorCount,
          },
        ],
      },
      selectDataShow: false,
      modalAction: {
        title: '选择实有人口',
        action: 'add',
      },
      pictureType: 'people',
      selectTree: {
        regionCode: '',
      },
      searchTestData: {
        name: '',
        idCard: '',
        type: 1,
        civilCode: '',
        pageNumber: 1,
        pageSize: 20,
      },
      defaultChecked: [],
      ...fieldData.data(),
    };
  },
  created() {
    // this.governanceevaluation()
  },
  mounted() {
    this.planMonth = this.$refs.testplan.planMonth;
    this.schemeList = this.$refs.testplan.schemeList;
  },
  methods: {
    ...mapActions({
      getAlldicData: 'algorithm/getAlldicData',
    }),
    async handleSubmit() {
      return this.$refs.form.validate(async (valid) => {
        if (valid) {
          this.formData.startCronData = [this.startCronData];
          this.formData.endCronData = [this.endCronData];
          this.formData.startTimePoints = [this.startTimePoints];
          this.formData.endTimePoints = [this.endTimePoints];
          return true;
        } else {
          this.$Message.error('请填写必须要填写的信息');
          return false;
        }
      });
    },
    onClickClear(row, index) {
      this.selectDataIndex = index;
      this.selectedData([]);
    },
    selectTestData(row, index) {
      this.selectDataIndex = index;
      this.defaultChecked = row.dataList ? row.dataList.map((row) => row.id) : [];
      const org = this.initialOrgList.find((rw) => rw.orgCode === row.orgCode);
      this.selectTree.regionCode = org.regionCode;
      this.searchTestData.civilCode = org.regionCode;
      this.$nextTick(() => {
        this.selectDataShow = true;
      });
    },
    selectedArea(area) {
      this.searchTestData.civilCode = area.regionCode;
    },
    selectedData(data) {
      const api = this.$util.common.deepCopy(this.formData.apis[this.selectDataIndex]);
      api.dataList = data.map((row) => {
        return {
          id: row.id,
          url: row.url,
        };
      });
      this.formData.apis.splice(this.selectDataIndex, 1, api);
    },
    reset() {
      this.selectTree.regionCode = '';
      if (this.pictureType === 'people') {
        this.searchTestData = {
          name: '',
          idCard: '',
          type: 1,
          civilCode: '',
          pageNumber: 1,
          pageSize: 20,
        };
      } else {
        this.searchTestData = {
          name: '',
          type: 2,
          pageNumber: 1,
          pageSize: 20,
        };
      }
    },
    // 检测时间
    checkTime(val) {
      Object.assign(this.formData, val);
    },
    // 算法接口
    async governanceevaluation() {
      try {
        await this.getAlldicData();
        let res = await this.$http.get(governanceevaluation.newGetOptionAlgVendors, {
          params: { indexId: this.moduleAction.indexId },
        });
        this.algorithm = res.data.data || [];
        this.algorithm.forEach((item) => {
          item.algorithmLable = this.getAlgorithmLabel(item.algorithmVendorType);
        });
      } catch (err) {
        console.log(err);
      }
    },
    getAlgorithmLabel(val) {
      var arr = this.algorithmList.filter((item) => {
        return val === item.dataKey;
      });
      return arr.length > 0 ? arr[0].dataValue : '--';
    },
    changePictureType() {
      this.reset();
      this.$nextTick(() => {
        this.$refs['select-data'].init();
      });
    },
  },
  watch: {
    moduleAction: {
      handler(val) {
        if (!val) return;
        this.selectAreaTree.regionCode = val.regionCode;
      },
      deep: true,
      immediate: true,
    },
    formModel: {
      handler(val) {
        if (val === 'edit') {
          Object.keys(this.configInfo).forEach((key) => {
            switch (key) {
              case 'startCronData':
              case 'endCronData':
              case 'startTimePoints':
              case 'endTimePoints':
                this.$set(this.formData, key, this.configInfo[key] ? this.configInfo[key] : []);
                this[key] = this.configInfo[key] ? this.configInfo[key] : [];
                break;
              case 'monitorCount':
                if (this.configInfo[key]) {
                  this.deploymentStrategy = 'randomlyIssued';
                } else {
                  this.deploymentStrategy = 'all';
                }
              default:
                this.$set(this.formData, key, this.configInfo[key]);
                break;
            }
          });
        } else {
          /**
           * 初始化默认加载该组织机构和该组织机构下所有子集 by 景瑞
           */
          const org = this.initialOrgList.find((row) => row.regionCode === this.moduleAction.regionCode);
          const orgList = this.initialOrgList.filter((row) => row.parentId === org.id);
          this.formData.apis.push({
            orgCode: org.orgCode,
            orgName: org.orgName,
            dataList: [],
          });
          orgList.forEach((row) => {
            this.formData.apis.push({
              orgCode: row.orgCode,
              orgName: row.orgName,
              dataList: [],
            });
          });
        }
      },
      immediate: true,
    },
  },
  computed: {
    ...mapGetters({
      algorithmList: 'algorithm/algorithmVendorType',
      initialOrgList: 'common/getInitialOrgList',
    }),
  },
  components: {
    ApiAreaTree: require('@/api-components/api-area-tree.vue').default,
    UiTable: require('@/components/ui-table.vue').default,
    SelectData: require('../components/select-data.vue').default,
    TestPlan: require('../../test-plan.vue').default,
    UiImage: require('@/components/ui-image.vue').default,
  },
};
</script>
<style lang="less" scoped>
@leftMargin: 200px;
.form-item {
  @{_deep}.ivu-form-item-label {
    width: @leftMargin;
  }
  @{_deep}.ivu-form-item-content {
    margin-left: @leftMargin;
  }
}
.detail-content {
  height: 700px;
  @{_deep} .select-width {
    width: 230px;
  }
  .check-time {
    width: 60px;
  }
  .sign {
    width: 80px;
  }
  .picture-type {
    vertical-align: middle;
    display: inline-block;
    margin-bottom: 3px;
  }
  .img-box {
    width: 80px;
    height: 80px;
    margin: 0 auto;
    position: relative;
    padding: 5px 0;
    cursor: pointer;
    img {
      width: 100%;
      height: 100%;
      object-fit: contain;
    }
    .num {
      position: absolute;
      width: 20px;
      height: 20px;
      background-color: var(--color-primary);
      color: #ffffff;
      z-index: 11;
    }
  }
  @{_deep}.form-row {
    margin-bottom: 0;
  }
  .text {
    span {
      width: 160px;
    }
    i {
      color: #8797ac;
      width: 14px;
      text-align: center;
      font-size: 12px;
    }
  }
}
</style>
