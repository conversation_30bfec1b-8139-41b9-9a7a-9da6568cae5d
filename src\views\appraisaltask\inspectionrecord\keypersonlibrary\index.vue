<template>
  <div class="auto-fill">
    <!-- 轨迹准确性 -->
    <Trajectory :taskObj="taskObj" ref="inspectionrecordContent" v-if="currentTree.id === 501" />
    <UploadTime :taskObj="taskObj" ref="inspectionrecordContent" v-if="currentTree.id === 502" />
    <TrajectoryImage :task-obj="taskObj" ref="inspectionrecordContent" v-if="currentTree.id === 503" />
    <TrackCorrelation
      :task-obj="taskObj"
      :currentTree="currentTree"
      ref="inspectionrecordContent"
      v-if="currentTree.id === 504"
    />
    <RepeatLocus
      :task-obj="taskObj"
      :currentTree="currentTree"
      ref="inspectionrecordContent"
      v-if="currentTree.id === 505"
    />
  </div>
</template>
<script>
export default {
  components: {
    TrajectoryImage: require('./trajectory-image/index.vue').default,
    Trajectory: require('./trajectory.vue').default,
    UploadTime: require('./uploadTime.vue').default,
    TrackCorrelation: require('./track-correlation').default,
    RepeatLocus: require('./repeat-locus').default,
  },
  props: {
    taskObj: {
      type: Object,
      default() {
        return {};
      },
    },
    currentTree: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  data() {
    return {};
  },
  computed: {},
  watch: {},
  filter: {},
  created() {},
  mounted() {},
  methods: {},
};
</script>
<style lang="less" scoped></style>
