<template>
  <ui-modal
    ref="modal"
    v-model="visible"
    title="资源分类配置"
    class="modal"
    @onCancel="onCancel"
    @onOk="onOK"
  >
    <div class="resource_layout">
      <div class="left">
        <ul v-if="menuList.length">
          <li
            v-for="item in menuList"
            :key="item.catalogId"
            :class="{ active: item.catalogId == currentId }"
            @click="selectMenu(item)"
          >
            <div>{{ item.catalogName }}</div>
            <div>{{ item.size }}</div>
          </li>
        </ul>
      </div>
      <div class="right">
        <Search @searchForm="searchForm" ref="search" />
        <div class="count">
          <div class="cancle" @click="cancleSelect()">取消选择</div>
          <div v-if="menuList.length">
            <span class="m-r18"
              >已选<span class="blue"
                >（ {{ currentMenu.sourceTableIds.length }} ）</span
              >
              个</span
            >
          </div>
        </div>
        <Table
          class="auto-fill table"
          ref="tableRef"
          :columns="columns"
          :loading="loading"
          :data="tableData"
          border
          @on-select="selectTable"
          @on-select-cancel="selectTableCancel"
          @on-select-all="selectTableAll"
          @on-select-all-cancel="selectTableAllCancel"
          height="380"
        >
          <template #fieldNameCn="{ index }">
            <Input
              placeholder="请输入"
              v-model="tableData[index].fieldNameCn"
              maxlength="50"
              class="input-wid"
            ></Input>
          </template>
        </Table>
        <!-- 分页 -->
        <ui-page
          :current="pageInfo.pageNumber"
          :total="pageInfo.total"
          :page-size="pageInfo.pageSize"
          @pageChange="pageChange"
          @pageSizeChange="pageSizeChange"
        >
        </ui-page>
      </div>
    </div>
  </ui-modal>
</template>

<script>
import { mapGetters, mapActions } from "vuex";
import { queryTableList, paramUpdate } from "@/api/config";
import { getSelectCatalogList } from "@/api/dataGovernance";
import Search from "./search";
export default {
  name: "UiConfirm",
  components: {
    Search,
  },
  props: {},
  data() {
    return {
      visible: false,
      loading: false,
      currentId: 0,
      menuList: [],
      tableData: [],
      selectTableData: [],
      columns: [
        { align: "center", width: 60, type: "selection" },
        {
          title: "序号",
          align: "center",
          width: 70,
          type: "index",
          key: "index",
        },
        { title: "表名称", key: "sourceTable" },
        { title: "表注释", key: "sourceTableCn" },
      ],
      pageInfo: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
    };
  },
  watch: {
    selectTableData() {
      let row = this.currentMenu;
      row.size = this.currentMenu.sourceTableIds.length;
      this.$forceUpdate();
    },
  },
  computed: {
    ...mapGetters({
      searchTypeList: "dictionary/getSearchTypeList", //检索类型
    }),
    // 当前选中的分类
    currentMenu() {
      return this.menuList.find((item) => item.catalogId === this.currentId);
    },
  },
  methods: {
    /**
     * @description: 初始化列表
     * @param {string} row 配置的参数
     */
    async show(row) {
      let viewData = [];
      if (row.paramValue) {
        viewData = JSON.parse(row.paramValue).tableResourceCatalogConfig || [];
      }
      this.visible = true;
      await this.getSelectCatalogList(viewData);
    },

    /**
     * @description: 左侧目录
     * @param {array} arr 已经配置的资源类
     */
    async getSelectCatalogList(arr) {
      await getSelectCatalogList({}).then((res) => {
        this.menuList = res.data.map((item) => {
          let row =
            arr.length && arr.find((cItem) => cItem.catalogId === item.id);
          return {
            catalogId: item.id,
            catalogName: item.catalogName,
            size: row ? row.size : 0,
            sourceTableIds: row ? row.sourceTableIds : [],
          };
        });
        this.selectMenu(this.menuList[0]);
      });
    },

    /**
     * @description: 右侧表格
     */
    async queryTableList() {
      this.loading = true;
      let param = {
        ...this.pageInfo,
        ...this.$refs.search.form,
        sourceType: this.currentMenu.catalogId,
      };
      await queryTableList(param)
        .then((res) => {
          this.tableData = res.data.entities;
          this.pageInfo.total = res.data.total;
          this.tableSelectStatus();
        })
        .finally(() => {
          this.loading = false;
        });
    },

    /**
     * @description: 关闭弹框
     */
    onCancel() {
      this.tableData = [];
      this.selectTableData = [];
      this.visible = false;
    },

    /**
     * @description: 确定
     */
    onOK() {
      let param = {
        paramKey: "ICBD_RESOURCE_CATALOG_CONFIG",
        paramType: "icbd",
        paramValue: JSON.stringify({
          tableResourceCatalogConfig: this.menuList,
        }),
      };
      paramUpdate(param).then(() => {
        this.$Message.success("修改成功");
        this.visible = false;
        this.$emit("refresh");
      });
    },

    /**
     * @description: 选中表格行
     * @param {array} selection 已选项数据
     * @param {object} row 刚选择的项数据
     */
    selectTable(selection, row) {
      this.selectTableData = selection;
      this.currentMenu.sourceTableIds.push(row.sourceTableId);
    },

    /**
     * @description: 取消选中表格行
     * @param {array} selection 已选项数据
     * @param {object} row 取消选择的项数据
     */
    selectTableCancel(selection, row) {
      this.selectTableData = selection;
      this.currentMenu.sourceTableIds = this.currentMenu.sourceTableIds.filter(
        (item) => {
          return item != row.sourceTableId;
        }
      );
    },

    /**
     * @description: 全选中
     * @param {array} selection 已选项数据
     */
    selectTableAll(selection) {
      this.selectTableData = selection;
      let ids = this.currentMenu.sourceTableIds;
      selection.forEach((item) => {
        if (!ids.includes(item.sourceTableId)) {
          this.currentMenu.sourceTableIds.push(item.sourceTableId);
        }
      });
    },

    /**
     * @description: 全选取消
     * @param {array} selection 已选项数据
     */
    selectTableAllCancel(selection) {
      this.selectTableData = selection;
      // 涉及到分页
      this.tableData.forEach((item) => {
        this.currentMenu.sourceTableIds =
          this.currentMenu.sourceTableIds.filter((i) => {
            return i != item.sourceTableId;
          });
      });
    },

    /**
     * @description: 页数改变
     * @param {number} size 页码
     */
    pageChange(size) {
      this.pageInfo.pageNumber = size;
      this.queryTableList();
    },

    /**
     * @description: 页数量改变
     * @param {number} size 每页数量
     */
    pageSizeChange(size) {
      this.pageInfo.pageNumber = 1;
      this.pageInfo.pageSize = size;
      this.queryTableList();
    },

    /**
     * @description: 左侧菜单切换
     * @param {object} row 选中的菜单
     */
    selectMenu(row) {
      this.currentId = row.catalogId;
      this.queryTableList();
    },

    /**
     * @description: 查询
     */
    searchForm() {
      this.queryTableList();
    },

    /**
     * @description: 设置table行的选中状态
     */
    tableSelectStatus() {
      if (!this.menuList.length) {
        return;
      }
      this.tableData.forEach((item) => {
        item._checked = this.currentMenu.sourceTableIds.includes(
          item.sourceTableId
        );
      });
    },

    /**
     * @description: 取消选择
     */
    cancleSelect() {
      this.currentMenu.sourceTableIds = [];
      this.currentMenu.size = 0;
      this.$refs.tableRef.selectAll(false);
    },
  },
};
</script>

<style lang="less" scoped>
.modal {
  /deep/.ivu-modal {
    width: 1300px !important;
  }
  .content-wrapper {
    height: 600px;
    width: 1270px;
    .map {
      width: 100%;
      height: 100%;
    }
  }
  .search-input {
    width: 400px;
    margin-bottom: 10px;
    /deep/.ivu-input {
      width: 400px;
    }
    /deep/.ivu-icon-ios-search {
      color: #fff;
    }
  }
}

.resource_layout {
  display: flex;
  .left {
    width: 200px;
    border: 1px solid #ddd;
    ul {
      li {
        display: flex;
        justify-content: space-between;
        padding: 6px 20px;
        cursor: pointer;
      }
      .active {
        background: #2c86f8;
        color: #fff;
      }
    }
  }
  .right {
    width: calc(~"100% - 200px");
    border: 1px solid #ddd;
    margin-left: -1px;
    padding: 10px;
  }
}
.count {
  border: 1px solid #2c86f8;
  background: #e8f2ff;
  margin-bottom: 20px;
  height: 36px;
  line-height: 36px;
  padding: 0 20px;
  .cancle {
    float: right;
    height: 36px;
    color: #2c86f8;
    cursor: pointer;
  }
  .m-r18 {
    margin-right: 18px;
  }
  .blue {
    color: #2c86f8;
  }
}
</style>
