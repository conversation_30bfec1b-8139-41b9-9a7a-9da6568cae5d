/** * 搜索结果右面 */
<template>
  <div
    class="rightBox"
    :style="{ top: top + 'px' }"
    :class="{ 'rightBox-pack': packUpDown }"
  >
    <div class="rightBox-page">
      <div class="title">
        <p>{{ title }}</p>
        <Icon type="ios-close" @click="handleCancel" />
      </div>
      <div class="hint_title">
        共<span> {{ total }} </span> 条检索结果
      </div>
      <ul class="box-content" v-infinite-scroll="load">
        <li
          class="box-list"
          v-for="(item, index) in facePeerCount"
          :key="index"
        >
          <div class="box-number">
            {{ index + 1 }}
          </div>
          <div class="box-right">
            <div class="content-top">
              <div class="content-top-img">
                <!-- <ui-image viewer :src="item.sceneImg" /> -->
                <img v-lazy="item.traitImg" alt="" />
              </div>
              <!-- <div class="content-top-right">
                <div class="content-top-right-name">
                  <span class="ellipsis flex">
                    <ui-icon :type="listIcon[typeIndex.tab][0]" :size="14"></ui-icon>
                    <span class="block">{{ item.absTime || item.plateNo || '--' }}</span>
                  </span>
                  <p class="list_title" @click="handlePeer($event,item)">
                    同行<span>{{ item.peerNum }}</span>次
                  </p>
                </div>
                <span class="ellipsis">
                  <ui-icon :type="listIcon[typeIndex.tab][1]" :size="14"></ui-icon>
                  <span class="bule" :class="{'block': !item.idCardNo}">{{ item.idCardNo ? hiddenId(item.idCardNo,3,3) : '--' }}</span>
                </span>
                <span class="ellipsis" v-if='typeIndex.tab == 0'>
                  <ui-icon :type="listIcon[typeIndex.tab][2]" :size="14"></ui-icon>
                  <span class="orange" :class="{'block': !item.vid}">{{ item.vid || '--' }}</span>
                </span>
              </div> -->
              <div class="content-top-right" @click="handlePeer($event, item)">
                <div
                  class="content-top-right-name"
                  v-for="(column, index) in columns"
                  :key="`${item.key}-${index}`"
                >
                  <span class="ellipsis flex">
                    <ui-icon :type="column.icon" :size="14"></ui-icon>
                    <span
                      class="ellipsis"
                      style="width: 90%"
                      :title="item[column.key]"
                      :class="column.class || 'block'"
                      >{{ item[column.key] || "&#45;&#45;" }}</span
                    >
                  </span>
                  <p class="list_title" v-show="column.peer">
                    同行<span>{{ item.peerNum }}</span
                    >次
                  </p>
                </div>
              </div>
            </div>

            <!-- <div class="content-bottom">
                            <div class="iconList">
                                <opera-floor iconSec="icon-dangan2"></opera-floor>
                            </div>
                        </div> -->
          </div>
        </li>

        <ui-empty
          v-if="facePeerCount.length === 0 && loading == false"
        ></ui-empty>
        <ui-loading v-if="loading"></ui-loading>
      </ul>
      <div
        class="footer"
        :class="{ packArrow: packUpDown }"
        @click="handlePackup"
      >
        <img :src="packUrl" alt="" />
        <p>{{ packUpDown ? "展开" : "收起" }}</p>
      </div>
      <p class="loading" v-if="loadingText">加载中...</p>
      <p class="loading" v-if="noMore && facePeerCount.length != 0">
        没有更多了
      </p>
    </div>
  </div>
</template>

<script>
import operaFloor from "../../../components/operat-floor/index.vue";
import {
  queryFacePeerCountPageList,
  queryVehiclePeerCountPageList,
  queryFacePeerCountPageListByTimeliness,
  queryVehiclePeerCountPageListByTimeliness,
  queryNonMotorPeerAnalysisPageList,
} from "@/api/modelMarket";
export default {
  name: "",
  props: {
    title: {
      type: String,
      default: "检索结果",
    },
    marginTop: {
      type: Number,
      default: 0,
    },
  },
  components: {
    operaFloor,
  },
  data() {
    return {
      // title: '对象信息',
      facePeerCount: [],
      removableTop: 0,
      objModal: false,
      page: {
        pageNumber: 1,
        pageSize: 10,
      },
      detailRequest: {
        // 0: queryFacePeerCountPageList, // 人脸
        // 1: queryVehiclePeerCountPageList, // 车辆
        0: queryFacePeerCountPageListByTimeliness, // 人脸
        1: queryVehiclePeerCountPageListByTimeliness, // 车辆
        2: queryNonMotorPeerAnalysisPageList, // 非车辆
      },
      listIcon: {
        0: ["time", "location", "camera"],
        1: ["chepai", "xingming", "shenfenzheng"],
      },
      packUrl: require("@/assets/img/model/icon/arrow.png"),
      typeIndex: {
        tab: 0,
        secTab: 0,
      },
      loading: false,
      // loadingText: '加载中',
      total: 0,
      loadingText: false,
      noMore: false,
      packUpDown: false,
      faceColumns: [
        {
          icon: "camera",
          key: "vid",
          class: "orange",
          peer: true,
        },
        {
          icon: "location",
          key: "deviceAddress",
        },

        {
          icon: "time",
          key: "absTime",
        },
      ],
      vehicleColumns: [
        {
          icon: "chepai",
          key: "plateNo",
          peer: true,
          class: "blue",
        },
        {
          icon: "shenfenzheng",
          key: "idCardNo",
        },
      ],
      searchInfo: {},
    };
  },
  watch: {
    marginTop: {
      handler(val) {
        // console.log(val, 'marginTop')
      },
      immediate: true,
    },
  },
  computed: {
    columns() {
      if (this.typeIndex.tab === 0) {
        return this.faceColumns;
      } else {
        return this.vehicleColumns;
      }
    },
    top() {
      return this.marginTop + 20;
    },
    scrollHeight() {
      let htmlFontSize = parseFloat(
        window.document.documentElement.style.fontSize
      );
      if (!!htmlFontSize) {
        return htmlFontSize * (450 / 192);
      }
      return 450;
    },
  },
  created() {},
  mounted() {},
  methods: {
    init(val, tabIndex) {
      this.typeIndex = tabIndex; //用于判断tab类型
      this.packUpDown = false;
      this.loading = true;
      this.page = {
        pageNumber: 1,
        pageSize: 999999,
      };
      this.facePeerCount = [];
      this.rightList(val);
      this.searchInfo = val;
      this.total = 0;
    },
    rightList(val) {
      let params = {
        ...val,
        ...this.page,
      };
      this.detailRequest[this.typeIndex.tab](params)
        .then((res) => {
          // let list = (res.data && res.data.entities) || [];
          // this.facePeerCount = this.facePeerCount.concat(...list);
          this.facePeerCount = res.data.entities || [];
          this.total = res.data.total || 0;
        })
        .finally(() => {
          this.loading = false;
          this.noMore = false;
          this.loadingText = false;
        });
    },
    load() {
      let totalPage = Math.ceil(this.total / this.page.pageSize);
      if (this.total <= 10) {
        return;
      }
      if (this.page.pageNumber >= totalPage) {
        this.noMore = true;
        setTimeout(() => {
          this.noMore = false;
        }, 1000);
        return;
      } else {
        this.loadingText = true;
        this.page.pageNumber = this.page.pageNumber + 1;
        this.rightList(this.searchInfo);
      }
    },
    handleCancel() {
      this.$emit("cancel");
    },
    // 同行
    handlePeer($event, item) {
      console.log(item, "item");
      $event.stopPropagation();
      this.$emit("peerNumber", item);
    },
    handlePackup() {
      this.packUpDown = !this.packUpDown;
    },
    // 加密身份证
    hiddenId(str, frontLen, endLen) {
      var len = str.length - frontLen - endLen;
      var xing = "";
      for (var i = 0; i < len; i++) {
        xing += "*";
      }
      return (
        str.substring(0, frontLen) + xing + str.substring(str.length - endLen)
      );
    },
  },
};
</script>

<style lang="less" scoped>
@import "./style/index";
.rightBox {
  width: 370px;
  position: absolute;
  right: 10px;
  top: 0;
  background: #fff;
  height: calc(~"100% - 40px");
  transition: height 0.2s ease-out;
  box-shadow: 0px 3px 5px 0px rgba(0, 0, 0, 0.3);
  &-page {
    height: 100%;
    width: 100%;
  }
  .hint_title {
    margin: 10px 0 0 15px;
    font-size: 12px;
    color: rgba(0, 0, 0, 0.6);
    span {
      color: rgba(44, 134, 248, 1);
    }
  }
  .box-content {
    min-height: 240px;
    // height: 400px;
    height: calc(~"100% - 110px");
    padding: 0px 10px 0px 10px;
    margin: 10px 0 0px;
    position: relative;
    overflow-y: auto;
    .box-list {
      margin-bottom: 10px;
      display: flex;
      // cursor: pointer;
      .box-number {
        width: 30px;
        height: 30px;
        min-width: 30px;
        min-height: 30px;
        color: #f29f4c;
        border-radius: 50%;
        border: 2px solid #f29f4c;
        line-height: 26px;
        text-align: center;
        margin-top: 34px;
        margin-right: 8px;
      }

      .box-right {
        background: #f9f9f9;
        flex: 1;
        padding: 5px 10px 0px 5px;
        overflow: hidden;
        &:hover {
          background: rgba(44, 134, 248, 0.1);
        }
        .content-top {
          display: flex;
          &-img {
            width: 80px;
            height: 80px;
            background: #f9f9f9;
            border: 1px solid #d3d7de;
            display: flex;
            justify-content: center;
            align-items: center;
            img {
              width: auto;
              height: auto;
              max-height: 80px;
              max-width: 80px;
            }
          }
          .content-top-right {
            margin-top: 3px;
            margin-left: 11px;
            font-size: 14px;
            width: calc(~"100% - 91px");
            /deep/ .iconfont {
              margin-right: 5px;
            }
            .bule {
              color: #2c86f8;
            }
            .orange {
              color: #f29f4c;
            }
            .block {
              color: #000000;
            }
            &-name {
              display: flex;
              margin-bottom: 8px;
              .flex {
                flex: 1;
              }
              .list_title {
                font-size: 12px;
                color: rgba(0, 0, 0, 0.6);
                cursor: pointer;
                span {
                  color: rgba(44, 134, 248, 1);
                }
              }
            }
          }
        }
        .content-bottom {
          display: flex;
          justify-content: space-between;
          margin-top: 5px;
          .iconList {
            width: 80px;
          }
          .analyseIcon {
            font-size: 12px;
            color: #5584ff;
            cursor: pointer;
          }
        }
      }
    }
  }
  .footer {
    // color: #000000;
    position: absolute;
    bottom: 0px;
    left: 50%;
    transform: translate(-50%, 0px);
    background: #fff;
    width: 96%;
  }
}
.rightBox-pack {
  height: 80px;
  transition: height 0.2s ease-out;
  overflow: hidden;
}
</style>
