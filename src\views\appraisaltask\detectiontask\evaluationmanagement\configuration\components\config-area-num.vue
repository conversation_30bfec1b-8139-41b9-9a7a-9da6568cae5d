<template>
  <ui-modal v-model="visible" title="选择采集区域类型" :styles="styles" class="ui-modal" @query="query">
    <base-config-area-num v-bind="$props" ref="baseConfigAreaNum"></base-config-area-num>
  </ui-modal>
</template>

<script>
export default {
  name: 'config-area-num',
  components: {
    BaseConfigAreaNum: require('./base-config-area-num.vue').default,
  },
  props: {
    value: {},
    data: {}, //所有的采集区域
  },
  data() {
    return {
      styles: {
        width: '5rem',
      },
      visible: false,
    };
  },
  computed: {},
  watch: {
    value(val) {
      this.visible = val;
    },
    visible(val) {
      this.$emit('input', val);
    },
  },
  filter: {},
  mounted() {},
  methods: {
    validate(importList, otherList) {
      return [...importList, ...otherList].every((item) => {
        if (!item.faceConfigNum || !item.vehicleConfigNum || !item.videoConfigNum) {
          this.$Message.error(
            `${item.cjqyType === 0 ? '重点' : '其他'}必报采集区域${item.key}${item.value}达标数量不能为空`,
          );
          return false;
        }
        return true;
      });
    },
    query() {
      let { importList, otherList } = this.$refs.baseConfigAreaNum.query();
      let validate = this.validate(importList, otherList);
      if (!validate) return;
      this.$emit('commit', importList, otherList);
    },
  },
};
</script>

<style lang="less" scoped>
.ui-table {
  height: 300px;
}
.search {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
