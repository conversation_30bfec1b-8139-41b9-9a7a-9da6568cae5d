<template>
  <div class="page-assessmentresult auto-fill">
    <div class="right-content auto-fill">
      <div class="search-wrapper">
        <ui-label class="fl" label="考核任务" :width="70">
          <Select class="width-md" placeholder="请选择考核任务" v-model="searchData.examTaskId" @on-change="changeTask">
            <Option v-for="item in taskList" :key="item.id" :value="item.id">
              {{ item.taskName }}
            </Option>
          </Select>
        </ui-label>
        <ui-label class="ml-lg fl" label="考核时间" :width="70">
          <template>
            <DatePicker
              type="month"
              format="yyyy-MM"
              placeholder="请选择考核时间"
              v-model="searchData.time"
            ></DatePicker>
          </template>
          <!-- <template v-else>
            <el-date-picker
              v-model="searchData.time"
              type="week"
              format="yyyy 第 WW 周"
              placeholder="选择周"
              :clearable="false"
              prefix-icon="el-icon-time"
            ></el-date-picker>
          </template> -->
        </ui-label>
        <ui-label :width="0" label=" " class="fl ml-lg">
          <Button type="primary" class="mr-sm fl" @click="searchBtn"> 查询 </Button>
          <Button class="mr-lg fl" @click="resetSearch">重置</Button>
        </ui-label>
        <ui-label :width="0" label=" " class="fr">
          <Button type="primary" class="fr" @click="exportHandle" :loading="btnLoading">
            <i class="icon-font icon-daochu f-12 mr-sm vt-middle" title="添加"></i>
            <span class="vt-middle">导出</span>
          </Button>
        </ui-label>
      </div>
      <div class="jump">
        <ui-breadcrumb :data="breadcrumbData" @change="handleChange"></ui-breadcrumb>
      </div>
      <div class="title-center">
        <div class="conter-center">
          <span>
            {{ tableTitleYear }}-{{ breadcrumbData.length ? breadcrumbData[breadcrumbData.length - 1].add : ''
            }}{{ tableTitleTaskName }}
          </span>
        </div>
      </div>
      <div class="table-box auto-fill">
        <ui-table
          ref="assessmentTable"
          class="ui-table auto-fill"
          :table-columns="tableColumns"
          :table-data="tableData"
          :loading="loading"
        >
          <template slot-scope="{ row }" slot="ORG_REGEION_CODE">
            <span v-if="orgCode === row.orgCode">
              {{ row.ORG_REGEION_CODE }}
            </span>
            <span v-else class="span-btn" @click="administrativeDivisionBtn(row)">
              {{ row.ORG_REGEION_CODE }}
            </span>
          </template>
        </ui-table>
      </div>
    </div>
    <picture-attribute-integrity
      ref="pictureAttributeIntegrity"
      :taskObj="infoObj"
      @selectModuleClick="selectModuleHandle"
    />
  </div>
</template>
<script>
import UiBreadcrumb from '../inspectionrecord/components/ui-breadcrumb';
import pictureAttributeIntegrity from './components/picture-attribute-integrity.vue';
import governanceevaluation from '@/config/api/governanceevaluation';
const renderTableHeader = (h, params) => {
  return h('div', [
    h('div', {}, params.column.title),
    h('div', {}, `(${params.column.score}分)`),
    // h(
    //   'div',
    //   {
    //     on: {
    //       click: () => {
    //         console.log('11111111')
    //       },
    //     },
    //   },
    //   '展开'
    // ),
  ]);
};
function renderTableTd(_that) {
  return (h, params) => {
    if (params.column.key === 'TOTAL_SCORE' || !params.row[params.column.key]) {
      return h('span', {}, params.row[params.column.key] ? params.row[params.column.key].score : '--');
    } else {
      return h(
        'span',
        {
          style: {
            color: 'var(--color-primary)',
            cursor: 'pointer',
          },
          on: {
            click: () => {
              _that.pictureAttributeIntegrityBtn(params);
            },
          },
        },
        params.row[params.column.key].score,
      );
    }
  };
}
export default {
  components: {
    UiTable: require('@/components/ui-table.vue').default,
    UiBreadcrumb,
    pictureAttributeIntegrity,
  },
  data() {
    return {
      loading: false,
      tableTitleYear: '',
      infoObj: '',
      taskList: [],
      firstOrgCode: '',
      taskInfo: {},
      tableTitleTaskName: '',
      orgCode: '',
      searchData: {
        examSchemeId: '',
        examTaskId: '',
        tableTitleTaskName: '',
        time: '',
        month: '',
        year: '',
        orgRegeionCode: '',
      },
      searchDataCopy: {},
      tableColumns: [
        {
          title: '行政区划',
          key: 'ORG_REGEION_CODE',
          slot: 'ORG_REGEION_CODE',
        },
      ],
      tableData: [],
      breadcrumbData: [],
      btnLoading: false,
      statisticsData: {},
    };
  },
  created() {},
  mounted() {
    this.$http
      .post(governanceevaluation.getAlltaskList, {})
      .then((res) => {
        let { data } = res.data;
        this.taskList = data;
        this.changeTask(this.$route.query.id, true);
        this.searchDataCopy = JSON.parse(JSON.stringify(this.searchData));
      })
      .catch(() => {});
  },
  methods: {
    handleChange(val) {
      this.searchData.orgRegeionCode = val.id;
      this.search();
    },
    administrativeDivisionBtn(row) {
      this.breadcrumbData.push({
        id: row.orgCode,
        add: row.ORG_REGEION_CODE,
      });
      this.searchData.orgRegeionCode = row.orgCode;
      this.search();
    },
    pictureAttributeIntegrityBtn(row) {
      this.infoObj = {
        ORG_REGEION_CODE: row.row.ORG_REGEION_CODE,
        title: row.column.title,
        parentTitle: row.column.parentTitle,
        score: row.column.score,
        examContentItemMonthResultId: row.row[row.column.key].examContentItemMonthResultId,
      };
      this.$refs.pictureAttributeIntegrity.init();
    },
    changeTask(id, flag) {
      if (id) {
        var item = this.taskList.find((v) => {
          return v.id == id;
        });
        this.taskInfo = item;
        this.searchData.tableTitleTaskName = item.taskName;
        if (item.taskRunState !== '0') {
          this.searchData.examTaskId = item.id;
          this.searchData.examSchemeId = item.schemeId;
          this.firstOrgCode = item.regionCode;
          this.orgCode = item.regionCode;
          this.searchData.orgRegeionCode = item.regionCode;
          //月考核-显示年月
          this.searchData.time = this.$util.common.formatDate(item.taskStartTime, 'yyyy-MM');
          this.searchData.year = parseInt(this.$util.common.formatDate(item.taskStartTime, 'yyyy'));
          this.searchData.month = parseInt(this.$util.common.formatDate(item.taskStartTime, 'MM'));
          if (flag) {
            this.search(flag);
          }
        } else {
          this.$Message.error('考核任务还未开始');
          this.tableData = [];
          this.breadcrumbData = [];
        }
      }
    },
    searchBtn() {
      this.breadcrumbData = [];
      if (this.taskInfo.taskRunState === '0') {
        this.$Message.error('考核任务还未开始');
        return;
      }
      this.searchData.orgRegeionCode = this.firstOrgCode;
      this.search(true);
    },
    search(flag) {
      this.loading = true;
      this.tableTitleYear = this.$util.common.formatDate(this.searchData.time, 'yyyy年MM月');
      this.tableTitleTaskName = this.searchData.tableTitleTaskName;
      this.searchData.year = parseInt(this.$util.common.formatDate(this.searchData.time, 'yyyy'));
      this.searchData.month = parseInt(this.$util.common.formatDate(this.searchData.time, 'MM'));
      this.$http
        .post(governanceevaluation.getExamStatistics, this.searchData)
        .then((res) => {
          this.statisticsData = res.data.data;
          let { headers, body } = res.data.data;
          // 处理表头
          this.handleTableHeaders(headers);
          console.log(this.tableColumns);
          this.tableColumns = headers;
          // 处理表内容
          this.handleTableBody(body, flag);
        })
        .catch(() => {})
        .finally(() => {
          this.loading = false;
        });
    },
    handleTableHeaders(arr, title) {
      arr.forEach((v) => {
        v.title = v.name;
        v.key = v.code;
        v.className = 'header-table';
        v.align = 'center';
        v.parentTitle = title;
        if (v.score || v.score == 0) {
          v.renderHeader = renderTableHeader;
        } else {
          delete v.score;
        }
        if (v.code === 'ORG_REGEION_CODE') {
          v.slot = 'ORG_REGEION_CODE';
          v.minWidth = 160;
        } else if (v.code === 'TOTAL_SCORE') {
          v.minWidth = 100;
        } else {
          v.minWidth = v.title.length * 14 + 20;
        }
        if (v.children && v.children.length) {
          this.handleTableHeaders(v.children, v.title);
        } else {
          if (v.code !== 'ORG_REGEION_CODE') v.render = renderTableTd(this);
          delete v.children;
        }
      });
    },
    handleTableBody(arr, flag) {
      let item = arr.length && arr[0].length ? arr[0][0] : '';
      this.orgCode = item.orgRegeionCode;
      //首次加载
      if (flag) {
        this.breadcrumbData.push({
          id: this.orgCode,
          add: item.orgRegeionName,
        });
      }
      let tableData = [];
      arr.forEach((v, i) => {
        tableData.push({});
        v.forEach((k) => {
          if (k.code === 'ORG_REGEION_CODE') {
            tableData[i].ORG_REGEION_CODE = k.orgRegeionName;
            tableData[i].orgCode = k.orgRegeionCode;
          } else {
            tableData[i][k.code] = {
              score: k.score,
              examContentItemMonthResultId: k.examContentItemMonthResultId,
            };
          }
        });
      });
      this.tableData = tableData;
    },
    resetSearch() {
      this.breadcrumbData = [];
      this.changeTask(this.searchDataCopy.examTaskId, true);
    },
    // 导出
    async exportHandle() {
      try {
        this.btnLoading = true;
        let params = this.statisticsData;
        let data = await this.$http.post(governanceevaluation.exportAssessment, params, {
          responseType: 'blob',
        });
        await this.$util.common.exportfile(
          data,
          `${this.tableTitleYear}-${
            this.breadcrumbData.length ? this.breadcrumbData[this.breadcrumbData.length - 1].add : ''
          }${this.tableTitleTaskName} -【IVDG】-【${this.$util.common.formatDate(new Date())}】`,
        );
      } catch (e) {
        console.log(e);
      } finally {
        this.btnLoading = false;
      }
    },
    selectModuleHandle(item) {
      this.$emit('changeComponent', item);
    },
  },
};
</script>
<style lang="less" scoped>
.page-assessmentresult {
  height: 100%;
  /deep/.head-center {
    display: flex;
    flex-direction: column;
    align-items: center;
  }
  /deep/ .ivu-date-picker-cells-cell-focused {
    background: #2d8cf0;
    color: #fff;
  }
  .right-content {
    float: right;
    width: 100%;
    height: 100%;
    background: var(--bg-content);
    @{_deep}.search-wrapper {
      overflow: hidden;
      padding: 0 20px 20px 20px !important;
      border-bottom: 1px solid var(--border-color);
      margin: 20px 0px;
      .el-date-editor {
        width: 212px;
      }
      .ivu-input,
      .el-input__inner {
        height: 34px;
        padding: 4px 32px 4px 8px;
        color: #fff;
        font-size: 14px;
        line-height: 32px;
        border-radius: 4px;
        background: #02162b;
      }
      .el-input__inner:hover {
        border: 1px solid var(--color-primary);
      }
      .el-icon-date {
        display: none;
      }
      .ui-label {
        line-height: 34px;
      }
      .ivu-select {
        height: 34px;
      }
      .el-input__prefix {
        left: unset;
        right: 0;
        width: 32px;
      }
      .el-input__icon,
      .ivu-input-suffix i {
        color: var(--color-primary);
        font-size: 16px;
      }
      .ivu-input-suffix,
      .ivu-input-suffix i {
        line-height: normal;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      // .el-input__suffix,
      // .el-input__suffix-inner,
      // .el-input__suffix-inner i {
      //   display: flex;
      //   align-items: center;
      //   justify-content: center;
      // }
      // .el-input__suffix-inner i {
      //   display: inline-flex;
      // }
      .el-input__icon {
        line-height: 34px;
      }
    }
    .jump {
      padding: 0 20px !important;
    }
    .title-center {
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 20px 0;
      .conter-center {
        height: 30px;
        padding: 0 100px;
        font-size: 16px;
        font-weight: 400;
        display: inline-block;
        vertical-align: middle;
        background: linear-gradient(to right, #08264d 0%, #114076 50%, #08264d 100%);
        opacity: 1;
        span {
          line-height: 30px;
          height: 100%;
          color: #fff;
          display: inline-block;
        }
      }
    }
    .table-box {
      padding: 0 20px 20px 20px;
      position: relative;
      .span-btn {
        cursor: pointer;
        color: var(--color-primary);
      }
      .no-data {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
      }
      @{_deep} .ivu-table-tbody {
        td {
          padding: 10px 0 10px 0;
        }
      }
      @{_deep} .ivu-table-body {
        td {
          padding: 10px 0 10px 0;
        }
      }
      /deep/ .ui-table {
        border-top: 1px solid var(--border-table);
        th .ivu-table-cell {
          color: #8797ac;
        }
      }
      /deep/ .ivu-table-body > table {
        border-right: 1px solid var(--border-table);
      }
      /deep/ .ivu-table-header {
        border-right: 1px solid var(--border-table);
      }
      /deep/ .header-table {
        box-shadow: none;
        // box-shadow: inset 1px -1px 0 0 #0d477d;
        border-left: 1px solid var(--border-table);
        border-bottom: 1px solid var(--border-table);
      }
    }
  }
}
</style>
