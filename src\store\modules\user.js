import md5 from "md5";
import {
  setToken,
  getToken,
  setdemoToken,
  getdemoToken,
} from "@/libs/configuration/util.common";
import Setting from "@/libs/configuration/setting";
import {
  userLogin,
  userLogout,
  getUserInfo,
  getApplicationInfo,
  updateToken,
  settingQuery,
} from "@/api/user";

import axios from "axios";

export default {
  state: {
    expirationTimestamp: 0, //token失效时间
    expiresIn: 0, //token失效时间间隔
    userInfo: {},
    individuation: {},
    token: getToken(),
    demoToken: getdemoToken(),
    hasGetInfo: false,
    sysApplication: {},
    applicationInfo: {},
    refreshToken: window.sessionStorage.getItem("refreshToken") || "",
    menuCollapse: false,
  },
  mutations: {
    setUserInfo(state, data) {
      state.userInfo = data;
    },
    setIndividuation(state, data) {
      state.individuation = data;
    },
    setToken(state, token) {
      state.token = token;
      setToken(token);
    },
    setdemoToken(state, demoToken) {
      state.demoToken = demoToken;
      setdemoToken(demoToken);
    },
    setTokenDetail(state, data) {
      state.refreshToken = data.refresh_token;
      state.expiresIn = data.expires_in;
      state.expirationTimestamp = new Date().getTime() + data.expires_in * 1000;
      window.sessionStorage.setItem("refreshToken", state.refreshToken);
      window.sessionStorage.setItem(
        "expirationTimestamp",
        state.expirationTimestamp
      );
    },
    setHasGetInfo(state, status) {
      state.hasGetInfo = status;
    },
    setSysApplication(state, data) {
      state.sysApplication = data;
    },
    setAppInfo(state, data) {
      state.applicationInfo = data;
    },

    /**
     * @description 修改 menuCollapse
     * @param {Object} state vuex state
     * @param {Boolean} collapse 折叠状态
     * */
    setMenuCollapse(state, data) {
      state.menuCollapse = data;
    },
    updateMenuCollapse(state, collapse) {
      state.menuCollapse = collapse;
    },
  },
  getters: {
    userInfo: (state) => state.userInfo,
    individuation: (state) => state.individuation,
    applicationInfo: (state) => state.applicationInfo,
    token: (state) => state.token,
    hasGetInfo: (state) => state.hasGetInfo,
    sysApplication: (state) => state.sysApplication,
    menuCollapse: (state) => state.menuCollapse,
  },
  actions: {
    appInfo({ state, commit }) {
      if (state.applicationInfo.applicationName) {
        return;
      }
      return new Promise((resolve, reject) => {
        getApplicationInfo()
          .then((res) => {
            // console.log('系统信息', res)
            if (res.data.length > 0) {
              commit("setAppInfo", res.data[0]);
              document.title = res.data[0].applicationName;
              resolve(res.data[0]);
            }
          })
          .catch((err) => {
            reject(err);
          });
      });
    },
    // 登录
    handleLogin({ commit }, { username, password }) {
      username = username.trim();
      return new Promise((resolve, reject) => {
        password = md5(password);
        const formData = new FormData();
        formData.append("username", username);
        formData.append("password", password);
        formData.append("scope", "server");
        formData.append("grant_type", "password");
        userLogin(formData)
          .then((data) => {
            commit("setToken", data.access_token);
            commit("setTokenDetail", data);
            if (developmentEnvironment == "binhai") {
              console.log("binhai");
              axios
                .post(
                  "http://50.146.187.33:10219/uums/auth/token",
                  {
                    password: "a7c19f5b87f7589e3bf37017d9083b4f3abb20301821",
                    grant_type: "password",
                    username: "xiling",
                  },
                  {
                    headers: { "origin-source": "sc_web" },
                  }
                )
                .then((res) => {
                  console.log(res.data.data, "res.data");
                  commit("setdemoToken", res.data.data.accessToken);
                });
            }
            resolve(data);
          })
          .catch((err) => {
            reject(err);
          });
      });
    },
    clearLoginToken({ state, commit }) {
      return new Promise((resolve, reject) => {
        commit("setToken", "");
        commit("setdemoToken", "");
        commit("setHasGetInfo", false);
        window.sessionStorage.clear();
        resolve();
      });
    },
    // 退出登录
    handleLogOut({ state, commit }) {
      return new Promise((resolve, reject) => {
        userLogout(state.token)
          .then((res) => {
            commit("setToken", "");
            commit("setdemoToken", "");
            commit("setHasGetInfo", false);
            window.localStorage.clear();
            resolve(res);
          })
          .catch((err) => {
            reject(err);
          });
      });
    },
    // 获取用户相关信息,包括基本信息，权限等
    getUserInfo({ state, commit, dispatch }) {
      return new Promise((resolve, reject) => {
        try {
          getUserInfo()
            .then(async (res) => {
              const sysApplication = res.data.sysApplicationVoList.find(
                (item) => item.applicationCode === applicationCode
              );
              const { sysUser, sysApplicationVoList, roleVoList, orgVoList } =
                res.data;
              const userInfo = {
                ...sysUser,
                sysApplicationVoList,
                roleVoList,
                orgVoList,
              };
              commit("setUserInfo", userInfo);
              commit("setSysApplication", sysApplication);
              await dispatch("getIndividuation");
              await dispatch("player/fetchShieldResourcePool");

              commit("setHasGetInfo", true);
              resolve(sysApplication);
            })
            .catch((err) => {
              reject(err);
            });
        } catch (error) {
          reject(error);
        }
      });
    },
    // 获取用户个性化设置
    getIndividuation({ state, commit }) {
      return new Promise((resolve, reject) => {
        try {
          settingQuery({
            paramType: "individuation",
            user: state.userInfo.username,
          })
            .then((res) => {
              if (res.data != null) {
                let configValue = JSON.parse(res.data.paramValue);
                commit("setMenuCollapse", !configValue.menuConfig || false);
                commit("setIndividuation", configValue);
                resolve(configValue);
              } else {
                commit("setIndividuation", {});
                resolve({});
              }
            })
            .catch((err) => {
              reject(err);
            });
        } catch (error) {
          reject(error);
        }
      });
    },
    // 刷新token
    updateToken({ commit, dispatch }, token) {
      return new Promise(async (resolve, reject) => {
        try {
          let params = "scope=server" + "&" + "refresh_token=" + token;
          let res = await updateToken(params);
          commit("setTokenDetail", res);
          commit("setToken", res.access_token);
          resolve(res);
        } catch (e) {
          reject(e);
        }
      });
    },
  },
};
