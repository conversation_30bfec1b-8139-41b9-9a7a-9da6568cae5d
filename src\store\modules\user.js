import user from '@/config/api/user';
import thirdPartyConfig from '@/config/thirdParty.config.js';
import md5 from 'md5';
export default {
  namespaced: true,
  state: {
    token: window.sessionStorage.getItem('token') || '',
    refreshToken: window.sessionStorage.getItem('refreshToken') || '',
    expirationTimestamp: 0, //token失效时间
    expiresIn: 0, //token失效时间间隔
    userInfo: {},
    managerAppUrl: '', // 采集系统地址
    isAdmin: false, // 是否是admin
    isThirdParty: window.sessionStorage.getItem('isThirdParty') || '0', //是否为第三方登录
    thirdPartyLoading: false, // 第三方登录loading
    userMessage: window.sessionStorage.getItem('userMessage') || {},
    clientId: window.sessionStorage.getItem('clientId') || "",
  },
  mutations: {
    setUserInfo(state, data) {
      state.userInfo = data;
    },
    setToken(state, data) {
      state.token = data.access_token;
      state.refreshToken = data.refresh_token;
      state.expiresIn = data.expires_in;
      state.expirationTimestamp = new Date().getTime() + data.expires_in * 1000;
      window.sessionStorage.setItem('token', state.token);
      window.sessionStorage.setItem('refreshToken', state.refreshToken);
      window.sessionStorage.setItem('expirationTimestamp', state.expirationTimestamp);
    },
    updateExpirationTime(state) {
      state.expirationTimestamp = new Date().getTime() + state.expiresIn * 1000;
      window.sessionStorage.setItem('expirationTimestamp', state.expirationTimestamp);
    },
    clearToken() {
      window.sessionStorage.clear();
    },
    setManagerAppUrl(state, url) {
      state.managerAppUrl = url;
    },
    setIsAdmin(state, boolean) {
      state.isAdmin = boolean;
    },
    setIsThirdParty(state, data) {
      window.sessionStorage.setItem('isThirdParty', data);
      state.isThirdParty = data;
    },
    setUserMessage(state, data) {
      let userMessage = {
        username: data.name,
        user_id: data.id,
        orgList: data?.orgVoList || [],
      };
      window.sessionStorage.setItem('userMessage', JSON.stringify(userMessage));
    },
    setThirdPartyLoading(state, thirdPartyLoading) {
      state.thirdPartyLoading = thirdPartyLoading;
    },
    setClientId(state, clientId) {
      state.clientId = clientId;
    },
  },
  getters: {
    getToken(state) {
      return state.token;
    },
    getUserInfo(state) {
      return state.userInfo;
    },
    getManagerAppUrl(state) {
      return state.managerAppUrl;
    },
    getIsAdmin(state) {
      return state.isAdmin;
    },
    getIsThirdParty(state) {
      return state.isThirdParty;
    },
    getThirdPartyLoading(state) {
      return state.thirdPartyLoading;
    },
    getClientId(state) {
      return state.clientId;
    },
  },
  actions: {
    /**
     *
     * 登录
     * @param params {username: "", password: ""}
     * @returns {Promise<unknown>}
     */
    handleLogin({ commit }, params) {
      return new Promise((resolve, reject) => {
        this._vm.$http
          .post(user.login, params)
          .then((res) => {
            commit('setToken', res.data);
            resolve(res);
          })
          .catch((err) => {
            reject(err);
          });
      });
    },
    // 获取用户相关信息
    getUserInfo({ commit, dispatch }) {
      return new Promise((resolve, reject) => {
        try {
          this._vm.$http
            .get(user.getUserInfo, {
              params: { applicationCode: '00000002' },
            })
            .then((res) => {
              const { sysUser, roleVoList, orgVoList } = res.data.data;
              const userInfo = {
                ...sysUser,
                roleVoList,
                orgVoList,
              };
              commit('setUserInfo', userInfo);
              commit('setUserMessage', sysUser);
              dispatch('websocket/startWebsock', null, { root: true });
              resolve(res);
            })
            .catch((err) => {
              console.error(err, 'err');
              reject(err);
            });
        } catch (error) {
          reject(error);
        }
      });
    },
    // 刷新token
    updateToken({ commit }, token) {
      let params = 'scope=server' + '&' + 'refresh_token=' + token;
      return new Promise((resolve, reject) => {
        this._vm.$http
          .post(user.updateToken, params)
          .then((res) => {
            commit('updateExpirationTime');
            if (res && res.data) {
              commit('setToken', res.data);
              resolve(res.data);
            }
          })
          .catch((err) => {
            reject(err);
          });
      });
    },
    setManagerAppUrl({ commit }, url) {
      commit('setManagerAppUrl', url);
    },
    setIsAdmin({ commit }, boolean) {
      commit('setIsAdmin', boolean);
    },
    setIsThirdParty({ commit }, params) {
      commit('setIsThirdParty', params);
    },
    /**
     * 第三方登录
     */
    //当isThirdParty === '1'时调用此接口获取用户信息，与getUserInfo对应
    //此接口校验token，并返回了用户信息
    getUserTokenByThirdToken({ commit }, params) {
      commit('setIsThirdParty', '1');
      //通过第三方获取我们系统token等信息的接口URL
      let url = params.url;
      delete params.url;
      return new Promise((resolve, reject) => {
        this._vm.$http
          .get(url, { params })
          .then(async (res) => {
            commit('setToken', res.data.data);
            resolve(res);
          })
          .catch((err) => {
            commit('setIsThirdParty', '0');
            reject(err);
          });
      });
    },
    // 通过第三方登录获取到的信息
    getUserInfoByThird({ commit, rootState }, params) {
      return new Promise(async (resolve, reject) => {
        try {
          let {distinguishVersion} = rootState.common.systemConfig;
          if (typeof thirdPartyConfig[distinguishVersion].getUserInfoUrl === "function") {
            try {
              const res = await thirdPartyConfig[distinguishVersion].getUserInfoUrl()
              const {sysUser, roleVoList, orgVoList} = res.data.data;
              const userInfo = {
                ...sysUser,
                roleVoList,
                orgVoList,
              };
              commit('setUserInfo', userInfo);
              commit('setUserMessage', sysUser);
              resolve(res);
              resolve(res)
              return
            } catch (e) {
              reject(e);
            }
          }
          //获取用户权限等相关信息的url
          let url = thirdPartyConfig[distinguishVersion].getUserInfoUrl;
          this._vm.$http
              .post(url, params)
              .then((res) => {
                const {sysUser, roleVoList, orgVoList} = res.data.data;
                const userInfo = {
                  ...sysUser,
                  roleVoList,
                  orgVoList,
                };
                commit('setUserInfo', userInfo);
                commit('setUserMessage', sysUser);
                resolve(res);
              })
              .catch((err) => {
                reject(err);
              });
        } catch (error) {
          reject(error);
        }
      });
    },
    // 直接使用 ivdg的指定账号密码登录的
    async loginUserByThird({ dispatch }) {
      let param = 'username=tempuser&password=' + md5('12345678') + '&grant_type=password&scope=server';
      await dispatch('handleLogin', param);
    },
    async getBTConfig({ dispatch, commit }) {
      try {
        const { data } = await this._vm.$http.get(user.btConfig)
        commit('setClientId', data.data.clientId);
        return data.data
      } catch (e) {
        throw new Error(e)
      }
    },
  },
};
