<template>
  <div class="unqualified-reason">
    <div class="ranking-title f-14">
      <span class="icon-font icon-buhegeyuanyinfenbutongji mr-xs"></span>
      <span>不合格原因分布统计</span>
    </div>
    <div class="echarts-box">
      <draw-echarts
        :echart-option="propertyEchart"
        ref="propertyChart"
        class="charts"
        :echarts-loading="echartLoading"
      ></draw-echarts>
    </div>
  </div>
</template>
<script>
import dealWatch from '@/mixins/deal-watch';
export default {
  name: 'unqualified-reason',
  data() {
    return {
      propertyEchart: {},
      echartData: [],
      series: [],
      echartLoading: false,
    };
  },
  props: {},
  mixins: [dealWatch],
  mounted() {
    this.startWatch(
      '$route.query',
      () => {
        this.getGraphsInfo();
      },
      { deep: true, immediate: true },
    );
  },
  methods: {
    getGraphsInfo() {
      try {
        this.echartData = [
          { 'code': 4000, 'desc': '接口未响应', 'reason': '接口未响应', 'count': 123 },
          { 'code': 4001, 'desc': '返回结果错误', 'reason': '返回结果错误', 'count': 123 },
          { 'code': 4001, 'desc': '其他', 'reason': '其他', 'count': 123 },
        ];
        this.initRing(this.echartData);
      } catch (e) {
        console.log(e);
      }
      // const { regionCode, orgCode, statisticType, taskSchemeId, indexType,access, indexId, batchId } = this.$route.query
      // this.echartLoading = true
      // let params = {
      //   indexId: indexId,
      //   batchId: batchId,
      //   access: access || 'TASK_RESULT',
      //   displayType: statisticType,
      //   orgRegionCode: statisticType === 'REGION' ? regionCode : orgCode,
      // }
      // try {
      //   let {data: {data}} = await this.$http.post(governanceevaluation.getCircularGraphsInfo, params)
      //   this.echartData = data.seriesData || []
      //   this.initRing(this.echartData)
      // } catch (err) {
      //   console.log(err)
      // } finally {
      //   this.echartLoading = false
      // }
    },
    initRing() {
      let data = this.echartData.map((item) => {
        return { value: item.count, name: `${item.reason} ${item.count}` };
      });
      let opts = {
        series: [
          {
            name: 'Access From',
            type: 'pie',
            center: ['20%', '50%'],
            radius: ['65%', '80%'],
            color: ['#B856F8', '#DF7A46', '#383EF6'],
            avoidLabelOverlap: false,
            label: {
              show: false,
              position: 'center',
            },
            emphasis: {
              disabled: true,
            },
            labelLine: {
              show: false,
            },
            data: data,
          },
        ],
      };
      this.propertyEchart = this.$util.doEcharts.ReviewConsequencePie(opts);
    },
  },
  computed: {},
  components: {
    DrawEcharts: require('@/components/draw-echarts').default,
  },
};
</script>
<style lang="less" scoped>
.unqualified-reason {
  position: relative;
  width: 488px;
  height: 215px;
  background: var(--bg-sub-echarts-content);
  box-shadow: var(--shadow-sub-echarts-content);
  .ranking-title {
    padding: 10px;
    color: var(--color-title-echarts);
    background: var(--bg-sub-echarts-title);
    .icon-font {
      color: var(--color-icon-echarts);
    }
  }
  .echarts-box {
    width: 100%;
    height: calc(100% - 44.5px) !important;
    .charts {
      width: 100%;
      height: 100% !important;
    }
  }
}
</style>
