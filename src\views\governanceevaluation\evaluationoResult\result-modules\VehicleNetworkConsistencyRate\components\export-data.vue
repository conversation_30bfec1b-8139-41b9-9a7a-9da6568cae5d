<template>
  <ui-modal v-model="visible" :title="title" :styles="styles">
    <div class="export-data">
      <Form ref="modalData" :model="searchData" class="form-content" :label-width="160">
        <FormItem label="导出方式">
          <RadioGroup v-model="searchData.orgRegionCode" @on-change="handleExportWay">
            <!-- <Radio label="orgCode">组织结构</Radio> -->
            <Radio label="regionCode">行政区划</Radio>
          </RadioGroup>
        </FormItem>
        <FormItem label="组织机构">
          <select-organization-tree-checkall
            v-if="visible"
            ref="SelectOrganizationTreeCheckall"
            :treeData="treeData"
            :node-key="nodeKey"
            :default-props="defaultProps"
            :default-checked-keys="defaultCheckedKeys"
            :placeholder="treePlaceholder"
            :is-select-grandchild="false"
            :is-expand-all="false"
            @getSelectTree="checkTree"
          >
          </select-organization-tree-checkall>
        </FormItem>
      </Form>
    </div>
    <template #footer>
      <Button type="primary" @click="exportAdd" :loading="exportLoading" class="plr-30">确 定</Button>
      <Button @click="visible = false" class="plr-30">取 消</Button>
    </template>
  </ui-modal>
</template>

<script>
import evaluationoverview from '@/config/api/evaluationoverview';
import { proxyInterfacefunc } from '@/views/disposalfeedback/cascadelist/util/proxyInterface.js';
export default {
  name: 'export-data',
  props: {
    // 级联清单单独处理
    cascadeId: {},
    superiorToken: {},
    exportLoading: {
      type: Boolean,
      default: false,
    },
    title: {
      type: String,
      default: '导出设备信息',
    },
  },
  data() {
    return {
      visible: false,
      styles: { width: '4rem' },
      searchData: {
        multiSheet: 'false',
        codes: [],
        orgRegionCode: 'regionCode',
      },
      treeData: [],
      defaultCheckedKeys: [],
      treeList: [],
      nodeKey: 'regionCode',
      defaultProps: {
        label: 'regionName',
        children: 'children',
      },
      treePlaceholder: '请选择组织机构',
    };
  },
  methods: {
    init(val) {
      this.visible = true;
      this.getOrg(val);
    },
    hide() {
      this.visible = false;
      this.searchData.multiSheet = 'false';
      this.searchData.orgCodes = [];
      this.treeData = [];
    },
    async getOrg(val) {
      // 级联清单特殊替换处理接口(后端转发)
      if (this.cascadeId) {
        let interfaceName = evaluationoverview.getCurrentUserSysOrgListByTaskId;
        await proxyInterfacefunc({ resultId: val }, interfaceName, this.cascadeId, this.superiorToken, 'get').then(
          (res) => {
            let tempArr = this.$util.common.deepCopy(res.data.data);
            tempArr = tempArr.map((item) => {
              return item;
            });
            this.treeData = this.$util.common.arrayToJson(tempArr, 'id', 'parentId');
          },
        );
        return;
      }
      try {
        let res = await this.$http.get(evaluationoverview.getCurrentUserSysOrgListByTaskId, {
          params: { batchId: val },
        });
        let tempArr = this.$util.common.deepCopy(res.data.data);
        tempArr = tempArr.map((item) => {
          return item;
        });
        this.treeData = this.$util.common.arrayToJson(tempArr, 'id', 'parentId');
      } catch (error) {
        console.log(error);
      }
    },
    checkTree(codes) {
      this.searchData.codes = codes;
      this.searchData.exportZip = true;
    },
    exportAdd() {
      const exportListkey = this.searchData.orgRegionCode === 'orgCode' ? 'orgCodes' : 'regionCodes';
      if (!this.searchData.codes.length) {
        this.$Message.error(`请选择${exportListkey === 'orgCodes' ? '组织机构' : '行政区划'}`);
        return;
      }
      const exportParams = {
        // exportZip: true,
        // multiSheet: this.searchData.multiSheet,
      };
      exportParams.displayType = this.searchData.orgRegionCode === 'orgCode' ? 'ORG' : 'REGION';
      exportParams[exportListkey] = this.searchData.codes;
      this.$emit('handleExport', exportParams);
    },
    handleExportWay(val) {
      this.$refs.SelectOrganizationTreeCheckall.handleCancelCheck();
      this.searchData.codes = [];
      this.nodeKey = val;
      this.defaultProps.label = val === 'orgCode' ? 'orgName' : 'regionName';
      this.treePlaceholder = `请选择${val === 'orgCode' ? '组织机构' : '行政区划'}`;
    },
  },
  watch: {},
  components: {
    SelectOrganizationTreeCheckall: require('@/api-components/select-organization-tree-checkall.vue').default,
  },
};
</script>

<style lang="less" scoped>
.export-data {
}
@{_deep} .ivu-modal {
  width: 520px;
  .ivu-modal-header {
    margin-bottom: 0;
  }
  &-body {
    padding: 70px 20px 140px !important;
  }
  .export-content {
    height: 420px;
    overflow: auto;
    position: relative;
  }
}
@{_deep} .select-organization-tree {
  width: 90% !important;
  .ivu-dropdown .ivu-select-dropdown {
    min-height: 150px;
    max-height: 150px;
  }
}
</style>
