<template>
  <ui-modal v-model="visible" :title="type === 'add' ? '新增目录' : '编辑'" :r-width="450" @onOk="comfirmHandle">
    <Form ref="form" :model="form" :rules="ruleForm" class="form">
      <!-- 新增目录-->
      <FormItem label="上级目录" v-if="type === 'add'" :label-width="80" prop="parentId">
        <ui-treeSelect v-if="visible" class="dialog-input select-node-tree filter-tree" v-model="form.parentId" filterable check-strictly placeholder="请选择" default-expand-all show-checkbox :expand-on-click-node="false" check-on-click-node node-key="id" :treeData="catalogList" />
      </FormItem>
      <!-- 编辑 -->
      <FormItem label="目录名称" :label-width="80" prop="catalogName">
        <Input v-model="form.catalogName" :maxlength="20" placeholder="请输入" />
      </FormItem>
    </Form>
  </ui-modal>
</template>
<script>
import { addCatalog, updateCatalog } from '@/api/dataGovernance'
import UiTreeSelect from '@/components/ui-tree-select'
export default {
  components: {
    UiTreeSelect
  },
  props: {
    // 上级目录
    list: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      type: 'add', //新增/编辑
      visible: false,
      catalogList: [], //目录树列表
      form: {
        catalogName: '',
        parentId: ''
      },
      ruleForm: {
        //表单目录名称校验
        catalogName: [{ required: true, message: '请输入', trigger: 'blur' }]
      }
    }
  },
  methods: {
    // 初始化
    show(item) {
      this.type = item ? 'edit' : 'add'
      this.visible = true
      this.catalogList = this.getTree(this.list)
      this.$nextTick(() => {
        this.$refs.form.resetFields()
        if (this.type === 'edit') {
          // 编辑字段回显
          this.form = {
            catalogName: item.catalogName,
            id: item.id
          }
        }
      })
    },
    // 递归
    getTree(tree = []) {
      let arr = []
      if (!!tree && tree.length !== 0) {
        tree.forEach(item => {
          // 目录树重新定义字段
          let obj = {
            title: item.catalogName,
            id: item.id,
            parentId: item.parentId,
            children: this.getTree(item.list)
          }
          arr.push(obj)
        })
      }
      return arr
    },
    // 确认提交
    comfirmHandle() {
      this.$refs.form.validate(valid => {
        if (valid) {
          // 新增
          if (this.type === 'add') {
            addCatalog({
              catalogName: this.form.catalogName,
              parentId: this.form.parentId || '0'
            }).then(res => {
              this.visible = false
              this.$Message.success(res.msg)
              this.$emit('refreshDataList')
            })
          } else {
            //编辑
            updateCatalog({
              catalogName: this.form.catalogName,
              id: this.form.id
            }).then(res => {
              this.visible = false
              this.$Message.success(res.msg)
              this.$emit('refreshDataList')
            })
          }
        }
      })
    }
  }
}
</script>
<style lang="less" scoped>
.dialog-input {
  width: 330px;
}
// .form {
//   height: 200px;
// }
</style>
