<template>
  <div class="archives-statistics-container">
    <div class="search-from mb-10">
      <Form ref="formData" inline>
        <FormItem label="统计日期" :label-width="80">
          <DatePicker
            v-model="startEndTime"
            class="date-picker"
            type="datetimerange"
            placement="bottom-end"
            :options="datePickerOptions"
            transfer
            :clearable="false"
            format="yyyy-MM-dd HH:mm:ss"
            @on-change="datePickerChange"
            placeholder="请选择时间"
          >
          </DatePicker>
        </FormItem>
        <FormItem>
          <Button type="primary" class="ml-15 mr-10" @click="handleSearch"
            >查询</Button
          >
          <Button type="default" @click="handleReset">重置</Button>
        </FormItem>
      </Form>
    </div>
    <div class="header mt-20">
      <span class="header-label">人员档案统计</span>
      <span class="header-content"
        >档案总数：<countTo
          class="header-count"
          :start-val="0"
          :end-val="statisticsData.allPersonArchiveCount || 0"
        ></countTo
      ></span>
    </div>
    <div class="personnel-archives">
      <div class="archives-wrapper">
        <div
          class="statistics-border statistics-detail"
          v-for="item in personStatisticsList"
          :key="item.archivesKey"
        >
          <div class="statistics-image ml-50">
            <img :src="item.icon" alt="" height="100%" width="100%" />
          </div>
          <div class="statistics-content">
            <div class="desc">{{ item.title }}</div>
            <countTo
              :class="[item.style, 'count']"
              :start-val="0"
              :end-val="statisticsData[item.archivesKey] || 0"
            ></countTo>
            <div class="time-wrapper">
              <div>
                <div :class="[item.dotStyle, 'dot']">
                  <span class="time-range f-12">近7日活跃</span>
                </div>
                <div class="time-count">
                  {{ statisticsData[item.recentlyDayKey] || 0 }}
                </div>
              </div>
              <div class="ml-80">
                <div :class="[item.dotStyle, 'dot']">
                  <span class="time-range f-12">今日新出现</span>
                </div>
                <div class="time-count">
                  {{ statisticsData[item.todayDayKey] || 0 }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="trend-wrapper">
        <div class="trend-echarts">
          <div class="trend-header">
            <span class="desc">每日新出现趋势</span>
          </div>
          <div class="echarts-wrapper">
            <echarts :options="everyDayEchartsOptions"></echarts>
          </div>
        </div>
        <div class="trend-echarts">
          <div class="trend-header">
            <span class="desc">重点人每日新出现趋势</span>
          </div>
          <div class="echarts-wrapper">
            <echarts :options="zdrEveryDayActiveEchartsOptions"></echarts>
          </div>
        </div>
      </div>
    </div>
    <div class="header mt-20">
      <span class="header-label">车辆档案统计</span>
      <span class="header-content"
        >档案总数：<countTo
          class="header-count"
          :start-val="0"
          :end-val="statisticsData.allVehicleArchiveCount || 0"
        ></countTo
      ></span>
    </div>
    <div class="personnel-archives">
      <div class="archives-wrapper">
        <div
          class="statistics-border statistics-detail"
          v-for="item in vehicleStatisticsList"
          :key="item.archivesKey"
        >
          <div class="statistics-image-vehicle ml-20">
            <img :src="item.icon" alt="" height="100%" width="100%" />
          </div>
          <div class="statistics-content">
            <div class="desc">{{ item.title }}</div>
            <countTo
              :class="[item.style, 'count']"
              :start-val="0"
              :end-val="statisticsData[item.archivesKey] || 0"
            ></countTo>
            <div class="time-wrapper">
              <div>
                <div :class="[item.dotStyle, 'dot']">
                  <span class="time-range f-12">近7日活跃</span>
                </div>
                <div class="time-count">
                  {{ statisticsData[item.recentlyDayKey] || 0 }}
                </div>
              </div>
              <div class="ml-80">
                <div :class="[item.dotStyle, 'dot']">
                  <span class="time-range f-12">今日新出现</span>
                </div>
                <div class="time-count">
                  {{ statisticsData[item.todayDayKey] || 0 }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="trend-wrapper">
        <div class="trend-echarts">
          <div class="trend-header">
            <span class="desc">机动车每日新出现趋垫</span>
          </div>
          <div class="echarts-wrapper">
            <echarts :options="vehicleEveryDayAppearEchartsOptions"></echarts>
          </div>
        </div>
        <div class="trend-echarts">
          <div class="trend-header">
            <span class="desc">非机动车每日新出现趋势</span>
          </div>
          <div class="echarts-wrapper">
            <echarts
              :options="nonMotorVehicleEveryDayAppearEchartsOptions"
            ></echarts>
          </div>
        </div>
      </div>
    </div>
    <ui-loading v-if="statisticsLoading" />
  </div>
</template>
<script>
import duration from "dayjs/plugin/duration";
import CountTo from "vue-count-to";
import Echarts from "@/components/ui-echarts.vue";
import {
  everyDayTrendEcharts,
  nonMotorVehicleEveryDayTrendEcharts,
  vehicleEveryDayTrendEcharts,
  zdrEveryDayTrendEcharts,
} from "@/views/holographic-archives/archives-statistics/echarts/echartsOption.js";
import {
  queryArchiveStatisticsData,
  queryChartStatisticsData,
} from "@/api/home";
export default {
  components: { CountTo, Echarts },
  props: {},
  filters: {
    filterCount(val) {
      if (!val) return 0;
      return Number((Number(val) / 10000).toFixed(2));
    },
  },
  data() {
    return {
      startEndTime: ["", ""],
      datePickerOptions: {
        disabledDate(date) {
          const today = new Date();
          const daysAgo = new Date(today);
          daysAgo.setMonth(today.getMonth() - 1);
          return (
            date.getTime() > today.getTime() ||
            date.getTime() < daysAgo.getTime()
          );
        },
      },
      searchForm: {
        startTime: "",
        endTime: "",
      },
      statisticsData: {},
      chartStatisticsData: {},
      statisticsLoading: false,
      options: {},
      everyDayEchartsOptions: {}, //每日新出现趋势
      zdrEveryDayActiveEchartsOptions: {}, //重点人每日活跃趋势
      vehicleEveryDayAppearEchartsOptions: {}, //机动车每日新出现趋垫
      nonMotorVehicleEveryDayAppearEchartsOptions: {}, //非机动车每日新出现趋势
      dateList: [],
      personStatisticsList: [
        {
          title: "实名档案",
          dotStyle: "bg-cyan",
          style: "color-cyan",
          icon: require(`@/assets/img/archives-statistics/real-name-statistics.png`),
          archivesKey: "realNameCount",
          recentlyDayKey: "realNameActiveCount",
          todayDayKey: "realNameTodayCount",
        },
        {
          title: "视频档案",
          dotStyle: "bg-yellow",
          style: "color-yellow",
          icon: require(`@/assets/img/archives-statistics/anonymous-statistics.png`),
          archivesKey: "vidFaceCount",
          recentlyDayKey: "vidArchiveActiveCount",
          todayDayKey: "vidFaceTodayCount",
        },
        {
          title: "重点人员档案",
          dotStyle: "bg-red",
          style: "color-red",
          icon: require(`@/assets/img/archives-statistics/zdr-statistics.png`),
          archivesKey: "importantCount",
          recentlyDayKey: "importantActiveCount",
          todayDayKey: "importantTodayCount",
        },
      ],
      vehicleStatisticsList: [
        {
          title: "机动车档案",
          dotStyle: "bg-purple",
          style: "color-purple",
          icon: require(`@/assets/img/archives-statistics/motor-vehicle-statistics.png`),
          archivesKey: "vehicleCount",
          recentlyDayKey: "vehicleActiveCount",
          todayDayKey: "vehicleTodayCount",
        },
        {
          title: "非机动车档案",
          dotStyle: "bg-light-blue",
          style: "color-light-blue",
          icon: require(`@/assets/img/archives-statistics/non-motor-vehicle-statistics.png`),
          archivesKey: "nonmotorCount",
          recentlyDayKey: "nonMotorActiveCount",
          todayDayKey: "nonMotorTodayCount",
        },
      ],
    };
  },
  async mounted() {
    this.resetDate();
    this.generateDate(this.searchForm.startTime, this.searchForm.endTime);
    await this.getStatisticsData();
    await this.getChartStatisticsData();
    this.getEveryDayEchartsOptions();
    this.getZdrEveryDayActiveEchartsOptions();
    this.getVehicleEveryDayAppearEchartsOptions();
    this.getNonMotorVehicleEveryDayAppearEchartsOptions();
  },
  methods: {
    datePickerChange(val) {
      let [startTime = "", endTime = ""] = val;
      this.searchForm.startTime = startTime;
      this.searchForm.endTime = endTime;
    },
    async handleSearch() {
      if (!this.searchForm.startTime || !this.searchForm.endTime) {
        this.resetDate();
      }
      this.generateDate(this.searchForm.startTime, this.searchForm.endTime);
      await this.getChartStatisticsData();
      this.getEveryDayEchartsOptions();
      this.getZdrEveryDayActiveEchartsOptions();
      this.getVehicleEveryDayAppearEchartsOptions();
      this.getNonMotorVehicleEveryDayAppearEchartsOptions();
    },
    async handleReset() {
      this.resetDate();
      this.generateDate(this.searchForm.startTime, this.searchForm.endTime);
      await this.getChartStatisticsData();
      this.getEveryDayEchartsOptions();
      this.getZdrEveryDayActiveEchartsOptions();
      this.getVehicleEveryDayAppearEchartsOptions();
      this.getNonMotorVehicleEveryDayAppearEchartsOptions();
    },
    getEveryDayEchartsOptions() {
      try {
        let monthRealNameCountList = [];
        let monthVidCountList = [];
        let { monthRealNameCount = {}, monthVidCount = {} } =
          this.chartStatisticsData;
        this.dateList.forEach((item) => {
          //实名档案
          monthRealNameCountList.push(
            this.$options.filters.filterCount(monthRealNameCount[item] || 0)
          );
          monthVidCountList.push(
            this.$options.filters.filterCount(monthVidCount[item] || 0)
          );
        });
        let options = {
          xAxisData: this.dateList,
          legend: ["实名档案", "视频档案"],
          series: {
            monthRealNameCountList,
            monthVidCountList,
          },
        };
        this.everyDayEchartsOptions = everyDayTrendEcharts(options);
      } catch (e) {
        console.log(e);
      }
    },
    getZdrEveryDayActiveEchartsOptions() {
      try {
        let monthImportantCountList = [];
        let { monthImportantCount = {} } = this.chartStatisticsData;
        this.dateList.forEach((item) => {
          monthImportantCountList.push(
            this.$options.filters.filterCount(monthImportantCount[item] || 0)
          );
        });
        let options = {
          xAxisData: this.dateList,
          legend: ["活跃档案"],
          series: {
            monthImportantCountList,
          },
        };
        this.zdrEveryDayActiveEchartsOptions = zdrEveryDayTrendEcharts(options);
      } catch (e) {
        console.log(e);
      }
    },
    getVehicleEveryDayAppearEchartsOptions() {
      try {
        let monthVehicleCountList = [];
        let { monthVehicleCount = {} } = this.chartStatisticsData;
        this.dateList.forEach((item) => {
          monthVehicleCountList.push(
            this.$options.filters.filterCount(monthVehicleCount[item] || 0)
          );
        });
        let options = {
          xAxisData: this.dateList,
          legend: ["活跃档案"],
          series: {
            monthVehicleCountList,
          },
        };
        this.vehicleEveryDayAppearEchartsOptions =
          vehicleEveryDayTrendEcharts(options);
      } catch (e) {
        console.log(e);
      }
    },
    getNonMotorVehicleEveryDayAppearEchartsOptions() {
      let monthNonVehicleCountList = [];
      let { monthNonVehicleCount = {} } = this.chartStatisticsData;
      this.dateList.forEach((item) => {
        monthNonVehicleCountList.push(
          this.$options.filters.filterCount(monthNonVehicleCount[item] || 0)
        );
      });
      let options = {
        xAxisData: this.dateList,
        legend: ["活跃档案"],
        series: {
          monthNonVehicleCountList,
        },
      };
      this.nonMotorVehicleEveryDayAppearEchartsOptions =
        nonMotorVehicleEveryDayTrendEcharts(options);
    },
    generateTimePoints(startTime, endTime, intervalDays) {
      const start = this.$dayjs(startTime);
      const end = this.$dayjs(endTime);
      const timePoints = [];

      let current = start;
      while (current.isBefore(end) || current.isSame(end)) {
        timePoints.push(current.format("YYYY-MM-DD"));
        current = current.add(intervalDays, "day");
      }
      return timePoints;
    },
    resetDate() {
      this.startEndTime = ["", ""];
      const today = this.$dayjs();
      const startDay = today.subtract(30, "day");
      this.searchForm.startTime = startDay.format("YYYY-MM-DD 00:00:00");
      this.searchForm.endTime = today.format("YYYY-MM-DD 00:00:00");
      this.startEndTime = [this.searchForm.startTime, this.searchForm.endTime];
    },
    generateDate(startDay, today) {
      this.dateList = this.generateTimePoints(startDay, today, 1);
    },
    async getStatisticsData() {
      try {
        this.statisticsLoading = true;
        let { data } = await queryArchiveStatisticsData();
        this.statisticsData = data;
      } catch (e) {
        console.log(e);
      } finally {
        this.statisticsLoading = false;
      }
    },
    async getChartStatisticsData() {
      try {
        this.statisticsLoading = true;
        let { data } = await queryChartStatisticsData(this.searchForm);
        this.chartStatisticsData = data;
      } catch (e) {
        console.log(e);
      } finally {
        this.statisticsLoading = false;
      }
    },
  },
};
</script>
<style lang="less" scoped>
@green: #1faf81;
@yellow: #f29f4c;
@red: #ea4a36;
@purple: #a786ff;
@blue: #48baff;

.color-cyan {
  color: @green;
}
.color-yellow {
  color: @yellow;
}
.color-red {
  color: @red;
}
.color-purple {
  color: @purple;
}
.color-light-blue {
  color: @blue;
}

.bg-cyan {
  &:before {
    background: @green;
  }
}
.bg-yellow {
  &:before {
    background: @yellow;
  }
}
.bg-red {
  &:before {
    background: @red;
  }
}
.bg-purple {
  &:before {
    background: @purple;
  }
}
.bg-light-blue {
  &:before {
    background: @blue;
  }
}
.ml-80 {
  margin-left: 80px;
}
.ml-50 {
  margin-left: 50px;
}
.statistics-border {
  border-radius: 4px;
  border: 1px solid #d3d7de;
}
.dot {
  position: relative;
  &:before {
    content: "";
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 8px;
    height: 8px;
    border-radius: 20px;
    //background: #1faf81;
  }
}

.archives-statistics-container {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: auto;
  border-radius: 4px;
  background: #ffffff;
  box-shadow: 0 3px 5px 0 rgba(147, 171, 206, 0.7);
  padding: 20px;
  .search-from {
    border-bottom: 1px solid #d3d7de;
    /deep/.ivu-form-item {
      margin-bottom: 15px;
    }
    .date-picker {
      width: 330px;
    }
  }
  .header {
    position: relative;
    display: flex;
    justify-content: space-between;

    .header-label {
      font-size: 18px;
      color: rgba(0, 0, 0, 0.9);
      margin-left: 15px;
      font-weight: 700;
    }
    .header-content {
      font-size: 18px;
      color: rgba(0, 0, 0, 0.6);
      font-weight: 700;
      .header-count {
        color: #2c86f8;
      }
    }

    &:before {
      content: "";
      position: absolute;
      width: 5px;
      height: 20px;
      top: 50%;
      transform: translateY(-50%);
      background: #2c86f8;
    }
  }

  .personnel-archives {
    position: relative;
    width: 100%;
    margin-top: 24px;

    .archives-wrapper {
      display: flex;

      .statistics-detail {
        margin-right: 20px;
        height: 190px;
        flex: 1;
        display: flex;
        justify-content: flex-start;
        align-items: center;

        &:last-child {
          margin-right: 0;
        }

        .statistics-image {
          width: 120px;
          height: 115.08px;
        }
        .statistics-image-vehicle {
          width: 165px;
          height: 139px;
        }
        .statistics-content {
          margin-left: 40px;
          .desc {
            font-weight: 700;
            font-size: 16px;
            color: rgba(0, 0, 0, 0.6);
          }
          .count {
            font-size: 38px;
            //color: #1faf81;
          }
          .time-wrapper {
            margin-top: 20px;
            display: flex;
            justify-content: space-between;
            .time-range {
              margin-left: 20px;
            }
            .time-count {
              margin-left: 20px;
              color: rgba(0, 0, 0, 0.9);
              font-size: 16px;
              font-weight: 700;
            }
          }
        }
      }
    }
    .trend-wrapper {
      position: relative;
      display: flex;
      margin-top: 20px;
      .trend-echarts {
        height: 316px;
        border: 1px solid #d3d7de;
        background: #ffffff;
        overflow: hidden;
        flex: 1;
        margin-right: 20px;
        &:last-child {
          margin-right: 0;
        }
        .trend-header {
          height: 40px;
          line-height: 40px;
          background: #ebedf1;
          width: 100%;
          .desc {
            font-weight: 700;
            font-size: 16px;
            margin-left: 10px;
          }
        }
        .echarts-wrapper {
          height: calc(~"100% - 40px");
        }
      }
    }
  }
}
</style>
