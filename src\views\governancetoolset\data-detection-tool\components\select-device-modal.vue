<template>
  <ui-modal v-model="visible" :title="modalAction.title" :styles="styles" @query="submitHandle">
    <div class="model-wrapper">
      <section class="left-content">
        <p class="content-title base-text-color mb-sm">
          待选择设备列表 <span class="font-red">{{ pageData.totalCount }}</span> 条
        </p>
        <ui-search-tree
          placeholder="请输入组织机构名称或组织机构编码"
          node-key="orgCode"
          :max-height="600"
          :tree-data="treeData"
          :default-props="defaultProps"
          :default-keys="defaultExpandedKeys"
          :current-node-key="currentNodeKey"
          @selectTree="selectTree"
        >
        </ui-search-tree>
      </section>
      <section class="middle-content">
        <div class="search-box content-title">
          <ui-label label="关键词" :width="60" class="inline search-input">
            <Input
              class="width-lg"
              suffix="ios-search"
              v-model="searchData.keyWord"
              placeholder="请输入设备名称或编码"
              @on-enter.native="searchKeyword"
            />
            <div class="ml-lg">
              <Button type="primary" class="mr-sm" @click="searchKeyword">查询</Button>
              <Button type="default" class="mr-lg" @click="resetSearchDataMx(searchData, searchKeyword)">重置</Button>
            </div>
          </ui-label>
        </div>
        <div class="table-box auto-fill">
          <ui-table
            ref="leftTable"
            class="ui-table auto-fill"
            :table-columns="tableColumns"
            :table-data="tableData"
            :loading="loading"
            @oneSelected="oneSelected"
            @cancelSelectTable="cancelSelectTable"
            @onSelectAllTable="onSelectAllTable"
            @cacelAllSelectTable="cacelAllSelectTable"
          >
            <template #longitude="{ row }">
              <span>{{ row.longitude | filterLngLat }}</span>
            </template>
            <template #latitude="{ row }">
              <span>{{ row.latitude | filterLngLat }}</span>
            </template>
            <template #orgName="{ row }">
              <div class="width-percent inline ellipsis" :class="row.rowClass" :title="row.orgName">
                {{ row.orgName }}
              </div>
            </template>
            <template slot="deviceId" slot-scope="{ row }">
              <span class="font-active-color pointer" @click="deviceArchives(row)">{{ row.deviceId }}</span>
            </template>
          </ui-table>
        </div>
        <ui-page
          class="page menu-content-background"
          :page-data="pageData"
          :simple-page="true"
          @changePage="changePage"
          @changePageSize="changePageSize"
        >
        </ui-page>
      </section>
      <div class="arrow-box">
        <double-arrow></double-arrow>
      </div>
      <section class="right-content auto-fill">
        <div class="content-title">
          <p class="base-text-color">
            已选择设备 <span class="font-red">{{ chooseTableData.length }}</span> 条
          </p>
          <div class="base-text-color">
            <Button type="primary" @click="removeDeviceHandle()">批量移除</Button>
          </div>
        </div>
        <ui-table
          ref="rightTable"
          class="ui-table auto-fill"
          :table-columns="rightTableColumns"
          :table-data="chooseTableData"
          @selectTable="handlePreviewSelectionChange"
        >
          <template slot="deviceId" slot-scope="{ row }">
            <span class="font-active-color pointer" @click="deviceArchives(row)">{{ row.deviceId }}</span>
          </template>
          <template #longitude="{ row }">
            <span>{{ row.longitude | filterLngLat }}</span>
          </template>
          <template #latitude="{ row }">
            <span>{{ row.latitude | filterLngLat }}</span>
          </template>
          <template #orgName="{ row }">
            <div class="width-percent inline ellipsis" :class="row.rowClass" :title="row.orgName">
              {{ row.orgName }}
            </div>
          </template>
          <template #actionSlot="{ row }">
            <ui-btn-tip
              class="operatbtn"
              icon="icon-yichu1"
              content="移除"
              @click.native="removeDeviceHandle(row)"
            ></ui-btn-tip>
          </template>
        </ui-table>
      </section>
    </div>
  </ui-modal>
</template>
<script>
import catalogmanagement from '@/config/api/catalogmanagement';
import { mapGetters, mapActions } from 'vuex';
import global from '@/util/global';

const tableColumns = [
  { type: 'selection', width: 50, align: 'center', fixed: 'left' },
  { title: '序号', width: 50, type: 'index', align: 'center' },
  { title: `${global.filedEnum.deviceId}`, slot: 'deviceId', align: 'left', width: 170 },
  { title: `${global.filedEnum.deviceName}`, minWidth: 150, key: 'deviceName', align: 'left', tooltip: true },
  { title: '行政区划', minWidth: 120, slot: 'orgName', key: 'orgName', align: 'left', tooltip: true },
  { title: 'ip', minWidth: 120, align: 'left', key: 'ipAddr' },
  { title: '端口号', minWidth: 80, align: 'left', key: 'port' },
  { title: '通道号', minWidth: 100, align: 'left', key: 'tdh' },
  { title: '账号', minWidth: 100, align: 'left', key: 'userId' },
  { title: '密码', minWidth: 100, align: 'left', key: 'password' },
  { title: '经度', minWidth: 90, slot: 'longitude', align: 'left', key: 'longitude' },
  { title: '纬度', minWidth: 90, slot: 'latitude', align: 'left', key: 'latitude' },
  { title: '安装地址', minWidth: 150, key: 'address', align: 'left', tooltip: true },
];
export default {
  props: {
    toolType: {
      type: String,
      default: '1',
    },
    selectedDeviceList: {
      type: [],
      default: () => [],
    },
  },
  components: {
    UiSearchTree: require('@/components/ui-search-tree.vue').default,
    UiTable: require('@/components/ui-table.vue').default,
    DoubleArrow: require('@/components/double-arrow.vue').default,
  },
  data() {
    return {
      modalAction: {
        title: '系统选择',
      },
      keyword: '',
      defaultProps: {
        label: 'orgName',
        children: 'children',
      },
      tableData: [],
      chooseTableData: [],
      dataPreviewListSelections: [], // 右侧复选框选项
      pageData: {
        totalCount: 0,
        pageNum: 1,
        pageSize: 20,
      },
      visible: false,
      styles: {
        width: '1850px',
      },
      loading: false,
      minusTable: 300,
      searchData: {
        keyWord: '',
        orgCode: null,
        pageNumber: 1,
        pageSize: 20,
        checkStatuses: [],
      },
      connectingOptions: {
        width: '1.2%',
        height: '0.04rem',
        top: '50%',
        left: '13.6%',
      },
      allDeviceChecked: false,
      currentNodeKey: '',
    };
  },
  computed: {
    ...mapGetters({
      treeData: 'common/getOrganizationList',
      getDefaultSelectedOrg: 'common/getDefaultSelectedOrg',
      defaultExpandedKeys: 'common/getDefaultOrgExpandedKeys',
    }),
    tableColumns() {
      let keys = null;
      switch (this.toolType) {
        case '1':
          keys = ['orgName', 'address', 'latitude', 'longitude'];
          break;
        case '2':
          keys = ['ipAddr', 'address', 'latitude', 'longitude', 'port', 'userId', 'password', 'tdh'];
          break;
        case '3':
          keys = ['ipAddr', 'port', 'userId', 'password', 'tdh'];
          break;
        default:
          keys = [];
          break;
      }
      return tableColumns.filter((item) => !keys.some((d) => item.key === d));
    },
    rightTableColumns() {
      let columns = [...this.tableColumns];
      columns.push({ title: '操作', key: 'action', slot: 'actionSlot', align: 'center', width: 60, fixed: 'right' });
      return columns;
    },
  },
  created() {
    this.setOrganizationList();
    this.selectTree(this.getDefaultSelectedOrg);
    this.currentNodeKey = this.getDefaultSelectedOrg.orgCode;
  },
  mounted() {
    this.copySearchDataMx(this.searchData);
  },
  methods: {
    ...mapActions({
      setOrganizationList: 'common/setOrganizationList',
    }),
    init() {
      this.visible = true;
      this.currentNodeKey = '';
      this.allDeviceChecked = false;
      this.searchData = {
        keyWord: '',
        orgCode: null,
        pageNumber: 1,
        pageSize: 20,
        checkStatuses: [],
      };
      this.pageData.pageNum = 1;
      this.pageData.pageSize = 20;
      this.chooseTableData = [...this.selectedDeviceList];
      this.setOrganizationList();
      this.selectTree(this.getDefaultSelectedOrg);
      this.currentNodeKey = this.getDefaultSelectedOrg.orgCode;
      this.handleData();
    },
    // 跳转详情
    deviceArchives(item) {
      let routeData = this.$router.resolve({
        name: 'archives',
        query: { id: item.id },
      });
      window.open(routeData.href, '_blank');
    },
    // 移除设备
    removeDeviceHandle(row) {
      const removeItems = row ? [row] : this.dataPreviewListSelections;
      if (removeItems.length === 0) {
        this.$Message.warning('请先选择移除项');
        return false;
      }
      removeItems.forEach((item) => {
        // 移除右侧已选设备
        const rightIndex = this.chooseTableData.findIndex((d) => d.id === item.id);
        this.chooseTableData.splice(rightIndex, 1);
      });
      if (row) {
        this.dataPreviewListSelections = [];
      } else {
        this.dataPreviewListSelections;
      }
      this.handleData(); //重新初始化 左侧数据
    },
    oneSelected(selection, row) {
      this.chooseTableData.push({ ...row });
    },
    cancelSelectTable(selection, row) {
      let rowId = this.tableData.find((item) => item.id === row.id);
      this.chooseTableData = this.chooseTableData.filter((item) => item.id !== rowId.id);
    },
    // 全选左侧设备
    onSelectAllTable() {
      let selectDataObject = {};
      this.chooseTableData.forEach((item) => {
        selectDataObject[item.id] = item;
      });
      this.tableData.forEach((row) => {
        // 没有就push进去
        if (!selectDataObject.hasOwnProperty(row.id)) {
          this.chooseTableData.push({ ...row });
        }
      });
    },
    // 取消左侧设备
    cacelAllSelectTable() {
      let tableDataIds = this.tableData.map((item) => item.id);
      this.chooseTableData = this.chooseTableData.filter((row) => !tableDataIds.includes(row.id));
    },
    // 选择右侧复选框
    handlePreviewSelectionChange(items) {
      this.dataPreviewListSelections = items;
    },
    changePage(val) {
      this.pageData.pageNum = val;
      this.searchData.pageNumber = val;
      this.initAllData();
    },
    changePageSize(val) {
      this.pageData.pageSize = val;
      this.searchData.pageSize = val;
      this.initAllData();
    },
    selectTree(data) {
      this.searchData.orgCode = data.orgCode;
      this.initAllData();
    },
    // 搜索方法
    searchKeyword() {
      this.searchData.pageNumber = 1;
      this.pageData.pageNum = 1;
      this.initAllData();
    },
    // 设置选择
    handleData() {
      let selectDataObject = {};
      this.chooseTableData.forEach((item) => {
        selectDataObject[item.id] = item;
      });
      this.tableData.forEach((row) => {
        if (selectDataObject.hasOwnProperty(row.id)) {
          this.$set(row, '_checked', true);
        } else {
          this.$set(row, '_checked', false);
        }
      });
    },
    // 查询设备数据列表
    async initAllData() {
      this.loading = true;
      this.tableData = [];
      try {
        let res = await this.$http.post(catalogmanagement.getPageDeviceList, this.searchData);
        this.tableData = res.data.data.entities;
        this.handleData();
        this.pageData.totalCount = res.data.data.total;
        this.loading = false;
      } catch (err) {
        this.loading = false;
      } finally {
        this.loading = false;
      }
    },
    submitHandle() {
      this.visible = false;
      const chooseTableData = [...this.chooseTableData];
      this.$emit('setDeviceHandle', chooseTableData);
    },
  },
};
</script>
<style lang="less" scoped>
.model-wrapper {
  display: flex;
  .content-title {
    height: 65px;
    line-height: 65px;
    padding-left: 20px;
  }
  .left-content {
    width: 304px;
    border-right: 1px solid var(--devider-line);
    border-bottom: 1px solid var(--devider-line);
    margin-right: 10px;
    position: relative;
    &:before {
      content: '';
      position: absolute;
      width: 1px;
      background: var(--devider-line);
      height: 100px;
      right: -1px;
      top: -30px;
    }
    .ui-search-tree {
      padding: 0 20px;
    }
  }
  .middle-content,
  .right-content {
    min-width: 715px;
    flex: 1;
    border: 1px solid var(--devider-line);
  }
  .middle-content {
    .search-box {
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
    .table-box {
      height: 600px;
    }
    .search-input {
      position: relative;
      display: flex;
      .icon-sousuo {
        position: absolute;
        right: 10px;
      }
    }
    .ui-table {
      width: 100%;
    }
  }
  .right-content {
    .content-title {
      display: flex;
      justify-content: space-between;
      padding: 0 20px;
    }
  }
  .font-green-color {
    color: var(--color-bluish-green-text);
  }
  .arrow-box {
    height: 600px;
    display: flex;
    align-items: center;
    margin: 0 10px;
  }
}
/deep/ .ivu-modal {
  .ivu-modal-body {
    padding-top: 0;
    padding-bottom: 0;
    padding-left: 0;
  }
  .ivu-modal-footer {
    border-top: 0 !important;
  }
}
</style>
