<template>
  <!-- 在线情况分析 -->
  <div class="online-situation-content">
    <div class="online-search">
      <div class="statistical-duration">
        <b>在线天数：</b>
        <span class="color-success">{{ info.onlineTotal }}</span>
        <b>离线天数：</b>
        <span class="color-info">{{ info.offlineTotal }}</span>
        <b>未检测天数：</b>
        <span class="color-warning">{{ info.unEvaluationTotal }}</span>
        <p class="statistical-status">
          <span class="bg-success"></span>
          <label class="auxiliary-color mr-20">当前在线</label>
          <span class="bg-info"></span>
          <label class="auxiliary-color">当前离线</label>
        </p>
      </div>
    </div>
    <p class="label-color time-check">
      <label class="mr-10">日期:</label>
      <DatePicker type="month" placeholder="请选择" format="yyyy年MM月" placement="top" v-model="yearMonth" :editable="false" :clearable="false" transfer @on-change="handleChange"></DatePicker>
    </p>
    <div v-if="list.length !== 0">
      <Calender class="calender" format="MM月dd日" :year-month="yearMonth">
        <template #calenderDay="{ label, value, notThisMonth }">
          <div class="calender-item">
            <span>{{ label }}</span>
            <span v-show="!notThisMonth" :class="isOnline(value)"></span>
          </div>
        </template>
      </Calender>
    </div>
    <ui-loading v-if="loading" />
    <ui-empty v-if="list.length === 0"></ui-empty>
  </div>
</template>

<script>
import { queryOnlineVideo } from '@/api/device'
import Calender from '@/components/calender'
export default {
  components: { Calender },
  name: 'calender',
  props: {
    deviceId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      loading: false,
      list: [],
      info: {},
      yearMonth: '' //月份
    }
  },
  created() {
    let year = new Date().getFullYear()
    let month = new Date().getMonth() + 1
    let monthIndex = month > 10 ? month : `0${month}`
    this.yearMonth = `${year}-${monthIndex}`
    this.queryOnlineVideo()
  },
  methods: {
    getDate() {
      let year = new Date().getFullYear()
      let month = new Date().getMonth() + 1
      let monthIndex = month > 10 ? month : `0${month}`
      this.yearMonth = `${year}-${monthIndex}`
    },
    // 视频在线情况分析
    queryOnlineVideo() {
      this.loading = true
      let data = {
        deviceId: this.deviceId,
        videoTime: this.yearMonth //1：近一月，2：近三月
      }
      queryOnlineVideo(data)
        .then(res => {
          const { offlineTotal, onlineTotal, unEvaluationTotal, deviceStatusList } = res.data
          this.info = {
            offlineTotal,
            onlineTotal,
            unEvaluationTotal
          }
          this.list = deviceStatusList
        })
        .catch(() => {})
        .finally(() => {
          this.loading = false
        })
    },
    handleChange(value) {
      let yearNumber = value.slice(0, 4)
      let monthNumber = value.slice(5, 7)
      this.yearMonth = `${yearNumber}-${monthNumber}`
      this.queryOnlineVideo()
    },
    isOnline(val) {
      const dayCheck = this.list.find(row => row.deviceOnlineDate === val)
      if (dayCheck && dayCheck.hasOwnProperty('deviceOnlineStatus')) {
        if (dayCheck.deviceOnlineStatus === '1') {
          return 'badge-online'
        } else if (dayCheck.deviceOnlineStatus === '2') {
          return 'badge-offline'
        }
      }
    }
  }
}
</script>
<style scoped lang="less">
.online-situation-content {
  height: 100%;
  width: 100%;
  position: relative;
  .online-search {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    justify-content: space-between;
    position: absolute;
    top: -40px;
    left: 12%;
    /deep/.ivu-input {
      width: 200px;
      height: 34px;
    }
    .statistical-duration {
      display: flex;
      align-items: center;
      flex-grow: 3;
      span {
        margin-right: 30px;
        font-weight: bold;
      }
      .statistical-status {
        margin-left: 80px;
        span {
          display: inline-block;
          width: 8px;
          height: 8px;
          border-radius: 50%;
          margin-right: 5px;
        }
        label {
          font-size: 12px !important;
        }
      }
    }
  }
  .time-check {
    position: absolute;
    top: -40px;
    right: 0;
  }
  .badge-online,
  .badge-offline {
    position: relative;
    display: inline-block;
    width: auto;
  }

  .badge-online:before,
  .badge-offline:before {
    content: '';
    width: 12px;
    height: 12px;
    border-radius: 50%;
    position: absolute;
    right: -45px;
    top: -10px;
  }
  /deep/.ivu-icon-ios-time-outline:before {
    content: '\F15B';
  }
}
</style>
