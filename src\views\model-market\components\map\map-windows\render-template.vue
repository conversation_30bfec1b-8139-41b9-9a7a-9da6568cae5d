<script>
export default {
  name: "render-template",
  props: {
    renderItem: {
      type: Function,
      default: () => {},
    },
    data: {
      type: Object,
      default: () => ({}),
    },
    item: {
      type: Object,
      default: () => ({}),
    },
    marketData: {
      type: Object,
      default: () => ({}),
    },
  },
  render(h) {
    const { renderItem, ...props } = this.$props;
    return this.renderItem(h, props);
  },
};
</script>
