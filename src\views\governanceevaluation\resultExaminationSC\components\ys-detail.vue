// 以撒、优云的详情展示
<template>
  <ui-modal v-model="visible" :title="title" :width="80" footerHide class="ys-detail">
    <div class="over-flow mb-sm">
      <Button type="primary" class="fr" @click="exportHandle" :loading="btnLoading">
        <i class="icon-font icon-daochu f-12 mr-sm vt-middle"></i>
        <span class="vt-middle">导出</span>
      </Button>
    </div>
    <div class="content auto-fill">
      <ui-table
        class="ui-table auto-fill"
        ref="tableContent"
        :table-columns="tableColumns"
        :table-data="tableData"
        :loading="loading"
      >
        <template #EVALUATION_TIME="{ row }">
          <span v-if="systemConfig.distinguishVersion !== '4'">
            {{ row.EVALUATION_TIME }}
          </span>
          <Button type="text" v-else class="span-btn">
            {{ row.EVALUATION_TIME }}
          </Button>
        </template>
      </ui-table>
      <ui-page class="page" :page-data="pageData" @changePage="changePage" @changePageSize="changePageSize"> </ui-page>
    </div>
  </ui-modal>
</template>
<script>
import scAsync from '@/config/api/sc-async';
export default {
  props: {
    value: {
      type: Boolean,
    },
    ysParams: {},
    title: {},
  },
  data() {
    return {
      loading: false,
      visible: false,
      btnLoading: false,
      tableColumns: [],
      searchData: {
        checkDate: '',
        indexType: '',
        regionCode: '',
        schemeId: '',
        pageNumber: 1,
        pageSize: 20,
      },
      tableData: [],
      pageData: {
        pageNum: 1,
        pageSize: 20,
        totalCount: 0,
      },
    };
  },
  created() {},
  methods: {
    async init() {
      try {
        this.searchData.checkDate = this.ysParams.time;
        this.searchData.indexType = this.ysParams.indexType;
        this.searchData.regionCode = this.ysParams.regionCode;
        this.searchData.schemeId = this.ysParams.schemeId;
        this.loading = true;
        const res = await this.$http.post(scAsync.queryExamineDetail, this.searchData);
        this.tableColumns = res.data.data.headers.map((row) => {
          return {
            title: row.name,
            key: row.code,
          };
        });
        this.tableData = res.data.data.body.entities.map((row) => {
          return row;
        });
        this.pageData.totalCount = res.data.data.body.total;
      } catch (err) {
        console.log(err);
      } finally {
        this.loading = false;
      }
    },
    search() {
      this.init();
    },
    changePage(val) {
      this.searchData.pageNumber = val;
      this.search();
    },
    changePageSize(val) {
      this.searchData.pageNumber = 1;
      this.searchData.pageSize = val;
      this.search();
    },
    // 导出
    async exportHandle() {
      try {
        this.btnLoading = true;
        const res = await this.$http.post(scAsync.exportExamineDetailToExcel, this.searchData);
        await this.$util.common.transformBlob(res.data.data);
      } catch (e) {
        console.log(e);
      } finally {
        this.btnLoading = false;
      }
    },
  },
  watch: {
    value(val) {
      if (val) {
        this.init();
      }
      this.visible = val;
    },
    visible(val) {
      this.$emit('input', val);
    },
  },
  components: {
    UiTable: require('@/components/ui-table').default,
  },
};
</script>
<style lang="less" scoped>
.content {
  height: 500px;
}
</style>
