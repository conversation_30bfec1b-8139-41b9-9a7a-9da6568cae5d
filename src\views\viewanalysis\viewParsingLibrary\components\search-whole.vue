<!--
    * @FileDescription: 全部搜索条件
    * @Author: H
    * @Date: 2024/2/26
 * @LastEditors: zhengmingming <EMAIL>
 * @LastEditTime: 2025-01-23 16:09:31
 -->
<template>
  <div class="search card-border-color">
    <Form
      :inline="true"
      :class="visible ? 'advanced-search-show' : ''"
      @submit.native.prevent
    >
      <div class="general-search">
        <div class="input-content">
          <div class="upload-input-list">
            <uiUploadImg
              :algorithmType="1"
              v-model="queryParam.urlList"
              @imgUrlChange="imgUrlChange"
              size="small"
            />
          </div>
          <div class="other-search">
            <div class="other-search-top card-border-color">
              <FormItem label="相似度:" class="slider-form-item">
                <div class="slider-content">
                  <i
                    class="iconfont icon-jian add-subtract"
                    @click="addAndSubtract(0)"
                  ></i>
                  <Slider v-model="queryParam.similarity"></Slider>
                  <i
                    class="iconfont icon-jia add-subtract"
                    @click="addAndSubtract(1)"
                  ></i>
                  <span>{{ queryParam.similarity }}%</span>
                </div>
              </FormItem>
              <FormItem label="任务类型:">
                <Select
                  v-model="queryParam.selectType"
                  @on-change="handleSeleChange"
                  clearable
                >
                  <Option :value="1" placeholder="请选择">实时结构化</Option>
                  <Option :value="2" placeholder="请选择">历史结构化</Option>
                  <Option :value="3" placeholder="请选择">文件结构化</Option>
                </Select>
              </FormItem>
              <FormItem label="任务名称:" v-if="queryParam.selectType">
                <div class="select-tag-button" @click="selectTask()">
                  选择任务（{{ queryParam.selectTaskList.length }}）
                </div>
              </FormItem>
            </div>
            <div class="other-search-bottom">
              <div class="flex">
                <FormItem
                  label="设备资源:"
                  v-if="
                    queryParam.selectType == 1 || queryParam.selectType == 2
                  "
                >
                  <div class="select-tag-button" @click="selectDevice()">
                    选择设备/已选（{{ queryParam.selectDeviceList.length }}）
                  </div>
                </FormItem>
                <FormItem label="文件选择:" v-if="queryParam.selectType == 3">
                  <div class="select-tag-button" @click="selectFile()">
                    选择文件/已选（{{ queryParam.selectFileList.length }}）
                  </div>
                </FormItem>
                <FormItem label="抓拍时段:">
                  <hl-timerange
                    ref="timerange"
                    @onceChange="handleTimeOnce"
                    @change="handleTimeChange"
                    :reflectValue="queryParam.timeSlot"
                    :reflectTime="{
                      startDate: queryParam.startDate,
                      endDate: queryParam.endDate,
                    }"
                  >
                  </hl-timerange>
                </FormItem>
              </div>
              <div class="btn-group">
                <!-- <span
                  class="advanced-search-text primary"
                  v-if="queryParam.features.length < 1"
                  @click="advancedSearchHandle($event)"
                >
                  更多条件 <i class="iconfont icon-jiantou"></i>
                </span> -->
                <Button type="primary" @click="searchHandle">查询</Button>
                <Button @click="resetHandle">重置</Button>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- 选择设备 -->
      <select-device
        ref="selectDevice"
        showOrganization
        :checkedLabels="checkedLabels"
        @selectData="selectData"
      />
      <select-file ref="selectFile" @selectData="selectFileData"></select-file>
      <select-task
        ref="selectTask"
        :type="queryParam.selectType"
        @selectData="selectTaskData"
      ></select-task>
    </Form>
  </div>
</template>
<script>
import { queryTaskSelect } from "@/api/viewAnalysis";
import { mapMutations, mapGetters } from "vuex";
import uiUploadImg from "@/components/ui-upload-new-img/index";
export default {
  props: {
    queryParam: {
      type: Object,
      default: () => {
        return {};
      },
    },
    visible: {
      type: Boolean,
      default: false,
    },
  },
  components: {
    uiUploadImg,
  },
  data() {
    return {
      checkedLabels: [],
    };
  },
  computed: {
    ...mapGetters({
      globalObj: "systemParam/globalObj",
      parsingLibrarySearchData: "common/getParsingLibrarySearchData", //查询数据
    }),
  },
  async mounted() {
    let { type, taskId, jobId, imgUrl, startDate, endDate } = this.$route.query;
    if (type) this.queryParam.selectType = Number(type);
    if (taskId) this.queryParam.selectTaskList = JSON.parse(taskId);
    if (type && jobId) {
      let res = await queryTaskSelect({ type, parentId: jobId });
      this.queryParam.selectTaskList = res.data || [];
    }
    if (startDate && endDate) {
      this.queryParam.timeSlot = "自定义";
      this.queryParam.startDate = this.$dayjs(Number(startDate)).format(
        "YYYY-MM-DD HH:mm:ss"
      );
      this.queryParam.endDate = this.$dayjs(Number(endDate)).format(
        "YYYY-MM-DD HH:mm:ss"
      );
    }

    let {
      selectType,
      selectDeviceList,
      selectTaskList,
      selectFileList,
      timeSlot,
      searchSelect,
    } = this.parsingLibrarySearchData[0];
    if (searchSelect == 1) {
      this.queryParam.selectType = selectType;
      this.queryParam.selectDeviceList = selectDeviceList;
      this.queryParam.selectTaskList = selectTaskList;
      this.queryParam.selectFileList = selectFileList;
      this.queryParam.timeSlot = timeSlot;
      this.queryParam.startDate = this.parsingLibrarySearchData[0].startDate;
      this.queryParam.endDate = this.parsingLibrarySearchData[0].endDate;
    }
    setTimeout(() => {
      if (this.globalObj.searchForPicturesDefaultSimilarity)
        this.queryParam.similarity =
          this.globalObj.searchForPicturesDefaultSimilarity - 0;
      this.$emit("search");
      this.setParsingLibrarySearchData({ ...this.queryParam, menuIndex: 0 });
    }, 100);
  },
  methods: {
    ...mapMutations("common", ["setParsingLibrarySearchData"]),
    searchHandle() {
      this.$emit("search");
      this.setParsingLibrarySearchData({ ...this.queryParam, menuIndex: 0 });
    },
    resetHandle() {
      this.resetClear();
      this.$emit("reset");
      this.setParsingLibrarySearchData({ ...this.queryParam, menuIndex: 0 });
      this.visible = false;
    },
    resetClear() {
      // 清空组件选中状态
      this.$refs.timerange.clearChecked(false);
      this.queryParam.similarity =
        this.globalObj.searchForPicturesDefaultSimilarity - 0;
      this.queryParam.urlList = [];
      this.queryParam.features = [];
      this.queryParam.imageBases = [];
      this.queryParam.selectDeviceList = [];
      this.queryParam.selectTaskList = [];
      this.queryParam.selectFileList = [];
      this.queryParam.selectType = "";
      this.$forceUpdate();
    },
    advancedSearchHandle($event) {
      $event.stopPropagation();
      if (this.visible) {
        this.$emit("update:visible", false);
        // this.visible = false
      } else {
        this.$emit("update:visible", true);
        // this.visible = true
      }
    },
    handleSeleChange() {
      this.queryParam.selectTaskList = [];
    },
    /**
     * 选择设备
     */
    selectDevice() {
      this.$refs.selectDevice.show(this.queryParam.selectDeviceList);
    },
    /**
     * 已选择设备数据返回
     */
    selectData(list) {
      this.queryParam.selectDeviceList = list;
      this.$forceUpdate();
    },
    selectFileData(list) {
      this.queryParam.selectFileList = list;
    },
    selectTaskData(list) {
      this.queryParam.selectTaskList = list;
    },
    // 时间
    handleTimeOnce(obj) {
      this.queryParam.timeSlot = obj.timeSlot;
      this.queryParam.startDate = obj.startDate;
      this.queryParam.endDate = obj.endDate;
    },
    handleTimeChange(obj) {
      this.queryParam.timeSlot = obj.timeSlot;
      this.queryParam.startDate = obj.startDate;
      this.queryParam.endDate = obj.endDate;
    },
    /**
     * 选择任务
     */
    selectTask() {
      this.$refs.selectTask.show(this.queryParam.selectTaskList);
    },
    /**
     * 选择文件
     */
    selectFile() {
      this.$refs.selectFile.show(this.queryParam.selectFileList);
    },
    urlImgList(list, index = 0) {
      if (index == 1) {
        this.queryParam.urlList = [...list];
        this.imgUrlChange([...list]);
      } else if (index == 2) {
        let arr = [...this.queryParam.urlList, ...list].filter((item) => {
          return item;
        });
        this.queryParam.urlList = [...arr, ""];
        this.imgUrlChange([...arr]);
      } else {
        this.queryParam.urlList.unshift(...list);
        this.imgUrlChange([...list]);
      }
    },
    /**
     * 图片上传结果返回
     */
    imgUrlChange(list) {
      // 以图搜图字段
      let features = [];
      let imageBases = [];
      list.forEach((item) => {
        if (item) {
          features.push(item.feature);
          imageBases.push(item.imageBase);
        }
      });
      this.queryParam.features = features;
      this.queryParam.imageBases = imageBases;
    },
    // 相似度加减
    addAndSubtract(index) {
      if (index == 0) {
        this.queryParam.similarity -= 1;
      } else {
        this.queryParam.similarity += 1;
      }
    },
  },
};
</script>
<style lang="less" scoped>
@import "style/index";
</style>
