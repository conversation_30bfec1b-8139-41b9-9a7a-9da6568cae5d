<template>
  <div class="base-search">
    <ui-label class="fl" label="组织机构" :width="66">
      <api-organization-tree :select-tree="selectOrgTree" @selectedTree="selectedOrgTree" placeholder="请选择">
      </api-organization-tree>
    </ui-label>
    <ui-label class="fl ml-lg" :label="global.filedEnum.deviceId" :width="66">
      <Input
        class="input-width"
        :placeholder="`请输入${global.filedEnum.deviceId}`"
        v-model="searchData.deviceId"
      ></Input>
    </ui-label>
    <ui-label class="fl ml-lg" :label="global.filedEnum.deviceName" :width="66">
      <Input
        class="input-width"
        :placeholder="`请输入${global.filedEnum.deviceName}`"
        v-model="searchData.deviceName"
      ></Input>
    </ui-label>
    <!-- <ui-label class="fl ml-lg" label="数据来源：" :width="70">
            <Select v-model="searchData.deviceId" clearable placeholder="请选择" class="input-width">
                <Option :value="2">不合格</Option>
                <Option :value="3">合格</Option>
            </Select>
        </ui-label> -->
    <ui-label :width="30" class="fl" label=" ">
      <Button type="primary" class="mr-sm" @click="$emit('startSearch', searchData)"> 查询 </Button>
      <Button type="default" class="mr-lg" @click="resetInitial"> 重置 </Button>
    </ui-label>
    <slot></slot>
  </div>
</template>
<script>
export default {
  props: {},
  data() {
    return {
      searchData: {
        deviceId: '',
        deviceName: '',
        orgCode: '',
      },
      copySearchData: {},
      selectOrgTree: {
        orgCode: '',
      },
    };
  },
  created() {
    this.copySearchDataMx(this.searchData);
  },
  methods: {
    selectedOrgTree(val) {
      this.selectOrgTree.orgCode = val.orgCode;
      this.searchData.orgCode = val.orgCode;
      this.$emit('startSearch', this.searchData);
    },
    resetInitial() {
      this.selectOrgTree.orgCode = null;
      this.resetSearchDataMx(this.searchData, () => this.$emit('startSearch', this.searchData));
    },
  },
  watch: {},
  components: {
    ApiOrganizationTree: require('@/api-components/api-organization-tree.vue').default,
  },
};
</script>
<style lang="less" scoped>
.base-search {
  overflow: hidden;
  margin: 20px 0;
  .input-width {
    width: 230px;
  }
  .ui-label {
    line-height: 40px;
  }
}
</style>
