export default {
  themeList: '/ivdg-data-governance-service/topic/queryDefaultList', // 治理主题首页列表

  // 视图
  subthemeList: '/ivdg-data-governance-service/topic/queryViewList', // 视图基础数据治理主题列表
  updateSubtheme: '/ivdg-data-governance-service/topic/update', // 视图基础数据治理主题新增以及更新
  removeSubtheme: '/ivdg-data-governance-service/topic/remove', // 视图基础数据治理主题删除
  view: '/ivdg-data-governance-service/topic/view', // 视图基础数据治理主题流程图
  getTopicTransferByMapping: '/ivdg-data-governance-service/topicTransfer/getTopicTransferByMapping', // 字段映射映射数据表
  queryTopicTransferFieldList: '/ivdg-data-governance-service/topicTransfer/queryTopicTransferFieldList', // 查询网关字段列表
  queryKafkaPropertyList: '/ivdg-data-governance-service/topicTransfer/queryKafkaPropertyList', // 查询主题字段映射列表
  mappingEdit: '/ivdg-data-governance-service/topicTransfer/updateTopicTransferField', // 查询字段映射标准数据表
  standardList: '/ivdg-asset-app/propertySearch/queryPropertySearchList', // 查询字段映射标准数据表
  queryTransferDictList: '/ivdg-data-governance-service/topicTransfer/queryTransferDictList', // 查询字典映射列表
  queryTransferDictDataList: '/ivdg-data-governance-service/topicTransfer/queryTransferDictDataList', // 查询字典映射数据列表
  updateTopicTransferDict: '/ivdg-data-governance-service/topicTransfer/updateTopicTransferDict', // 修改主题字典映射
  updateTheme: '/ivdg-data-governance-service/topic/update', // 主题编辑
  queryTopicPropertyCheckList: '/ivdg-data-governance-service/topicComponent/queryTopicPropertyCheckList', // 查询规则的字段配置列表
  updateTopicPropertyCheck: '/ivdg-data-governance-service/topicComponent/updateTopicPropertyCheck', // 编辑规则的字段配置
  queryTopicComponentOfLngLat: '/ivdg-data-governance-service/topicComponent/queryTopicComponentOfLngLat', // 查询经纬度检测配置
  updateTopicComponentOfLngLat: '/ivdg-data-governance-service/topicComponent/updateTopicComponentOfLngLat', // 修改经纬度检测配置
  queryTopicComponentOfDeviceTime: '/ivdg-data-governance-service/topicComponent/queryTopicComponentOfDeviceTime', // 查询时钟信息配置
  updateTopicComponentOfDeviceTime: '/ivdg-data-governance-service/topicComponent/updateTopicComponentOfDeviceTime', // 修改时钟信息配置
  queryDeviceErrorMessageList: '/ivdg-data-governance-service/topicDeviceResult/queryDeviceErrorMessageList', // 设备数据错误原因列表
  queryDeviceCheckResultList: '/ivdg-data-governance-service/topicDeviceResult/queryDeviceCheckResultList', // 设备数据错误详情
  checkDeviceInfo: '/ivdg-data-governance-service/topic/checkDeviceInfo', // 检测设备信息（开始运行）
  checkDeviceInfoTemp: '/ivdg-data-governance-service/topic/checkDeviceInfoTemp', // 检测临时设备信息（开始运行）
  queryByTopicId: '/ivdg-data-governance-service/topicCheckConfig/queryByTopicId', // 根据主题id获取详情
  update: '/ivdg-data-governance-service/topicCheckConfig/update', // 编辑选择治理数据
  deleteTransferDict: '/ivdg-data-governance-service/topicTransfer/deleteTransferDict', // 删除字典

  // 人脸
  queryTopicComponentOfUploadTime: '/ivdg-data-governance-service/topicComponent/queryTopicComponentOfUploadTime', // 查询图像上传及时性配置 、图像重复检测
  updateTopicComponentOfUploadTime: '/ivdg-data-governance-service/topicComponent/updateTopicComponentOfUploadTime', // 查询图像上传及时性配置、修改图像重复检测
  queryTopicAlgorithmList: '/ivdg-data-governance-service/topicComponent/queryTopicAlgorithmList', // 查询算法配置列表
  updateTopicAlgorithm: '/ivdg-data-governance-service/topicComponent/updateTopicAlgorithm', // 修改主题算法
  faceLibInfoCheck: '/ivdg-data-governance-service/topic/faceLibInfoCheck', // 检测人脸信息（开始运行）

  // 重点人员
  importantPersonCheck: '/ivdg-data-governance-service/topic/importantPersonCheck', //检测重点人员轨迹（开始运行）
  importantPersonBaseCheck: '/ivdg-data-governance-service/topic/importantPersonBaseCheck', // 检测重点人员基础（开始运行）

  //首页
  queryIndexTopicCheckCount: '/ivdg-data-governance-service/topic/statistics/queryIndexTopicCheckCount', // 首页查询主题检测总数
  queryIndexSumCheckCount: '/ivdg-data-governance-service/topic/statistics/queryIndexSumCheckCount', // 首页查询检测总数

  // 首页 数据优化校准   优化模块
  queryIndexGovernanceCount: '/ivdg-data-governance-service/topic/statistics/queryIndexGovernanceCount', // 首页查询优化总数
  queryIndexTopicGovernanceCount: '/ivdg-data-governance-service/topic/statistics/queryIndexTopicGovernanceCount', // 首页查询治理优化统计
};
