import { LayerType } from "@/map/core/enum/LayerType.js";
import { getNearResource } from "@/api/modelMarket";
import { getMapMarkerImage } from "@/util/modules/common.js";

var _mapMain = null,
  _mapGeometery = null,
  _defualtLayer = null,
  _operationLayer = null,
  _skillsLayer = null,
  _polygonLine = null,
  _routeGroup = null,
  _routeMakerGroup = null,
  _buffPolygon = null,
  _startMarker = null,
  _endMarker = null,
  _isShowPolygon = false, //是否展示面
  _isShowRemoveBar = null,
  _geometry = null, // 绘制的线
  _extent = null,
  _buffDis = 25, //设置缓冲距离
  //线样式
  _lineStyle = {
    strokeColor: "#2D87F9", //绘制过程中边框的颜色
    color: "#2D87F9",
    weight: 2,
    opacity: 1,
    virtualStyle: {
      //绘制时的圆点样式
      pointRadius: 6,
      fillColor: "#FFFFFF",
      fillOpacity: 1,
      strokeOpacity: 1,
      strokeColor: "#2c9eff",
      strokeWidth: 2,
    },
  },
  _roadStyle = {
    startMarker: {},
    endMarker: {},
    startText: "",
    endText: "",
  },
  _buffStyle = {
    color: "#1976ED",
    fillColor: "#1976ED", //填充颜色
    opacity: 0.1,
    weight: 1,
    fillOpacity: 0.25, //填充的透明度，取值范围0 - 1
  },
  //选择框样式
  _selectContentStyle = {
    fontSize: "12px",
    color: "#999",
    width: "140px",
    height: "45px",
    border: "1px",
    borderColor: "#ccc",
    borderStyle: "solid",
    boxShadow: "5px 5px 0px rgba(34, 34, 34, 0.1)",
    padding: "6px",
    lineHeight: "15px",
  },
  _animationLine = null,
  _carMarker = null,
  passPointMarker = null,
  nowPointMarker = null,
  nextPointMarker = null,
  oneDevice = null, // 只有一个设备的警卫轨迹
  oneDeviceIndex = -1;
export const trailSelectMixin = {
  data: function () {
    return {};
  },
  watch: {},
  methods: {
    initOperationbar: function (map, opt) {
      _mapMain = map;
      _defualtLayer = _mapMain.map.getDefaultLayer();
      _operationLayer = _defualtLayer.getGroupByName("operation-layer");
      if (!_operationLayer) {
        _defualtLayer.addGroup("operation-layer");
        _operationLayer = _defualtLayer.getGroupByName("operation-layer");
      }
      if (!_routeGroup) {
        _defualtLayer.addGroup("route");
        _routeGroup = _defualtLayer.getGroupByName("route");
      }
      if (!_routeMakerGroup) {
        _defualtLayer.addGroup("route-maker");
        _routeMakerGroup = _defualtLayer.getGroupByName("route-maker");
      }
      if (!_skillsLayer) {
        _defualtLayer.addGroup("skillsLayer");
        _skillsLayer = _defualtLayer.getGroupByName("skillsLayer");
      }
      _mapGeometery = new MapPlatForm.Base.MapGeometry(_mapMain.map);
      if (opt) {
        // 是否地图展示绘制结果
        _isShowPolygon = opt.isShowPolygon || _isShowPolygon;
        // 是否显示可删除的面板
        (_isShowRemoveBar = opt.isShowRemoveBar || _isShowRemoveBar),
          // 设置缓冲距离
          (_buffDis = opt.buffDis || _buffDis);

        _lineStyle = opt.lineStyle || _lineStyle;

        if (opt.roadStyle) {
          _roadStyle = opt.roadStyle;
        } else {
          _roadStyle = {
            startMarker: {},
            endMarker: {},
            startText: "点击左键标记起点，右键取消",
            endText: "点击左键标记终点，右键取消",
          };
        }
      }
    },
    //线选
    operationByLine: function () {
      this.resetMap();
      this.activeOperation = "line";
      this.isDrawing = true;
      _mapMain.mapTools.drawLine(
        function (extent, geometry) {
          _extent = extent;
          this.isDrawing = false;
          if (!geometry) {
            this.cancelDrew("line");
            return;
          }
          _polygonLine = geometry;
          this.addGeometryOnMap(geometry, "manual");
          _mapMain.map.deactivateMouseContext();
          this.addBuff(geometry, "manual");
        }.bind(this),
        () => {},
        false,
        _lineStyle
      );
      //激活鼠标文字跟踪
      this.setMouseStyle("单击开始绘制，双击结束。");
    },
    resetMap() {
      _routeGroup.removeAllOverlays()
      this.resetDefualtLayer();
      _startMarker = null;
      _endMarker = null;
      _polygonLine = null;
      _defualtLayer.removeGroup("route");
      this.isShowRemoveBar = false;
    },
    //目的是为了清除默认图层上的起点、终点
    resetDefualtLayer: function () {
      _defualtLayer.removeAllOverlays();
    },
    //地图右键取消操作
    cancelDrew: function (type) {
      if (_mapMain.mapTools) {
        _mapMain.mapTools.cancelDraw();
      }
      this.closeRoadRemoveInfowindow();
      //取消右键事件
      _mapMain.map.removeEventListener(NPMap.MAP_EVENT_RIGHT_CLICK);
      //如果时线选的话，相当于清空路径了
      if (type === "line") {
        // this.removeRoute();
      }
    },
    //设置鼠标跟随样式,第二个参数是内容框的偏移
    setMouseStyle: function (text, styleObj) {
      text = text || "按住鼠标左键开始绘制,</br>释放完成绘制。";
      if (styleObj) {
        _mapMain.map.activateMouseContext(text, styleObj);
      } else {
        _mapMain.map.activateMouseContext(text);
      }
      _mapMain.map.setMouseContextStyle(_selectContentStyle);
      _mapMain.map.addEventListener(
        NPMap.MAP_EVENT_RIGHT_CLICK,
        this.cancelDrew
      );
    },
    closeRoadRemoveInfowindow: function () {
      if (this.roadRemoveInfowindow) {
        _mapMain.map.removeOverlay(this.roadRemoveInfowindow);
      }
    },
    //添加覆盖物到地图
    addGeometryOnMap: function (geometry) {
      if (!_routeGroup) {
        _defualtLayer.addGroup("route");
        _routeGroup = _defualtLayer.getGroupByName("route");
      }
      if (geometry) {
        geometry.setStyle(_lineStyle);
      }
      _routeGroup.addOverlay(geometry);
    },
    //添加缓冲区
    addBuff: function (geometry, type, model) {
      this.$emit("drawedLine", [geometry, type]);
      _geometry = geometry;
      if (_buffPolygon) {
        _routeGroup && _routeGroup.removeOverlay(_buffPolygon);
      }
      _mapMain.mapService.getGeometryBuffer(
        geometry,
        _buffDis,
        function (result) {
          _buffPolygon = new NPMap.Geometry.Polygon(result.rings);
          _buffPolygon.setStyle(_buffStyle);
          if (_isShowPolygon) {
            _routeGroup.addOverlay(_buffPolygon);
          }
          if (_isShowRemoveBar) {
            this.showRemoveBar(geometry);
          }
          this.operationCallback(_buffPolygon, model);
        }.bind(this)
      );
    },
    //修改缓冲区
    changeBuff: function (buffDis) {
      if (!_routeGroup) {
        return;
      }
      _buffPolygon = _routeGroup
        .getAllOverlayers()
        .find((v) => v.CLASS_NAME === "NPMap.Geometry.Polygon");
      _geometry = _routeGroup
        .getAllOverlayers()
        .find((v) => v.CLASS_NAME === "NPMap.Geometry.Polyline");
      if (!_mapMain || !_buffPolygon || !_geometry) {
        return;
      }
      _mapMain.mapService.getGeometryBuffer(
        _geometry,
        buffDis,
        function (result) {
          if (_buffPolygon) {
            _routeGroup.removeOverlay(_buffPolygon);
          }
          _buffPolygon = new NPMap.Geometry.Polygon(result.rings);
          _buffPolygon.setStyle(_buffStyle);
          _routeGroup.addOverlay(_buffPolygon);
          this.operationCallback(_buffPolygon);
        }.bind(this)
      );
    },
    showRemoveBar: function (geometry) {
      this.isShowRemoveBar = true;
      var content = this.$refs.trailRemove;
      this.roadRemoveInfowindow = new NPMap.Symbols.InfoWindow(
        geometry.getEndPoint(),
        "",
        content,
        {
          width: 86,
          height: 26,
          autoSize: false,
          iscommon: true,
          offset: new NPMap.Geometry.Size(40, 22),
        }
      );
      _mapMain.map.addOverlay(this.roadRemoveInfowindow);
      this.roadRemoveInfowindow.open();
    },
    // 取消框选
    removeRoute() {
      this.resetMap();
    },
    //地图操作后回调方法，查询结构直接返回业务层
    operationCallback: function (geometry, model) {
      this.activeOperation = "";
      var geometryJson = null;
      if (geometry) {
        geometryJson = _mapGeometery.getGGeoJsonByGeometry(geometry);
      }
      if (model != undefined) {
        //获取保存了的摄像机 警卫路线
        // serviceStore.getCameraListByPoliceId({groupId: model}).then(res => {
        //     this.showOrHideDefaultLayers(false);
        //     this.addResourceOnmap(res.cameras);
        //     this.$emit("input",{
        //         startPoint: _mapGeometery.getGGeoJsonByGeometry(_polygonLine.getStartPoint()),
        //         stopPoint: _mapGeometery.getGGeoJsonByGeometry(_polygonLine.getEndPoint()),
        //         pointinfo: _mapGeometery.getGGeoJsonByGeometry(_polygonLine),
        //         buffDis: _buffDis,
        //         cameraIds: IX.map(res.cameras, function(item) {
        //             item.nodeType = item.type;
        //             return item;
        //         }),
        //         startPointName: this.value.startPointName,
        //         endPointName: this.value.endPointName,
        //         type: 'edit',
        //         hasRoute: true
        //     })
        // })
      } else {
        // 排除设备类型 1：枪机 2：球机 3：人脸卡口 4：车辆卡口 5：Wi-Fi设备 6：RFID设备 7：电围设备
        let param = {
          geometry: geometryJson,
          excludeDeviceTypes: [3, 4, 5, 6, 7, 8],
        };
        getNearResource(param).then((res) => {
          if (res.data.length === 0) {
            this.$Message.warning("该路径上无资源，请重新进行路径绘制！");
          }
          this.addResourceOnmap(res.data);
          if (!_polygonLine) {
            _polygonLine = _geometry;
          }
          this.$emit("drawDown", {
            startPointData: _mapGeometery.getGGeoJsonByGeometry(
              _polygonLine.getStartPoint()
            ),
            endPointData: _mapGeometery.getGGeoJsonByGeometry(
              _polygonLine.getEndPoint()
            ),
            linePointData: _mapGeometery.getGGeoJsonByGeometry(_polygonLine),
            devices: res.data,
            hasRoute: true,
          });
        });
      }
    },
    //获取摄像机的图片（框选事默认使用hover时的大图，且本身无hover事件）
    getHoverCameraImages: function (data) {
      var cameraType = data.deviceType;
      let url;
      switch (cameraType) {
        case "1":
          if (data.deviceChildType == "1" || data.deviceChildType == "2") {
            url = LayerType["Camera_QiuJi"].hoverUrl;
          } else {
            url = LayerType["Camera_QiangJi"].hoverUrl;
          }
          break;
        case "11":
          url = LayerType["Camera_Face"].hoverUrl;
          break;
        case "2":
          url = LayerType["Camera_Vehicle"].hoverUrl;
          break;
        default:
          url = LayerType["Camera"].hoverUrl;
          break;
      }
      return url;
    },
    //在地图上添加marker
    addResourceOnmap: function (resources) {
      //是否展示多个绘制面
      if (_operationLayer) {
        _operationLayer.removeAllOverlays();
      }
      this.markerList = [];
      if (resources && resources.length > 0) {
        for (var i = 0; i < resources.length; i++) {
          if (
            resources[i].longitude === 0 ||
            resources[i].longitude === null ||
            resources[i].latitude === 0 ||
            resources[i].latitude === null
          ) {
            continue;
          }
          var el = resources[i],
            lon = el.longitude,
            lat = el.latitude,
            marker = null,
            //定义坐标
            point = new NPMap.Geometry.Point(lon, lat);
          var _cameraIcon = this.getHoverCameraImages(el);
          //创建标注
          marker = _mapGeometery.createMarker(point, {
            url: _cameraIcon,
            size: {
              width: 24,
              height: 24,
            },
            markerType: 0,
          });
          el.type = "camera";
          marker.setData(el);
          this.markerList.push(marker);
          _operationLayer.addOverlay(marker);
          marker.addEventListener(NPMap.MARKER_EVENT_MOUSE_OVER, (marker) => {
            this.$emit("activeMarkerMouseover", marker.getData());
          });
          marker.addEventListener(NPMap.MARKER_EVENT_MOUSE_OUT, (marker) => {
            this.$emit("activeMarkerMouseout", marker.getData());
          });
          marker.addEventListener(NPMap.MARKER_EVENT_CLICK, (marker) => {
            this.$emit("activeMarkerClick", marker.getData());
          });
        }
        // _operationLayer.addOverlays(markerList);
      }
    },
    // 警卫路线设置动画线之前初始处理
    beforeSetAnimation(routeInfo) {
      var polyline = _mapGeometery.getGeometryByGeoJson(
        routeInfo.linePointData
      );
      this.showRouteOnMap(polyline, routeInfo);
      this.addResourceOnmap(routeInfo.devices);
      _routeGroup.setZIndex(100);
      _operationLayer.setZIndex(600);
      _skillsLayer.setZIndex(700);
      _routeMakerGroup.setZIndex(800);
    },
    //路径展示在地图
    showRouteOnMap(polyline, routeInfo) {
      _routeGroup.removeAllOverlays();
      _routeMakerGroup.removeAllOverlays();
      //添加起点、终点
      var start_point = _mapGeometery.getGeometryByGeoJson(
          routeInfo.startPointData
        ),
        end_point = _mapGeometery.getGeometryByGeoJson(routeInfo.endPointData),
        startMarker = _mapGeometery.createMarker(
          start_point,
          getMapMarkerImage("BLUE_START")
        ),
        endMarker = _mapGeometery.createMarker(
          end_point,
          getMapMarkerImage("RED_END")
        );
      _routeMakerGroup.addOverlays([startMarker, endMarker]);
      //添加起点终点ending
      //添加缓冲

      this.addShowBuff(polyline, routeInfo.distance, routeInfo.gpsSpeed);
      polyline.setStyle(_lineStyle);
      //添加路径
      _routeGroup.addOverlay(polyline);
    },
    //添加缓存区
    addShowBuff: function (routeLine, distance, carSpeed) {
      _mapMain.mapService.getGeometryBuffer(
        routeLine,
        distance,
        function (result) {
          _buffPolygon = new NPMap.Geometry.Polygon(result.rings);
          _buffPolygon.setStyle(_buffStyle);
          _routeGroup.addOverlay(_buffPolygon);
          var extent = _buffPolygon.getExtent();
          _mapMain.map.zoomToExtent(extent, false, true);
        }
      );
    },
    playRoute(routeInfo) {
      var polyLine = _mapGeometery.getGeometryByGeoJson(
        routeInfo.linePointData
      );
      this.carSetAnimation(polyLine, routeInfo).then(() => {
        _animationLine.start();
      });
    },
    stopRoute(routeInfo) {
      _animationLine.stop();
      _animationLine.remove();
      _skillsLayer.removeOverlay(_animationLine.headerMarker);
      //删除存的路线中对应的小车动画线数据
      _animationLine = null;
      this.$emit("routeRefresh", {
        isPlaying: false,
        curCamera: null,
      });
      oneDevice = null;
    },
    //警卫路线自动播放
    carSetAnimation(polyline, routeInfo) {
      return new Promise((resolve, reject) => {
        //清空之前的小车marker
        _carMarker = null;
        passPointMarker = null;
        nowPointMarker = null;
        nextPointMarker = null;

        //设置小车
        this.setCarToMap(polyline.getStartPoint()).then((res) => {
          //设置动画线
          _animationLine = new NPMap.Symbols.AnimationLine(
            _mapMain.map.id,
            polyline.getPath(),
            {
              headerMarker: _carMarker,
              color: "#2d87f9",
              fillOpacity: "#38aef3",
              opacity: 1,
              weight: 4,
              layer: _skillsLayer,
            }
          );
          // 速度单位为千米每小时
          var speed = routeInfo.gpsSpeed / 18 || 10 / 18; //默认一秒50米 180 km/h

          _animationLine.setSpeed(speed);
          _animationLine.panWay = 1;
          //注册事件，主要是动画线结束后播放控制按钮样式调整
          _animationLine.events.register("preDraw", (evt) => {
            if (evt.index == polyline.getPath().length - 1) {
              this.$emit("routeRefresh", {
                isPlaying: false,
                curCamera: null,
                nextCameraId: "",
                prevCameraId: "",
              });

              //移除动画线
              evt.srcObject.remove();
              //移除小车
              _skillsLayer.removeOverlay(evt.srcObject.headerMarker);
              this.removeCameraIcon();
            }
          });
          _animationLine.events.register("afterStep", (evt) => {
            if (!evt.point) {
              return;
            }
            if (routeInfo.devices.length === 1) {
              let index = -1;
              var marker = this.findShortestMarker(evt.point, routeInfo);
              // 即将通过
              if (!marker.nowPass && !marker.endPass) {
                index = 2;
              }
              // 正在通过
              if (marker.nowPass) {
                index = 1;
              }
              // 已经通过
              if (marker.endPass) {
                index = 0;
                oneDevice = null
              }
              if (index != oneDeviceIndex) {
                oneDeviceIndex = index;
                this.$emit("routeRefresh", {
                  device: marker,
                  index: oneDeviceIndex,
                });
              }
            } else {
              var marker = this.findShortestMarker(evt.point, routeInfo);
              if (!marker) {
                return;
              }
              if (
                !this.curCamera ||
                this.curCamera.deviceId != marker.deviceId
              ) {
                this.curCamera = marker;
                //播放视频
                this.$emit("routeRefresh", {
                  isPlaying: true,
                  curCamera: this.curCamera,
                });
              }
            }
          });
          resolve();
        });
      });
    },
    /**
     * 在地图上设置小车
     *
     */
    setCarToMap(position) {
      return new Promise((resolve, reject) => {
        if (_carMarker) {
          var angle = 0;
          angle = -90;
          _carMarker && _carMarker.rotate(angle);
          _carMarker.setPosition(position);
          resolve();
        } else {
          _carMarker = _mapGeometery.createMarker(
            position,
            getMapMarkerImage("POLICE_CAR")
          );
          if (angle) {
            _carMarker.rotate(angle);
          }
          _skillsLayer.addOverlay(_carMarker);
          resolve();
        }
      });
    },
    /**
     *查询最近的资源标注点位
     **/
    findShortestMarker(point, routeInfo) {
      var distance = 9999;
      var marker;
      var cameras = routeInfo.devices;
      // 只有一个设备 需要正在通过和已通过
      if (cameras.length === 1) {
        if (!oneDevice) {
          oneDevice = cameras[0];
          oneDevice.nowPass = false;
          oneDevice.endPass = false;
        }
        var d =
          (point.lon - oneDevice.longitude) *
            (point.lon - oneDevice.longitude) +
          (point.lat - oneDevice.latitude) * (point.lat - oneDevice.latitude);
        // 如果距离小于500 表示正在通过
        if (d * 10000000 < 500) {
          oneDevice.nowPass = true;
        } else {
          if (oneDevice.nowPass) {
            oneDevice.endPass = true;
            oneDevice.nowPass = false;
          }
        }
        return oneDevice;
      }
      for (var i = 0; i < cameras.length; i++) {
        var p = cameras[i];
        var d =
          (point.lon - p.longitude) * (point.lon - p.longitude) +
          (point.lat - p.latitude) * (point.lat - p.latitude);
        if (distance > d) {
          distance = d;
          marker = cameras[i];
        }
      }
      return marker;
    },
    setPlayingDeviceIcon(playingObj) {
      this.markerList.forEach((v) => {
        let markerData = v.getData();
        if (markerData.deviceId) {
          var _cameraIcon = this.getHoverCameraImages(markerData);
          let icon = _mapGeometery.getIconByParam({
            url: _cameraIcon,
            size: {
              width: 24,
              height: 24,
            },
            markerType: 0,
          });
          v.setIcon(icon);
          v.refresh();
          if (
            playingObj.passDevice &&
            markerData.deviceId == playingObj.passDevice.deviceId
          ) {
            let icon = _mapGeometery.getIconByParam(
              getMapMarkerImage("GUARDROUTE_CAMERA_PASS")
            );
            v.setIcon(icon);
            v.refresh();
          }
          if (
            playingObj.nowDevice &&
            markerData.deviceId == playingObj.nowDevice.deviceId
          ) {
            let icon = _mapGeometery.getIconByParam(
              getMapMarkerImage("GUARDROUTE_CAMERA_NOW")
            );
            v.setIcon(icon);
            v.refresh();
          }
          if (
            playingObj.nextDevice &&
            markerData.deviceId == playingObj.nextDevice.deviceId
          ) {
            let icon = _mapGeometery.getIconByParam(
              getMapMarkerImage("GUARDROUTE_CAMERA_NEXT")
            );
            v.setIcon(icon);
            v.refresh();
          }
        }
      });
    },
    removeCameraIcon() {},
  },
  beforeDestroy() {
    _buffDis = 25;
    _polygonLine = null;
    _endMarker = null;
    _startMarker = null;
    _routeGroup = null;
    _operationLayer = null;
    _routeMakerGroup = null;
    _skillsLayer = null;
    this.roadRemoveInfowindow && this.roadRemoveInfowindow.close();
    oneDevice = null;
  },
};
