<template>
  <div class="liquid-container" :class="getFullscreen ? 'full-screen-container' : ''">
    <HomeTitle icon="icon-shipinliujiance" v-model="activeValue" :data="tabData"> </HomeTitle>
    <div class="echarts-box" v-ui-loading="{ loading: echartsLoading, tableData: Object.keys(echartsData) }">
      <draw-echarts :echart-option="chartsOption1" :echart-style="echartStyle" class="charts"></draw-echarts>
    </div>
  </div>
</template>

<script>
/**
 * "echarts": "^4.9.0",
  "echarts-liquidfill": "^2.0.6",
 */
import home from '@/config/api/home';
import { mapGetters } from 'vuex';
export default {
  name: 'liquidFill',
  components: {
    DrawEcharts: require('@/components/draw-echarts').default,
    HomeTitle: require('./home-title').default,
  },
  props: {},
  data() {
    return {
      activeValue: 'video',
      tabData: [{ label: '视频流检测', id: 'video' }],
      chartsOption1: {},
      echartStyle: {
        width: '100%',
        height: '100%',
      },
      echartsLoading: false,
      echartsData: {},
    };
  },
  computed: {
    ...mapGetters({
      getFullscreen: 'home/getFullscreen',
    }),
  },
  watch: {},
  filter: {},
  created() {
    this.init();
  },
  methods: {
    doEcharts(data) {
      let searies = {
        videoClockAccuracy: [
          {
            value: data.videoClockAccuracyErrorCountPercentum / 100,
            count: data.videoClockAccuracyErrorCount,
            origin: data.videoClockAccuracyErrorCountPercentum,
          },
          data.videoClockAccuracyErrorCountPercentum / 100,
          data.videoClockAccuracyErrorCountPercentum / 100,
        ],
        videoHistory: [
          {
            value: data.videoHistoryAccuracyErrorCountPercentum / 100,
            count: data.videoHistoryAccuracyErrorCount,
            origin: data.videoHistoryAccuracyErrorCountPercentum,
          },
          data.videoHistoryAccuracyErrorCountPercentum / 100,
          data.videoHistoryAccuracyErrorCountPercentum / 100,
        ],
        videoOsdAccuracy: [
          {
            value: data.videoOsdAccuracyErrorCountPercentum / 100,
            count: data.videoOsdAccuracyErrorCount,
            origin: data.videoOsdAccuracyErrorCountPercentum,
          },
          data.videoOsdAccuracyErrorCountPercentum / 100,
          data.videoOsdAccuracyErrorCountPercentum / 100,
        ],
        videoPlayingAccuracy: [
          {
            value: data.videoPlayingAccuracyErrorCountPercentum / 100,
            count: data.videoPlayingAccuracyErrorCount,
            origin: data.videoPlayingAccuracyErrorCountPercentum,
          },
          data.videoPlayingAccuracyErrorCountPercentum / 100,
          data.videoPlayingAccuracyErrorCountPercentum / 100,
        ],
      };
      this.chartsOption1 = this.$util.doEcharts.homepageLiquidFill(searies);
    },
    async init() {
      try {
        this.echartsLoading = true;
        let {
          data: { data },
        } = await this.$http.get(home.queryVideoDataErrorCount);
        this.echartsData = data || {};
        this.doEcharts(data);
      } catch (e) {
        // console.log(e)
      } finally {
        this.echartsLoading = false;
      }
    },
  },
};
</script>

<style lang="less" scoped>
.liquid-container {
  position: absolute;
  bottom: 10px;
  right: 50.5%;
  width: 23%;
  height: 33%;
  margin-left: 10px;
  margin-right: 10px;
  background: rgba(0, 104, 183, 0.13);
  .echarts-box {
    height: calc(100% - 42px) !important;
    width: 100%;
  }
}
.full-screen-container {
  height: 30.6%;
}
</style>
