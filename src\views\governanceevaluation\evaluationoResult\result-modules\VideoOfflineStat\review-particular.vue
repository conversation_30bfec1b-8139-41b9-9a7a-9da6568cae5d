<template>
  <div class="review-particular-box auto-fill">
    <dynamic-condition :form-item-data="formItemData" :form-data="formData" @search="startSearch" @reset="startSearch">
    </dynamic-condition>
    <div class="btn-bar mb-md">
      <Button type="primary" class="button-export" @click="onExport" :loading="exportLoading">
        <i class="icon-font icon-daochu font-white mr-xs f-14"></i>
        <span class="ml-xs">导出</span>
      </Button>
    </div>
    <!-- @onSortChange="sortChange" -->
    <ui-table class="ui-table auto-fill" :table-columns="tableColumns" :table-data="tableData" :loading="tableLoading">
      <template #offLineTotalOfM="{ row }">
        <Tooltip
          transfer
          transfer-class-name="calender-tip"
          placement="bottom"
          @on-popper-show="handlePopperShow($event, row)"
          @on-popper-hide="handlePopperHide($event, row)"
        >
          <span class="unqualified-color underline-text">{{ row.offLineTotalOfM }}</span>
          <div slot="content" v-if="row.offLineTotalOfM">
            <nopage-calender
              v-if="row.visible"
              :start-date="row.checkStartDate"
              :end-date="row.checkEndDate"
              :hiatus="JSON.parse(row.timeDayJsonStr)"
            ></nopage-calender>
          </div>
        </Tooltip>
      </template>
      <template #serialOffLineOfM="{ row }">
        <Tooltip
          transfer
          transfer-class-name="calender-tip"
          placement="bottom"
          @on-popper-show="handlePopperShow($event, row)"
          @on-popper-hide="handlePopperHide($event, row)"
        >
          <span class="unqualified-color underline-text">{{ row.serialOffLineOfM }}</span>
          <div slot="content" v-if="row.serialOffLineOfM">
            <nopage-calender
              v-if="row.visible"
              :start-date="row.checkStartDate"
              :end-date="row.checkEndDate"
              :hiatus="JSON.parse(row.timeDayJsonStr)"
            ></nopage-calender>
          </div>
        </Tooltip>
      </template>
      <template #phyStatus="{ row }">
        <span v-if="row.phyStatusText" :class="row.phyStatusText === '可用' ? 'qualified-color' : 'unqualified-color'">
          {{ row.phyStatusText }}
        </span>
        <span v-else>--</span>
      </template>
      <template #tagNames="{ row }">
        <tags-more :tag-list="row.tagList || []"></tags-more>
      </template>
    </ui-table>
    <ui-page
      slot="page"
      class="page menu-content-background"
      :page-data="pageData"
      @changePage="handlePage"
      @changePageSize="handlePageSize"
    >
    </ui-page>
    <!-- 导出   -->
    <export-data ref="exportModule" :exportLoading="exportLoading" @handleExport="handleExport"> </export-data>
  </div>
</template>
<script>
import dealWatch from '@/mixins/deal-watch';
import downLoadTips from '@/mixins/download-tips';
import particularMixin from '@/views/governanceevaluation/evaluationoResult/util/Mixins/particularMixin';
import { formItemData, tableColumns } from './util/enum/ReviewParticular';

export default {
  props: {},
  components: {
    ExportData: require('../components/export-data').default,
    UiTable: require('@/components/ui-table.vue').default,
    DynamicCondition: require('@/views/governanceevaluation/evaluationoResult/components/dynamic-condition.vue')
      .default,
    TagsMore: require('@/components/tags-more.vue').default,
    NopageCalender:
      require('@/views/governanceevaluation/evaluationoResult/result-modules/VideoHistoryComplete/components/nopage-calender.vue')
        .default,
  },
  mixins: [dealWatch, downLoadTips, particularMixin],
  data() {
    return {
      // 过滤条件
      formItemData: formItemData,
      formData: {
        deviceId: '',
        deviceName: '',
        isImportant: '',
        serialOffMin: null,
        serialOffMax: null,
        offerTotalMin: null,
        offerTotalMax: null,
      },
      // 表格
      tableLoading: false,
      tableColumns: tableColumns,
      tableData: [],
      pageData: {
        pageNum: 1,
        pageSize: 20,
        totalCount: 0,
      },
      sortData: {
        // sortField: '', // 排序字段
        // sort: '', // 排序方式: ASC("升序") | DESC("降序")
      },
      // 导出
      exportLoading: false,
    };
  },
  computed: {},
  created() {
    this.startWatch(
      '$route.query',
      () => {
        this.getTableList();
      },
      { deep: true, immediate: true },
    );
  },
  watch: {},
  methods: {
    // sortChange({ key, order }) {
    //   if (order === 'normal') {
    //     this.sortData = {};
    //   } else {
    //     this.sortData = {
    //       sortField: key,
    //       sort: order.toUpperCase(),
    //     };
    //   }
    //   this.getTableList();
    // },
    handlePage(val) {
      this.pageData.pageNum = val;
      this.getTableList();
    },
    handlePageSize(val) {
      this.pageData.pageNum = 1;
      this.pageData.pageSize = val;
      this.getTableList();
    },
    startSearch(data) {
      this.pageData.pageNum = 1;
      this.formData = data;
      this.getTableList();
    },
    async getTableList() {
      this.MixinGetTableData().then((data) => {
        this.tableData = data.entities || [];
        this.pageData.totalCount = data.total;
      });
    },
    handlePopperShow(e, row) {
      this.$set(row, 'visible', true);
    },
    handlePopperHide(e, row) {
      this.$set(row, 'visible', false);
    },
    // 导出
    onExport: function () {
      this.$refs.exportModule.init(this.$route.query.batchId);
    },
    handleExport(val) {
      this.MixinGetExport(val);
    },
  },
};
</script>
<style lang="less" scoped>
.review-particular-box {
  padding: 10px;
  height: 100%;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  .btn-bar {
    display: flex;
    justify-content: flex-end;
  }
  .underline-text {
    cursor: pointer;
    text-decoration: underline;
  }
  .unqualified-color {
    color: var(--color-failed);
  }
  .qualified-color {
    color: var(--color-success);
  }
}
</style>
