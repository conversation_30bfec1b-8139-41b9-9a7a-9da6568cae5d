<template>
  <ui-modal v-model="visible" title="经纬度精度检测配置" width="39.06rem" @query="handleSave">
    <Form ref="formValidate" :label-width="0">
      <FormItem label="" prop="point" v-if="indexRuleId === 7">
        <div class="testing-item">
          <p>
            <span class="base-text-color">经纬度不能少于</span>
            <InputNumber v-model="longitudeLatitudeAccuracy" class="form-width ml-sm" />
            <span class="base-text-color ml-sm">位小数</span>
          </p>
        </div>
      </FormItem>
      <FormItem label="" prop="point" v-if="indexRuleId === 9">
        <div class="testing-item">
          <p>
            <span class="base-text-color">经纬度与安装地址偏移距离不超过</span>
            <InputNumber v-model="longitudeLatitudeOffset" class="form-width ml-sm" />
            <span class="base-text-color ml-sm">米</span>
          </p>
        </div>
      </FormItem>
    </Form>
  </ui-modal>
</template>
<script>
import cascade from '@/config/api/cascade';

export default {
  props: {},
  data() {
    return {
      formData: {
        list: [],
        ruleId: '',
      },
      visible: false,
      indexRuleId: '',
      longitudeLatitudeAccuracy: 6,
      longitudeLatitudeOffset: 100,
    };
  },
  methods: {
    resetForm() {
      this.longitudeLatitudeAccuracy = 6;
      this.longitudeLatitudeOffset = 100;
    },
    init({ indexRuleId }) {
      this.indexRuleId = indexRuleId;
      this.visible = true;
      this.getInitData();
    },
    async getInitData() {
      try {
        let params = {
          ruleId: this.indexRuleId,
        };
        let {
          data: { data },
        } = await this.$http.post(cascade.configQueryAllData, params);
        let dataRight = data.right;
        let valueData = parseFloat(dataRight[0].repeatCount || 0);
        this.formData.list = dataRight;
        this.indexRuleId === 7
          ? (this.longitudeLatitudeAccuracy = valueData || 6)
          : (this.longitudeLatitudeOffset = valueData || 100);
      } catch (e) {
        console.error(e);
      }
    },
    async handleSave() {
      try {
        if (this.indexRuleId === 7 && !this.longitudeLatitudeAccuracy) {
          return this.$Message.error('请输入经纬度精度');
        } else if (this.indexRuleId === 9 && !this.longitudeLatitudeOffset) {
          return this.$Message.error('请输入偏移距离');
        }
        let resultFromData = { ...this.formData };
        resultFromData.list[0].repeatCount =
          this.indexRuleId === 7 ? this.longitudeLatitudeAccuracy : this.longitudeLatitudeOffset;
        resultFromData.ruleId = this.indexRuleId;
        await this.$http.post(cascade.configUpdateAllData, resultFromData);
        this.$Message.success('成功');
        this.visible = false;
      } catch (e) {
        console.error(e);
      }
    },
  },
  watch: {},
  components: {},
};
</script>
<style lang="less" scoped>
.form-width {
  width: 118px;
}

@{_deep} .ivu-modal-body {
  padding: 20px 50px !important;
  text-align: center;
}
</style>
