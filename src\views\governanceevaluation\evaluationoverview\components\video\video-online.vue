<template>
  <!-- 视频监控在线率提升 -->
  <div class="video-online auto-fill">
    <div class="content auto-fill">
      <div class="abnormal-title">
        <div>
          <i class="icon-font icon-jiancejieguotongji f-16 color-filter"></i>
          <span class="f-16 color-filter ml-sm">检测结果统计</span>
        </div>
        <div>
          <create-tabs
            class="mr-md inline"
            componentName="VideoHistoryOnline"
            tabs-text="本年度视频历史最高在线情况"
            :tabs-query="{ regionName: regionName, examineTime, ...paramsData }"
          >
            <span class="underline-text font-blue mr-lg f-16">查看本年度视频历史最高在线情况</span>
          </create-tabs>
          <Button type="primary" class="btn_search" @click="getExport" :loading="exportLoading">
            <i class="icon-font icon-daochu font-white f-14 mr-xs"></i>
            <span class="inline ml-xs f-14">导出</span>
          </Button>
        </div>
      </div>
      <ui-table class="auto-fill" :tableColumns="tableColumns" :table-data="tableData" :loading="loading">
        <template v-for="(item, index) in tableColumns" :slot="item.slot" slot-scope="{ row }">
          <slot v-if="item.slot === 'onLineCount' || item.slot === 'offLineCount'" :name="item.slot" :row="row">
            <create-tabs
              :key="'line' + index"
              class="mr-md inline"
              componentName="OnOffLineDetail"
              :tabs-text="(item.slot === 'onLineCount' ? '在线' : '离线') + '视频监控'"
              :tabs-query="{
                regionName: row.orgRegionText,
                examineTime,
                isOnline: item.slot === 'onLineCount' ? '1' : '0',
                ...paramsData,
                orgRegionCode: row.orgRegionCode,
              }"
            >
              <span class="underline-text font-blue">{{ row[item.slot] }}</span>
            </create-tabs>
          </slot>
        </template>
      </ui-table>
    </div>
  </div>
</template>
<script>
import evaluationoverview from '@/config/api/evaluationoverview';
import downLoadTips from '@/mixins/download-tips';
import dealWatch from '@/mixins/deal-watch';
import { onlineTableColumns } from './staticfields';

export default {
  name: 'video-online',
  mixins: [downLoadTips, dealWatch],
  data() {
    return {
      loading: false,
      tableColumns: [],
      tableData: [],
      exportLoading: false,
      componentName: null,
    };
  },
  props: {
    paramsData: {
      type: Object,
      default: () => {},
    },
    regionName: {
      type: String,
      default: '',
    },
    examineTime: {
      type: String,
      default: '',
    },
  },
  created() {
    this.tableColumns = onlineTableColumns;
  },
  methods: {
    async getExport() {
      this.exportLoading = true;
      let params = {
        indexId: this.paramsData.indexId,
        batchId: this.paramsData.batchId,
        access: this.paramsData.access,
        displayType: this.paramsData.displayType,
        orgRegionCode: this.paramsData.orgRegionCode,
      };
      try {
        this.$_openDownloadTip();
        const res = await this.$http.post(evaluationoverview.exportDeviceDetailData, params);
        await this.$util.common.transformBlob(res.data.data);
        this.exportLoading = false;
      } catch (err) {
        console.log(err);
      }
    },
    async getTableList() {
      try {
        const params = {
          indexId: this.paramsData.indexId,
          batchId: this.paramsData.batchId,
          access: this.paramsData.access,
          displayType: this.paramsData.displayType,
          orgRegionCode: this.paramsData.orgRegionCode,
        };
        let res = await this.$http.post(evaluationoverview.getDetailData, params);
        this.tableData = res.data.data;
      } catch (e) {
        console.log(e);
      }
    },
  },
  watch: {
    paramsData: {
      handler(val) {
        if (val.orgRegionCode) {
          this.getTableList();
        }
      },
      deep: true,
      immediate: true,
    },
  },
  components: {
    UiTable: require('@/components/ui-table.vue').default,
    CreateTabs: require('@/components/create-tabs/create-tabs.vue').default,
  },
};
</script>

<style lang="less" scoped>
.video-online {
  width: 100%;
  height: 100%;

  .content {
    color: #fff;
    background: @bg-blue-block;
    border-radius: 4px;
    position: relative;

    .abnormal-title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 48px;
      .color-filter {
        color: rgba(43, 132, 226, 1);
        vertical-align: middle;
      }
    }
  }
  .underline-text {
    text-decoration: underline;
    cursor: pointer;
  }
}
</style>
