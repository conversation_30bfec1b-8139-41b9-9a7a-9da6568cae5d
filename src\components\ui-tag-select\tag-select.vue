<template>
	<div class="ivu-tag-select">
		<div :class="classes" v-if="!hideCheckAll" @click="handleCheckAll">全部</div>
		<slot></slot>
	</div>
</template>
<script>
	import { findComponentsDownward } from '@/libs/system/index'
	import Emitter from './mixins/emitter.js'
	export default {
		name: 'TagSelect',
		mixins: [Emitter],
		provide() {
			return { TagSelectInstance: this }
		},
		props: {
			value: {
				type: [Array, String],
				default() {
					return []
				}
			},
			type: {
				type: String,
				default: 'radio' //radio  checkbox
			},
			multiple: {
				type: Boolean,
				default: false
			},
			// 回显，指定选中标签
			echo: {
				type: String,
				default: null
			},
            hideCheckAll: {
                type: Boolean,
				default: false
            }
		},
		data() {
			return {
				// hideCheckAll: false,
				currentValue: this.value,
				checkedAll: false
			}
		},
		computed: {
			classes() {
				return {
					'ivu-tag-select-option': true,
					'select': this.checkedAll
					// primary: this.checkedAll
				}
			}
		},
		watch: {
			value(val) {
				this.currentValue = val
				this.handleUpdateTags()
			},
			echo(newVal, oldVal) {
				//   console.log('特殊车辆回显数据', newVal, oldVal)
				if (newVal) {
					const tags = findComponentsDownward(this, 'TagSelectOption')
					tags.forEach(tag => {
						if (tag.name === newVal) {
							tag.checked = true
						} else {
							tag.checked = false
						}
					})
					if (newVal) {
						this.checkedAll = false
					}
					this.currentValue = newVal
				}
			}
		},
		methods: {
			handleUpdateTags() {
				const tags = findComponentsDownward(this, 'TagSelectOption')
				tags.forEach(tag => {
					if (this.currentValue.indexOf(tag.name) >= 0) {
						tag.checked = true;
					} else {
						tag.checked = false
					}
				})
				if (this.value == '近一天') {
					this.checkedAll = false
				} else {
					this.checkedAll = true
				}


			},
			handleChangeTag(name) {
				let checkedAll = true
				const tags = findComponentsDownward(this, 'TagSelectOption')
				if (this.type !== 'radio') {
					const checkedNames = []
					tags.forEach(tag => {
						if (tag.checked) {
							checkedNames.push(tag.name)
						} else {
							checkedAll = false
						}
					})
					this.currentValue = checkedNames
					this.$emit('input', checkedNames)
					this.$emit('on-change', [...checkedNames], name)
					// name 有值，说明是从内部点击 Tag 来的，而非点击全部，这时检测全选的状态
					if (name) {
						this.checkedAll = checkedAll
					}
                    if(checkedNames.length == 0) {
                        this.checkedAll = true
                    }
				} else {
					tags.forEach(tag => {
						if (tag.name === name) {
							tag.checked = true
						} else {
							tag.checked = false
						}
					})
					if (name) {
						this.checkedAll = false
					}
					this.currentValue = name
					this.$emit('input', name)
				}
				this.dispatch('FormItem', 'on-form-change', name)
			},
			handleCheckAll() {
				if (this.checkedAll) {
					this.checkedAll = false
				} else {
					this.checkedAll = true
				}
				if (this.type !== 'radio') {
					const tags = findComponentsDownward(this, 'TagSelectOption')
					tags.forEach(tag => {
						// tag.checked = this.checkedAll // 暂时不需要全选
					})
				}
				this.handleChangeTag()
				this.$emit('on-checked-all', this.checkedAll)
			},
			// 清空选中
			clearChecked(state = true) {//不需要全部传false
				this.checkedAll = state;
				this.currentValue = null
				const tags = findComponentsDownward(this, 'TagSelectOption')
				tags.forEach((tag, index) => {
                    if(index == 0 && !state) { //处理不需要全部按钮事件
                        tag.checked = true
                    }else{
                        tag.checked = false
                    }
				})
			}
		},
		mounted() {
			this.handleUpdateTags()
		}
	}
</script>
<style lang="less" scoped>
	.ivu-tag-select {
		display: flex;
		align-items: center;
		flex-wrap: wrap;
		.ivu-tag-select-option {
			cursor: pointer;
			font-size: 14px;
			box-sizing: border-box;
			margin-right: 20px;
			white-space: nowrap;
			line-height: 24px;
		}
	}
	.select {
		color: #fff;
		background: #2c86f8;
		padding: 0 4px;
		border-radius: 2px;
	}
</style>
