<template>
  <!-- 目前 personTypes不统一，单独拆分组件-->
  <div class="ui-gather-card" @click="detail">
    <!-- @click.stop="viewBigPic()" -->
    <div class="ui-gather-card-left">
      <ui-image :src="list.facePath" />
      <!-- <img :src="list.identityPhoto ? list.identityPhoto : errimg" alt="" /> -->
    </div>
    <div class="ui-gather-card-right">
      <div class="ui-gather-card-right-item" v-for="(item, index) in cardInfo" :key="index">
        <div v-if="!item.children">
          <span class="ui-gather-card-right-item-label">{{ item.name }}</span>
          <span
            :title="list[item.value]"
            v-if="list[item.value]"
            class="ui-gather-card-right-item-value"
            :style="{ color: item.color ? item.color : '#ffffff' }"
            >{{ list[item.value] }}</span
          >
          <span v-else style="opacity: 0" class="ui-gather-card-right-item-value">暂无</span>
        </div>
        <div v-else>
          <div
            class="ui-gather-card-right-item"
            v-for="(item1, index) in item.children"
            :key="index"
            style="width: 50%; display: inline-block; white-space: nowrap"
          >
            <span class="ui-gather-card-right-item-label">{{ item1.name }}</span>
            <span
              :title="list[item1.value]"
              v-if="list[item1.value]"
              class="ui-gather-card-right-item-value"
              :style="{ color: item1.color ? item1.color : '#ffffff' }"
              >{{ list[item1.value] }}</span
            >
            <span v-else style="opacity: 0" class="ui-gather-card-right-item-value">暂无</span>
          </div>
        </div>
      </div>
      <tags-more
        :personTypeList="personTypeList"
        :tagList="list.personType ? list.personType.split(',') : []"
        :defaultTags="defaultTags"
        placement="left-start"
        bgColor="#2D435F"
      ></tags-more>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    cardInfo: {
      type: Array,
      default: () => [],
    },
    list: {
      type: Object,
      default: () => {},
    },
    defaultTags: {
      type: Number,
      default: 4,
    },
    personTypeList: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      tagData: [],
    };
  },
  created() {},
  methods: {
    detail() {
      this.$emit('detail', this.list);
    },
    viewBigPic() {
      this.$emit('viewBigPic', this.list);
    },
  },
  watch: {
    defaultTags() {
      return this.defaultTags;
    },
  },
  components: {
    TagsMore: require('../../components/tags-more').default,
    uiImage: require('@/components/ui-image').default,
  },
};
</script>
<style lang="less" scoped>
.ui-gather-card {
  display: flex;
  height: max-content;
  // min-height: 158px;
  margin-bottom: 10px;
  padding: 10px;
  background: #0f2f59;
  &-left {
    width: 138px;
    height: 138px;
    margin-right: 20px;
    img {
      width: 100%;
      height: 100%;
    }
  }
  &-right {
    flex: 1;
    &-item {
      margin-bottom: 8px;
      font-size: 14px;
      &-label {
        color: #8797ac;
      }
      &-value {
        color: #ffffff;
      }
    }
  }
  .tag {
    margin-right: 10px;
    padding: 3px 10px;
    font-size: 12px;
    background-color: #0e8f0e;
    color: #ffffff;
    border-radius: 4px;
  }
  .ui-gather-card-right-item-value {
    display: inline-block;
    width: 64%;
    overflow: hidden;
    text-overflow: ellipsis;
    position: relative;
    top: 6px;
  }
}
// @{_deep} .ivu {
//   &-tag{
//     &:hover{
//       background: var(--color-primary) !important;
//     }
//   }
// }
</style>
