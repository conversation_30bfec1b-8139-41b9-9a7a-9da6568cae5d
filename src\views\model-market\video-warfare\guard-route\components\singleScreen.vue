<template>
  <div
    ref="single-screen-wrap"
    class="single-screen-wrap"
    :style="optionsStyle"
  >
    <div class="single-screen-header">
      <span class="common-right" @click.stop="changeStyle"
        >视频播放栏
        <i
          class="iconfont icon-jiantou fr font-style"
          :class="{ up: toggle }"
        ></i>
      </span>
    </div>
    <div class="single-screen-ocx-wrap UIOCX_SINGLE_SCREEN_MAIN_WRAPER">
      <h5-player
        :options="h5Options"
        :noVideoMsg="true"
        ref="monitorH5Player"
        sceneFrom="guardRouteVideo"
      ></h5-player>
    </div>
    <div class="single-screen-bottom">
      <span>已经通过</span>
      <span>正在通过</span>
      <span>即将通过</span>
    </div>
  </div>
</template>
<script>
import { playVideo } from "@/api/device";

export default {
  name: "singleScreen",
  data() {
    return {
      toggle: false,
      h5Options: {
        layout: "1*3",
      },
      passDevice: null,
      nowDevice: null,
      nextDevice: null,
    };
  },
  props: {},
  computed: {
    optionsStyle() {
      let height = this.$refs["single-screen-wrap"]
        ? this.$refs["single-screen-wrap"].offsetHeight
        : 0;
      return {
        width: "100%",
        bottom: this.toggle ? `-${height}px` : "5px",
        position: "fixed",
        display: "inline",
      };
    },
  },
  mounted() {},
  methods: {
    changeStyle() {
      this.toggle = !this.toggle;
    },
    clearAll() {
      this.$refs.monitorH5Player.closeAll();
      this.passDevice = null;
      this.nowDevice = null;
      this.nextDevice = null;
    },
    playVideoByIndex(device) {
      let index = this.$refs.monitorH5Player.videoObj.focusIndex;
      if (index == 0) {
        this.passDevice = device;
      } else if (index == 1) {
        this.nowDevice = device;
      } else if (index == 2) {
        this.nextDevice = device;
      }
      this.$refs.monitorH5Player.playStream(device, "live", index);
      this.$emit("playingDevice", {
        passDevice: this.passDevice,
        nowDevice: this.nowDevice,
        nextDevice: this.nextDevice,
      });
    },
    playVideo(device, type = "next") {
      if (type == "next") {
        this.passDevice = this.nowDevice;
        this.nowDevice = this.nextDevice;
        this.nextDevice = device;
        this.$refs.monitorH5Player.playStream(this.nextDevice, "live", 2);
        this.passDevice &&
          this.$refs.monitorH5Player.playStream(this.passDevice, "live", 0);
        this.nowDevice &&
          this.$refs.monitorH5Player.playStream(this.nowDevice, "live", 1);
        this.$emit("playingDevice", {
          passDevice: this.passDevice,
          nowDevice: this.nowDevice,
          nextDevice: this.nextDevice,
        });
      } else {
        this.nextDevice = this.nowDevice;
        this.nowDevice = this.passDevice;
        this.passDevice = device;
        this.$refs.monitorH5Player.playStream(this.passDevice, "live", 0);
        this.nowDevice &&
          this.$refs.monitorH5Player.playStream(this.nowDevice, "live", 1);
        this.nextDevice &&
          this.$refs.monitorH5Player.playStream(this.nextDevice, "live", 2);
        this.$emit("playingDevice", {
          passDevice: this.passDevice,
          nowDevice: this.nowDevice,
          nextDevice: this.nextDevice,
        });
      }
    },
    // 路线上只有一个设备
    playVideoByOneDevice(device, index) {
      if(index === 1) {
        this.$refs.monitorH5Player.stopStream(2);
      }
      if(index === 0) {
        this.$refs.monitorH5Player.stopStream(1);
      }
      this.$refs.monitorH5Player.playStream(device, "live", index);
      this.$emit("playingDevice", {
          passDevice: device,
          nowDevice: device,
          nextDevice: device,
        });
    },
  },
};
</script>
<style lang="less">
.single-screen-wrap {
  z-index: 650;
  padding: 0 20px;
  max-width: 50vw;
  left: 50%;
  transform: translate(-50%);
  background: #ffffff;
  transition: all 1s;
  -moz-transition: all 1s; /* Firefox 4 */
  -webkit-transition: all 1s; /* Safari and Chrome */
  -o-transition: all 1s; /* Opera */
  &.background-style {
    background: #b1b5bf;
  }
  .single-screen-bottom {
    width: 100%;
    height: 35px;
    line-height: 35px;
    font-weight: bold;
    span {
      display: inline-block;
      width: 32%;
      text-align: center;
    }
  }
  .single-screen-header {
    height: 20px;
    .route-name {
      line-height: 35px;
      font-weight: bold;
      font-size: 14px;
    }
    .up {
      transform: rotate(180deg);
      display: inline-block;
    }
  }
  .common-right {
    float: right;
    cursor: pointer;
    margin-top: -35px;
    width: 100px;
    background: #ffffff;
    height: 35px;
    line-height: 35px;
    margin-right: -20px;
    padding: 0 10px;
  }
  .ocx-wrap {
    width: 100%;
    height: 250px;
    overflow: hidden;
    padding: 20px 20px 0 20px;
    background: #b1b5bf;
  }
  .font-style {
    font-size: 12px;
  }
}
.UIOCX_SINGLE_SCREEN_MAIN_WRAPER {
  width: 100%;
  height: 210px;
}
</style>
