<template>
  <!-- 人像档案置信率 -->
  <div class="basic-information auto-fill">
    <div class="information-header">
      <statistics class="statistics" :statistics-list="statisticsList" :isflexfix="false"></statistics>
    </div>
    <div class="information-main auto-fill">
      <div class="abnormal-title">
        <div>
          <i class="icon-font icon-yijudangshiyourenkoutongji f-16 color-filter"></i>
          <span class="f-16 color-filter ml-sm">检测结果详情</span>
        </div>
      </div>
      <!-- 列表 -->
      <TableCard ref="infoCard" :card-info="cardInfo" :load-data="loadDataCard">
        <template #card="{ row }">
          <UiGatherCard
            class="card"
            :list="row"
            :person-type-list="personTypeList"
            :card-info="cardInfo"
          ></UiGatherCard>
        </template>
      </TableCard>
    </div>
  </div>
</template>
<script>
import { mapActions, mapGetters } from 'vuex';
export default {
  name: 'file-confidence',
  components: {
    UiGatherCard: require('./component/ui-gather-card.vue').default,
    TableCard: require('./component/tableCard_str.vue').default,
    statistics: require('@/components/icon-statistics').default,
  },
  props: {
    paramsData: {
      type: Object,
      default: () => {},
    },
    statisticalData: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      statisticalList: {},
      paramsList: {},
      bigPictureShow: false,
      imgList: [],
      cardInfo: [
        { name: '姓名：', value: 'name' },
        { name: '证件号：', value: 'idCard' },
        { name: '抓拍总数: ', value: 'urlNum' },
      ],
      statisticsList: [
        {
          name: '人像档案置信数量',
          value: 19,
          icon: 'icon-shiyourenkoudengjizongshu',
          iconColor: 'icon-bg1',
          liBg: 'li-bg1',
          type: 'number',
          key: 'accuracyCount',
          textColor: 'color1',
        },
        {
          name: '人像档案总数',
          value: 20,
          icon: 'icon-shiyourenkoujudangshuliang',
          iconColor: 'icon-bg2',
          liBg: 'li-bg2',
          type: 'number',
          key: 'count',
          textColor: 'color2',
        },
        {
          name: '人像档案未置信数量',
          value: 1,
          icon: 'icon-shiyourenkouweijudangshuliang',
          iconColor: 'icon-bg4',
          liBg: 'li-bg4',
          type: 'number',
          key: 'unCount',
          textColor: 'color4',
        },
        {
          name: '人像档案置信率',
          value: '95%',
          icon: 'icon-renxiangdanganzhixinshuai',
          iconColor: 'icon-bg3',
          liBg: 'li-bg3',
          type: 'percentage',
          qualified: true,
          key: 'fileRate',
          textColor: 'color3',
        },
      ],
      loadDataCard: () => {
        return new Promise((resolve) => {
          resolve({
            data: {
              entities: [
                {
                  name: '李清',
                  idCard: '110112199106053012',
                  urlNum: '245',
                  abnormalTrajectoryNum: '3',
                  resultValue: '1.22',
                  facePath: require('@/assets/img/test/face1.jpg'),
                },
                {
                  name: '张志宏',
                  idCard: '110112199302013035',
                  urlNum: '50',
                  abnormalTrajectoryNum: '1',
                  resultValue: '2',
                  facePath: require('@/assets/img/test/face2.jpg'),
                },
                {
                  name: '荣瞬柜',
                  idCard: '******************',
                  urlNum: '245',
                  abnormalTrajectoryNum: '3',
                  resultValue: '2',
                  facePath: require('@/assets/img/test/face3.png'),
                },
                {
                  name: '章鸣亮',
                  idCard: '330520198805293076',
                  urlNum: '245',
                  abnormalTrajectoryNum: '3',
                  resultValue: '1.22',
                  facePath: require('@/assets/img/test/face4.jpg'),
                },
                {
                  name: '刘铮',
                  idCard: '******************',
                  urlNum: '185',
                  abnormalTrajectoryNum: '5',
                  resultValue: '2.7',
                  facePath: require('@/assets/img/test/face5.jpg'),
                },
                {
                  name: '徐壁玫',
                  idCard: '120100199709041428',
                  urlNum: '264',
                  abnormalTrajectoryNum: '15',
                  resultValue: '5.68',
                  facePath: require('@/assets/img/test/face6.jpg'),
                },
                {
                  name: '白鹭',
                  idCard: '230100197704191046',
                  urlNum: '143',
                  abnormalTrajectoryNum: '0',
                  resultValue: '0',
                  facePath: require('@/assets/img/test/face7.jpg'),
                },
                {
                  name: '何景瑞',
                  idCard: '150204198508086155',
                  urlNum: '76',
                  abnormalTrajectoryNum: '20',
                  resultValue: '26.32',
                  facePath: require('@/assets/img/test/face8.jpg'),
                },
                {
                  name: '乔佳周',
                  idCard: '510283198111268631',
                  urlNum: '435',
                  abnormalTrajectoryNum: '13',
                  resultValue: '2.99',
                  facePath: require('@/assets/img/test/face9.jpg'),
                },
                {
                  name: '赵荣西',
                  idCard: '230227198601300026',
                  urlNum: '156',
                  abnormalTrajectoryNum: '1',
                  resultValue: '0.64',
                  facePath: require('@/assets/img/test/face10.jpg'),
                },
                {
                  name: '尤耀华',
                  idCard: '******************',
                  urlNum: '486',
                  abnormalTrajectoryNum: '35',
                  resultValue: '7.2',
                  facePath: require('@/assets/img/test/face11.jpg'),
                },
                {
                  name: '韩载键',
                  idCard: '******************',
                  urlNum: '352',
                  abnormalTrajectoryNum: '0',
                  resultValue: '0',
                  facePath: require('@/assets/img/test/face12.jpg'),
                },
                {
                  name: '黄贾鹏',
                  idCard: '332529198509040019',
                  urlNum: '187',
                  abnormalTrajectoryNum: '0',
                  resultValue: '0',
                  facePath: require('@/assets/img/test/face13.jpg'),
                },
                {
                  name: '周陆',
                  idCard: '******************',
                  urlNum: '124',
                  abnormalTrajectoryNum: '11',
                  resultValue: '8.87',
                  facePath: require('@/assets/img/test/face14.jpg'),
                },
                {
                  name: '刘发',
                  idCard: '370882198903184217',
                  urlNum: '435',
                  abnormalTrajectoryNum: '0',
                  resultValue: '0',
                  facePath: require('@/assets/img/test/face15.jpg'),
                },
                {
                  name: '刘生光',
                  idCard: '120221199003230014',
                  urlNum: '54',
                  abnormalTrajectoryNum: '0',
                  resultValue: '0',
                  facePath: require('@/assets/img/test/face16.jpg'),
                },
                {
                  name: '李水萍',
                  idCard: '330226198911237031',
                  urlNum: '128',
                  abnormalTrajectoryNum: '3',
                  resultValue: '2.34',
                  facePath: require('@/assets/img/test/face17.jpg'),
                },
                {
                  name: '李茂闻',
                  idCard: '320106198410290421',
                  urlNum: '38',
                  abnormalTrajectoryNum: '0',
                  resultValue: '0',
                  facePath: require('@/assets/img/test/face18.jpg'),
                },
                {
                  name: '许杨晶',
                  idCard: '142701198401031224',
                  urlNum: '625',
                  abnormalTrajectoryNum: '8',
                  resultValue: '1.28',
                  facePath: require('@/assets/img/test/face19.jpg'),
                },
                {
                  name: '王威',
                  idCard: '330224197510040812',
                  urlNum: '285',
                  abnormalTrajectoryNum: '4',
                  resultValue: '1.4',
                  facePath: require('@/assets/img/test/face20.jpg'),
                },
              ],
              total: 20,
            },
          });
        });
      },
    };
  },
  watch: {
    statisticalData: {
      handler(val) {
        if (val) {
          // this.statisticalList = val;
          // this.statistical();
        }
      },
      deep: true,
      immediate: true,
    },
    paramsData: {
      handler(val) {
        if (val.orgRegionCode) {
          this.paramsList = val;
          this.init();
        }
      },
      deep: true,
      immediate: true,
    },
  },
  computed: {
    ...mapGetters({
      personTypeList: 'algorithm/person_type',
    }),
  },
  async mounted() {
    if (this.personTypeList.length == 0) await this.getAlldicData();
  },
  methods: {
    ...mapActions({
      getAlldicData: 'algorithm/getAlldicData',
    }),
    // 初始化
    init() {
      this.$nextTick(() => {
        this.$refs.infoCard.info(true);
      });
    },
    //统计
    statistical() {
      this.statisticsList[0].value = this.statisticalList.accuracyCount;
      this.statisticsList[1].value = this.statisticalList.count;
      this.statisticsList[2].value = this.statisticalList.unCount;
      if (this.statisticsList[0].value > 0 && this.statisticsList[1].value) {
        let rate = parseFloat(
          parseFloat(parseFloat(this.statisticsList[0].value) / parseFloat(this.statisticsList[1].value)) * 100,
        ).toFixed(2);
        if (rate >= this.statisticalList.qualifiedRate) {
          this.statisticsList[3].qualified = true;
        } else {
          this.statisticsList[3].qualified = false;
        }
        this.statisticsList[3].value = rate ? rate + '%' : '';
      } else {
        this.statisticsList[3].value = '0';
      }
    },
  },
};
</script>
<style lang="less" scoped>
.basic-information {
  .information-header {
    margin-top: 10px;
    height: 140px;
    display: flex;
    @{_deep}.information-statistics {
      width: 100%;
      height: 140px;
      padding: 20px;
      background: var(--bg-sub-content);
    }
    @{_deep}.information-statistics .statistics-ul li {
      width: 24.55% !important;
    }
  }
  .information-main {
    height: calc(100% - 140px);
    .abnormal-title {
      padding: 10px 0;
      margin-bottom: 10px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-bottom: 1px solid var(--devider-line);
      .color-filter {
        color: rgba(43, 132, 226, 1);
        vertical-align: middle;
      }
    }
  }
  .card {
    width: calc(calc(100% - 40px) / 4);
    margin: 0 5px 10px;
  }
  /deep/.information-statistics .statistics-ul .icon-budabiao,
  .icon-dabiao {
    top: 70px !important;
  }
}
</style>
