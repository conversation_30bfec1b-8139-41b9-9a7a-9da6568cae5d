<!-- 轨迹重复率 -->
<template>
  <div class="focus-repeat-rate" ref="contentScroll">
    <div class="information-header">
      <statistics class="statistics" :statistics-list="statisticsList" :isflexfix="false"></statistics>
    </div>
    <div class="information-main">
      <div class="abnormal-title">
        <div class="fl">
          <i class="icon-font icon-jiancejieguoxiangqing f-16 color-filter"></i>
          <span class="f-16 color-filter ml-sm">检测结果详情</span>
        </div>
      </div>
      <TableCard
        ref="infoCard"
        class="card-list"
        :loadData="loadDataCard"
        :cardInfo="cardInfo"
        :contentClientHeight="contentClientHeight"
      >
        <div slot="search" class="hearder-title">
          <SearchCard :searchNum="1" :checkStatus="checkStatus" @startSearch="startSearch"></SearchCard>
        </div>
        <template #card="{ row }">
          <InfoCard class="card1" :list="row" :cardInfo="cardInfo" @clickImage="detailInfo(row)"> </InfoCard>
        </template>
      </TableCard>
      <LookScene v-model="bigPictureShow" :img-list="imgList"></LookScene>
      <focus-detail ref="focusDetail" :paramsList="paramsList"></focus-detail>
    </div>
  </div>
</template>
<style lang="less" scoped>
.focus-repeat-rate {
  position: relative;
  overflow-y: auto;
  &::-webkit-scrollbar {
    display: none; /* Chrome Safari */
  }
  @{_deep} .ivu-table {
    &-tip,
    &-overflowX,
    &-body {
      min-height: calc(100vh - 520px) !important;
      //min-height: 290px !important;
    }
  }
  .card-list {
    width: 100%;
    min-height: 480px !important;
    max-height: calc(100vh - 480px);
  }
  .information-header {
    margin-top: 10px;
    display: flex;
    .information-statistics {
      display: flex;
      width: 100%;
      height: 100%;
      background: var(--bg-sub-content);
      padding: 15px;
      margin-right: 0px !important;
    }
    @{_deep}.information-statistics .statistics-ul li {
      width: 19.54% !important;
      margin-bottom: 0px !important;
      &:nth-child(5) {
        margin-right: 0;
      }
      &:nth-child(6) {
        margin-top: 10px;
      }
      .monitoring-data {
        margin-left: 30px;
      }
    }
  }
  .information-main {
    // height: calc(100% - 252px);
    .abnormal-title {
      height: 54px;
      line-height: 54px;
      border-bottom: 1px solid var(--devider-line);
      padding: 0 10px;
      .color-filter {
        color: rgba(43, 132, 226, 1);
        vertical-align: middle;
      }
    }
    .list {
      .ui-images {
        width: 56px !important;
        height: 56px !important;
      }
    }
    .hearder-title {
      position: relative;
      padding: 10px 0 10px 10px;

      .btn_search {
        position: absolute;
        right: 0px;
        top: 10px;
      }
    }
  }

  .card1 {
    width: calc(calc(100% - 100px) / 10);
    margin: 0 10px 10px 0;
  }
}
</style>

<script>
import evaluationoverview from '@/config/api/evaluationoverview';
import { mapActions, mapGetters } from 'vuex';
export default {
  name: 'focus-repeat-rate',
  data() {
    return {
      rankLoading: true,
      focusDetailVisible: false,
      getSecondaryPopUpData: evaluationoverview.getSecondaryPopUpData,
      statisticsList: [
        {
          key: 'personAmount',
          name: 'ZDR人员总量',
          value: 0,
          icon: 'icon-ZRDzongliang',
          iconColor: 'icon-bg1',
          liBg: 'li-bg1',
          type: 'number',
          textColor: 'color1',
        },
        {
          key: 'trackAmount',
          name: 'ZRD人像轨迹总量',
          value: 0,
          icon: 'icon-guijizhunqueshuai',
          iconColor: 'icon-bg2',
          liBg: 'li-bg2',
          type: 'number',
          textColor: 'color2',
        },
        {
          key: 'detectionAmount',
          name: '检测轨迹数量',
          value: 0,
          icon: 'icon-jianceguijishuliang',
          iconColor: 'icon-bg5',
          liBg: 'li-bg5',
          type: 'number',
          textColor: 'color5',
        },

        {
          key: 'impeachAmount',
          name: '重复轨迹数量',
          value: 0,
          icon: 'icon-zhunquexingcunyiguijishuliang',
          iconColor: 'icon-bg6',
          liBg: 'li-bg6',
          type: 'number',
          textColor: 'color6',
        },

        {
          key: 'qualifiedAmount',
          name: '未重复轨迹数量',
          value: 0,
          icon: 'icon-wufajianceguijishuliang',
          iconColor: 'icon-bg4',
          liBg: 'li-bg4',
          type: 'number',
          textColor: 'color4',
        },
        {
          name: '轨迹重复率',
          value: 0,
          icon: 'icon-zhongfuguijijiance',
          iconColor: 'icon-bg3',
          liBg: 'li-bg3',
          type: 'percentage',
          textColor: 'color3',
          key: 'resultValue',
        },
      ],
      loading: false,
      tableColumns: [
        { type: 'index', width: 70, title: '序号', align: 'center' },
        {
          title: '轨迹图像',
          key: 'trackImage',
          slot: 'trackImage',
          tooltip: true,
          minWidth: 150,
        },
        { title: '抓拍时间', key: 'shotTime', tooltip: true, minWidth: 150 },
        { title: '抓拍点位', key: 'catchPlace', tooltip: true, minWidth: 150 },
        {
          title: '证件照',
          key: 'identityPhoto',
          slot: 'identityPhoto',
          tooltip: true,
          minWidth: 150,
        },
        { title: '姓名', key: 'name', tooltip: true, minWidth: 150 },
        { title: '证件号', key: 'idCard', tooltip: true, minWidth: 150 },
        { title: '原始算法', key: 'similarity', tooltip: true, minWidth: 150 },
        {
          title: '算法识别结果',
          key: 'algResult',
          slot: 'algResult',
          tooltip: true,
          minWidth: 150,
        },
        {
          title: '综合判定',
          key: 'unableDetectTip',
          slot: 'unableDetectTip',
          tooltip: true,
          minWidth: 150,
        },
      ],
      checkStatus: [
        { name: '重复', checkKey: 1 },
        { name: '未重复', checkKey: 0 },
      ],
      tableData: [],
      searchData: {},
      exportLoading: false,
      statisticalList: {},
      rankData: [],
      paramsList: {},
      loadDataList: (parameter) => {
        return this.$http
          .post(
            evaluationoverview.getDetailData,
            Object.assign(
              parameter,
              {
                indexId: this.paramsList.indexId,
                batchId: this.paramsList.batchId,
                access: this.paramsList.access,
                displayType: this.paramsList.displayType,
                orgRegionCode: this.paramsList.orgRegionCode,
              },
              this.searchData,
            ),
          )
          .then((res) => {
            return res.data;
          });
      },
      loadDataCard: (parameter) => {
        return this.$http
          .post(
            evaluationoverview.getDetailData,
            Object.assign(
              parameter,
              {
                indexId: this.paramsList.indexId,
                batchId: this.paramsList.batchId,
                access: this.paramsList.access,
                displayType: this.paramsList.displayType,
                orgRegionCode: this.paramsList.orgRegionCode,
              },
              this.searchData,
            ),
          )
          .then((res) => {
            return res.data;
          });
      },
      cardInfo: [
        { name: '', value: 'shotTime' },
        { name: '', value: 'catchPlace' },
      ],
      imgList: [require('@/assets/img/navigation-page/systemmanagement.png')],
      bigPictureShow: false, //大图展示
      checkPicture: false, //查看图片
      currentRow: {},
      personList: [
        {
          label: '抓拍总量',
          count: 0,
          filedName: 'catchAmount',
        },
        {
          label: '准确性存疑轨迹数量',
          count: 0,
          filedName: 'impeachAmount',
        },
      ],
      contentClientHeight: 0,
    };
  },
  async mounted() {
    const proportion = window.screen.height / 1080;
    const clientHeight = this.$refs.contentScroll.clientHeight;
    this.contentClientHeight = clientHeight > 0 ? clientHeight - 50 * proportion : 0;
    if (this.personTypeList.length == 0) await this.getAlldicData();
  },
  filters: {
    separateNum(str) {
      str = String(str);
      let counter = 0;
      let result = '';
      for (let j = str.length - 1; j >= 0; j--) {
        counter++;
        result = str.charAt(j) + result;
        if (!(counter % 3) && j != 0) {
          result = ',' + result;
        }
      }

      return result;
    },
  },

  methods: {
    ...mapActions({
      getAlldicData: 'algorithm/getAlldicData',
    }),
    // 查看不合格图片
    clickRow(row) {
      this.currentRow = row;
      this.checkPicture = true;
    },
    startSearch(searchData) {
      this.searchData = searchData;
      this.$refs.infoCard.info(true);
    },
    viewBigPic(item) {
      if (!item) {
        this.$Message.warning('大图URL缺失');
        return;
      }
      this.imgList = [item];
      this.bigPictureShow = true;
    },
    async init() {
      this.$nextTick(() => {
        if (this.$refs.tagView) {
          this.$refs.tagView.curTag = 0;
        }
      });
      this.$nextTick(() => {
        this.$refs.infoCard.info(true);
      });
    },
    detailInfo(item) {
      this.$refs.focusDetail.show(item);
    },
    startDetailSearch(params) {
      this.$refs.captureDetail.startSearch(params);
    },

    changePage(val) {
      this.searchData.pageNum = val;
      this.getTableData();
    },
    changePageSize(val) {
      this.searchData.pageSize = val;
      this.getTableData();
    },
    statistical() {
      this.statisticsList.map((val) => {
        val.value = this.statisticalList[val.key] || 0;
        if (val.key === 'resultValue' && this.statisticalList.qualified === '1') {
          val.qualified = true;
        } else if (val.key === 'resultValue' && this.statisticalList.qualified !== '1') {
          val.qualified = false;
        }
      });
    },
  },
  props: {
    paramsData: {
      type: Object,
      default: () => {},
    },
    statisticalData: {
      type: Object,
      default: () => {},
    },

    rankList: {
      type: Array,
      default: () => [],
    },
  },
  computed: {
    ...mapGetters({
      personTypeList: 'algorithm/person_type',
    }),
  },
  watch: {
    statisticalData: {
      handler(val) {
        if (val) {
          // Object.keys(val).forEach((key) => {
          //   this.statisticsList.forEach((items) => {
          //     if (items.key === key) {
          //       this.$set(items, 'value', val[key] || 0)
          //       this.$set(items, 'qualified', val['qualified'])
          //     }
          //   })
          // })
          this.statisticalList = val;
          this.statistical();
        }
      },
      deep: true,
      immediate: true,
    },
    rankList: {
      handler(val) {
        if (val.length !== 0) {
          this.rankData = val;
        }
      },
      deep: true,
      immediate: true,
    },

    paramsData: {
      handler(val) {
        if (val.batchId) {
          this.paramsList = val;
          this.init();
        }
      },
      deep: true,
      immediate: true,
    },
  },

  components: {
    TableCard: require('./components/tableCard.vue').default,
    SearchCard: require('./components/searchCard.vue').default,
    InfoCard: require('./components/infoCard').default,
    FocusDetail: require('./components/focus-detail').default,
    LookScene: require('@/components/look-scene').default,
    statistics: require('@/components/icon-statistics').default,
  },
};
</script>
