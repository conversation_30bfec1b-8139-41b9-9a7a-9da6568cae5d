<template>
  <ui-modal v-model="visible" :styles="styles" title="比对详情" footerHide>
    <div class="asset-comparison-wrap auto-fill">
      <div>
        <Button type="primary" class="fr mb-sm" @click="handleUpdateResult" :loading="loading">
          <i class="icon-font icon-shoudongshuaxin"></i>
          <span class="inline vt-middle ml-xs">更新比对结果</span>
        </Button>
      </div>
      <div class="header clear-b">
        <div class="header-left">
          <div class="title">
            <span>
              <span class="tag">
                <span class="ml-sm">上级最新检测：{{ superiorStatisticsByDevice.examineTime }}</span>
              </span>
            </span>
          </div>
          <div class="content">
            <template v-for="(e, i) in statisticalQuantityList">
              <div class="content-item" :key="i">
                <span>
                  <i class="icon-font" :class="[e.icon, e.style]"></i>
                </span>
                <span>
                  <div class="name">{{ e.name }}</div>
                  <div class="count" :style="{ color: e.color }">
                    {{ e.leftCount }}
                  </div>
                </span>
              </div>
            </template>
          </div>
        </div>
        <div class="header-right">
          <div class="title">
            <span>
              <span class="tag">
                <span class="ml-sm">本级最新检测：{{ selfStatisticsByDevice.examineTime }}</span>
              </span>
            </span>
          </div>
          <div class="content">
            <template v-for="(e, i) in statisticalQuantityList">
              <div class="content-item" :key="i">
                <span>
                  <i class="icon-font" :class="[e.icon, e.style]"></i>
                </span>
                <span>
                  <div class="name">{{ e.name }}</div>
                  <div class="count" :style="{ color: e.color }">
                    {{ e.rightCount }}
                  </div>
                </span>
              </div>
            </template>
          </div>
        </div>
      </div>
      <div class="tab-line">
        <div class="tab-line-left f-16 font-blue">
          结果差异（<span class="font-warning f-14">{{ difference }}</span
          >）
        </div>
        <div class="tab-line-right">
          <Button type="primary" class="ml-sm button-blue" @click="handleReviews(UNOPPOSE)">
            <i class="icon-font icon-quxiaofuhe f-16"></i>
            <span class="inline vt-middle ml-xs">取消复核</span>
          </Button>
          <Button type="primary" class="ml-sm button-blue" @click="handleReviews(OPPOSE)">
            <i class="icon-font icon-jieguofuhe f-16"></i>
            <span class="inline vt-middle ml-xs">结果复核</span>
          </Button>
          <Button type="primary" class="ml-sm button-blue" :loading="exportDataLoading" @click="exportExcel">
            <i class="icon-font icon-daochu delete-icon"></i>
            <span class="inline vt-middle ml-xs">导出</span>
          </Button>
        </div>
      </div>
      <div class="content">
        <div class="content-left auto-fill">
          <ui-table
            class="ui-table auto-fill"
            :loading="loading"
            :table-columns="tableColumns"
            :table-data="superiorDeviceListByDevice"
            @selectTable="selectTable"
          >
            <template #qualified="{ row }">
              <span class="check-status" :class="row.qualified === '1' ? 'bg-success' : 'bg-failed'">
                {{ row.result }}
              </span>
            </template>
            <template #action="{ row }">
              <ui-btn-tip
                class="operatbtn"
                :icon="row.oppose === OPPOSE ? 'icon-quxiaofuhe' : 'icon-jieguofuhe'"
                :content="row.oppose === OPPOSE ? '取消复核' : '结果复核'"
                @handleClick="handleReview(row)"
              ></ui-btn-tip>
            </template>
          </ui-table>
        </div>
        <div class="content-right auto-fill">
          <ui-table
            class="ui-table auto-fill"
            :loading="loading"
            :table-columns="rightTableColumns"
            :table-data="selfDeviceListByDevice"
          >
            <template #qualified="{ row }">
              <span :class="row.initialize ? 'qualified' : ''">
                {{ row.initialize ? '本级未检测' : '' }}
              </span>
            </template>
          </ui-table>
        </div>
      </div>

      <ui-page class="page" transfer :page-data="pageData" @changePage="changePage" @changePageSize="changePageSize">
      </ui-page>
    </div>
  </ui-modal>
</template>
<script>
import { deviceTableColumns } from '@/views/disposalfeedback/cascadelist/util/enum.js';
import deviceCompareMixin from '@/views/disposalfeedback/cascadelist/mixins/deviceCompareMixin.js';
import downLoadTips from '@/mixins/download-tips';
export default {
  name: 'device-result-compare',
  components: {
    UiTable: require('@/components/ui-table.vue').default,
  },
  props: {
    value: {},
    activeItem: {},
  },
  mixins: [deviceCompareMixin, downLoadTips],
  data() {
    return {
      visible: false,
      detail: {},
      styles: {
        width: '7rem',
      },
      difference: null,
      tableColumns: [
        { type: 'selection', width: 50, align: 'center' },
        ...deviceTableColumns,
        {
          minWidth: 60,
          title: '操作',
          slot: 'action',
          align: 'center',
        },
      ],
      rightTableColumns: [...deviceTableColumns],
      pageData: {
        totalCount: 0,
        pageNum: 1,
        pageSize: 20,
      },

      selectTableData: [],
      statisticalQuantityList: [
        {
          name: '检测总量',
          leftCount: 0,
          rightCount: 0,
          color: '#22C326',
          style: 'linear-blue',
          icon: 'icon-jianceshebeishuliang',
          key: 'actualNum',
        },
        {
          name: '合格数量',
          leftCount: 0,
          rightCount: 0,
          color: 'var(--color-primary)',
          style: 'linear-green',
          icon: 'icon-jianceshebeishuliang',
          key: 'qualifiedNum',
        },
        {
          name: '不合格数量',
          leftCount: 0,
          rightCount: 0,
          color: '#DD4826',
          style: 'linear-red',
          icon: 'icon-jianceshebeishuliang',
          key: 'unqualifiedNum',
        },
      ],
    };
  },
  watch: {
    async visible(val) {
      this.$emit('input', val);
      if (val) {
        await this.initList(false);
        this.setStatisticalQuantityList();
      }
    },
    value(val) {
      this.visible = val;
    },
  },
  created() {
    this.visible = true;
  },
  methods: {
    selectTable(val) {
      this.selectTableData = val;
    },
  },
};
</script>
<style scoped lang="less">
@{_deep} .ivu-modal-body {
  height: 700px;
}

.linear-blue {
  background: linear-gradient(180deg, #4ba0f5 0%, #0c44a7 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.linear-green {
  background: linear-gradient(180deg, #26d82c 0%, #127d0a 98%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.linear-red {
  background: linear-gradient(180deg, #f24e2c 0%, #772c0a 98%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.qualified {
  color: #ffffff;
  padding: 0 5px;
  display: inline-block;
  height: 22px;
  background: #4f5458;
  border-radius: 4px;
}

.asset-comparison-wrap {
  height: 100%;

  .header {
    display: flex;

    > div {
      width: 50%;
      padding-bottom: 10px;

      .title {
        height: 53px;
        line-height: 53px;
        font-size: 16px;
        color: var(--color-primary);
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;

        .tag {
          position: relative;
          &::before {
            content: '';
            position: absolute;
            width: 5.11px;
            height: 16.65px;
            background: var(--color-primary);
            top: 50%;
            transform: translate(0, -50%);
          }
        }

        & > span {
          margin-right: 62px;

          i {
            margin-right: 4px;
          }
        }
      }

      .content {
        height: 110px;
        display: flex;
        align-items: center;
        justify-content: space-evenly;
        width: 100%;
        background: var(--bg-sub-content);

        .content-item {
          width: 27%;
          display: flex;
          align-items: center;
          position: relative;

          .icon-font {
            font-size: 37px;
            margin-left: 30px;
            margin-right: 10px;
          }

          .name {
            font-size: 14px;
            color: #f5f5f5;
          }

          .count {
            font-size: 18px;
          }

          &:first-child::after {
            content: '';
            width: 0px;
          }
        }

        .content-item::after {
          content: '';
          width: 1px;
          height: 40px;
          position: absolute;
          top: 10px;
          left: 0;
          background: #1568ad;
        }
      }
    }

    .header-left {
      border-right: 1px solid var(--devider-line);
      padding-right: 10px;
    }

    .header-right {
      padding-left: 10px;
    }
  }

  .tab-line {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 48px;
    line-height: 48px;
    background: var(--bg-sub-content);
    padding: 10px;
    margin-bottom: 10px;

    .ivu-tabs-tab {
      float: left;
      height: 36px;
      line-height: 36px;
      font-size: 14px;
      border: 1px solid #1b82d2;
      border-right: none;
      padding: 0 22px;
      color: #406186;

      &:hover {
        background: var(--color-primary);
        color: #fff;
        cursor: pointer;
      }

      &:last-child {
        border-right: 1px solid #1b82d2;
      }
    }

    .active {
      background: var(--color-primary);
      color: #fff;
    }

    .tab-line-left {
      font-weight: 700;
    }

    .tab-line-right {
      display: flex;
      align-items: center;
    }
  }

  /deep/ .content {
    height: 400px;
    display: flex;

    .content-left {
      width: 50%;
      border-right: 1px solid var(--devider-line);
      padding-right: 10px;

      .operatbtn {
        color: var(--color-primary);
      }
    }

    .content-right {
      width: 50%;
      padding-left: 10px;
    }

    .content-left,
    .content-right {
      .ui-table {
        .ivu-table-column-left {
          padding-left: 10px;
        }
      }
    }

    .ivu-table-cell-ellipsis {
      .ivu-table-cell-slot {
        > span {
          display: block;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }
  }

  .footer {
    display: flex;

    .page-right,
    .page-left {
      width: 50%;
      padding: 20px 8px;
    }
  }
}
</style>
