<template>
  <div
    class="body-container"
    v-ui-loading="{ loading: echartLoading, tableData: showBack ? rankInfoList : filterEvaluationIndexResultData }"
  >
    <!-- 查看各区详细指标柱状图 -->
    <template>
      <self-drop-down
        v-show="showBack"
        :dropdown-list="dropdownList"
        :default-active-id="currentIndex.indexId || dropdownList[0]?.id"
        @chooseOne="chooseOneIndex"
      ></self-drop-down>
      <draw-echarts
        v-show="showBack"
        class="echarts"
        :echart-style="echartStyle"
        :echart-option="detailBarEchartOption"
        ref="detailBarEchartRef"
        @echartClick="echartClickJump"
      ></draw-echarts>
    </template>
    <!-- 所有指标的质量 -->
    <template>
      <draw-echarts
        v-show="!showBack"
        :echart-style="echartStyle"
        :echart-option="echartOption"
        ref="videoImageQualityRef"
        @echartClick="echartClick"
      ></draw-echarts>
    </template>
    <span class="next-echart">
      <i class="icon-font icon-zuojiantou1 f-12" @click="scrollAllData"> </i>
    </span>
  </div>
</template>

<script>
import governanceevaluation from '@/config/api/governanceevaluation';
import evaluationoverview from '@/config/api/evaluationoverview';
import TooltipDom from '@/views/home/<USER>/echarts-dom/tooltip-dom.vue';
import dataZoom from '@/mixins/data-zoom';
import xaxisLabelWorkNum from '@/mixins/xaxis-label-work-num';
import { mapGetters } from 'vuex';
export default {
  name: 'video-image-quality',
  mixins: [dataZoom, xaxisLabelWorkNum],
  components: {
    DrawEcharts: require('@/components/draw-echarts').default,
    selfDropDown: require('@/views/home/<USER>/echarts-dom/self-drop-down.vue').default,
  },
  props: {
    tableData: {},
    loading: {},
    styleType: {},
  },
  data() {
    return {
      echartStyle: {
        width: '1000%',
        height: '1000%',
      },
      activeTab: '2', // 脸
      evaluationIndexResultData: [],
      filterEvaluationIndexResultData: [],
      rankInfoList: [],
      rankList: [],
      currentIndex: {},
      echartOption: {},
      showBack: false,
      detailBarEchartOption: {},
      echartLoading: false,
      viewEchartIndexQualityRef: 0,
    };
  },
  computed: {
    ...mapGetters({
      getFullscreen: 'home/getFullscreen',
      comprehensiveConfig: 'common/getComprehensiveConfig',
    }),
    dropdownList() {
      return this.filterEvaluationIndexResultData.map((item) => {
        return {
          id: item.indexId,
          label: item.indexName,
        };
      });
    },
  },
  watch: {
    showBack(val) {
      if (val) {
        this.viewEchartIndex = 0;
      } else {
        this.viewEchartIndex = this.viewEchartIndexQualityRef;
      }
      this.initList();
    },
    viewEchartIndex(val) {
      if (!this.showBack) {
        this.viewEchartIndexQualityRef = val;
      }
    },
    loading(val) {
      this.echartLoading = val;
    },
    tableData: {
      handler(val) {
        this.evaluationIndexResultData = val || [];
        if (val) {
          this.initList();
        }
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {
    back() {
      this.showBack = false;
      this.initEchartsOption();
    },
    scrollAllData() {
      this.showBack
        ? this.scrollRight('detailBarEchartRef', this.rankInfoList, [], this.comprehensiveConfig.homeNum)
        : this.scrollOriginRight('videoImageQualityRef', this.filterEvaluationIndexResultData, [], 6);
    },
    scrollOriginRight(echartRef, echartList, xAxisData, viewNum) {
      // if (echartList.length < this.viewEchartNumber) {
      //   this.$Message.warning('没有可滑动展示的数据')
      //   return false
      // }
      // const num = Math.ceil(echartList.length / this.viewEchartNumber) - 1
      // if (this.viewEchartIndex === num) {
      //   this.viewEchartIndex = 0
      // } else {
      //   this.viewEchartIndex++
      // }
      //this.setDataZoom(echartRef, xAxisData, viewNum)
      this.scrollRight(echartRef, echartList, xAxisData, viewNum);
    },
    async getRankInfo() {
      this.echartLoading = true;
      const { civilCode, indexId, access, batchId } = this.currentIndex;
      let data = {
        indexId: indexId,
        batchId: batchId,
        access: access || 'REPORT_MODE',
        displayType: 'REGION',
        orgRegionCode: civilCode,
      };
      try {
        let res = await this.$http.post(evaluationoverview.getRankInfo, data);
        this.rankList = res.data.data || [];
      } catch (err) {
        console.log(err);
      } finally {
        this.echartLoading = false;
      }
    },
    async getNumRankInfo() {
      this.echartLoading = true;
      const { civilCode, indexId, access, batchId } = this.currentIndex;
      let data = {
        indexId: indexId,
        batchId: batchId,
        access: access || 'REPORT_MODE',
        displayType: 'REGION',
        orgRegionCode: civilCode,
        sortField: 'REGION_CODE',
      };
      try {
        let res = await this.$http.post(governanceevaluation.getNumRankInfo, data);
        this.rankInfoList = res.data.data || [];
      } catch (err) {
        console.log(err);
      } finally {
        this.echartLoading = false;
      }
    },
    async initList() {
      this.filterEvaluationIndexResultData = this.evaluationIndexResultData.filter((item) => {
        item.resultValue = item?.resultValue ?? 0;
        return item.indexModule === this.activeTab;
      });
      await this.initEchartsOption();
    },
    initEchartsOption() {
      return new Promise((resolve, reject) => {
        try {
          let opts = {
            data: this.filterEvaluationIndexResultData,
            homeStyleType: this.styleType,
          };
          this.echartOption = this.$util.doEcharts.videoImageQualityOption(opts);
          setTimeout(() => {
            this.setDataZoom('videoImageQualityRef', [], 6);
            this.resetXaxisLabelNumFn('videoImageQualityRef', this.echartOption, 6);
            resolve();
          });
        } catch (e) {
          reject();
        }
      });
    },
    async echartClick(params) {
      let { targetType, dataIndex } = params;
      if (targetType === 'axisLabel') {
        this.$emit('on-index-detail', this.filterEvaluationIndexResultData[dataIndex]);
        return;
      }
      this.currentIndex = params.data;
      await this.getNumRankInfo();
      this.showBack = true;
      // await this.getRankInfo()
      this.handleBarCharts();
    },
    echartClickJump(params) {
      this.$emit('on-jump', params.data.originData);
    },
    // 点进去详细的区详细质量的echarts
    handleBarCharts() {
      let { indexId, batchId, indexType } = this.currentIndex;
      let qualityData = [];
      let disQualityData = [];
      let xAxisData = [];
      let lineData = [];
      this.rankInfoList.forEach((item) => {
        let qualityRate = (item?.qualifiedNum / item?.actualNum) * 100 || 0;
        let detailData = {
          name: item.regionName,
          title: this.currentIndex.indexName,
          titleNum: `${qualityRate.toFixed(2)}%`,
          list: [
            {
              label: '合格数量',
              color: '#005AFE',
              num: item?.qualifiedNum ?? 0,
            },
            {
              label: '不合格数量',
              color: '#F1416F',
              num: item?.unqualifiedNum ?? 0,
            },
          ],
        };
        lineData.push(qualityRate);
        qualityData.push({
          value: item.qualifiedNum,
          data: detailData,
          originData: {
            ...item,
            indexId,
            batchId,
            indexType,
          },
          itemStyle: {
            borderWidth: item.qualifiedNum ? 1 : 0,
            borderColor: (() => {
              return new this.$echarts.graphic.LinearGradient(
                0,
                0,
                0,
                1,
                [
                  {
                    offset: 0,
                    color: 'rgba(157, 242, 255, 1)',
                  },
                  {
                    offset: 1,
                    color: 'rgba(0, 131, 253, 1)',
                  },
                ],
                false,
              );
            })(),
          },
        });
        disQualityData.push({
          value: item.unqualifiedNum,
          data: detailData,
          originData: {
            ...item,
            indexId,
            batchId,
            indexType,
          },
          itemStyle: {
            borderWidth: item.unqualifiedNum ? 1 : 0,
            borderColor: (() => {
              return new this.$echarts.graphic.LinearGradient(
                0,
                0,
                0,
                1,
                [
                  {
                    offset: 0,
                    color: 'rgba(255, 146, 176, 1)',
                  },
                  {
                    offset: 1,
                    color: 'rgba(251, 20, 79, 1)',
                  },
                ],
                false,
              );
            })(),
          },
        });
        xAxisData.push(item.regionName);
      });
      this.detailBarEchartOption = this.$util.doEcharts.getHomeAreaBar({
        toolTipDom: TooltipDom,
        yAxisData: [
          {
            min: 0,
            max: 100,
            axisLabel: {
              //y轴文字的配置
              color: '#8ABAFB',
              formatter: '{value} %',
              fontSize: this.$util.common.fontSize(12),
            },
          },
          {
            min: 0,
            axisLabel: {
              //y轴文字的配置
              color: '#8ABAFB',
              formatter: '{value}',
              fontSize: this.$util.common.fontSize(12),
            },
          },
        ],
        series: [
          {
            type: 'bar',
            name: '合格',
            color: ['rgba(0,186,255,1)', 'rgba(0,90,254,0.5)'],
            borderColor: ['rgba(157, 242, 255, 1)', 'rgba(0, 131, 253, 1)'],
            data: qualityData,
            yAxisIndex: 1,
          },
          {
            name: '不合格',
            type: 'bar',
            color: ['rgba(241, 65, 111, .5)', 'rgba(198, 2, 53, 1)'],
            borderColor: ['rgba(255, 146, 176, 1)', 'rgba(251, 20, 79, 1)'],
            data: disQualityData,
            yAxisIndex: 1,
          },
          {
            type: 'line',
            data: lineData,
            yAxisIndex: 0,
          },
        ],
        xAxisData: xAxisData,
      });
      setTimeout(() => {
        this.setDataZoom('detailBarEchartRef', [], this.comprehensiveConfig.homeNum);
      });
    },
    chooseOneIndex(indexId) {
      let one = this.filterEvaluationIndexResultData.find((item) => {
        return item.indexId === indexId;
      });
      this.echartClick({ data: one });
    },
  },
};
</script>

<style lang="less" scoped>
.body-container {
  position: relative;
  height: 100%;
  overflow-y: auto;
  overflow-x: hidden;

  .echarts {
    height: 100% !important;
    width: 100% !important;
  }
}
</style>
