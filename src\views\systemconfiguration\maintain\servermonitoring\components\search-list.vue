<template>
  <div class="search-list">
    <ui-label class="inline" label="关键词">
      <Input v-model="searchData.keyword" class="width-lg" placeholder="请输入IP地址、服务器名称"></Input>
    </ui-label>
    <ui-label class="inline ml-lg" label="设备状态">
      <Select class="width-md" v-model="searchData.status" placeholder="请选择设备状态" clearable :max-tag-count="1">
        <Option v-for="(item, index) in statusList" :key="index" :value="item.value">{{ item.label }} </Option>
      </Select>
    </ui-label>
    <div class="inline ml-lg">
      <Button type="primary" @click="search">查询</Button>
      <Button class="ml-sm" @click="reset">重置</Button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'search-list',
  props: {},
  data() {
    return {
      searchData: {
        keyword: '',
        status: '',
      },
      statusList: [
        { label: '在线', value: 1 },
        { label: '离线', value: 0 },
      ],
    };
  },
  mounted() {
    this.copySearchDataMx(this.searchData);
  },
  methods: {
    search() {
      this.$emit('startSearch', this.searchData);
    },
    reset() {
      this.resetSearchDataMx(this.searchData, () => {
        this.$emit('startSearch', this.searchData);
      });
    },
  },
};
</script>

<style lang="less" scoped>
.search-list {
  width: 100%;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}
</style>
